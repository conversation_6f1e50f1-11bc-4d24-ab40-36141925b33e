Command line invocation:
    /Applications/Xcode.app/Contents/Developer/usr/bin/xcodebuild -scheme VoicingTrainer -destination platform=macOS build-for-testing

Resolve Package Graph


Resolved source packages:
  KissFFT: https://github.com/AudioKit/KissFFT @ 1.0.0
  AudioKit: https://github.com/AudioKit/AudioKit @ 5.6.5
  MIDIKit: https://github.com/orchetect/MIDIKit @ 0.10.1
  Tonic: https://github.com/AudioKit/Tonic @ 2.1.0
  SoundpipeAudioKit: https://github.com/AudioKit/SoundpipeAudioKit @ 5.7.3
  TimecodeKit: https://github.com/orchetect/TimecodeKit @ 2.3.3
  AudioKitEX: https://github.com/AudioKit/AudioKitEX @ 5.6.2

ComputePackagePrebuildTargetDependencyGraph

Prepare packages

CreateBuildRequest

SendProjectDescription

CreateBuildOperation

ComputeTargetDependencyGraph
note: Building targets in dependency order
note: Target dependency graph (35 targets)
    Target 'VoicingTrainerUITests' in project 'VoicingTrainer'
        ➜ Explicit dependency on target 'VoicingTrainer' in project 'VoicingTrainer'
    Target 'VoicingTrainerTests' in project 'VoicingTrainer'
        ➜ Explicit dependency on target 'VoicingTrainer' in project 'VoicingTrainer'
    Target 'VoicingTrainer' in project 'VoicingTrainer'
        ➜ Explicit dependency on target 'MIDIKitControlSurfaces' in project 'MIDIKit'
        ➜ Explicit dependency on target 'AudioKitEX' in project 'AudioKitEX'
        ➜ Explicit dependency on target 'SoundpipeAudioKit' in project 'SoundpipeAudioKit'
        ➜ Explicit dependency on target 'MIDIKitIO' in project 'MIDIKit'
        ➜ Explicit dependency on target 'MIDIKit' in project 'MIDIKit'
        ➜ Explicit dependency on target 'MIDIKitSMF' in project 'MIDIKit'
        ➜ Explicit dependency on target 'AudioKit' in project 'AudioKit'
        ➜ Explicit dependency on target 'MIDIKitCore' in project 'MIDIKit'
    Target 'MIDIKitCore' in project 'MIDIKit'
        ➜ Explicit dependency on target 'MIDIKitCore' in project 'MIDIKit'
        ➜ Explicit dependency on target 'MIDIKitInternals' in project 'MIDIKit'
    Target 'MIDIKitSMF' in project 'MIDIKit'
        ➜ Explicit dependency on target 'MIDIKitSMF' in project 'MIDIKit'
        ➜ Explicit dependency on target 'MIDIKitInternals' in project 'MIDIKit'
        ➜ Explicit dependency on target 'MIDIKitCore' in project 'MIDIKit'
        ➜ Explicit dependency on target 'TimecodeKitCore' in project 'TimecodeKit'
    Target 'MIDIKit' in project 'MIDIKit'
        ➜ Explicit dependency on target 'MIDIKit' in project 'MIDIKit'
        ➜ Explicit dependency on target 'MIDIKitInternals' in project 'MIDIKit'
        ➜ Explicit dependency on target 'MIDIKitCore' in project 'MIDIKit'
        ➜ Explicit dependency on target 'MIDIKitIO' in project 'MIDIKit'
        ➜ Explicit dependency on target 'MIDIKitControlSurfaces' in project 'MIDIKit'
        ➜ Explicit dependency on target 'MIDIKitSMF' in project 'MIDIKit'
        ➜ Explicit dependency on target 'MIDIKitSync' in project 'MIDIKit'
        ➜ Explicit dependency on target 'MIDIKitUI' in project 'MIDIKit'
        ➜ Explicit dependency on target 'TimecodeKitCore' in project 'TimecodeKit'
    Target 'MIDIKit' in project 'MIDIKit'
        ➜ Explicit dependency on target 'MIDIKitInternals' in project 'MIDIKit'
        ➜ Explicit dependency on target 'MIDIKitCore' in project 'MIDIKit'
        ➜ Explicit dependency on target 'MIDIKitIO' in project 'MIDIKit'
        ➜ Explicit dependency on target 'MIDIKitControlSurfaces' in project 'MIDIKit'
        ➜ Explicit dependency on target 'MIDIKitSMF' in project 'MIDIKit'
        ➜ Explicit dependency on target 'MIDIKitSync' in project 'MIDIKit'
        ➜ Explicit dependency on target 'MIDIKitUI' in project 'MIDIKit'
        ➜ Explicit dependency on target 'TimecodeKitCore' in project 'TimecodeKit'
    Target 'MIDIKitUI' in project 'MIDIKit'
        ➜ Explicit dependency on target 'MIDIKitInternals' in project 'MIDIKit'
        ➜ Explicit dependency on target 'MIDIKitCore' in project 'MIDIKit'
        ➜ Explicit dependency on target 'MIDIKitIO' in project 'MIDIKit'
    Target 'MIDIKitSync' in project 'MIDIKit'
        ➜ Explicit dependency on target 'MIDIKitInternals' in project 'MIDIKit'
        ➜ Explicit dependency on target 'MIDIKitCore' in project 'MIDIKit'
        ➜ Explicit dependency on target 'MIDIKitIO' in project 'MIDIKit'
        ➜ Explicit dependency on target 'TimecodeKitCore' in project 'TimecodeKit'
    Target 'MIDIKitSMF' in project 'MIDIKit'
        ➜ Explicit dependency on target 'MIDIKitInternals' in project 'MIDIKit'
        ➜ Explicit dependency on target 'MIDIKitCore' in project 'MIDIKit'
        ➜ Explicit dependency on target 'TimecodeKitCore' in project 'TimecodeKit'
    Target 'TimecodeKitCore' in project 'TimecodeKit'
        ➜ Explicit dependency on target 'TimecodeKitCore' in project 'TimecodeKit'
    Target 'TimecodeKitCore' in project 'TimecodeKit' (no dependencies)
    Target 'MIDIKitIO' in project 'MIDIKit'
        ➜ Explicit dependency on target 'MIDIKitIO' in project 'MIDIKit'
        ➜ Explicit dependency on target 'MIDIKit_MIDIKitIO' in project 'MIDIKit'
        ➜ Explicit dependency on target 'MIDIKitInternals' in project 'MIDIKit'
        ➜ Explicit dependency on target 'MIDIKitCore' in project 'MIDIKit'
    Target 'SoundpipeAudioKit' in project 'SoundpipeAudioKit'
        ➜ Explicit dependency on target 'SoundpipeAudioKit' in project 'SoundpipeAudioKit'
        ➜ Explicit dependency on target 'Soundpipe' in project 'SoundpipeAudioKit'
        ➜ Explicit dependency on target 'CSoundpipeAudioKit' in project 'SoundpipeAudioKit'
        ➜ Explicit dependency on target 'KissFFT' in project 'KissFFT'
        ➜ Explicit dependency on target 'AudioKit' in project 'AudioKit'
        ➜ Explicit dependency on target 'AudioKitEX' in project 'AudioKitEX'
        ➜ Explicit dependency on target 'Tonic' in project 'Tonic'
    Target 'SoundpipeAudioKit' in project 'SoundpipeAudioKit'
        ➜ Explicit dependency on target 'Soundpipe' in project 'SoundpipeAudioKit'
        ➜ Explicit dependency on target 'CSoundpipeAudioKit' in project 'SoundpipeAudioKit'
        ➜ Explicit dependency on target 'KissFFT' in project 'KissFFT'
        ➜ Explicit dependency on target 'AudioKit' in project 'AudioKit'
        ➜ Explicit dependency on target 'AudioKitEX' in project 'AudioKitEX'
        ➜ Explicit dependency on target 'Tonic' in project 'Tonic'
    Target 'Tonic' in project 'Tonic'
        ➜ Explicit dependency on target 'Tonic' in project 'Tonic'
        ➜ Explicit dependency on target 'Tonic_Tonic' in project 'Tonic'
    Target 'Tonic' in project 'Tonic'
        ➜ Explicit dependency on target 'Tonic_Tonic' in project 'Tonic'
    Target 'Tonic_Tonic' in project 'Tonic' (no dependencies)
    Target 'CSoundpipeAudioKit' in project 'SoundpipeAudioKit'
        ➜ Explicit dependency on target 'Soundpipe' in project 'SoundpipeAudioKit'
        ➜ Explicit dependency on target 'KissFFT' in project 'KissFFT'
        ➜ Explicit dependency on target 'AudioKit' in project 'AudioKit'
        ➜ Explicit dependency on target 'AudioKitEX' in project 'AudioKitEX'
    Target 'Soundpipe' in project 'SoundpipeAudioKit'
        ➜ Explicit dependency on target 'KissFFT' in project 'KissFFT'
    Target 'KissFFT' in project 'KissFFT'
        ➜ Explicit dependency on target 'KissFFT' in project 'KissFFT'
    Target 'KissFFT' in project 'KissFFT' (no dependencies)
    Target 'AudioKitEX' in project 'AudioKitEX'
        ➜ Explicit dependency on target 'AudioKitEX' in project 'AudioKitEX'
        ➜ Explicit dependency on target 'CAudioKitEX' in project 'AudioKitEX'
        ➜ Explicit dependency on target 'AudioKit' in project 'AudioKit'
    Target 'AudioKitEX' in project 'AudioKitEX'
        ➜ Explicit dependency on target 'CAudioKitEX' in project 'AudioKitEX'
        ➜ Explicit dependency on target 'AudioKit' in project 'AudioKit'
    Target 'AudioKit' in project 'AudioKit'
        ➜ Explicit dependency on target 'AudioKit' in project 'AudioKit'
        ➜ Explicit dependency on target 'AudioKit_AudioKit' in project 'AudioKit'
    Target 'AudioKit' in project 'AudioKit'
        ➜ Explicit dependency on target 'AudioKit_AudioKit' in project 'AudioKit'
    Target 'AudioKit_AudioKit' in project 'AudioKit' (no dependencies)
    Target 'CAudioKitEX' in project 'AudioKitEX' (no dependencies)
    Target 'MIDIKitControlSurfaces' in project 'MIDIKit'
        ➜ Explicit dependency on target 'MIDIKitControlSurfaces' in project 'MIDIKit'
        ➜ Explicit dependency on target 'MIDIKitInternals' in project 'MIDIKit'
        ➜ Explicit dependency on target 'MIDIKitCore' in project 'MIDIKit'
        ➜ Explicit dependency on target 'MIDIKitIO' in project 'MIDIKit'
    Target 'MIDIKitControlSurfaces' in project 'MIDIKit'
        ➜ Explicit dependency on target 'MIDIKitInternals' in project 'MIDIKit'
        ➜ Explicit dependency on target 'MIDIKitCore' in project 'MIDIKit'
        ➜ Explicit dependency on target 'MIDIKitIO' in project 'MIDIKit'
    Target 'MIDIKitIO' in project 'MIDIKit'
        ➜ Explicit dependency on target 'MIDIKit_MIDIKitIO' in project 'MIDIKit'
        ➜ Explicit dependency on target 'MIDIKitInternals' in project 'MIDIKit'
        ➜ Explicit dependency on target 'MIDIKitCore' in project 'MIDIKit'
    Target 'MIDIKit_MIDIKitIO' in project 'MIDIKit' (no dependencies)
    Target 'MIDIKitCore' in project 'MIDIKit'
        ➜ Explicit dependency on target 'MIDIKitInternals' in project 'MIDIKit'
    Target 'MIDIKitInternals' in project 'MIDIKit'
        ➜ Explicit dependency on target 'MIDIKit_MIDIKitInternals' in project 'MIDIKit'
    Target 'MIDIKit_MIDIKitInternals' in project 'MIDIKit' (no dependencies)

GatherProvisioningInputs

CreateBuildDescription

ExecuteExternalTool /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc --version

ExecuteExternalTool /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang -v -E -dM -arch x86_64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk -x c++ -c /dev/null

ExecuteExternalTool /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang -v -E -dM -arch x86_64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk -x c -c /dev/null

ExecuteExternalTool /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang -v -E -dM -arch x86_64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk -x objective-c++ -c /dev/null

ExecuteExternalTool /Applications/Xcode.app/Contents/Developer/usr/bin/actool --version --output-format xml1

ExecuteExternalTool /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang -v -E -dM -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk -x c -c /dev/null

ExecuteExternalTool /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang -v -E -dM -arch x86_64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk -x objective-c -c /dev/null

ExecuteExternalTool /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/ld -version_details

ReadFileContents /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/share/docc/features.json

Build description signature: c6be605bade29033f82d4321f85612cb
Build description path: /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/XCBuildData/c6be605bade29033f82d4321f85612cb.xcbuilddata
error: unable to attach DB: error: accessing build database "/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/XCBuildData/build.db": database is locked Possibly there are two concurrent builds running in the same filesystem location.
** TEST BUILD FAILED **


The following build commands failed:
	Building project VoicingTrainer for testing with scheme VoicingTrainer
(1 failure)
