#!/bin/bash

# VoicingTrainer 每日测试脚本
# 用于自动化执行日常测试检查，确保代码质量

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志文件
LOG_FILE="daily_test_$(date +%Y%m%d).log"

# 函数：打印带颜色的消息
print_status() {
    echo -e "${BLUE}📅 [$(date '+%H:%M:%S')] $1${NC}" | tee -a "$LOG_FILE"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}" | tee -a "$LOG_FILE"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}" | tee -a "$LOG_FILE"
}

print_error() {
    echo -e "${RED}❌ $1${NC}" | tee -a "$LOG_FILE"
}

# 函数：检查Git状态
check_git_status() {
    print_status "检查Git状态..."
    
    # 检查是否有未提交的更改
    if ! git diff --quiet; then
        print_warning "发现未提交的更改，建议先提交后再运行完整测试"
        git status --short
    else
        print_success "Git工作区干净"
    fi
    
    # 显示最近5次提交
    print_status "最近的提交记录:"
    git log --oneline -n 5 | tee -a "$LOG_FILE"
}

# 函数：快速健康检查
quick_health_check() {
    print_status "🏃‍♂️ 开始快速健康检查..."
    
    if [ -f "./run_chord_tests.sh" ]; then
        chmod +x ./run_chord_tests.sh
        if ./run_chord_tests.sh >> "$LOG_FILE" 2>&1; then
            print_success "快速健康检查通过"
            return 0
        else
            print_error "快速健康检查失败，查看 $LOG_FILE 了解详情"
            return 1
        fi
    else
        print_warning "找不到 run_chord_tests.sh，跳过快速检查"
        return 0
    fi
}

# 函数：运行单元测试
run_unit_tests() {
    print_status "🧪 运行单元测试..."
    
    # 检查是否可以运行xcodebuild
    if ! command -v xcodebuild &> /dev/null; then
        print_error "未找到xcodebuild命令，请确保安装了Xcode Command Line Tools"
        return 1
    fi
    
    # 运行测试
    if xcodebuild test -scheme VoicingTrainer -destination 'platform=macOS' -quiet >> "$LOG_FILE" 2>&1; then
        print_success "所有单元测试通过"
        return 0
    else
        print_error "单元测试失败，查看 $LOG_FILE 了解详情"
        
        # 提取测试失败信息
        echo -e "\n${RED}=== 测试失败详情 ===${NC}"
        grep -A 5 -B 5 "FAIL\|ERROR\|failed" "$LOG_FILE" | tail -20
        return 1
    fi
}

# 函数：检查测试覆盖率
check_test_coverage() {
    print_status "📊 检查测试覆盖率..."
    
    # 运行带覆盖率的测试
    if xcodebuild test -scheme VoicingTrainer -destination 'platform=macOS' -enableCodeCoverage YES -quiet >> "$LOG_FILE" 2>&1; then
        print_success "测试覆盖率检查完成"
        print_status "覆盖率详情可在Xcode中查看 (⌘+9 -> Code Coverage)"
    else
        print_warning "无法获取测试覆盖率信息"
    fi
}

# 函数：运行性能测试
run_performance_tests() {
    print_status "⚡ 运行性能测试..."
    
    # 只运行性能相关的测试
    if xcodebuild test -scheme VoicingTrainer -destination 'platform=macOS' -only-testing:VoicingTrainerTests -quiet >> "$LOG_FILE" 2>&1; then
        print_success "性能测试完成"
    else
        print_warning "性能测试执行异常，查看日志了解详情"
    fi
}

# 函数：清理和总结
cleanup_and_summary() {
    print_status "🧹 清理临时文件..."
    
    # 清理构建缓存（如果需要）
    if [ "$1" == "--clean" ]; then
        xcodebuild clean -scheme VoicingTrainer >> "$LOG_FILE" 2>&1
        print_success "构建缓存已清理"
    fi
    
    # 测试总结
    echo -e "\n${BLUE}=== 测试总结 ===${NC}"
    echo "📅 测试日期: $(date '+%Y-%m-%d %H:%M:%S')"
    echo "📊 日志文件: $LOG_FILE"
    echo "🔄 Git状态: $(git status --porcelain | wc -l) 个文件有变更"
    echo "📈 测试覆盖率: 可在Xcode中查看"
}

# 函数：显示使用帮助
show_help() {
    echo "VoicingTrainer 每日测试脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  --quick, -q        只运行快速检查"
    echo "  --full, -f         运行完整测试套件（默认）"
    echo "  --clean, -c        测试前清理构建缓存"
    echo "  --coverage         包含代码覆盖率检查"
    echo "  --performance      包含性能测试"
    echo "  --help, -h         显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0                 # 运行标准每日测试"
    echo "  $0 --quick         # 只运行快速检查"
    echo "  $0 --full --clean  # 运行完整测试并清理缓存"
}

# 主函数
main() {
    echo -e "${BLUE}🎯 VoicingTrainer 每日测试开始${NC}"
    echo "开始时间: $(date '+%Y-%m-%d %H:%M:%S')"
    echo "日志文件: $LOG_FILE"
    echo ""
    
    # 解析命令行参数
    QUICK_ONLY=false
    CLEAN_BUILD=false
    CHECK_COVERAGE=false
    RUN_PERFORMANCE=false
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            --quick|-q)
                QUICK_ONLY=true
                shift
                ;;
            --full|-f)
                QUICK_ONLY=false
                shift
                ;;
            --clean|-c)
                CLEAN_BUILD=true
                shift
                ;;
            --coverage)
                CHECK_COVERAGE=true
                shift
                ;;
            --performance)
                RUN_PERFORMANCE=true
                shift
                ;;
            --help|-h)
                show_help
                exit 0
                ;;
            *)
                print_error "未识别的选项: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 开始测试流程
    START_TIME=$(date +%s)
    
    # 1. 检查Git状态
    check_git_status
    
    # 2. 快速健康检查
    if ! quick_health_check; then
        print_error "快速检查失败，停止后续测试"
        exit 1
    fi
    
    # 如果只要求快速检查，则结束
    if [ "$QUICK_ONLY" = true ]; then
        print_success "快速检查完成！"
        exit 0
    fi
    
    # 3. 运行完整单元测试
    if ! run_unit_tests; then
        print_error "单元测试失败，请修复后重试"
        exit 1
    fi
    
    # 4. 检查测试覆盖率（如果要求）
    if [ "$CHECK_COVERAGE" = true ]; then
        check_test_coverage
    fi
    
    # 5. 运行性能测试（如果要求）
    if [ "$RUN_PERFORMANCE" = true ]; then
        run_performance_tests
    fi
    
    # 6. 清理和总结
    if [ "$CLEAN_BUILD" = true ]; then
        cleanup_and_summary --clean
    else
        cleanup_and_summary
    fi
    
    # 计算总耗时
    END_TIME=$(date +%s)
    DURATION=$((END_TIME - START_TIME))
    
    echo ""
    print_success "🎉 所有测试执行完成！"
    echo -e "${GREEN}总耗时: ${DURATION}秒${NC}"
    echo -e "${BLUE}建议: 查看 $LOG_FILE 了解详细信息${NC}"
    
    # 如果测试通过，可以考虑自动提交（可选）
    if [ "$1" != "--no-commit" ] && ! git diff --quiet; then
        echo ""
        print_status "发现未提交的更改，是否要提交？(y/N)"
        read -r -n 1 -t 10 response || response="n"
        echo ""
        if [[ "$response" =~ ^[Yy]$ ]]; then
            git add .
            git commit -m "test: 每日测试通过 - $(date '+%Y-%m-%d')"
            print_success "代码已自动提交"
        fi
    fi
}

# 执行主函数
main "$@" 