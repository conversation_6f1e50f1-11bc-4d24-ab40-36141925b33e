# VoicingTrainer 开发规则与经验教训

## 项目概述
VoicingTrainer 是一个钢琴和弦训练应用，使用 SwiftUI 和 AudioKit 开发。

## 核心开发规则

### 1. 状态管理与时序问题 ⚠️

#### 问题描述
**非原子操作导致的时序Bug**（2025年发现）

#### 具体案例
在 `ProgressionGameManager` 中处理和弦匹配时，出现了以下问题：

```swift
// ❌ 错误的做法：分离的检查和判断
// 步骤1：用户按对和弦，播放ding音效
if pressedNotes == expectedNotes {
    playCorrectSound()  // 在 .playingChord 状态
}

// 步骤2：状态切换后检查音符数组
DispatchQueue.main.asyncAfter(...) {
    gameState = .waitingForResponse
    if !playedNotes.isEmpty {  // ❌ 此时用户可能已松开按键
        checkChordMatch()      // 检查失败，无法进入下一和弦
    }
}
```

#### 根本原因
- **非原子性**：检查标志和检查音符数组不是原子操作
- **时序竞态**：用户按对和弦时设置反馈，但状态切换时检查音符数组，此时按键状态可能已变化
- **状态不一致**：音效已播放（表示成功），但游戏状态未推进

#### 解决方案
```swift
// ✅ 正确的做法：使用状态标志变量
private var chordNotesAllMatch: Bool = false

// 播放期间检测到正确
if pressedNotes == expectedNotes && !chordNotesAllMatch {
    playCorrectSound()
    chordNotesAllMatch = true  // 设置标志
}

// 状态切换时检查标志，而不是实时音符
DispatchQueue.main.asyncAfter(...) {
    gameState = .waitingForResponse
    if chordNotesAllMatch {  // ✅ 使用可靠的标志变量
        handleCorrectChord()  // 推进游戏状态
    }
}
```

#### 核心原则
1. **状态一致性**：音效反馈和游戏状态推进必须保持一致
2. **标志优先**：使用可靠的布尔标志，而不是依赖实时变化的数据
3. **原子操作**：相关的检查和状态更新应该在同一个时机完成
4. **防御编程**：考虑用户交互的各种时序可能性

---

### 2. MIDI 输入处理规则

#### 基本原则
- 在合适的游戏状态下接收输入（如 `.playingChord` 和 `.waitingForResponse`）
- 避免重复的音效播放
- 保持用户交互的流畅性

#### 实现要点
- 使用状态机管理游戏流程
- 每个和弦只播放一次正确音效
- 支持播放期间的即时反馈，但不打断完整的听觉体验

---

### 3. PlayByEar 训练原则

#### 核心理念
- **完整听觉体验**：确保用户听到完整的和弦播放，获得完整听觉记忆
- **即时反馈**：提供及时的成功反馈，增强学习动机
- **五感协调**：视觉（键盘高亮）+ 听觉（播放+反馈）+ 触觉（按键）

#### 实现约束
- 不得提前终止和弦播放
- 保持视觉高亮显示的完整性
- 支持渐进式学习（熟练用户可提前输入）

---

### 4. 调试与日志规范

#### 日志格式
- 使用 emoji 前缀标识不同类型的日志
- `🎯` 游戏逻辑和状态
- `🎵` 音频播放相关
- `✅` 成功操作
- `❌` 错误或失败
- `🔵` 界面状态更新

#### 关键调试信息
- 游戏状态变化
- 音符匹配过程
- 标志变量状态
- 时序相关操作

---

## 历史Bug记录

### Bug #001 - 和弦匹配时序问题
- **发现时间**：2025年
- **影响**：用户按对和弦后无法进入下一个和弦
- **根本原因**：非原子操作导致的状态不一致
- **解决方案**：引入 `chordNotesAllMatch` 标志变量
- **预防措施**：优先使用状态标志，避免依赖实时变化的数据

---

## 开发建议

1. **状态管理**：优先考虑状态的一致性和可靠性
2. **用户交互**：考虑各种交互时序的可能性
3. **音频体验**：保持 PlayByEar 训练的完整性
4. **调试友好**：添加详细的日志和状态跟踪
5. **代码审查**：重点关注状态变化和时序相关的代码

---

*最后更新：2025年*
*维护者：开发团队* 