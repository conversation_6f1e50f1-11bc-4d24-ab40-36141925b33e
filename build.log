Command line invocation:
    /Applications/Xcode.app/Contents/Developer/usr/bin/xcodebuild -project VoicingTrainer.xcodeproj -scheme VoicingTrainer -destination "platform=iOS Simulator,name=iPhone 16 Pro" -configuration Debug build

Resolve Package Graph


Resolved source packages:
  KissFFT: https://github.com/AudioKit/KissFFT @ 1.0.0
  TimecodeKit: https://github.com/orchetect/TimecodeKit @ 2.3.3
  AudioKit: https://github.com/AudioKit/AudioKit @ 5.6.5
  SoundpipeAudioKit: https://github.com/AudioKit/SoundpipeAudioKit.git @ 5.7.1
  MIDIKit: https://github.com/orchetect/MIDIKit @ 0.10.1
  AudioKitEX: https://github.com/AudioKit/AudioKitEX @ 5.6.2

--- xcodebuild: WARNING: Using the first of multiple matching destinations:
{ platform:iOS Simulator, arch:arm64, id:327CF6B3-12F6-4129-97F7-F2FB7B5B4300, OS:18.4, name:iPhone 16 Pro }
{ platform:iOS Simulator, arch:x86_64, id:327CF6B3-12F6-4129-97F7-F2FB7B5B4300, OS:18.4, name:iPhone 16 Pro }
ComputePackagePrebuildTargetDependencyGraph

Prepare packages

CreateBuildRequest

SendProjectDescription

CreateBuildOperation

ComputeTargetDependencyGraph
note: Building targets in dependency order
note: Target dependency graph (30 targets)
    Target 'VoicingTrainer' in project 'VoicingTrainer'
        ➜ Explicit dependency on target 'MIDIKitControlSurfaces' in project 'MIDIKit'
        ➜ Explicit dependency on target 'AudioKitEX' in project 'AudioKitEX'
        ➜ Explicit dependency on target 'SoundpipeAudioKit' in project 'SoundpipeAudioKit'
        ➜ Explicit dependency on target 'MIDIKitIO' in project 'MIDIKit'
        ➜ Explicit dependency on target 'MIDIKit' in project 'MIDIKit'
        ➜ Explicit dependency on target 'MIDIKitSMF' in project 'MIDIKit'
        ➜ Explicit dependency on target 'AudioKit' in project 'AudioKit'
        ➜ Explicit dependency on target 'MIDIKitCore' in project 'MIDIKit'
    Target 'MIDIKitCore' in project 'MIDIKit'
        ➜ Explicit dependency on target 'MIDIKitCore' in project 'MIDIKit'
        ➜ Explicit dependency on target 'MIDIKitInternals' in project 'MIDIKit'
    Target 'MIDIKitSMF' in project 'MIDIKit'
        ➜ Explicit dependency on target 'MIDIKitSMF' in project 'MIDIKit'
        ➜ Explicit dependency on target 'MIDIKitInternals' in project 'MIDIKit'
        ➜ Explicit dependency on target 'MIDIKitCore' in project 'MIDIKit'
        ➜ Explicit dependency on target 'TimecodeKitCore' in project 'TimecodeKit'
    Target 'MIDIKit' in project 'MIDIKit'
        ➜ Explicit dependency on target 'MIDIKit' in project 'MIDIKit'
        ➜ Explicit dependency on target 'MIDIKitInternals' in project 'MIDIKit'
        ➜ Explicit dependency on target 'MIDIKitCore' in project 'MIDIKit'
        ➜ Explicit dependency on target 'MIDIKitIO' in project 'MIDIKit'
        ➜ Explicit dependency on target 'MIDIKitControlSurfaces' in project 'MIDIKit'
        ➜ Explicit dependency on target 'MIDIKitSMF' in project 'MIDIKit'
        ➜ Explicit dependency on target 'MIDIKitSync' in project 'MIDIKit'
        ➜ Explicit dependency on target 'MIDIKitUI' in project 'MIDIKit'
        ➜ Explicit dependency on target 'TimecodeKitCore' in project 'TimecodeKit'
    Target 'MIDIKit' in project 'MIDIKit'
        ➜ Explicit dependency on target 'MIDIKitInternals' in project 'MIDIKit'
        ➜ Explicit dependency on target 'MIDIKitCore' in project 'MIDIKit'
        ➜ Explicit dependency on target 'MIDIKitIO' in project 'MIDIKit'
        ➜ Explicit dependency on target 'MIDIKitControlSurfaces' in project 'MIDIKit'
        ➜ Explicit dependency on target 'MIDIKitSMF' in project 'MIDIKit'
        ➜ Explicit dependency on target 'MIDIKitSync' in project 'MIDIKit'
        ➜ Explicit dependency on target 'MIDIKitUI' in project 'MIDIKit'
        ➜ Explicit dependency on target 'TimecodeKitCore' in project 'TimecodeKit'
    Target 'MIDIKitUI' in project 'MIDIKit'
        ➜ Explicit dependency on target 'MIDIKitInternals' in project 'MIDIKit'
        ➜ Explicit dependency on target 'MIDIKitCore' in project 'MIDIKit'
        ➜ Explicit dependency on target 'MIDIKitIO' in project 'MIDIKit'
    Target 'MIDIKitSync' in project 'MIDIKit'
        ➜ Explicit dependency on target 'MIDIKitInternals' in project 'MIDIKit'
        ➜ Explicit dependency on target 'MIDIKitCore' in project 'MIDIKit'
        ➜ Explicit dependency on target 'MIDIKitIO' in project 'MIDIKit'
        ➜ Explicit dependency on target 'TimecodeKitCore' in project 'TimecodeKit'
    Target 'MIDIKitSMF' in project 'MIDIKit'
        ➜ Explicit dependency on target 'MIDIKitInternals' in project 'MIDIKit'
        ➜ Explicit dependency on target 'MIDIKitCore' in project 'MIDIKit'
        ➜ Explicit dependency on target 'TimecodeKitCore' in project 'TimecodeKit'
    Target 'TimecodeKitCore' in project 'TimecodeKit'
        ➜ Explicit dependency on target 'TimecodeKitCore' in project 'TimecodeKit'
    Target 'TimecodeKitCore' in project 'TimecodeKit' (no dependencies)
    Target 'MIDIKitIO' in project 'MIDIKit'
        ➜ Explicit dependency on target 'MIDIKitIO' in project 'MIDIKit'
        ➜ Explicit dependency on target 'MIDIKit_MIDIKitIO' in project 'MIDIKit'
        ➜ Explicit dependency on target 'MIDIKitInternals' in project 'MIDIKit'
        ➜ Explicit dependency on target 'MIDIKitCore' in project 'MIDIKit'
    Target 'SoundpipeAudioKit' in project 'SoundpipeAudioKit'
        ➜ Explicit dependency on target 'SoundpipeAudioKit' in project 'SoundpipeAudioKit'
        ➜ Explicit dependency on target 'Soundpipe' in project 'SoundpipeAudioKit'
        ➜ Explicit dependency on target 'CSoundpipeAudioKit' in project 'SoundpipeAudioKit'
        ➜ Explicit dependency on target 'KissFFT' in project 'KissFFT'
        ➜ Explicit dependency on target 'AudioKit' in project 'AudioKit'
        ➜ Explicit dependency on target 'AudioKitEX' in project 'AudioKitEX'
    Target 'SoundpipeAudioKit' in project 'SoundpipeAudioKit'
        ➜ Explicit dependency on target 'Soundpipe' in project 'SoundpipeAudioKit'
        ➜ Explicit dependency on target 'CSoundpipeAudioKit' in project 'SoundpipeAudioKit'
        ➜ Explicit dependency on target 'KissFFT' in project 'KissFFT'
        ➜ Explicit dependency on target 'AudioKit' in project 'AudioKit'
        ➜ Explicit dependency on target 'AudioKitEX' in project 'AudioKitEX'
    Target 'CSoundpipeAudioKit' in project 'SoundpipeAudioKit'
        ➜ Explicit dependency on target 'Soundpipe' in project 'SoundpipeAudioKit'
        ➜ Explicit dependency on target 'KissFFT' in project 'KissFFT'
        ➜ Explicit dependency on target 'AudioKit' in project 'AudioKit'
        ➜ Explicit dependency on target 'AudioKitEX' in project 'AudioKitEX'
    Target 'Soundpipe' in project 'SoundpipeAudioKit'
        ➜ Explicit dependency on target 'KissFFT' in project 'KissFFT'
    Target 'KissFFT' in project 'KissFFT'
        ➜ Explicit dependency on target 'KissFFT' in project 'KissFFT'
    Target 'KissFFT' in project 'KissFFT' (no dependencies)
    Target 'AudioKitEX' in project 'AudioKitEX'
        ➜ Explicit dependency on target 'AudioKitEX' in project 'AudioKitEX'
        ➜ Explicit dependency on target 'CAudioKitEX' in project 'AudioKitEX'
        ➜ Explicit dependency on target 'AudioKit' in project 'AudioKit'
    Target 'AudioKitEX' in project 'AudioKitEX'
        ➜ Explicit dependency on target 'CAudioKitEX' in project 'AudioKitEX'
        ➜ Explicit dependency on target 'AudioKit' in project 'AudioKit'
    Target 'AudioKit' in project 'AudioKit'
        ➜ Explicit dependency on target 'AudioKit' in project 'AudioKit'
        ➜ Explicit dependency on target 'AudioKit_AudioKit' in project 'AudioKit'
    Target 'AudioKit' in project 'AudioKit'
        ➜ Explicit dependency on target 'AudioKit_AudioKit' in project 'AudioKit'
    Target 'AudioKit_AudioKit' in project 'AudioKit' (no dependencies)
    Target 'CAudioKitEX' in project 'AudioKitEX' (no dependencies)
    Target 'MIDIKitControlSurfaces' in project 'MIDIKit'
        ➜ Explicit dependency on target 'MIDIKitControlSurfaces' in project 'MIDIKit'
        ➜ Explicit dependency on target 'MIDIKitInternals' in project 'MIDIKit'
        ➜ Explicit dependency on target 'MIDIKitCore' in project 'MIDIKit'
        ➜ Explicit dependency on target 'MIDIKitIO' in project 'MIDIKit'
    Target 'MIDIKitControlSurfaces' in project 'MIDIKit'
        ➜ Explicit dependency on target 'MIDIKitInternals' in project 'MIDIKit'
        ➜ Explicit dependency on target 'MIDIKitCore' in project 'MIDIKit'
        ➜ Explicit dependency on target 'MIDIKitIO' in project 'MIDIKit'
    Target 'MIDIKitIO' in project 'MIDIKit'
        ➜ Explicit dependency on target 'MIDIKit_MIDIKitIO' in project 'MIDIKit'
        ➜ Explicit dependency on target 'MIDIKitInternals' in project 'MIDIKit'
        ➜ Explicit dependency on target 'MIDIKitCore' in project 'MIDIKit'
    Target 'MIDIKit_MIDIKitIO' in project 'MIDIKit' (no dependencies)
    Target 'MIDIKitCore' in project 'MIDIKit'
        ➜ Explicit dependency on target 'MIDIKitInternals' in project 'MIDIKit'
    Target 'MIDIKitInternals' in project 'MIDIKit'
        ➜ Explicit dependency on target 'MIDIKit_MIDIKitInternals' in project 'MIDIKit'
    Target 'MIDIKit_MIDIKitInternals' in project 'MIDIKit' (no dependencies)

GatherProvisioningInputs

CreateBuildDescription

ExecuteExternalTool /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc --version

ExecuteExternalTool /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang -v -E -dM -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.4.sdk -x c++ -c /dev/null

ExecuteExternalTool /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang -v -E -dM -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.4.sdk -x c -c /dev/null

ExecuteExternalTool /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang -v -E -dM -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.4.sdk -x objective-c++ -c /dev/null

ExecuteExternalTool /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang -v -E -dM -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.4.sdk -x c -c /dev/null

ExecuteExternalTool /Applications/Xcode.app/Contents/Developer/usr/bin/actool --print-asset-tag-combinations --output-format xml1 /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/Resources/Assets/Assets.xcassets

ExecuteExternalTool /Applications/Xcode.app/Contents/Developer/usr/bin/actool --version --output-format xml1

ExecuteExternalTool /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang -v -E -dM -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.4.sdk -x objective-c -c /dev/null

ExecuteExternalTool /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/ld -version_details

ReadFileContents /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/share/docc/features.json

Build description signature: a34616b6a094dddc8a74e57ee0ad46a8
Build description path: /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/XCBuildData/a34616b6a094dddc8a74e57ee0ad46a8.xcbuilddata
ClangStatCache /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang-stat-cache /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.4.sdk /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/iphonesimulator18.4-22E235-71825a6d136aa4733edcbe86ece5525b.sdkstatcache
    cd /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer.xcodeproj
    /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang-stat-cache /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.4.sdk -o /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/iphonesimulator18.4-22E235-71825a6d136aa4733edcbe86ece5525b.sdkstatcache

ProcessInfoPlistFile /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug-iphonesimulator/MIDIKit_MIDIKitInternals.bundle/Info.plist /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/MIDIKit.build/Debug-iphonesimulator/MIDIKit_MIDIKitInternals.build/empty-MIDIKit_MIDIKitInternals.plist (in target 'MIDIKit_MIDIKitInternals' from project 'MIDIKit')
    cd /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/SourcePackages/checkouts/MIDIKit
    builtin-infoPlistUtility /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/MIDIKit.build/Debug-iphonesimulator/MIDIKit_MIDIKitInternals.build/empty-MIDIKit_MIDIKitInternals.plist -producttype com.apple.product-type.bundle -expandbuildsettings -format binary -platform iphonesimulator -o /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug-iphonesimulator/MIDIKit_MIDIKitInternals.bundle/Info.plist

ProcessInfoPlistFile /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug-iphonesimulator/AudioKit_AudioKit.bundle/Info.plist /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/AudioKit.build/Debug-iphonesimulator/AudioKit_AudioKit.build/empty-AudioKit_AudioKit.plist (in target 'AudioKit_AudioKit' from project 'AudioKit')
    cd /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/SourcePackages/checkouts/AudioKit
    builtin-infoPlistUtility /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/AudioKit.build/Debug-iphonesimulator/AudioKit_AudioKit.build/empty-AudioKit_AudioKit.plist -producttype com.apple.product-type.bundle -expandbuildsettings -format binary -platform iphonesimulator -o /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug-iphonesimulator/AudioKit_AudioKit.bundle/Info.plist

ProcessInfoPlistFile /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug-iphonesimulator/MIDIKit_MIDIKitIO.bundle/Info.plist /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/MIDIKit.build/Debug-iphonesimulator/MIDIKit_MIDIKitIO.build/empty-MIDIKit_MIDIKitIO.plist (in target 'MIDIKit_MIDIKitIO' from project 'MIDIKit')
    cd /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/SourcePackages/checkouts/MIDIKit
    builtin-infoPlistUtility /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/MIDIKit.build/Debug-iphonesimulator/MIDIKit_MIDIKitIO.build/empty-MIDIKit_MIDIKitIO.plist -producttype com.apple.product-type.bundle -expandbuildsettings -format binary -platform iphonesimulator -o /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug-iphonesimulator/MIDIKit_MIDIKitIO.bundle/Info.plist

WriteAuxiliaryFile /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug-iphonesimulator/VoicingTrainer.build/Objects-normal/arm64/VoicingTrainer.SwiftFileList (in target 'VoicingTrainer' from project 'VoicingTrainer')
    cd /Users/<USER>/DEV/VoicingTrainer
    write-file /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug-iphonesimulator/VoicingTrainer.build/Objects-normal/arm64/VoicingTrainer.SwiftFileList

WriteAuxiliaryFile /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug-iphonesimulator/VoicingTrainer.build/Objects-normal/arm64/VoicingTrainer.SwiftConstValuesFileList (in target 'VoicingTrainer' from project 'VoicingTrainer')
    cd /Users/<USER>/DEV/VoicingTrainer
    write-file /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug-iphonesimulator/VoicingTrainer.build/Objects-normal/arm64/VoicingTrainer.SwiftConstValuesFileList

WriteAuxiliaryFile /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug-iphonesimulator/VoicingTrainer.build/Objects-normal/arm64/VoicingTrainer.LinkFileList (in target 'VoicingTrainer' from project 'VoicingTrainer')
    cd /Users/<USER>/DEV/VoicingTrainer
    write-file /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug-iphonesimulator/VoicingTrainer.build/Objects-normal/arm64/VoicingTrainer.LinkFileList

ScanDependencies /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/growl.o /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/SourcePackages/checkouts/SoundpipeAudioKit/Sources/Soundpipe/external/growl.c normal arm64 c com.apple.compilers.llvm.clang.1_0.compiler (in target 'Soundpipe' from project 'SoundpipeAudioKit')
    cd /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer.xcodeproj
    
    Using response file: /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp
    
    builtin-ScanDependencies -o /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/growl.o.scan -- /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang -x c -ivfsstatcache /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/iphonesimulator18.4-22E235-71825a6d136aa4733edcbe86ece5525b.sdkstatcache -fmessage-length\=0 -fdiagnostics-show-note-include-stack -fmacro-backtrace-limit\=0 -fno-color-diagnostics -fmodules-prune-interval\=86400 -fmodules-prune-after\=345600 -fbuild-session-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation -fmodules-validate-once-per-build-session -Wnon-modular-include-in-framework-module -Werror\=non-modular-include-in-framework-module -Wno-trigraphs -Wno-missing-field-initializers -Wno-missing-prototypes -Wno-return-type -Wno-missing-braces -Wparentheses -Wswitch -Wno-unused-function -Wno-unused-label -Wno-unused-parameter -Wno-unused-variable -Wunused-value -Wno-empty-body -Wno-uninitialized -Wno-unknown-pragmas -w -Wno-shadow -Wno-four-char-constants -Wno-conversion -Wno-constant-conversion -Wno-int-conversion -Wno-bool-conversion -Wno-enum-conversion -Wno-float-conversion -Wno-non-literal-null-conversion -Wno-objc-literal-conversion -Wshorten-64-to-32 -Wpointer-sign -Wno-newline-eof -Wno-implicit-fallthrough -fstrict-aliasing -Wdeprecated-declarations -Wno-sign-conversion -Wno-infinite-recursion -Wno-comma -Wno-block-capture-autoreleasing -Wno-strict-prototypes -Wno-semicolon-before-method-body -index-store-path /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Index.noindex/DataStore @/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp -MMD -MT dependencies -MF /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/growl.d --serialize-diagnostics /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/growl.dia -c /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/SourcePackages/checkouts/SoundpipeAudioKit/Sources/Soundpipe/external/growl.c -o /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/growl.o -index-unit-output-path /SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/growl.o

ScanDependencies /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/KissFFT.build/Debug-iphonesimulator/KissFFT.build/Objects-normal/arm64/kiss_fft.o /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/SourcePackages/checkouts/KissFFT/Sources/KissFFT/kiss_fft.c normal arm64 c com.apple.compilers.llvm.clang.1_0.compiler (in target 'KissFFT' from project 'KissFFT')
    cd /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer.xcodeproj
    
    Using response file: /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/KissFFT.build/Debug-iphonesimulator/KissFFT.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp
    
    builtin-ScanDependencies -o /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/KissFFT.build/Debug-iphonesimulator/KissFFT.build/Objects-normal/arm64/kiss_fft.o.scan -- /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang -x c -ivfsstatcache /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/iphonesimulator18.4-22E235-71825a6d136aa4733edcbe86ece5525b.sdkstatcache -fmessage-length\=0 -fdiagnostics-show-note-include-stack -fmacro-backtrace-limit\=0 -fno-color-diagnostics -fmodules-prune-interval\=86400 -fmodules-prune-after\=345600 -fbuild-session-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation -fmodules-validate-once-per-build-session -Wnon-modular-include-in-framework-module -Werror\=non-modular-include-in-framework-module -Wno-trigraphs -Wno-missing-field-initializers -Wno-missing-prototypes -Wno-return-type -Wno-missing-braces -Wparentheses -Wswitch -Wno-unused-function -Wno-unused-label -Wno-unused-parameter -Wno-unused-variable -Wunused-value -Wno-empty-body -Wno-uninitialized -Wno-unknown-pragmas -w -Wno-shadow -Wno-four-char-constants -Wno-conversion -Wno-constant-conversion -Wno-int-conversion -Wno-bool-conversion -Wno-enum-conversion -Wno-float-conversion -Wno-non-literal-null-conversion -Wno-objc-literal-conversion -Wshorten-64-to-32 -Wpointer-sign -Wno-newline-eof -Wno-implicit-fallthrough -fstrict-aliasing -Wdeprecated-declarations -Wno-sign-conversion -Wno-infinite-recursion -Wno-comma -Wno-block-capture-autoreleasing -Wno-strict-prototypes -Wno-semicolon-before-method-body -index-store-path /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Index.noindex/DataStore @/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/KissFFT.build/Debug-iphonesimulator/KissFFT.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp -MMD -MT dependencies -MF /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/KissFFT.build/Debug-iphonesimulator/KissFFT.build/Objects-normal/arm64/kiss_fft.d --serialize-diagnostics /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/KissFFT.build/Debug-iphonesimulator/KissFFT.build/Objects-normal/arm64/kiss_fft.dia -c /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/SourcePackages/checkouts/KissFFT/Sources/KissFFT/kiss_fft.c -o /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/KissFFT.build/Debug-iphonesimulator/KissFFT.build/Objects-normal/arm64/kiss_fft.o -index-unit-output-path /KissFFT.build/Debug-iphonesimulator/KissFFT.build/Objects-normal/arm64/kiss_fft.o

ScanDependencies /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/AudioKitEX.build/Debug-iphonesimulator/CAudioKitEX.build/Objects-normal/arm64/AudioToolboxLinker.o /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/SourcePackages/checkouts/AudioKitEX/Sources/CAudioKitEX/Internals/AudioToolboxLinker.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler (in target 'CAudioKitEX' from project 'AudioKitEX')
    cd /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer.xcodeproj
    
    Using response file: /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/AudioKitEX.build/Debug-iphonesimulator/CAudioKitEX.build/Objects-normal/arm64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp
    
    builtin-ScanDependencies -o /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/AudioKitEX.build/Debug-iphonesimulator/CAudioKitEX.build/Objects-normal/arm64/AudioToolboxLinker.o.scan -- /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang -x objective-c -ivfsstatcache /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/iphonesimulator18.4-22E235-71825a6d136aa4733edcbe86ece5525b.sdkstatcache -fmessage-length\=0 -fdiagnostics-show-note-include-stack -fmacro-backtrace-limit\=0 -fno-color-diagnostics -fmodules-prune-interval\=86400 -fmodules-prune-after\=345600 -fbuild-session-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation -fmodules-validate-once-per-build-session -Wnon-modular-include-in-framework-module -Werror\=non-modular-include-in-framework-module -Wno-trigraphs -Wno-missing-field-initializers -Wno-missing-prototypes -Wno-return-type -Wno-implicit-atomic-properties -Wno-objc-interface-ivars -Wno-arc-repeated-use-of-weak -Wno-missing-braces -Wparentheses -Wswitch -Wno-unused-function -Wno-unused-label -Wno-unused-parameter -Wno-unused-variable -Wunused-value -Wno-empty-body -Wno-uninitialized -Wno-unknown-pragmas -w -Wno-shadow -Wno-four-char-constants -Wno-conversion -Wno-constant-conversion -Wno-int-conversion -Wno-bool-conversion -Wno-enum-conversion -Wno-float-conversion -Wno-non-literal-null-conversion -Wno-objc-literal-conversion -Wshorten-64-to-32 -Wpointer-sign -Wno-newline-eof -Wno-selector -Wno-strict-selector-match -Wno-undeclared-selector -Wno-deprecated-implementations -Wno-implicit-fallthrough -fstrict-aliasing -Wprotocol -Wdeprecated-declarations -Wno-sign-conversion -Wno-infinite-recursion -Wno-comma -Wno-block-capture-autoreleasing -Wno-strict-prototypes -Wno-semicolon-before-method-body -index-store-path /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Index.noindex/DataStore @/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/AudioKitEX.build/Debug-iphonesimulator/CAudioKitEX.build/Objects-normal/arm64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp -MMD -MT dependencies -MF /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/AudioKitEX.build/Debug-iphonesimulator/CAudioKitEX.build/Objects-normal/arm64/AudioToolboxLinker.d --serialize-diagnostics /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/AudioKitEX.build/Debug-iphonesimulator/CAudioKitEX.build/Objects-normal/arm64/AudioToolboxLinker.dia -c /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/SourcePackages/checkouts/AudioKitEX/Sources/CAudioKitEX/Internals/AudioToolboxLinker.m -o /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/AudioKitEX.build/Debug-iphonesimulator/CAudioKitEX.build/Objects-normal/arm64/AudioToolboxLinker.o -index-unit-output-path /AudioKitEX.build/Debug-iphonesimulator/CAudioKitEX.build/Objects-normal/arm64/AudioToolboxLinker.o

ProcessProductPackaging /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/VoicingTrainer.entitlements /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug-iphonesimulator/VoicingTrainer.build/VoicingTrainer.app.xcent (in target 'VoicingTrainer' from project 'VoicingTrainer')
    cd /Users/<USER>/DEV/VoicingTrainer
    
    Entitlements:
    
    {
}
    
    builtin-productPackagingUtility /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/VoicingTrainer.entitlements -entitlements -format xml -o /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug-iphonesimulator/VoicingTrainer.build/VoicingTrainer.app.xcent

ProcessProductPackaging /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/VoicingTrainer.entitlements /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug-iphonesimulator/VoicingTrainer.build/VoicingTrainer.app-Simulated.xcent (in target 'VoicingTrainer' from project 'VoicingTrainer')
    cd /Users/<USER>/DEV/VoicingTrainer
    
    Entitlements:
    
    {
    "application-identifier" = "2NT377F2QG.com.shzx.voicingtrainer";
}
    
    builtin-productPackagingUtility /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/VoicingTrainer.entitlements -entitlements -format xml -o /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug-iphonesimulator/VoicingTrainer.build/VoicingTrainer.app-Simulated.xcent

ProcessProductPackagingDER /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug-iphonesimulator/VoicingTrainer.build/VoicingTrainer.app.xcent /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug-iphonesimulator/VoicingTrainer.build/VoicingTrainer.app.xcent.der (in target 'VoicingTrainer' from project 'VoicingTrainer')
    cd /Users/<USER>/DEV/VoicingTrainer
    /usr/bin/derq query -f xml -i /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug-iphonesimulator/VoicingTrainer.build/VoicingTrainer.app.xcent -o /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug-iphonesimulator/VoicingTrainer.build/VoicingTrainer.app.xcent.der --raw

ProcessProductPackagingDER /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug-iphonesimulator/VoicingTrainer.build/VoicingTrainer.app-Simulated.xcent /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug-iphonesimulator/VoicingTrainer.build/VoicingTrainer.app-Simulated.xcent.der (in target 'VoicingTrainer' from project 'VoicingTrainer')
    cd /Users/<USER>/DEV/VoicingTrainer
    /usr/bin/derq query -f xml -i /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug-iphonesimulator/VoicingTrainer.build/VoicingTrainer.app-Simulated.xcent -o /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug-iphonesimulator/VoicingTrainer.build/VoicingTrainer.app-Simulated.xcent.der --raw

SwiftDriver VoicingTrainer normal arm64 com.apple.xcode.tools.swift.compiler (in target 'VoicingTrainer' from project 'VoicingTrainer')
    cd /Users/<USER>/DEV/VoicingTrainer
    builtin-SwiftDriver -- /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc -module-name VoicingTrainer -Onone -enforce-exclusivity\=checked @/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug-iphonesimulator/VoicingTrainer.build/Objects-normal/arm64/VoicingTrainer.SwiftFileList -DDEBUG -Xcc -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/GeneratedModuleMaps-iphonesimulator/CAudioKitEX.modulemap -Xcc -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/GeneratedModuleMaps-iphonesimulator/KissFFT.modulemap -Xcc -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/GeneratedModuleMaps-iphonesimulator/CSoundpipeAudioKit.modulemap -Xcc -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/GeneratedModuleMaps-iphonesimulator/Soundpipe.modulemap -enable-bare-slash-regex -enable-experimental-feature DebugDescriptionMacro -sdk /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.4.sdk -target arm64-apple-ios16.0-simulator -g -module-cache-path /Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex -Xfrontend -serialize-debugging-options -profile-coverage-mapping -profile-generate -enable-testing -index-store-path /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Index.noindex/DataStore -swift-version 5 -I /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug-iphonesimulator -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug-iphonesimulator -emit-localized-strings -emit-localized-strings-path /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug-iphonesimulator/VoicingTrainer.build/Objects-normal/arm64 -c -j8 -enable-batch-mode -incremental -Xcc -ivfsstatcache -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/iphonesimulator18.4-22E235-71825a6d136aa4733edcbe86ece5525b.sdkstatcache -output-file-map /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug-iphonesimulator/VoicingTrainer.build/Objects-normal/arm64/VoicingTrainer-OutputFileMap.json -use-frontend-parseable-output -save-temps -no-color-diagnostics -serialize-diagnostics -emit-dependencies -emit-module -emit-module-path /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug-iphonesimulator/VoicingTrainer.build/Objects-normal/arm64/VoicingTrainer.swiftmodule -validate-clang-modules-once -clang-build-session-file /Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug-iphonesimulator/VoicingTrainer.build/swift-overrides.hmap -emit-const-values -Xfrontend -const-gather-protocols-file -Xfrontend /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug-iphonesimulator/VoicingTrainer.build/Objects-normal/arm64/VoicingTrainer_const_extract_protocols.json -Xcc -iquote -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug-iphonesimulator/VoicingTrainer.build/VoicingTrainer-generated-files.hmap -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug-iphonesimulator/VoicingTrainer.build/VoicingTrainer-own-target-headers.hmap -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug-iphonesimulator/VoicingTrainer.build/VoicingTrainer-all-non-framework-target-headers.hmap -Xcc -ivfsoverlay -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug-iphonesimulator/VoicingTrainer-5f2ddef3a416ec04434590c5d76bba5c-VFS-iphonesimulator/all-product-headers.yaml -Xcc -iquote -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug-iphonesimulator/VoicingTrainer.build/VoicingTrainer-project-headers.hmap -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/SourcePackages/checkouts/AudioKitEX/Sources/CAudioKitEX/include -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/SourcePackages/checkouts/KissFFT/Sources/KissFFT/include -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/SourcePackages/checkouts/SoundpipeAudioKit/Sources/CSoundpipeAudioKit/include -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/SourcePackages/checkouts/SoundpipeAudioKit/Sources/Soundpipe/include -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug-iphonesimulator/include -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug-iphonesimulator/VoicingTrainer.build/DerivedSources-normal/arm64 -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug-iphonesimulator/VoicingTrainer.build/DerivedSources/arm64 -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug-iphonesimulator/VoicingTrainer.build/DerivedSources -Xcc -DDEBUG\=1 -emit-objc-header -emit-objc-header-path /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug-iphonesimulator/VoicingTrainer.build/Objects-normal/arm64/VoicingTrainer-Swift.h -working-directory /Users/<USER>/DEV/VoicingTrainer -experimental-emit-module-separately -disable-cmo

CompileC /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/KissFFT.build/Debug-iphonesimulator/KissFFT.build/Objects-normal/arm64/kiss_fft.o /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/SourcePackages/checkouts/KissFFT/Sources/KissFFT/kiss_fft.c normal arm64 c com.apple.compilers.llvm.clang.1_0.compiler (in target 'KissFFT' from project 'KissFFT')
    cd /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer.xcodeproj
    
    Using response file: /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/KissFFT.build/Debug-iphonesimulator/KissFFT.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp
    
    /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang -x c -ivfsstatcache /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/iphonesimulator18.4-22E235-71825a6d136aa4733edcbe86ece5525b.sdkstatcache -fmessage-length\=0 -fdiagnostics-show-note-include-stack -fmacro-backtrace-limit\=0 -fno-color-diagnostics -fmodules-prune-interval\=86400 -fmodules-prune-after\=345600 -fbuild-session-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation -fmodules-validate-once-per-build-session -Wnon-modular-include-in-framework-module -Werror\=non-modular-include-in-framework-module -Wno-trigraphs -Wno-missing-field-initializers -Wno-missing-prototypes -Wno-return-type -Wno-missing-braces -Wparentheses -Wswitch -Wno-unused-function -Wno-unused-label -Wno-unused-parameter -Wno-unused-variable -Wunused-value -Wno-empty-body -Wno-uninitialized -Wno-unknown-pragmas -w -Wno-shadow -Wno-four-char-constants -Wno-conversion -Wno-constant-conversion -Wno-int-conversion -Wno-bool-conversion -Wno-enum-conversion -Wno-float-conversion -Wno-non-literal-null-conversion -Wno-objc-literal-conversion -Wshorten-64-to-32 -Wpointer-sign -Wno-newline-eof -Wno-implicit-fallthrough -fstrict-aliasing -Wdeprecated-declarations -Wno-sign-conversion -Wno-infinite-recursion -Wno-comma -Wno-block-capture-autoreleasing -Wno-strict-prototypes -Wno-semicolon-before-method-body -index-store-path /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Index.noindex/DataStore @/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/KissFFT.build/Debug-iphonesimulator/KissFFT.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp -MMD -MT dependencies -MF /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/KissFFT.build/Debug-iphonesimulator/KissFFT.build/Objects-normal/arm64/kiss_fft.d --serialize-diagnostics /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/KissFFT.build/Debug-iphonesimulator/KissFFT.build/Objects-normal/arm64/kiss_fft.dia -c /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/SourcePackages/checkouts/KissFFT/Sources/KissFFT/kiss_fft.c -o /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/KissFFT.build/Debug-iphonesimulator/KissFFT.build/Objects-normal/arm64/kiss_fft.o -index-unit-output-path /KissFFT.build/Debug-iphonesimulator/KissFFT.build/Objects-normal/arm64/kiss_fft.o

CompileC /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/growl.o /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/SourcePackages/checkouts/SoundpipeAudioKit/Sources/Soundpipe/external/growl.c normal arm64 c com.apple.compilers.llvm.clang.1_0.compiler (in target 'Soundpipe' from project 'SoundpipeAudioKit')
    cd /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer.xcodeproj
    
    Using response file: /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp
    
    /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang -x c -ivfsstatcache /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/iphonesimulator18.4-22E235-71825a6d136aa4733edcbe86ece5525b.sdkstatcache -fmessage-length\=0 -fdiagnostics-show-note-include-stack -fmacro-backtrace-limit\=0 -fno-color-diagnostics -fmodules-prune-interval\=86400 -fmodules-prune-after\=345600 -fbuild-session-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation -fmodules-validate-once-per-build-session -Wnon-modular-include-in-framework-module -Werror\=non-modular-include-in-framework-module -Wno-trigraphs -Wno-missing-field-initializers -Wno-missing-prototypes -Wno-return-type -Wno-missing-braces -Wparentheses -Wswitch -Wno-unused-function -Wno-unused-label -Wno-unused-parameter -Wno-unused-variable -Wunused-value -Wno-empty-body -Wno-uninitialized -Wno-unknown-pragmas -w -Wno-shadow -Wno-four-char-constants -Wno-conversion -Wno-constant-conversion -Wno-int-conversion -Wno-bool-conversion -Wno-enum-conversion -Wno-float-conversion -Wno-non-literal-null-conversion -Wno-objc-literal-conversion -Wshorten-64-to-32 -Wpointer-sign -Wno-newline-eof -Wno-implicit-fallthrough -fstrict-aliasing -Wdeprecated-declarations -Wno-sign-conversion -Wno-infinite-recursion -Wno-comma -Wno-block-capture-autoreleasing -Wno-strict-prototypes -Wno-semicolon-before-method-body -index-store-path /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Index.noindex/DataStore @/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp -MMD -MT dependencies -MF /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/growl.d --serialize-diagnostics /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/growl.dia -c /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/SourcePackages/checkouts/SoundpipeAudioKit/Sources/Soundpipe/external/growl.c -o /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/growl.o -index-unit-output-path /SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/growl.o

CompileC /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/AudioKitEX.build/Debug-iphonesimulator/CAudioKitEX.build/Objects-normal/arm64/AudioToolboxLinker.o /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/SourcePackages/checkouts/AudioKitEX/Sources/CAudioKitEX/Internals/AudioToolboxLinker.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler (in target 'CAudioKitEX' from project 'AudioKitEX')
    cd /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer.xcodeproj
    
    Using response file: /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/AudioKitEX.build/Debug-iphonesimulator/CAudioKitEX.build/Objects-normal/arm64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp
    
    /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang -x objective-c -ivfsstatcache /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/iphonesimulator18.4-22E235-71825a6d136aa4733edcbe86ece5525b.sdkstatcache -fmessage-length\=0 -fdiagnostics-show-note-include-stack -fmacro-backtrace-limit\=0 -fno-color-diagnostics -fmodules-prune-interval\=86400 -fmodules-prune-after\=345600 -fbuild-session-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation -fmodules-validate-once-per-build-session -Wnon-modular-include-in-framework-module -Werror\=non-modular-include-in-framework-module -Wno-trigraphs -Wno-missing-field-initializers -Wno-missing-prototypes -Wno-return-type -Wno-implicit-atomic-properties -Wno-objc-interface-ivars -Wno-arc-repeated-use-of-weak -Wno-missing-braces -Wparentheses -Wswitch -Wno-unused-function -Wno-unused-label -Wno-unused-parameter -Wno-unused-variable -Wunused-value -Wno-empty-body -Wno-uninitialized -Wno-unknown-pragmas -w -Wno-shadow -Wno-four-char-constants -Wno-conversion -Wno-constant-conversion -Wno-int-conversion -Wno-bool-conversion -Wno-enum-conversion -Wno-float-conversion -Wno-non-literal-null-conversion -Wno-objc-literal-conversion -Wshorten-64-to-32 -Wpointer-sign -Wno-newline-eof -Wno-selector -Wno-strict-selector-match -Wno-undeclared-selector -Wno-deprecated-implementations -Wno-implicit-fallthrough -fstrict-aliasing -Wprotocol -Wdeprecated-declarations -Wno-sign-conversion -Wno-infinite-recursion -Wno-comma -Wno-block-capture-autoreleasing -Wno-strict-prototypes -Wno-semicolon-before-method-body -index-store-path /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Index.noindex/DataStore @/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/AudioKitEX.build/Debug-iphonesimulator/CAudioKitEX.build/Objects-normal/arm64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp -MMD -MT dependencies -MF /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/AudioKitEX.build/Debug-iphonesimulator/CAudioKitEX.build/Objects-normal/arm64/AudioToolboxLinker.d --serialize-diagnostics /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/AudioKitEX.build/Debug-iphonesimulator/CAudioKitEX.build/Objects-normal/arm64/AudioToolboxLinker.dia -c /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/SourcePackages/checkouts/AudioKitEX/Sources/CAudioKitEX/Internals/AudioToolboxLinker.m -o /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/AudioKitEX.build/Debug-iphonesimulator/CAudioKitEX.build/Objects-normal/arm64/AudioToolboxLinker.o -index-unit-output-path /AudioKitEX.build/Debug-iphonesimulator/CAudioKitEX.build/Objects-normal/arm64/AudioToolboxLinker.o

ScanDependencies /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/expon.o /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/SourcePackages/checkouts/SoundpipeAudioKit/Sources/Soundpipe/modules/expon.c normal arm64 c com.apple.compilers.llvm.clang.1_0.compiler (in target 'Soundpipe' from project 'SoundpipeAudioKit')
    cd /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer.xcodeproj
    
    Using response file: /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp
    
    builtin-ScanDependencies -o /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/expon.o.scan -- /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang -x c -ivfsstatcache /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/iphonesimulator18.4-22E235-71825a6d136aa4733edcbe86ece5525b.sdkstatcache -fmessage-length\=0 -fdiagnostics-show-note-include-stack -fmacro-backtrace-limit\=0 -fno-color-diagnostics -fmodules-prune-interval\=86400 -fmodules-prune-after\=345600 -fbuild-session-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation -fmodules-validate-once-per-build-session -Wnon-modular-include-in-framework-module -Werror\=non-modular-include-in-framework-module -Wno-trigraphs -Wno-missing-field-initializers -Wno-missing-prototypes -Wno-return-type -Wno-missing-braces -Wparentheses -Wswitch -Wno-unused-function -Wno-unused-label -Wno-unused-parameter -Wno-unused-variable -Wunused-value -Wno-empty-body -Wno-uninitialized -Wno-unknown-pragmas -w -Wno-shadow -Wno-four-char-constants -Wno-conversion -Wno-constant-conversion -Wno-int-conversion -Wno-bool-conversion -Wno-enum-conversion -Wno-float-conversion -Wno-non-literal-null-conversion -Wno-objc-literal-conversion -Wshorten-64-to-32 -Wpointer-sign -Wno-newline-eof -Wno-implicit-fallthrough -fstrict-aliasing -Wdeprecated-declarations -Wno-sign-conversion -Wno-infinite-recursion -Wno-comma -Wno-block-capture-autoreleasing -Wno-strict-prototypes -Wno-semicolon-before-method-body -index-store-path /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Index.noindex/DataStore @/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp -MMD -MT dependencies -MF /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/expon.d --serialize-diagnostics /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/expon.dia -c /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/SourcePackages/checkouts/SoundpipeAudioKit/Sources/Soundpipe/modules/expon.c -o /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/expon.o -index-unit-output-path /SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/expon.o

ScanDependencies /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/eqfil.o /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/SourcePackages/checkouts/SoundpipeAudioKit/Sources/Soundpipe/modules/eqfil.c normal arm64 c com.apple.compilers.llvm.clang.1_0.compiler (in target 'Soundpipe' from project 'SoundpipeAudioKit')
    cd /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer.xcodeproj
    
    Using response file: /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp
    
    builtin-ScanDependencies -o /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/eqfil.o.scan -- /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang -x c -ivfsstatcache /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/iphonesimulator18.4-22E235-71825a6d136aa4733edcbe86ece5525b.sdkstatcache -fmessage-length\=0 -fdiagnostics-show-note-include-stack -fmacro-backtrace-limit\=0 -fno-color-diagnostics -fmodules-prune-interval\=86400 -fmodules-prune-after\=345600 -fbuild-session-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation -fmodules-validate-once-per-build-session -Wnon-modular-include-in-framework-module -Werror\=non-modular-include-in-framework-module -Wno-trigraphs -Wno-missing-field-initializers -Wno-missing-prototypes -Wno-return-type -Wno-missing-braces -Wparentheses -Wswitch -Wno-unused-function -Wno-unused-label -Wno-unused-parameter -Wno-unused-variable -Wunused-value -Wno-empty-body -Wno-uninitialized -Wno-unknown-pragmas -w -Wno-shadow -Wno-four-char-constants -Wno-conversion -Wno-constant-conversion -Wno-int-conversion -Wno-bool-conversion -Wno-enum-conversion -Wno-float-conversion -Wno-non-literal-null-conversion -Wno-objc-literal-conversion -Wshorten-64-to-32 -Wpointer-sign -Wno-newline-eof -Wno-implicit-fallthrough -fstrict-aliasing -Wdeprecated-declarations -Wno-sign-conversion -Wno-infinite-recursion -Wno-comma -Wno-block-capture-autoreleasing -Wno-strict-prototypes -Wno-semicolon-before-method-body -index-store-path /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Index.noindex/DataStore @/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp -MMD -MT dependencies -MF /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/eqfil.d --serialize-diagnostics /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/eqfil.dia -c /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/SourcePackages/checkouts/SoundpipeAudioKit/Sources/Soundpipe/modules/eqfil.c -o /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/eqfil.o -index-unit-output-path /SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/eqfil.o

ScanDependencies /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/fft.o /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/SourcePackages/checkouts/SoundpipeAudioKit/Sources/Soundpipe/lib/fft/fft.c normal arm64 c com.apple.compilers.llvm.clang.1_0.compiler (in target 'Soundpipe' from project 'SoundpipeAudioKit')
    cd /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer.xcodeproj
    
    Using response file: /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp
    
    builtin-ScanDependencies -o /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/fft.o.scan -- /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang -x c -ivfsstatcache /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/iphonesimulator18.4-22E235-71825a6d136aa4733edcbe86ece5525b.sdkstatcache -fmessage-length\=0 -fdiagnostics-show-note-include-stack -fmacro-backtrace-limit\=0 -fno-color-diagnostics -fmodules-prune-interval\=86400 -fmodules-prune-after\=345600 -fbuild-session-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation -fmodules-validate-once-per-build-session -Wnon-modular-include-in-framework-module -Werror\=non-modular-include-in-framework-module -Wno-trigraphs -Wno-missing-field-initializers -Wno-missing-prototypes -Wno-return-type -Wno-missing-braces -Wparentheses -Wswitch -Wno-unused-function -Wno-unused-label -Wno-unused-parameter -Wno-unused-variable -Wunused-value -Wno-empty-body -Wno-uninitialized -Wno-unknown-pragmas -w -Wno-shadow -Wno-four-char-constants -Wno-conversion -Wno-constant-conversion -Wno-int-conversion -Wno-bool-conversion -Wno-enum-conversion -Wno-float-conversion -Wno-non-literal-null-conversion -Wno-objc-literal-conversion -Wshorten-64-to-32 -Wpointer-sign -Wno-newline-eof -Wno-implicit-fallthrough -fstrict-aliasing -Wdeprecated-declarations -Wno-sign-conversion -Wno-infinite-recursion -Wno-comma -Wno-block-capture-autoreleasing -Wno-strict-prototypes -Wno-semicolon-before-method-body -index-store-path /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Index.noindex/DataStore @/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp -MMD -MT dependencies -MF /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/fft.d --serialize-diagnostics /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/fft.dia -c /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/SourcePackages/checkouts/SoundpipeAudioKit/Sources/Soundpipe/lib/fft/fft.c -o /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/fft.o -index-unit-output-path /SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/fft.o

ScanDependencies /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/dynamicosc.o /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/SourcePackages/checkouts/SoundpipeAudioKit/Sources/Soundpipe/modules/dynamicosc.c normal arm64 c com.apple.compilers.llvm.clang.1_0.compiler (in target 'Soundpipe' from project 'SoundpipeAudioKit')
    cd /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer.xcodeproj
    
    Using response file: /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp
    
    builtin-ScanDependencies -o /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/dynamicosc.o.scan -- /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang -x c -ivfsstatcache /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/iphonesimulator18.4-22E235-71825a6d136aa4733edcbe86ece5525b.sdkstatcache -fmessage-length\=0 -fdiagnostics-show-note-include-stack -fmacro-backtrace-limit\=0 -fno-color-diagnostics -fmodules-prune-interval\=86400 -fmodules-prune-after\=345600 -fbuild-session-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation -fmodules-validate-once-per-build-session -Wnon-modular-include-in-framework-module -Werror\=non-modular-include-in-framework-module -Wno-trigraphs -Wno-missing-field-initializers -Wno-missing-prototypes -Wno-return-type -Wno-missing-braces -Wparentheses -Wswitch -Wno-unused-function -Wno-unused-label -Wno-unused-parameter -Wno-unused-variable -Wunused-value -Wno-empty-body -Wno-uninitialized -Wno-unknown-pragmas -w -Wno-shadow -Wno-four-char-constants -Wno-conversion -Wno-constant-conversion -Wno-int-conversion -Wno-bool-conversion -Wno-enum-conversion -Wno-float-conversion -Wno-non-literal-null-conversion -Wno-objc-literal-conversion -Wshorten-64-to-32 -Wpointer-sign -Wno-newline-eof -Wno-implicit-fallthrough -fstrict-aliasing -Wdeprecated-declarations -Wno-sign-conversion -Wno-infinite-recursion -Wno-comma -Wno-block-capture-autoreleasing -Wno-strict-prototypes -Wno-semicolon-before-method-body -index-store-path /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Index.noindex/DataStore @/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp -MMD -MT dependencies -MF /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/dynamicosc.d --serialize-diagnostics /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/dynamicosc.dia -c /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/SourcePackages/checkouts/SoundpipeAudioKit/Sources/Soundpipe/modules/dynamicosc.c -o /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/dynamicosc.o -index-unit-output-path /SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/dynamicosc.o

ScanDependencies /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/dust.o /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/SourcePackages/checkouts/SoundpipeAudioKit/Sources/Soundpipe/modules/dust.c normal arm64 c com.apple.compilers.llvm.clang.1_0.compiler (in target 'Soundpipe' from project 'SoundpipeAudioKit')
    cd /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer.xcodeproj
    
    Using response file: /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp
    
    builtin-ScanDependencies -o /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/dust.o.scan -- /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang -x c -ivfsstatcache /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/iphonesimulator18.4-22E235-71825a6d136aa4733edcbe86ece5525b.sdkstatcache -fmessage-length\=0 -fdiagnostics-show-note-include-stack -fmacro-backtrace-limit\=0 -fno-color-diagnostics -fmodules-prune-interval\=86400 -fmodules-prune-after\=345600 -fbuild-session-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation -fmodules-validate-once-per-build-session -Wnon-modular-include-in-framework-module -Werror\=non-modular-include-in-framework-module -Wno-trigraphs -Wno-missing-field-initializers -Wno-missing-prototypes -Wno-return-type -Wno-missing-braces -Wparentheses -Wswitch -Wno-unused-function -Wno-unused-label -Wno-unused-parameter -Wno-unused-variable -Wunused-value -Wno-empty-body -Wno-uninitialized -Wno-unknown-pragmas -w -Wno-shadow -Wno-four-char-constants -Wno-conversion -Wno-constant-conversion -Wno-int-conversion -Wno-bool-conversion -Wno-enum-conversion -Wno-float-conversion -Wno-non-literal-null-conversion -Wno-objc-literal-conversion -Wshorten-64-to-32 -Wpointer-sign -Wno-newline-eof -Wno-implicit-fallthrough -fstrict-aliasing -Wdeprecated-declarations -Wno-sign-conversion -Wno-infinite-recursion -Wno-comma -Wno-block-capture-autoreleasing -Wno-strict-prototypes -Wno-semicolon-before-method-body -index-store-path /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Index.noindex/DataStore @/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp -MMD -MT dependencies -MF /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/dust.d --serialize-diagnostics /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/dust.dia -c /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/SourcePackages/checkouts/SoundpipeAudioKit/Sources/Soundpipe/modules/dust.c -o /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/dust.o -index-unit-output-path /SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/dust.o

ScanDependencies /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/dtrig.o /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/SourcePackages/checkouts/SoundpipeAudioKit/Sources/Soundpipe/modules/dtrig.c normal arm64 c com.apple.compilers.llvm.clang.1_0.compiler (in target 'Soundpipe' from project 'SoundpipeAudioKit')
    cd /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer.xcodeproj
    
    Using response file: /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp
    
    builtin-ScanDependencies -o /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/dtrig.o.scan -- /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang -x c -ivfsstatcache /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/iphonesimulator18.4-22E235-71825a6d136aa4733edcbe86ece5525b.sdkstatcache -fmessage-length\=0 -fdiagnostics-show-note-include-stack -fmacro-backtrace-limit\=0 -fno-color-diagnostics -fmodules-prune-interval\=86400 -fmodules-prune-after\=345600 -fbuild-session-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation -fmodules-validate-once-per-build-session -Wnon-modular-include-in-framework-module -Werror\=non-modular-include-in-framework-module -Wno-trigraphs -Wno-missing-field-initializers -Wno-missing-prototypes -Wno-return-type -Wno-missing-braces -Wparentheses -Wswitch -Wno-unused-function -Wno-unused-label -Wno-unused-parameter -Wno-unused-variable -Wunused-value -Wno-empty-body -Wno-uninitialized -Wno-unknown-pragmas -w -Wno-shadow -Wno-four-char-constants -Wno-conversion -Wno-constant-conversion -Wno-int-conversion -Wno-bool-conversion -Wno-enum-conversion -Wno-float-conversion -Wno-non-literal-null-conversion -Wno-objc-literal-conversion -Wshorten-64-to-32 -Wpointer-sign -Wno-newline-eof -Wno-implicit-fallthrough -fstrict-aliasing -Wdeprecated-declarations -Wno-sign-conversion -Wno-infinite-recursion -Wno-comma -Wno-block-capture-autoreleasing -Wno-strict-prototypes -Wno-semicolon-before-method-body -index-store-path /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Index.noindex/DataStore @/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp -MMD -MT dependencies -MF /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/dtrig.d --serialize-diagnostics /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/dtrig.dia -c /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/SourcePackages/checkouts/SoundpipeAudioKit/Sources/Soundpipe/modules/dtrig.c -o /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/dtrig.o -index-unit-output-path /SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/dtrig.o

ScanDependencies /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/zitarev.o /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/SourcePackages/checkouts/SoundpipeAudioKit/Sources/Soundpipe/modules/zitarev.c normal arm64 c com.apple.compilers.llvm.clang.1_0.compiler (in target 'Soundpipe' from project 'SoundpipeAudioKit')
    cd /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer.xcodeproj
    
    Using response file: /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp
    
    builtin-ScanDependencies -o /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/zitarev.o.scan -- /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang -x c -ivfsstatcache /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/iphonesimulator18.4-22E235-71825a6d136aa4733edcbe86ece5525b.sdkstatcache -fmessage-length\=0 -fdiagnostics-show-note-include-stack -fmacro-backtrace-limit\=0 -fno-color-diagnostics -fmodules-prune-interval\=86400 -fmodules-prune-after\=345600 -fbuild-session-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation -fmodules-validate-once-per-build-session -Wnon-modular-include-in-framework-module -Werror\=non-modular-include-in-framework-module -Wno-trigraphs -Wno-missing-field-initializers -Wno-missing-prototypes -Wno-return-type -Wno-missing-braces -Wparentheses -Wswitch -Wno-unused-function -Wno-unused-label -Wno-unused-parameter -Wno-unused-variable -Wunused-value -Wno-empty-body -Wno-uninitialized -Wno-unknown-pragmas -w -Wno-shadow -Wno-four-char-constants -Wno-conversion -Wno-constant-conversion -Wno-int-conversion -Wno-bool-conversion -Wno-enum-conversion -Wno-float-conversion -Wno-non-literal-null-conversion -Wno-objc-literal-conversion -Wshorten-64-to-32 -Wpointer-sign -Wno-newline-eof -Wno-implicit-fallthrough -fstrict-aliasing -Wdeprecated-declarations -Wno-sign-conversion -Wno-infinite-recursion -Wno-comma -Wno-block-capture-autoreleasing -Wno-strict-prototypes -Wno-semicolon-before-method-body -index-store-path /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Index.noindex/DataStore @/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp -MMD -MT dependencies -MF /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/zitarev.d --serialize-diagnostics /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/zitarev.dia -c /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/SourcePackages/checkouts/SoundpipeAudioKit/Sources/Soundpipe/modules/zitarev.c -o /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/zitarev.o -index-unit-output-path /SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/zitarev.o

Ld /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug-iphonesimulator/KissFFT.o normal (in target 'KissFFT' from project 'KissFFT')
    cd /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/SourcePackages/checkouts/KissFFT
    /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang -Xlinker -reproducible -target arm64-apple-ios12.0-simulator -r -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.4.sdk -O0 -w -L/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphonesimulator -L/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug-iphonesimulator -L/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/usr/lib -F/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphonesimulator -F/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug-iphonesimulator -iframework /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/Library/Frameworks -iframework /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.4.sdk/Developer/Library/Frameworks -filelist /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/KissFFT.build/Debug-iphonesimulator/KissFFT.build/Objects-normal/arm64/KissFFT.LinkFileList -nostdlib -Xlinker -object_path_lto -Xlinker /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/KissFFT.build/Debug-iphonesimulator/KissFFT.build/Objects-normal/arm64/KissFFT_lto.o -rdynamic -Xlinker -no_deduplicate -Xlinker -objc_abi_version -Xlinker 2 -Xlinker -debug_variant -Xlinker -dependency_info -Xlinker /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/KissFFT.build/Debug-iphonesimulator/KissFFT.build/Objects-normal/arm64/KissFFT_dependency_info.dat -o /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug-iphonesimulator/KissFFT.o

Ld /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug-iphonesimulator/CAudioKitEX.o normal (in target 'CAudioKitEX' from project 'AudioKitEX')
    cd /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/SourcePackages/checkouts/AudioKitEX
    /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang++ -Xlinker -reproducible -target arm64-apple-ios13.0-simulator -r -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.4.sdk -O0 -w -L/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphonesimulator -L/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug-iphonesimulator -L/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/usr/lib -F/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphonesimulator -F/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug-iphonesimulator -iframework /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/Library/Frameworks -iframework /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.4.sdk/Developer/Library/Frameworks -filelist /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/AudioKitEX.build/Debug-iphonesimulator/CAudioKitEX.build/Objects-normal/arm64/CAudioKitEX.LinkFileList -nostdlib -Xlinker -object_path_lto -Xlinker /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/AudioKitEX.build/Debug-iphonesimulator/CAudioKitEX.build/Objects-normal/arm64/CAudioKitEX_lto.o -rdynamic -Xlinker -no_deduplicate -Xlinker -objc_abi_version -Xlinker 2 -Xlinker -debug_variant -Xlinker -dependency_info -Xlinker /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/AudioKitEX.build/Debug-iphonesimulator/CAudioKitEX.build/Objects-normal/arm64/CAudioKitEX_dependency_info.dat -fobjc-arc -fobjc-link-runtime -o /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug-iphonesimulator/CAudioKitEX.o

CompileC /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/dynamicosc.o /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/SourcePackages/checkouts/SoundpipeAudioKit/Sources/Soundpipe/modules/dynamicosc.c normal arm64 c com.apple.compilers.llvm.clang.1_0.compiler (in target 'Soundpipe' from project 'SoundpipeAudioKit')
    cd /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer.xcodeproj
    
    Using response file: /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp
    
    /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang -x c -ivfsstatcache /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/iphonesimulator18.4-22E235-71825a6d136aa4733edcbe86ece5525b.sdkstatcache -fmessage-length\=0 -fdiagnostics-show-note-include-stack -fmacro-backtrace-limit\=0 -fno-color-diagnostics -fmodules-prune-interval\=86400 -fmodules-prune-after\=345600 -fbuild-session-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation -fmodules-validate-once-per-build-session -Wnon-modular-include-in-framework-module -Werror\=non-modular-include-in-framework-module -Wno-trigraphs -Wno-missing-field-initializers -Wno-missing-prototypes -Wno-return-type -Wno-missing-braces -Wparentheses -Wswitch -Wno-unused-function -Wno-unused-label -Wno-unused-parameter -Wno-unused-variable -Wunused-value -Wno-empty-body -Wno-uninitialized -Wno-unknown-pragmas -w -Wno-shadow -Wno-four-char-constants -Wno-conversion -Wno-constant-conversion -Wno-int-conversion -Wno-bool-conversion -Wno-enum-conversion -Wno-float-conversion -Wno-non-literal-null-conversion -Wno-objc-literal-conversion -Wshorten-64-to-32 -Wpointer-sign -Wno-newline-eof -Wno-implicit-fallthrough -fstrict-aliasing -Wdeprecated-declarations -Wno-sign-conversion -Wno-infinite-recursion -Wno-comma -Wno-block-capture-autoreleasing -Wno-strict-prototypes -Wno-semicolon-before-method-body -index-store-path /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Index.noindex/DataStore @/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp -MMD -MT dependencies -MF /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/dynamicosc.d --serialize-diagnostics /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/dynamicosc.dia -c /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/SourcePackages/checkouts/SoundpipeAudioKit/Sources/Soundpipe/modules/dynamicosc.c -o /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/dynamicosc.o -index-unit-output-path /SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/dynamicosc.o

ScanDependencies /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/wpkorg35.o /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/SourcePackages/checkouts/SoundpipeAudioKit/Sources/Soundpipe/modules/wpkorg35.c normal arm64 c com.apple.compilers.llvm.clang.1_0.compiler (in target 'Soundpipe' from project 'SoundpipeAudioKit')
    cd /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer.xcodeproj
    
    Using response file: /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp
    
    builtin-ScanDependencies -o /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/wpkorg35.o.scan -- /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang -x c -ivfsstatcache /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/iphonesimulator18.4-22E235-71825a6d136aa4733edcbe86ece5525b.sdkstatcache -fmessage-length\=0 -fdiagnostics-show-note-include-stack -fmacro-backtrace-limit\=0 -fno-color-diagnostics -fmodules-prune-interval\=86400 -fmodules-prune-after\=345600 -fbuild-session-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation -fmodules-validate-once-per-build-session -Wnon-modular-include-in-framework-module -Werror\=non-modular-include-in-framework-module -Wno-trigraphs -Wno-missing-field-initializers -Wno-missing-prototypes -Wno-return-type -Wno-missing-braces -Wparentheses -Wswitch -Wno-unused-function -Wno-unused-label -Wno-unused-parameter -Wno-unused-variable -Wunused-value -Wno-empty-body -Wno-uninitialized -Wno-unknown-pragmas -w -Wno-shadow -Wno-four-char-constants -Wno-conversion -Wno-constant-conversion -Wno-int-conversion -Wno-bool-conversion -Wno-enum-conversion -Wno-float-conversion -Wno-non-literal-null-conversion -Wno-objc-literal-conversion -Wshorten-64-to-32 -Wpointer-sign -Wno-newline-eof -Wno-implicit-fallthrough -fstrict-aliasing -Wdeprecated-declarations -Wno-sign-conversion -Wno-infinite-recursion -Wno-comma -Wno-block-capture-autoreleasing -Wno-strict-prototypes -Wno-semicolon-before-method-body -index-store-path /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Index.noindex/DataStore @/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp -MMD -MT dependencies -MF /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/wpkorg35.d --serialize-diagnostics /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/wpkorg35.dia -c /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/SourcePackages/checkouts/SoundpipeAudioKit/Sources/Soundpipe/modules/wpkorg35.c -o /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/wpkorg35.o -index-unit-output-path /SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/wpkorg35.o

CompileC /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/fft.o /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/SourcePackages/checkouts/SoundpipeAudioKit/Sources/Soundpipe/lib/fft/fft.c normal arm64 c com.apple.compilers.llvm.clang.1_0.compiler (in target 'Soundpipe' from project 'SoundpipeAudioKit')
    cd /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer.xcodeproj
    
    Using response file: /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp
    
    /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang -x c -ivfsstatcache /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/iphonesimulator18.4-22E235-71825a6d136aa4733edcbe86ece5525b.sdkstatcache -fmessage-length\=0 -fdiagnostics-show-note-include-stack -fmacro-backtrace-limit\=0 -fno-color-diagnostics -fmodules-prune-interval\=86400 -fmodules-prune-after\=345600 -fbuild-session-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation -fmodules-validate-once-per-build-session -Wnon-modular-include-in-framework-module -Werror\=non-modular-include-in-framework-module -Wno-trigraphs -Wno-missing-field-initializers -Wno-missing-prototypes -Wno-return-type -Wno-missing-braces -Wparentheses -Wswitch -Wno-unused-function -Wno-unused-label -Wno-unused-parameter -Wno-unused-variable -Wunused-value -Wno-empty-body -Wno-uninitialized -Wno-unknown-pragmas -w -Wno-shadow -Wno-four-char-constants -Wno-conversion -Wno-constant-conversion -Wno-int-conversion -Wno-bool-conversion -Wno-enum-conversion -Wno-float-conversion -Wno-non-literal-null-conversion -Wno-objc-literal-conversion -Wshorten-64-to-32 -Wpointer-sign -Wno-newline-eof -Wno-implicit-fallthrough -fstrict-aliasing -Wdeprecated-declarations -Wno-sign-conversion -Wno-infinite-recursion -Wno-comma -Wno-block-capture-autoreleasing -Wno-strict-prototypes -Wno-semicolon-before-method-body -index-store-path /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Index.noindex/DataStore @/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp -MMD -MT dependencies -MF /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/fft.d --serialize-diagnostics /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/fft.dia -c /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/SourcePackages/checkouts/SoundpipeAudioKit/Sources/Soundpipe/lib/fft/fft.c -o /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/fft.o -index-unit-output-path /SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/fft.o

CompileC /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/eqfil.o /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/SourcePackages/checkouts/SoundpipeAudioKit/Sources/Soundpipe/modules/eqfil.c normal arm64 c com.apple.compilers.llvm.clang.1_0.compiler (in target 'Soundpipe' from project 'SoundpipeAudioKit')
    cd /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer.xcodeproj
    
    Using response file: /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp
    
    /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang -x c -ivfsstatcache /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/iphonesimulator18.4-22E235-71825a6d136aa4733edcbe86ece5525b.sdkstatcache -fmessage-length\=0 -fdiagnostics-show-note-include-stack -fmacro-backtrace-limit\=0 -fno-color-diagnostics -fmodules-prune-interval\=86400 -fmodules-prune-after\=345600 -fbuild-session-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation -fmodules-validate-once-per-build-session -Wnon-modular-include-in-framework-module -Werror\=non-modular-include-in-framework-module -Wno-trigraphs -Wno-missing-field-initializers -Wno-missing-prototypes -Wno-return-type -Wno-missing-braces -Wparentheses -Wswitch -Wno-unused-function -Wno-unused-label -Wno-unused-parameter -Wno-unused-variable -Wunused-value -Wno-empty-body -Wno-uninitialized -Wno-unknown-pragmas -w -Wno-shadow -Wno-four-char-constants -Wno-conversion -Wno-constant-conversion -Wno-int-conversion -Wno-bool-conversion -Wno-enum-conversion -Wno-float-conversion -Wno-non-literal-null-conversion -Wno-objc-literal-conversion -Wshorten-64-to-32 -Wpointer-sign -Wno-newline-eof -Wno-implicit-fallthrough -fstrict-aliasing -Wdeprecated-declarations -Wno-sign-conversion -Wno-infinite-recursion -Wno-comma -Wno-block-capture-autoreleasing -Wno-strict-prototypes -Wno-semicolon-before-method-body -index-store-path /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Index.noindex/DataStore @/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp -MMD -MT dependencies -MF /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/eqfil.d --serialize-diagnostics /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/eqfil.dia -c /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/SourcePackages/checkouts/SoundpipeAudioKit/Sources/Soundpipe/modules/eqfil.c -o /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/eqfil.o -index-unit-output-path /SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/eqfil.o

CompileC /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/expon.o /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/SourcePackages/checkouts/SoundpipeAudioKit/Sources/Soundpipe/modules/expon.c normal arm64 c com.apple.compilers.llvm.clang.1_0.compiler (in target 'Soundpipe' from project 'SoundpipeAudioKit')
    cd /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer.xcodeproj
    
    Using response file: /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp
    
    /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang -x c -ivfsstatcache /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/iphonesimulator18.4-22E235-71825a6d136aa4733edcbe86ece5525b.sdkstatcache -fmessage-length\=0 -fdiagnostics-show-note-include-stack -fmacro-backtrace-limit\=0 -fno-color-diagnostics -fmodules-prune-interval\=86400 -fmodules-prune-after\=345600 -fbuild-session-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation -fmodules-validate-once-per-build-session -Wnon-modular-include-in-framework-module -Werror\=non-modular-include-in-framework-module -Wno-trigraphs -Wno-missing-field-initializers -Wno-missing-prototypes -Wno-return-type -Wno-missing-braces -Wparentheses -Wswitch -Wno-unused-function -Wno-unused-label -Wno-unused-parameter -Wno-unused-variable -Wunused-value -Wno-empty-body -Wno-uninitialized -Wno-unknown-pragmas -w -Wno-shadow -Wno-four-char-constants -Wno-conversion -Wno-constant-conversion -Wno-int-conversion -Wno-bool-conversion -Wno-enum-conversion -Wno-float-conversion -Wno-non-literal-null-conversion -Wno-objc-literal-conversion -Wshorten-64-to-32 -Wpointer-sign -Wno-newline-eof -Wno-implicit-fallthrough -fstrict-aliasing -Wdeprecated-declarations -Wno-sign-conversion -Wno-infinite-recursion -Wno-comma -Wno-block-capture-autoreleasing -Wno-strict-prototypes -Wno-semicolon-before-method-body -index-store-path /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Index.noindex/DataStore @/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp -MMD -MT dependencies -MF /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/expon.d --serialize-diagnostics /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/expon.dia -c /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/SourcePackages/checkouts/SoundpipeAudioKit/Sources/Soundpipe/modules/expon.c -o /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/expon.o -index-unit-output-path /SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/expon.o

CompileC /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/zitarev.o /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/SourcePackages/checkouts/SoundpipeAudioKit/Sources/Soundpipe/modules/zitarev.c normal arm64 c com.apple.compilers.llvm.clang.1_0.compiler (in target 'Soundpipe' from project 'SoundpipeAudioKit')
    cd /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer.xcodeproj
    
    Using response file: /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp
    
    /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang -x c -ivfsstatcache /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/iphonesimulator18.4-22E235-71825a6d136aa4733edcbe86ece5525b.sdkstatcache -fmessage-length\=0 -fdiagnostics-show-note-include-stack -fmacro-backtrace-limit\=0 -fno-color-diagnostics -fmodules-prune-interval\=86400 -fmodules-prune-after\=345600 -fbuild-session-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation -fmodules-validate-once-per-build-session -Wnon-modular-include-in-framework-module -Werror\=non-modular-include-in-framework-module -Wno-trigraphs -Wno-missing-field-initializers -Wno-missing-prototypes -Wno-return-type -Wno-missing-braces -Wparentheses -Wswitch -Wno-unused-function -Wno-unused-label -Wno-unused-parameter -Wno-unused-variable -Wunused-value -Wno-empty-body -Wno-uninitialized -Wno-unknown-pragmas -w -Wno-shadow -Wno-four-char-constants -Wno-conversion -Wno-constant-conversion -Wno-int-conversion -Wno-bool-conversion -Wno-enum-conversion -Wno-float-conversion -Wno-non-literal-null-conversion -Wno-objc-literal-conversion -Wshorten-64-to-32 -Wpointer-sign -Wno-newline-eof -Wno-implicit-fallthrough -fstrict-aliasing -Wdeprecated-declarations -Wno-sign-conversion -Wno-infinite-recursion -Wno-comma -Wno-block-capture-autoreleasing -Wno-strict-prototypes -Wno-semicolon-before-method-body -index-store-path /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Index.noindex/DataStore @/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp -MMD -MT dependencies -MF /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/zitarev.d --serialize-diagnostics /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/zitarev.dia -c /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/SourcePackages/checkouts/SoundpipeAudioKit/Sources/Soundpipe/modules/zitarev.c -o /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/zitarev.o -index-unit-output-path /SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/zitarev.o

CompileC /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/wpkorg35.o /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/SourcePackages/checkouts/SoundpipeAudioKit/Sources/Soundpipe/modules/wpkorg35.c normal arm64 c com.apple.compilers.llvm.clang.1_0.compiler (in target 'Soundpipe' from project 'SoundpipeAudioKit')
    cd /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer.xcodeproj
    
    Using response file: /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp
    
    /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang -x c -ivfsstatcache /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/iphonesimulator18.4-22E235-71825a6d136aa4733edcbe86ece5525b.sdkstatcache -fmessage-length\=0 -fdiagnostics-show-note-include-stack -fmacro-backtrace-limit\=0 -fno-color-diagnostics -fmodules-prune-interval\=86400 -fmodules-prune-after\=345600 -fbuild-session-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation -fmodules-validate-once-per-build-session -Wnon-modular-include-in-framework-module -Werror\=non-modular-include-in-framework-module -Wno-trigraphs -Wno-missing-field-initializers -Wno-missing-prototypes -Wno-return-type -Wno-missing-braces -Wparentheses -Wswitch -Wno-unused-function -Wno-unused-label -Wno-unused-parameter -Wno-unused-variable -Wunused-value -Wno-empty-body -Wno-uninitialized -Wno-unknown-pragmas -w -Wno-shadow -Wno-four-char-constants -Wno-conversion -Wno-constant-conversion -Wno-int-conversion -Wno-bool-conversion -Wno-enum-conversion -Wno-float-conversion -Wno-non-literal-null-conversion -Wno-objc-literal-conversion -Wshorten-64-to-32 -Wpointer-sign -Wno-newline-eof -Wno-implicit-fallthrough -fstrict-aliasing -Wdeprecated-declarations -Wno-sign-conversion -Wno-infinite-recursion -Wno-comma -Wno-block-capture-autoreleasing -Wno-strict-prototypes -Wno-semicolon-before-method-body -index-store-path /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Index.noindex/DataStore @/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp -MMD -MT dependencies -MF /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/wpkorg35.d --serialize-diagnostics /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/wpkorg35.dia -c /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/SourcePackages/checkouts/SoundpipeAudioKit/Sources/Soundpipe/modules/wpkorg35.c -o /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/wpkorg35.o -index-unit-output-path /SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/wpkorg35.o

SwiftEmitModule normal arm64 Emitting\ module\ for\ VoicingTrainer (in target 'VoicingTrainer' from project 'VoicingTrainer')
EmitSwiftModule normal arm64 (in target 'VoicingTrainer' from project 'VoicingTrainer')
    cd /Users/<USER>/DEV/VoicingTrainer
    

CompileC /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/dust.o /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/SourcePackages/checkouts/SoundpipeAudioKit/Sources/Soundpipe/modules/dust.c normal arm64 c com.apple.compilers.llvm.clang.1_0.compiler (in target 'Soundpipe' from project 'SoundpipeAudioKit')
    cd /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer.xcodeproj
    
    Using response file: /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp
    
    /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang -x c -ivfsstatcache /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/iphonesimulator18.4-22E235-71825a6d136aa4733edcbe86ece5525b.sdkstatcache -fmessage-length\=0 -fdiagnostics-show-note-include-stack -fmacro-backtrace-limit\=0 -fno-color-diagnostics -fmodules-prune-interval\=86400 -fmodules-prune-after\=345600 -fbuild-session-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation -fmodules-validate-once-per-build-session -Wnon-modular-include-in-framework-module -Werror\=non-modular-include-in-framework-module -Wno-trigraphs -Wno-missing-field-initializers -Wno-missing-prototypes -Wno-return-type -Wno-missing-braces -Wparentheses -Wswitch -Wno-unused-function -Wno-unused-label -Wno-unused-parameter -Wno-unused-variable -Wunused-value -Wno-empty-body -Wno-uninitialized -Wno-unknown-pragmas -w -Wno-shadow -Wno-four-char-constants -Wno-conversion -Wno-constant-conversion -Wno-int-conversion -Wno-bool-conversion -Wno-enum-conversion -Wno-float-conversion -Wno-non-literal-null-conversion -Wno-objc-literal-conversion -Wshorten-64-to-32 -Wpointer-sign -Wno-newline-eof -Wno-implicit-fallthrough -fstrict-aliasing -Wdeprecated-declarations -Wno-sign-conversion -Wno-infinite-recursion -Wno-comma -Wno-block-capture-autoreleasing -Wno-strict-prototypes -Wno-semicolon-before-method-body -index-store-path /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Index.noindex/DataStore @/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp -MMD -MT dependencies -MF /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/dust.d --serialize-diagnostics /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/dust.dia -c /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/SourcePackages/checkouts/SoundpipeAudioKit/Sources/Soundpipe/modules/dust.c -o /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/dust.o -index-unit-output-path /SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/dust.o

CompileC /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/dtrig.o /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/SourcePackages/checkouts/SoundpipeAudioKit/Sources/Soundpipe/modules/dtrig.c normal arm64 c com.apple.compilers.llvm.clang.1_0.compiler (in target 'Soundpipe' from project 'SoundpipeAudioKit')
    cd /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer.xcodeproj
    
    Using response file: /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp
    
    /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang -x c -ivfsstatcache /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/iphonesimulator18.4-22E235-71825a6d136aa4733edcbe86ece5525b.sdkstatcache -fmessage-length\=0 -fdiagnostics-show-note-include-stack -fmacro-backtrace-limit\=0 -fno-color-diagnostics -fmodules-prune-interval\=86400 -fmodules-prune-after\=345600 -fbuild-session-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation -fmodules-validate-once-per-build-session -Wnon-modular-include-in-framework-module -Werror\=non-modular-include-in-framework-module -Wno-trigraphs -Wno-missing-field-initializers -Wno-missing-prototypes -Wno-return-type -Wno-missing-braces -Wparentheses -Wswitch -Wno-unused-function -Wno-unused-label -Wno-unused-parameter -Wno-unused-variable -Wunused-value -Wno-empty-body -Wno-uninitialized -Wno-unknown-pragmas -w -Wno-shadow -Wno-four-char-constants -Wno-conversion -Wno-constant-conversion -Wno-int-conversion -Wno-bool-conversion -Wno-enum-conversion -Wno-float-conversion -Wno-non-literal-null-conversion -Wno-objc-literal-conversion -Wshorten-64-to-32 -Wpointer-sign -Wno-newline-eof -Wno-implicit-fallthrough -fstrict-aliasing -Wdeprecated-declarations -Wno-sign-conversion -Wno-infinite-recursion -Wno-comma -Wno-block-capture-autoreleasing -Wno-strict-prototypes -Wno-semicolon-before-method-body -index-store-path /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Index.noindex/DataStore @/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp -MMD -MT dependencies -MF /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/dtrig.d --serialize-diagnostics /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/dtrig.dia -c /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/SourcePackages/checkouts/SoundpipeAudioKit/Sources/Soundpipe/modules/dtrig.c -o /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/dtrig.o -index-unit-output-path /SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/dtrig.o

SwiftCompile normal arm64 Compiling\ PowerTestManager.swift /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/Core/Managers/PowerTestManager.swift (in target 'VoicingTrainer' from project 'VoicingTrainer')
SwiftCompile normal arm64 /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/Core/Managers/PowerTestManager.swift (in target 'VoicingTrainer' from project 'VoicingTrainer')
    cd /Users/<USER>/DEV/VoicingTrainer
    
/Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/Core/Managers/PowerTestManager.swift:32:13: warning: immutable property will not be decoded because it is declared with an initial value which cannot be overwritten
        let id = UUID()
            ^
/Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/Core/Managers/PowerTestManager.swift:32:13: note: set the initial value via the initializer or explicitly define a CodingKeys enum including a 'id' case to silence this warning
        let id = UUID()
            ^
/Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/Core/Managers/PowerTestManager.swift:32:13: note: make the property mutable instead
        let id = UUID()
        ~~~ ^
        var

SwiftCompile normal arm64 Compiling\ PowerDebugView.swift /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/UI/PowerDebugView.swift (in target 'VoicingTrainer' from project 'VoicingTrainer')
SwiftCompile normal arm64 /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/UI/PowerDebugView.swift (in target 'VoicingTrainer' from project 'VoicingTrainer')
    cd /Users/<USER>/DEV/VoicingTrainer
    

SwiftCompile normal arm64 Compiling\ NotesView.swift /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/Game/Note/NotesView.swift (in target 'VoicingTrainer' from project 'VoicingTrainer')

SwiftCompile normal arm64 /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/Game/Note/NotesView.swift (in target 'VoicingTrainer' from project 'VoicingTrainer')
    cd /Users/<USER>/DEV/VoicingTrainer
    

SwiftCompile normal arm64 Compiling\ ChordBrowserView.swift /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/Game/Chord/ChordBrowserView.swift (in target 'VoicingTrainer' from project 'VoicingTrainer')

SwiftCompile normal arm64 /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/Game/Chord/ChordBrowserView.swift (in target 'VoicingTrainer' from project 'VoicingTrainer')
    cd /Users/<USER>/DEV/VoicingTrainer
    

SwiftCompile normal arm64 Compiling\ LevelClearView.swift /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/Game/Shared/LevelClearView.swift (in target 'VoicingTrainer' from project 'VoicingTrainer')

SwiftCompile normal arm64 /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/Game/Shared/LevelClearView.swift (in target 'VoicingTrainer' from project 'VoicingTrainer')
    cd /Users/<USER>/DEV/VoicingTrainer
    

SwiftCompile normal arm64 Compiling\ NewProgressionView.swift /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/UI/Views/NewProgressionView.swift (in target 'VoicingTrainer' from project 'VoicingTrainer')
SwiftCompile normal arm64 /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/UI/Views/NewProgressionView.swift (in target 'VoicingTrainer' from project 'VoicingTrainer')
    cd /Users/<USER>/DEV/VoicingTrainer
    
/Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/UI/Views/NewProgressionView.swift:125:28: error: reference to member 'windowBackgroundColor' cannot be resolved without a contextual type
        .background(Color(.windowBackgroundColor))
                           ^

Failed frontend command:
/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swift-frontend -frontend -c /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/UI/Views/SettingsView.swift /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/Music/Analysis/ChordNameParser.swift /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/Core/Managers/SoundEffectManager.swift /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/Music/Models/ChordsParser.swift /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/Core/Configuration/GameConfig.swift /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/Game/Shared/CompactGameStatus.swift /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/Core/Managers/AchievementManager.swift /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/Game/Note/WaveStringView.swift /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/Music/Models/NoteNameGenerator.swift /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/Game/Shared/PianoKeyboardView.swift /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/Game/Note/Models/NoteLevelData.swift /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/Music/Audio/ChordPlayer.swift /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/UI/Shared/HapticFeedback.swift /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/Game/Chord/ChordBrowserView.swift /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/Game/Note/Views/CountdownView.swift /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/Game/Shared/StringNoteView.swift /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/Game/Shared/RefactoredTargetNotesDisplay.swift /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/Game/Note/Views/LevelStartView.swift /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/Core/Managers/PowerManager.swift /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/Game/Note/NotesView.swift -primary-file /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/UI/Views/NewProgressionView.swift /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/Core/Configuration/PlatformDependant.swift /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/Core/Managers/UserProgressionManager.swift /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/Core/App/VoicingTrainerApp.swift /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/Core/Managers/ChordGameManager.swift /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/Core/Managers/MIDIManager.swift /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/Game/Chord/CompactVoicingPicker.swift /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/Core/Managers/NoteGameManager.swift /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/UI/PowerDebugView.swift /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/UI/Shared/ChordBadge.swift /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/Music/Analysis/ChordAnalyzer.swift /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/Game/Chord/ChordsSelectView.swift /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/Music/Models/ChordVoicing.swift /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/Game/Note/Models/AchievementData.swift /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/Game/Shared/SimpleNoteDisplay.swift /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/Core/Managers/PowerTestManager.swift /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/Game/Shared/PianoKeyboardViewModel.swift /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/Music/Audio/ParticleEffect.swift /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/UI/Views/ProgressionsView.swift /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/Game/Note/Views/LevelSelectView.swift /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/Game/Shared/NoteGameStateManager.swift /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/Model/ProgressionReader.swift /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/UI/Views/SongsView.swift /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/Music/Audio/SpriteKitParticleSystem.swift /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/Core/App/MusicAppView.swift /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/Game/Chord/ChordsView.swift /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/Game/Shared/LevelClearView.swift /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/Game/Note/NoteGenerator.swift /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/Core/Managers/ProgressionGameManager.swift /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug-iphonesimulator/VoicingTrainer.build/DerivedSources/GeneratedAssetSymbols.swift -emit-dependencies-path /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug-iphonesimulator/VoicingTrainer.build/Objects-normal/arm64/NewProgressionView.d -emit-const-values-path /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug-iphonesimulator/VoicingTrainer.build/Objects-normal/arm64/NewProgressionView.swiftconstvalues -emit-reference-dependencies-path /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug-iphonesimulator/VoicingTrainer.build/Objects-normal/arm64/NewProgressionView.swiftdeps -serialize-diagnostics-path /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug-iphonesimulator/VoicingTrainer.build/Objects-normal/arm64/NewProgressionView.dia -emit-localized-strings -emit-localized-strings-path /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug-iphonesimulator/VoicingTrainer.build/Objects-normal/arm64 -target arm64-apple-ios16.0-simulator -Xllvm -aarch64-use-tbi -enable-objc-interop -sdk /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.4.sdk -I /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug-iphonesimulator -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug-iphonesimulator -no-color-diagnostics -enable-testing -g -debug-info-format\=dwarf -dwarf-version\=4 -module-cache-path /Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex -profile-generate -profile-coverage-mapping -swift-version 5 -enforce-exclusivity\=checked -Onone -D DEBUG -serialize-debugging-options -const-gather-protocols-file /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug-iphonesimulator/VoicingTrainer.build/Objects-normal/arm64/VoicingTrainer_const_extract_protocols.json -enable-experimental-feature DebugDescriptionMacro -enable-bare-slash-regex -empty-abi-descriptor -validate-clang-modules-once -clang-build-session-file /Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation -Xcc -working-directory -Xcc /Users/<USER>/DEV/VoicingTrainer -resource-dir /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift -enable-anonymous-context-mangled-names -file-compilation-dir /Users/<USER>/DEV/VoicingTrainer -Xcc -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/GeneratedModuleMaps-iphonesimulator/CAudioKitEX.modulemap -Xcc -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/GeneratedModuleMaps-iphonesimulator/KissFFT.modulemap -Xcc -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/GeneratedModuleMaps-iphonesimulator/CSoundpipeAudioKit.modulemap -Xcc -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/GeneratedModuleMaps-iphonesimulator/Soundpipe.modulemap -Xcc -ivfsstatcache -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/iphonesimulator18.4-22E235-71825a6d136aa4733edcbe86ece5525b.sdkstatcache -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug-iphonesimulator/VoicingTrainer.build/swift-overrides.hmap -Xcc -iquote -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug-iphonesimulator/VoicingTrainer.build/VoicingTrainer-generated-files.hmap -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug-iphonesimulator/VoicingTrainer.build/VoicingTrainer-own-target-headers.hmap -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug-iphonesimulator/VoicingTrainer.build/VoicingTrainer-all-non-framework-target-headers.hmap -Xcc -ivfsoverlay -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug-iphonesimulator/VoicingTrainer-5f2ddef3a416ec04434590c5d76bba5c-VFS-iphonesimulator/all-product-headers.yaml -Xcc -iquote -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug-iphonesimulator/VoicingTrainer.build/VoicingTrainer-project-headers.hmap -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/SourcePackages/checkouts/AudioKitEX/Sources/CAudioKitEX/include -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/SourcePackages/checkouts/KissFFT/Sources/KissFFT/include -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/SourcePackages/checkouts/SoundpipeAudioKit/Sources/CSoundpipeAudioKit/include -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/SourcePackages/checkouts/SoundpipeAudioKit/Sources/Soundpipe/include -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug-iphonesimulator/include -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug-iphonesimulator/VoicingTrainer.build/DerivedSources-normal/arm64 -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug-iphonesimulator/VoicingTrainer.build/DerivedSources/arm64 -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug-iphonesimulator/VoicingTrainer.build/DerivedSources -Xcc -DDEBUG\=1 -module-name VoicingTrainer -frontend-parseable-output -disable-clang-spi -target-sdk-version 18.4 -target-sdk-name iphonesimulator18.4 -external-plugin-path /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr/lib/swift/host/plugins\#/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr/bin/swift-plugin-server -external-plugin-path /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr/local/lib/swift/host/plugins\#/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr/bin/swift-plugin-server -in-process-plugin-server-path /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/host/libSwiftInProcPluginServer.dylib -plugin-path /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/host/plugins -plugin-path /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/local/lib/swift/host/plugins -o /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug-iphonesimulator/VoicingTrainer.build/Objects-normal/arm64/NewProgressionView.o -index-unit-output-path /VoicingTrainer.build/Debug-iphonesimulator/VoicingTrainer.build/Objects-normal/arm64/NewProgressionView.o -index-store-path /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Index.noindex/DataStore -index-system-modules
ScanDependencies /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/waveset.o /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/SourcePackages/checkouts/SoundpipeAudioKit/Sources/Soundpipe/modules/waveset.c normal arm64 c com.apple.compilers.llvm.clang.1_0.compiler (in target 'Soundpipe' from project 'SoundpipeAudioKit')
    cd /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer.xcodeproj
    
    Using response file: /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp
    
    builtin-ScanDependencies -o /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/waveset.o.scan -- /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang -x c -ivfsstatcache /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/iphonesimulator18.4-22E235-71825a6d136aa4733edcbe86ece5525b.sdkstatcache -fmessage-length\=0 -fdiagnostics-show-note-include-stack -fmacro-backtrace-limit\=0 -fno-color-diagnostics -fmodules-prune-interval\=86400 -fmodules-prune-after\=345600 -fbuild-session-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation -fmodules-validate-once-per-build-session -Wnon-modular-include-in-framework-module -Werror\=non-modular-include-in-framework-module -Wno-trigraphs -Wno-missing-field-initializers -Wno-missing-prototypes -Wno-return-type -Wno-missing-braces -Wparentheses -Wswitch -Wno-unused-function -Wno-unused-label -Wno-unused-parameter -Wno-unused-variable -Wunused-value -Wno-empty-body -Wno-uninitialized -Wno-unknown-pragmas -w -Wno-shadow -Wno-four-char-constants -Wno-conversion -Wno-constant-conversion -Wno-int-conversion -Wno-bool-conversion -Wno-enum-conversion -Wno-float-conversion -Wno-non-literal-null-conversion -Wno-objc-literal-conversion -Wshorten-64-to-32 -Wpointer-sign -Wno-newline-eof -Wno-implicit-fallthrough -fstrict-aliasing -Wdeprecated-declarations -Wno-sign-conversion -Wno-infinite-recursion -Wno-comma -Wno-block-capture-autoreleasing -Wno-strict-prototypes -Wno-semicolon-before-method-body -index-store-path /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Index.noindex/DataStore @/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp -MMD -MT dependencies -MF /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/waveset.d --serialize-diagnostics /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/waveset.dia -c /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/SourcePackages/checkouts/SoundpipeAudioKit/Sources/Soundpipe/modules/waveset.c -o /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/waveset.o -index-unit-output-path /SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/waveset.o

ScanDependencies /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/vocwrapper.o /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/SourcePackages/checkouts/SoundpipeAudioKit/Sources/Soundpipe/external/vocwrapper.c normal arm64 c com.apple.compilers.llvm.clang.1_0.compiler (in target 'Soundpipe' from project 'SoundpipeAudioKit')
    cd /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer.xcodeproj
    
    Using response file: /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp
    
    builtin-ScanDependencies -o /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/vocwrapper.o.scan -- /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang -x c -ivfsstatcache /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/iphonesimulator18.4-22E235-71825a6d136aa4733edcbe86ece5525b.sdkstatcache -fmessage-length\=0 -fdiagnostics-show-note-include-stack -fmacro-backtrace-limit\=0 -fno-color-diagnostics -fmodules-prune-interval\=86400 -fmodules-prune-after\=345600 -fbuild-session-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation -fmodules-validate-once-per-build-session -Wnon-modular-include-in-framework-module -Werror\=non-modular-include-in-framework-module -Wno-trigraphs -Wno-missing-field-initializers -Wno-missing-prototypes -Wno-return-type -Wno-missing-braces -Wparentheses -Wswitch -Wno-unused-function -Wno-unused-label -Wno-unused-parameter -Wno-unused-variable -Wunused-value -Wno-empty-body -Wno-uninitialized -Wno-unknown-pragmas -w -Wno-shadow -Wno-four-char-constants -Wno-conversion -Wno-constant-conversion -Wno-int-conversion -Wno-bool-conversion -Wno-enum-conversion -Wno-float-conversion -Wno-non-literal-null-conversion -Wno-objc-literal-conversion -Wshorten-64-to-32 -Wpointer-sign -Wno-newline-eof -Wno-implicit-fallthrough -fstrict-aliasing -Wdeprecated-declarations -Wno-sign-conversion -Wno-infinite-recursion -Wno-comma -Wno-block-capture-autoreleasing -Wno-strict-prototypes -Wno-semicolon-before-method-body -index-store-path /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Index.noindex/DataStore @/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp -MMD -MT dependencies -MF /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/vocwrapper.d --serialize-diagnostics /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/vocwrapper.dia -c /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/SourcePackages/checkouts/SoundpipeAudioKit/Sources/Soundpipe/external/vocwrapper.c -o /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/vocwrapper.o -index-unit-output-path /SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/vocwrapper.o

CompileC /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/waveset.o /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/SourcePackages/checkouts/SoundpipeAudioKit/Sources/Soundpipe/modules/waveset.c normal arm64 c com.apple.compilers.llvm.clang.1_0.compiler (in target 'Soundpipe' from project 'SoundpipeAudioKit')
    cd /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer.xcodeproj
    
    Using response file: /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp
    
    /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang -x c -ivfsstatcache /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/iphonesimulator18.4-22E235-71825a6d136aa4733edcbe86ece5525b.sdkstatcache -fmessage-length\=0 -fdiagnostics-show-note-include-stack -fmacro-backtrace-limit\=0 -fno-color-diagnostics -fmodules-prune-interval\=86400 -fmodules-prune-after\=345600 -fbuild-session-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation -fmodules-validate-once-per-build-session -Wnon-modular-include-in-framework-module -Werror\=non-modular-include-in-framework-module -Wno-trigraphs -Wno-missing-field-initializers -Wno-missing-prototypes -Wno-return-type -Wno-missing-braces -Wparentheses -Wswitch -Wno-unused-function -Wno-unused-label -Wno-unused-parameter -Wno-unused-variable -Wunused-value -Wno-empty-body -Wno-uninitialized -Wno-unknown-pragmas -w -Wno-shadow -Wno-four-char-constants -Wno-conversion -Wno-constant-conversion -Wno-int-conversion -Wno-bool-conversion -Wno-enum-conversion -Wno-float-conversion -Wno-non-literal-null-conversion -Wno-objc-literal-conversion -Wshorten-64-to-32 -Wpointer-sign -Wno-newline-eof -Wno-implicit-fallthrough -fstrict-aliasing -Wdeprecated-declarations -Wno-sign-conversion -Wno-infinite-recursion -Wno-comma -Wno-block-capture-autoreleasing -Wno-strict-prototypes -Wno-semicolon-before-method-body -index-store-path /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Index.noindex/DataStore @/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp -MMD -MT dependencies -MF /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/waveset.d --serialize-diagnostics /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/waveset.dia -c /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/SourcePackages/checkouts/SoundpipeAudioKit/Sources/Soundpipe/modules/waveset.c -o /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/waveset.o -index-unit-output-path /SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/waveset.o

CompileC /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/vocwrapper.o /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/SourcePackages/checkouts/SoundpipeAudioKit/Sources/Soundpipe/external/vocwrapper.c normal arm64 c com.apple.compilers.llvm.clang.1_0.compiler (in target 'Soundpipe' from project 'SoundpipeAudioKit')
    cd /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer.xcodeproj
    
    Using response file: /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp
    
    /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang -x c -ivfsstatcache /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/iphonesimulator18.4-22E235-71825a6d136aa4733edcbe86ece5525b.sdkstatcache -fmessage-length\=0 -fdiagnostics-show-note-include-stack -fmacro-backtrace-limit\=0 -fno-color-diagnostics -fmodules-prune-interval\=86400 -fmodules-prune-after\=345600 -fbuild-session-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation -fmodules-validate-once-per-build-session -Wnon-modular-include-in-framework-module -Werror\=non-modular-include-in-framework-module -Wno-trigraphs -Wno-missing-field-initializers -Wno-missing-prototypes -Wno-return-type -Wno-missing-braces -Wparentheses -Wswitch -Wno-unused-function -Wno-unused-label -Wno-unused-parameter -Wno-unused-variable -Wunused-value -Wno-empty-body -Wno-uninitialized -Wno-unknown-pragmas -w -Wno-shadow -Wno-four-char-constants -Wno-conversion -Wno-constant-conversion -Wno-int-conversion -Wno-bool-conversion -Wno-enum-conversion -Wno-float-conversion -Wno-non-literal-null-conversion -Wno-objc-literal-conversion -Wshorten-64-to-32 -Wpointer-sign -Wno-newline-eof -Wno-implicit-fallthrough -fstrict-aliasing -Wdeprecated-declarations -Wno-sign-conversion -Wno-infinite-recursion -Wno-comma -Wno-block-capture-autoreleasing -Wno-strict-prototypes -Wno-semicolon-before-method-body -index-store-path /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Index.noindex/DataStore @/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp -MMD -MT dependencies -MF /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/vocwrapper.d --serialize-diagnostics /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/vocwrapper.dia -c /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/SourcePackages/checkouts/SoundpipeAudioKit/Sources/Soundpipe/external/vocwrapper.c -o /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/vocwrapper.o -index-unit-output-path /SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/vocwrapper.o

ScanDependencies /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/vocoder.o /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/SourcePackages/checkouts/SoundpipeAudioKit/Sources/Soundpipe/modules/vocoder.c normal arm64 c com.apple.compilers.llvm.clang.1_0.compiler (in target 'Soundpipe' from project 'SoundpipeAudioKit')
    cd /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer.xcodeproj
    
    Using response file: /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp
    
    builtin-ScanDependencies -o /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/vocoder.o.scan -- /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang -x c -ivfsstatcache /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/iphonesimulator18.4-22E235-71825a6d136aa4733edcbe86ece5525b.sdkstatcache -fmessage-length\=0 -fdiagnostics-show-note-include-stack -fmacro-backtrace-limit\=0 -fno-color-diagnostics -fmodules-prune-interval\=86400 -fmodules-prune-after\=345600 -fbuild-session-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation -fmodules-validate-once-per-build-session -Wnon-modular-include-in-framework-module -Werror\=non-modular-include-in-framework-module -Wno-trigraphs -Wno-missing-field-initializers -Wno-missing-prototypes -Wno-return-type -Wno-missing-braces -Wparentheses -Wswitch -Wno-unused-function -Wno-unused-label -Wno-unused-parameter -Wno-unused-variable -Wunused-value -Wno-empty-body -Wno-uninitialized -Wno-unknown-pragmas -w -Wno-shadow -Wno-four-char-constants -Wno-conversion -Wno-constant-conversion -Wno-int-conversion -Wno-bool-conversion -Wno-enum-conversion -Wno-float-conversion -Wno-non-literal-null-conversion -Wno-objc-literal-conversion -Wshorten-64-to-32 -Wpointer-sign -Wno-newline-eof -Wno-implicit-fallthrough -fstrict-aliasing -Wdeprecated-declarations -Wno-sign-conversion -Wno-infinite-recursion -Wno-comma -Wno-block-capture-autoreleasing -Wno-strict-prototypes -Wno-semicolon-before-method-body -index-store-path /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Index.noindex/DataStore @/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp -MMD -MT dependencies -MF /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/vocoder.d --serialize-diagnostics /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/vocoder.dia -c /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/SourcePackages/checkouts/SoundpipeAudioKit/Sources/Soundpipe/modules/vocoder.c -o /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/vocoder.o -index-unit-output-path /SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/vocoder.o

ScanDependencies /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/voc.o /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/SourcePackages/checkouts/SoundpipeAudioKit/Sources/Soundpipe/modules/voc.c normal arm64 c com.apple.compilers.llvm.clang.1_0.compiler (in target 'Soundpipe' from project 'SoundpipeAudioKit')
    cd /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer.xcodeproj
    
    Using response file: /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp
    
    builtin-ScanDependencies -o /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/voc.o.scan -- /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang -x c -ivfsstatcache /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/iphonesimulator18.4-22E235-71825a6d136aa4733edcbe86ece5525b.sdkstatcache -fmessage-length\=0 -fdiagnostics-show-note-include-stack -fmacro-backtrace-limit\=0 -fno-color-diagnostics -fmodules-prune-interval\=86400 -fmodules-prune-after\=345600 -fbuild-session-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation -fmodules-validate-once-per-build-session -Wnon-modular-include-in-framework-module -Werror\=non-modular-include-in-framework-module -Wno-trigraphs -Wno-missing-field-initializers -Wno-missing-prototypes -Wno-return-type -Wno-missing-braces -Wparentheses -Wswitch -Wno-unused-function -Wno-unused-label -Wno-unused-parameter -Wno-unused-variable -Wunused-value -Wno-empty-body -Wno-uninitialized -Wno-unknown-pragmas -w -Wno-shadow -Wno-four-char-constants -Wno-conversion -Wno-constant-conversion -Wno-int-conversion -Wno-bool-conversion -Wno-enum-conversion -Wno-float-conversion -Wno-non-literal-null-conversion -Wno-objc-literal-conversion -Wshorten-64-to-32 -Wpointer-sign -Wno-newline-eof -Wno-implicit-fallthrough -fstrict-aliasing -Wdeprecated-declarations -Wno-sign-conversion -Wno-infinite-recursion -Wno-comma -Wno-block-capture-autoreleasing -Wno-strict-prototypes -Wno-semicolon-before-method-body -index-store-path /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Index.noindex/DataStore @/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp -MMD -MT dependencies -MF /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/voc.d --serialize-diagnostics /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/voc.dia -c /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/SourcePackages/checkouts/SoundpipeAudioKit/Sources/Soundpipe/modules/voc.c -o /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/voc.o -index-unit-output-path /SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/voc.o

CompileC /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/vocoder.o /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/SourcePackages/checkouts/SoundpipeAudioKit/Sources/Soundpipe/modules/vocoder.c normal arm64 c com.apple.compilers.llvm.clang.1_0.compiler (in target 'Soundpipe' from project 'SoundpipeAudioKit')
    cd /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer.xcodeproj
    
    Using response file: /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp
    
    /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang -x c -ivfsstatcache /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/iphonesimulator18.4-22E235-71825a6d136aa4733edcbe86ece5525b.sdkstatcache -fmessage-length\=0 -fdiagnostics-show-note-include-stack -fmacro-backtrace-limit\=0 -fno-color-diagnostics -fmodules-prune-interval\=86400 -fmodules-prune-after\=345600 -fbuild-session-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation -fmodules-validate-once-per-build-session -Wnon-modular-include-in-framework-module -Werror\=non-modular-include-in-framework-module -Wno-trigraphs -Wno-missing-field-initializers -Wno-missing-prototypes -Wno-return-type -Wno-missing-braces -Wparentheses -Wswitch -Wno-unused-function -Wno-unused-label -Wno-unused-parameter -Wno-unused-variable -Wunused-value -Wno-empty-body -Wno-uninitialized -Wno-unknown-pragmas -w -Wno-shadow -Wno-four-char-constants -Wno-conversion -Wno-constant-conversion -Wno-int-conversion -Wno-bool-conversion -Wno-enum-conversion -Wno-float-conversion -Wno-non-literal-null-conversion -Wno-objc-literal-conversion -Wshorten-64-to-32 -Wpointer-sign -Wno-newline-eof -Wno-implicit-fallthrough -fstrict-aliasing -Wdeprecated-declarations -Wno-sign-conversion -Wno-infinite-recursion -Wno-comma -Wno-block-capture-autoreleasing -Wno-strict-prototypes -Wno-semicolon-before-method-body -index-store-path /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Index.noindex/DataStore @/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp -MMD -MT dependencies -MF /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/vocoder.d --serialize-diagnostics /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/vocoder.dia -c /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/SourcePackages/checkouts/SoundpipeAudioKit/Sources/Soundpipe/modules/vocoder.c -o /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/vocoder.o -index-unit-output-path /SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/vocoder.o

CompileC /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/voc.o /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/SourcePackages/checkouts/SoundpipeAudioKit/Sources/Soundpipe/modules/voc.c normal arm64 c com.apple.compilers.llvm.clang.1_0.compiler (in target 'Soundpipe' from project 'SoundpipeAudioKit')
    cd /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer.xcodeproj
    
    Using response file: /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp
    
    /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang -x c -ivfsstatcache /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/iphonesimulator18.4-22E235-71825a6d136aa4733edcbe86ece5525b.sdkstatcache -fmessage-length\=0 -fdiagnostics-show-note-include-stack -fmacro-backtrace-limit\=0 -fno-color-diagnostics -fmodules-prune-interval\=86400 -fmodules-prune-after\=345600 -fbuild-session-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation -fmodules-validate-once-per-build-session -Wnon-modular-include-in-framework-module -Werror\=non-modular-include-in-framework-module -Wno-trigraphs -Wno-missing-field-initializers -Wno-missing-prototypes -Wno-return-type -Wno-missing-braces -Wparentheses -Wswitch -Wno-unused-function -Wno-unused-label -Wno-unused-parameter -Wno-unused-variable -Wunused-value -Wno-empty-body -Wno-uninitialized -Wno-unknown-pragmas -w -Wno-shadow -Wno-four-char-constants -Wno-conversion -Wno-constant-conversion -Wno-int-conversion -Wno-bool-conversion -Wno-enum-conversion -Wno-float-conversion -Wno-non-literal-null-conversion -Wno-objc-literal-conversion -Wshorten-64-to-32 -Wpointer-sign -Wno-newline-eof -Wno-implicit-fallthrough -fstrict-aliasing -Wdeprecated-declarations -Wno-sign-conversion -Wno-infinite-recursion -Wno-comma -Wno-block-capture-autoreleasing -Wno-strict-prototypes -Wno-semicolon-before-method-body -index-store-path /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Index.noindex/DataStore @/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp -MMD -MT dependencies -MF /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/voc.d --serialize-diagnostics /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/voc.dia -c /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/SourcePackages/checkouts/SoundpipeAudioKit/Sources/Soundpipe/modules/voc.c -o /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/voc.o -index-unit-output-path /SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/voc.o

ScanDependencies /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/vdelay.o /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/SourcePackages/checkouts/SoundpipeAudioKit/Sources/Soundpipe/modules/vdelay.c normal arm64 c com.apple.compilers.llvm.clang.1_0.compiler (in target 'Soundpipe' from project 'SoundpipeAudioKit')
    cd /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer.xcodeproj
    
    Using response file: /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp
    
    builtin-ScanDependencies -o /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/vdelay.o.scan -- /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang -x c -ivfsstatcache /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/iphonesimulator18.4-22E235-71825a6d136aa4733edcbe86ece5525b.sdkstatcache -fmessage-length\=0 -fdiagnostics-show-note-include-stack -fmacro-backtrace-limit\=0 -fno-color-diagnostics -fmodules-prune-interval\=86400 -fmodules-prune-after\=345600 -fbuild-session-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation -fmodules-validate-once-per-build-session -Wnon-modular-include-in-framework-module -Werror\=non-modular-include-in-framework-module -Wno-trigraphs -Wno-missing-field-initializers -Wno-missing-prototypes -Wno-return-type -Wno-missing-braces -Wparentheses -Wswitch -Wno-unused-function -Wno-unused-label -Wno-unused-parameter -Wno-unused-variable -Wunused-value -Wno-empty-body -Wno-uninitialized -Wno-unknown-pragmas -w -Wno-shadow -Wno-four-char-constants -Wno-conversion -Wno-constant-conversion -Wno-int-conversion -Wno-bool-conversion -Wno-enum-conversion -Wno-float-conversion -Wno-non-literal-null-conversion -Wno-objc-literal-conversion -Wshorten-64-to-32 -Wpointer-sign -Wno-newline-eof -Wno-implicit-fallthrough -fstrict-aliasing -Wdeprecated-declarations -Wno-sign-conversion -Wno-infinite-recursion -Wno-comma -Wno-block-capture-autoreleasing -Wno-strict-prototypes -Wno-semicolon-before-method-body -index-store-path /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Index.noindex/DataStore @/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp -MMD -MT dependencies -MF /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/vdelay.d --serialize-diagnostics /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/vdelay.dia -c /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/SourcePackages/checkouts/SoundpipeAudioKit/Sources/Soundpipe/modules/vdelay.c -o /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/vdelay.o -index-unit-output-path /SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/vdelay.o

ScanDependencies /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/tseq.o /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/SourcePackages/checkouts/SoundpipeAudioKit/Sources/Soundpipe/modules/tseq.c normal arm64 c com.apple.compilers.llvm.clang.1_0.compiler (in target 'Soundpipe' from project 'SoundpipeAudioKit')
    cd /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer.xcodeproj
    
    Using response file: /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp
    
    builtin-ScanDependencies -o /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/tseq.o.scan -- /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang -x c -ivfsstatcache /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/iphonesimulator18.4-22E235-71825a6d136aa4733edcbe86ece5525b.sdkstatcache -fmessage-length\=0 -fdiagnostics-show-note-include-stack -fmacro-backtrace-limit\=0 -fno-color-diagnostics -fmodules-prune-interval\=86400 -fmodules-prune-after\=345600 -fbuild-session-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation -fmodules-validate-once-per-build-session -Wnon-modular-include-in-framework-module -Werror\=non-modular-include-in-framework-module -Wno-trigraphs -Wno-missing-field-initializers -Wno-missing-prototypes -Wno-return-type -Wno-missing-braces -Wparentheses -Wswitch -Wno-unused-function -Wno-unused-label -Wno-unused-parameter -Wno-unused-variable -Wunused-value -Wno-empty-body -Wno-uninitialized -Wno-unknown-pragmas -w -Wno-shadow -Wno-four-char-constants -Wno-conversion -Wno-constant-conversion -Wno-int-conversion -Wno-bool-conversion -Wno-enum-conversion -Wno-float-conversion -Wno-non-literal-null-conversion -Wno-objc-literal-conversion -Wshorten-64-to-32 -Wpointer-sign -Wno-newline-eof -Wno-implicit-fallthrough -fstrict-aliasing -Wdeprecated-declarations -Wno-sign-conversion -Wno-infinite-recursion -Wno-comma -Wno-block-capture-autoreleasing -Wno-strict-prototypes -Wno-semicolon-before-method-body -index-store-path /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Index.noindex/DataStore @/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp -MMD -MT dependencies -MF /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/tseq.d --serialize-diagnostics /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/tseq.dia -c /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/SourcePackages/checkouts/SoundpipeAudioKit/Sources/Soundpipe/modules/tseq.c -o /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/tseq.o -index-unit-output-path /SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/tseq.o

CompileC /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/vdelay.o /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/SourcePackages/checkouts/SoundpipeAudioKit/Sources/Soundpipe/modules/vdelay.c normal arm64 c com.apple.compilers.llvm.clang.1_0.compiler (in target 'Soundpipe' from project 'SoundpipeAudioKit')
    cd /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer.xcodeproj
    
    Using response file: /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp
    
    /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang -x c -ivfsstatcache /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/iphonesimulator18.4-22E235-71825a6d136aa4733edcbe86ece5525b.sdkstatcache -fmessage-length\=0 -fdiagnostics-show-note-include-stack -fmacro-backtrace-limit\=0 -fno-color-diagnostics -fmodules-prune-interval\=86400 -fmodules-prune-after\=345600 -fbuild-session-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation -fmodules-validate-once-per-build-session -Wnon-modular-include-in-framework-module -Werror\=non-modular-include-in-framework-module -Wno-trigraphs -Wno-missing-field-initializers -Wno-missing-prototypes -Wno-return-type -Wno-missing-braces -Wparentheses -Wswitch -Wno-unused-function -Wno-unused-label -Wno-unused-parameter -Wno-unused-variable -Wunused-value -Wno-empty-body -Wno-uninitialized -Wno-unknown-pragmas -w -Wno-shadow -Wno-four-char-constants -Wno-conversion -Wno-constant-conversion -Wno-int-conversion -Wno-bool-conversion -Wno-enum-conversion -Wno-float-conversion -Wno-non-literal-null-conversion -Wno-objc-literal-conversion -Wshorten-64-to-32 -Wpointer-sign -Wno-newline-eof -Wno-implicit-fallthrough -fstrict-aliasing -Wdeprecated-declarations -Wno-sign-conversion -Wno-infinite-recursion -Wno-comma -Wno-block-capture-autoreleasing -Wno-strict-prototypes -Wno-semicolon-before-method-body -index-store-path /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Index.noindex/DataStore @/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp -MMD -MT dependencies -MF /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/vdelay.d --serialize-diagnostics /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/vdelay.dia -c /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/SourcePackages/checkouts/SoundpipeAudioKit/Sources/Soundpipe/modules/vdelay.c -o /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/vdelay.o -index-unit-output-path /SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/vdelay.o

CompileC /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/tseq.o /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/SourcePackages/checkouts/SoundpipeAudioKit/Sources/Soundpipe/modules/tseq.c normal arm64 c com.apple.compilers.llvm.clang.1_0.compiler (in target 'Soundpipe' from project 'SoundpipeAudioKit')
    cd /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer.xcodeproj
    
    Using response file: /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp
    
    /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang -x c -ivfsstatcache /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/iphonesimulator18.4-22E235-71825a6d136aa4733edcbe86ece5525b.sdkstatcache -fmessage-length\=0 -fdiagnostics-show-note-include-stack -fmacro-backtrace-limit\=0 -fno-color-diagnostics -fmodules-prune-interval\=86400 -fmodules-prune-after\=345600 -fbuild-session-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation -fmodules-validate-once-per-build-session -Wnon-modular-include-in-framework-module -Werror\=non-modular-include-in-framework-module -Wno-trigraphs -Wno-missing-field-initializers -Wno-missing-prototypes -Wno-return-type -Wno-missing-braces -Wparentheses -Wswitch -Wno-unused-function -Wno-unused-label -Wno-unused-parameter -Wno-unused-variable -Wunused-value -Wno-empty-body -Wno-uninitialized -Wno-unknown-pragmas -w -Wno-shadow -Wno-four-char-constants -Wno-conversion -Wno-constant-conversion -Wno-int-conversion -Wno-bool-conversion -Wno-enum-conversion -Wno-float-conversion -Wno-non-literal-null-conversion -Wno-objc-literal-conversion -Wshorten-64-to-32 -Wpointer-sign -Wno-newline-eof -Wno-implicit-fallthrough -fstrict-aliasing -Wdeprecated-declarations -Wno-sign-conversion -Wno-infinite-recursion -Wno-comma -Wno-block-capture-autoreleasing -Wno-strict-prototypes -Wno-semicolon-before-method-body -index-store-path /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Index.noindex/DataStore @/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp -MMD -MT dependencies -MF /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/tseq.d --serialize-diagnostics /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/tseq.dia -c /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/SourcePackages/checkouts/SoundpipeAudioKit/Sources/Soundpipe/modules/tseq.c -o /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/tseq.o -index-unit-output-path /SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/tseq.o

ScanDependencies /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/tseg.o /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/SourcePackages/checkouts/SoundpipeAudioKit/Sources/Soundpipe/modules/tseg.c normal arm64 c com.apple.compilers.llvm.clang.1_0.compiler (in target 'Soundpipe' from project 'SoundpipeAudioKit')
    cd /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer.xcodeproj
    
    Using response file: /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp
    
    builtin-ScanDependencies -o /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/tseg.o.scan -- /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang -x c -ivfsstatcache /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/iphonesimulator18.4-22E235-71825a6d136aa4733edcbe86ece5525b.sdkstatcache -fmessage-length\=0 -fdiagnostics-show-note-include-stack -fmacro-backtrace-limit\=0 -fno-color-diagnostics -fmodules-prune-interval\=86400 -fmodules-prune-after\=345600 -fbuild-session-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation -fmodules-validate-once-per-build-session -Wnon-modular-include-in-framework-module -Werror\=non-modular-include-in-framework-module -Wno-trigraphs -Wno-missing-field-initializers -Wno-missing-prototypes -Wno-return-type -Wno-missing-braces -Wparentheses -Wswitch -Wno-unused-function -Wno-unused-label -Wno-unused-parameter -Wno-unused-variable -Wunused-value -Wno-empty-body -Wno-uninitialized -Wno-unknown-pragmas -w -Wno-shadow -Wno-four-char-constants -Wno-conversion -Wno-constant-conversion -Wno-int-conversion -Wno-bool-conversion -Wno-enum-conversion -Wno-float-conversion -Wno-non-literal-null-conversion -Wno-objc-literal-conversion -Wshorten-64-to-32 -Wpointer-sign -Wno-newline-eof -Wno-implicit-fallthrough -fstrict-aliasing -Wdeprecated-declarations -Wno-sign-conversion -Wno-infinite-recursion -Wno-comma -Wno-block-capture-autoreleasing -Wno-strict-prototypes -Wno-semicolon-before-method-body -index-store-path /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Index.noindex/DataStore @/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp -MMD -MT dependencies -MF /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/tseg.d --serialize-diagnostics /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/tseg.dia -c /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/SourcePackages/checkouts/SoundpipeAudioKit/Sources/Soundpipe/modules/tseg.c -o /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/tseg.o -index-unit-output-path /SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/tseg.o

ScanDependencies /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/trand.o /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/SourcePackages/checkouts/SoundpipeAudioKit/Sources/Soundpipe/modules/trand.c normal arm64 c com.apple.compilers.llvm.clang.1_0.compiler (in target 'Soundpipe' from project 'SoundpipeAudioKit')
    cd /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer.xcodeproj
    
    Using response file: /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp
    
    builtin-ScanDependencies -o /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/trand.o.scan -- /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang -x c -ivfsstatcache /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/iphonesimulator18.4-22E235-71825a6d136aa4733edcbe86ece5525b.sdkstatcache -fmessage-length\=0 -fdiagnostics-show-note-include-stack -fmacro-backtrace-limit\=0 -fno-color-diagnostics -fmodules-prune-interval\=86400 -fmodules-prune-after\=345600 -fbuild-session-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation -fmodules-validate-once-per-build-session -Wnon-modular-include-in-framework-module -Werror\=non-modular-include-in-framework-module -Wno-trigraphs -Wno-missing-field-initializers -Wno-missing-prototypes -Wno-return-type -Wno-missing-braces -Wparentheses -Wswitch -Wno-unused-function -Wno-unused-label -Wno-unused-parameter -Wno-unused-variable -Wunused-value -Wno-empty-body -Wno-uninitialized -Wno-unknown-pragmas -w -Wno-shadow -Wno-four-char-constants -Wno-conversion -Wno-constant-conversion -Wno-int-conversion -Wno-bool-conversion -Wno-enum-conversion -Wno-float-conversion -Wno-non-literal-null-conversion -Wno-objc-literal-conversion -Wshorten-64-to-32 -Wpointer-sign -Wno-newline-eof -Wno-implicit-fallthrough -fstrict-aliasing -Wdeprecated-declarations -Wno-sign-conversion -Wno-infinite-recursion -Wno-comma -Wno-block-capture-autoreleasing -Wno-strict-prototypes -Wno-semicolon-before-method-body -index-store-path /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Index.noindex/DataStore @/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp -MMD -MT dependencies -MF /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/trand.d --serialize-diagnostics /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/trand.dia -c /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/SourcePackages/checkouts/SoundpipeAudioKit/Sources/Soundpipe/modules/trand.c -o /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/trand.o -index-unit-output-path /SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/trand.o

CompileC /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/tseg.o /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/SourcePackages/checkouts/SoundpipeAudioKit/Sources/Soundpipe/modules/tseg.c normal arm64 c com.apple.compilers.llvm.clang.1_0.compiler (in target 'Soundpipe' from project 'SoundpipeAudioKit')
    cd /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer.xcodeproj
    
    Using response file: /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp
    
    /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang -x c -ivfsstatcache /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/iphonesimulator18.4-22E235-71825a6d136aa4733edcbe86ece5525b.sdkstatcache -fmessage-length\=0 -fdiagnostics-show-note-include-stack -fmacro-backtrace-limit\=0 -fno-color-diagnostics -fmodules-prune-interval\=86400 -fmodules-prune-after\=345600 -fbuild-session-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation -fmodules-validate-once-per-build-session -Wnon-modular-include-in-framework-module -Werror\=non-modular-include-in-framework-module -Wno-trigraphs -Wno-missing-field-initializers -Wno-missing-prototypes -Wno-return-type -Wno-missing-braces -Wparentheses -Wswitch -Wno-unused-function -Wno-unused-label -Wno-unused-parameter -Wno-unused-variable -Wunused-value -Wno-empty-body -Wno-uninitialized -Wno-unknown-pragmas -w -Wno-shadow -Wno-four-char-constants -Wno-conversion -Wno-constant-conversion -Wno-int-conversion -Wno-bool-conversion -Wno-enum-conversion -Wno-float-conversion -Wno-non-literal-null-conversion -Wno-objc-literal-conversion -Wshorten-64-to-32 -Wpointer-sign -Wno-newline-eof -Wno-implicit-fallthrough -fstrict-aliasing -Wdeprecated-declarations -Wno-sign-conversion -Wno-infinite-recursion -Wno-comma -Wno-block-capture-autoreleasing -Wno-strict-prototypes -Wno-semicolon-before-method-body -index-store-path /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Index.noindex/DataStore @/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp -MMD -MT dependencies -MF /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/tseg.d --serialize-diagnostics /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/tseg.dia -c /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/SourcePackages/checkouts/SoundpipeAudioKit/Sources/Soundpipe/modules/tseg.c -o /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/tseg.o -index-unit-output-path /SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/tseg.o

CompileC /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/trand.o /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/SourcePackages/checkouts/SoundpipeAudioKit/Sources/Soundpipe/modules/trand.c normal arm64 c com.apple.compilers.llvm.clang.1_0.compiler (in target 'Soundpipe' from project 'SoundpipeAudioKit')
    cd /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer.xcodeproj
    
    Using response file: /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp
    
    /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang -x c -ivfsstatcache /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/iphonesimulator18.4-22E235-71825a6d136aa4733edcbe86ece5525b.sdkstatcache -fmessage-length\=0 -fdiagnostics-show-note-include-stack -fmacro-backtrace-limit\=0 -fno-color-diagnostics -fmodules-prune-interval\=86400 -fmodules-prune-after\=345600 -fbuild-session-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation -fmodules-validate-once-per-build-session -Wnon-modular-include-in-framework-module -Werror\=non-modular-include-in-framework-module -Wno-trigraphs -Wno-missing-field-initializers -Wno-missing-prototypes -Wno-return-type -Wno-missing-braces -Wparentheses -Wswitch -Wno-unused-function -Wno-unused-label -Wno-unused-parameter -Wno-unused-variable -Wunused-value -Wno-empty-body -Wno-uninitialized -Wno-unknown-pragmas -w -Wno-shadow -Wno-four-char-constants -Wno-conversion -Wno-constant-conversion -Wno-int-conversion -Wno-bool-conversion -Wno-enum-conversion -Wno-float-conversion -Wno-non-literal-null-conversion -Wno-objc-literal-conversion -Wshorten-64-to-32 -Wpointer-sign -Wno-newline-eof -Wno-implicit-fallthrough -fstrict-aliasing -Wdeprecated-declarations -Wno-sign-conversion -Wno-infinite-recursion -Wno-comma -Wno-block-capture-autoreleasing -Wno-strict-prototypes -Wno-semicolon-before-method-body -index-store-path /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Index.noindex/DataStore @/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp -MMD -MT dependencies -MF /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/trand.d --serialize-diagnostics /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/trand.dia -c /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/SourcePackages/checkouts/SoundpipeAudioKit/Sources/Soundpipe/modules/trand.c -o /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/trand.o -index-unit-output-path /SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/trand.o

ScanDependencies /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/tone.o /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/SourcePackages/checkouts/SoundpipeAudioKit/Sources/Soundpipe/modules/tone.c normal arm64 c com.apple.compilers.llvm.clang.1_0.compiler (in target 'Soundpipe' from project 'SoundpipeAudioKit')
    cd /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer.xcodeproj
    
    Using response file: /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp
    
    builtin-ScanDependencies -o /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/tone.o.scan -- /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang -x c -ivfsstatcache /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/iphonesimulator18.4-22E235-71825a6d136aa4733edcbe86ece5525b.sdkstatcache -fmessage-length\=0 -fdiagnostics-show-note-include-stack -fmacro-backtrace-limit\=0 -fno-color-diagnostics -fmodules-prune-interval\=86400 -fmodules-prune-after\=345600 -fbuild-session-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation -fmodules-validate-once-per-build-session -Wnon-modular-include-in-framework-module -Werror\=non-modular-include-in-framework-module -Wno-trigraphs -Wno-missing-field-initializers -Wno-missing-prototypes -Wno-return-type -Wno-missing-braces -Wparentheses -Wswitch -Wno-unused-function -Wno-unused-label -Wno-unused-parameter -Wno-unused-variable -Wunused-value -Wno-empty-body -Wno-uninitialized -Wno-unknown-pragmas -w -Wno-shadow -Wno-four-char-constants -Wno-conversion -Wno-constant-conversion -Wno-int-conversion -Wno-bool-conversion -Wno-enum-conversion -Wno-float-conversion -Wno-non-literal-null-conversion -Wno-objc-literal-conversion -Wshorten-64-to-32 -Wpointer-sign -Wno-newline-eof -Wno-implicit-fallthrough -fstrict-aliasing -Wdeprecated-declarations -Wno-sign-conversion -Wno-infinite-recursion -Wno-comma -Wno-block-capture-autoreleasing -Wno-strict-prototypes -Wno-semicolon-before-method-body -index-store-path /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Index.noindex/DataStore @/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp -MMD -MT dependencies -MF /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/tone.d --serialize-diagnostics /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/tone.dia -c /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/SourcePackages/checkouts/SoundpipeAudioKit/Sources/Soundpipe/modules/tone.c -o /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/tone.o -index-unit-output-path /SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/tone.o

SwiftDriverJobDiscovery normal arm64 Emitting module for VoicingTrainer (in target 'VoicingTrainer' from project 'VoicingTrainer')

SwiftDriver\ Compilation\ Requirements VoicingTrainer normal arm64 com.apple.xcode.tools.swift.compiler (in target 'VoicingTrainer' from project 'VoicingTrainer')
    cd /Users/<USER>/DEV/VoicingTrainer
    builtin-Swift-Compilation-Requirements -- /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc -module-name VoicingTrainer -Onone -enforce-exclusivity\=checked @/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug-iphonesimulator/VoicingTrainer.build/Objects-normal/arm64/VoicingTrainer.SwiftFileList -DDEBUG -Xcc -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/GeneratedModuleMaps-iphonesimulator/CAudioKitEX.modulemap -Xcc -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/GeneratedModuleMaps-iphonesimulator/KissFFT.modulemap -Xcc -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/GeneratedModuleMaps-iphonesimulator/CSoundpipeAudioKit.modulemap -Xcc -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/GeneratedModuleMaps-iphonesimulator/Soundpipe.modulemap -enable-bare-slash-regex -enable-experimental-feature DebugDescriptionMacro -sdk /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.4.sdk -target arm64-apple-ios16.0-simulator -g -module-cache-path /Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex -Xfrontend -serialize-debugging-options -profile-coverage-mapping -profile-generate -enable-testing -index-store-path /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Index.noindex/DataStore -swift-version 5 -I /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug-iphonesimulator -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug-iphonesimulator -emit-localized-strings -emit-localized-strings-path /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug-iphonesimulator/VoicingTrainer.build/Objects-normal/arm64 -c -j8 -enable-batch-mode -incremental -Xcc -ivfsstatcache -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/iphonesimulator18.4-22E235-71825a6d136aa4733edcbe86ece5525b.sdkstatcache -output-file-map /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug-iphonesimulator/VoicingTrainer.build/Objects-normal/arm64/VoicingTrainer-OutputFileMap.json -use-frontend-parseable-output -save-temps -no-color-diagnostics -serialize-diagnostics -emit-dependencies -emit-module -emit-module-path /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug-iphonesimulator/VoicingTrainer.build/Objects-normal/arm64/VoicingTrainer.swiftmodule -validate-clang-modules-once -clang-build-session-file /Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug-iphonesimulator/VoicingTrainer.build/swift-overrides.hmap -emit-const-values -Xfrontend -const-gather-protocols-file -Xfrontend /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug-iphonesimulator/VoicingTrainer.build/Objects-normal/arm64/VoicingTrainer_const_extract_protocols.json -Xcc -iquote -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug-iphonesimulator/VoicingTrainer.build/VoicingTrainer-generated-files.hmap -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug-iphonesimulator/VoicingTrainer.build/VoicingTrainer-own-target-headers.hmap -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug-iphonesimulator/VoicingTrainer.build/VoicingTrainer-all-non-framework-target-headers.hmap -Xcc -ivfsoverlay -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug-iphonesimulator/VoicingTrainer-5f2ddef3a416ec04434590c5d76bba5c-VFS-iphonesimulator/all-product-headers.yaml -Xcc -iquote -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug-iphonesimulator/VoicingTrainer.build/VoicingTrainer-project-headers.hmap -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/SourcePackages/checkouts/AudioKitEX/Sources/CAudioKitEX/include -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/SourcePackages/checkouts/KissFFT/Sources/KissFFT/include -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/SourcePackages/checkouts/SoundpipeAudioKit/Sources/CSoundpipeAudioKit/include -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/SourcePackages/checkouts/SoundpipeAudioKit/Sources/Soundpipe/include -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug-iphonesimulator/include -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug-iphonesimulator/VoicingTrainer.build/DerivedSources-normal/arm64 -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug-iphonesimulator/VoicingTrainer.build/DerivedSources/arm64 -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug-iphonesimulator/VoicingTrainer.build/DerivedSources -Xcc -DDEBUG\=1 -emit-objc-header -emit-objc-header-path /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug-iphonesimulator/VoicingTrainer.build/Objects-normal/arm64/VoicingTrainer-Swift.h -working-directory /Users/<USER>/DEV/VoicingTrainer -experimental-emit-module-separately -disable-cmo

CompileC /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/tone.o /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/SourcePackages/checkouts/SoundpipeAudioKit/Sources/Soundpipe/modules/tone.c normal arm64 c com.apple.compilers.llvm.clang.1_0.compiler (in target 'Soundpipe' from project 'SoundpipeAudioKit')
    cd /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer.xcodeproj
    
    Using response file: /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp
    
    /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang -x c -ivfsstatcache /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/iphonesimulator18.4-22E235-71825a6d136aa4733edcbe86ece5525b.sdkstatcache -fmessage-length\=0 -fdiagnostics-show-note-include-stack -fmacro-backtrace-limit\=0 -fno-color-diagnostics -fmodules-prune-interval\=86400 -fmodules-prune-after\=345600 -fbuild-session-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation -fmodules-validate-once-per-build-session -Wnon-modular-include-in-framework-module -Werror\=non-modular-include-in-framework-module -Wno-trigraphs -Wno-missing-field-initializers -Wno-missing-prototypes -Wno-return-type -Wno-missing-braces -Wparentheses -Wswitch -Wno-unused-function -Wno-unused-label -Wno-unused-parameter -Wno-unused-variable -Wunused-value -Wno-empty-body -Wno-uninitialized -Wno-unknown-pragmas -w -Wno-shadow -Wno-four-char-constants -Wno-conversion -Wno-constant-conversion -Wno-int-conversion -Wno-bool-conversion -Wno-enum-conversion -Wno-float-conversion -Wno-non-literal-null-conversion -Wno-objc-literal-conversion -Wshorten-64-to-32 -Wpointer-sign -Wno-newline-eof -Wno-implicit-fallthrough -fstrict-aliasing -Wdeprecated-declarations -Wno-sign-conversion -Wno-infinite-recursion -Wno-comma -Wno-block-capture-autoreleasing -Wno-strict-prototypes -Wno-semicolon-before-method-body -index-store-path /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Index.noindex/DataStore @/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp -MMD -MT dependencies -MF /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/tone.d --serialize-diagnostics /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/tone.dia -c /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/SourcePackages/checkouts/SoundpipeAudioKit/Sources/Soundpipe/modules/tone.c -o /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/tone.o -index-unit-output-path /SoundpipeAudioKit.build/Debug-iphonesimulator/Soundpipe.build/Objects-normal/arm64/tone.o

Copy /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug-iphonesimulator/VoicingTrainer.swiftmodule/arm64-apple-ios-simulator.swiftmodule /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug-iphonesimulator/VoicingTrainer.build/Objects-normal/arm64/VoicingTrainer.swiftmodule (in target 'VoicingTrainer' from project 'VoicingTrainer')
    cd /Users/<USER>/DEV/VoicingTrainer
    builtin-copy -exclude .DS_Store -exclude CVS -exclude .svn -exclude .git -exclude .hg -resolve-src-symlinks -rename /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug-iphonesimulator/VoicingTrainer.build/Objects-normal/arm64/VoicingTrainer.swiftmodule /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug-iphonesimulator/VoicingTrainer.swiftmodule/arm64-apple-ios-simulator.swiftmodule

SwiftDriverJobDiscovery normal arm64 Compiling PowerTestManager.swift (in target 'VoicingTrainer' from project 'VoicingTrainer')

Copy /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug-iphonesimulator/VoicingTrainer.swiftmodule/arm64-apple-ios-simulator.swiftdoc /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug-iphonesimulator/VoicingTrainer.build/Objects-normal/arm64/VoicingTrainer.swiftdoc (in target 'VoicingTrainer' from project 'VoicingTrainer')
    cd /Users/<USER>/DEV/VoicingTrainer
    builtin-copy -exclude .DS_Store -exclude CVS -exclude .svn -exclude .git -exclude .hg -resolve-src-symlinks -rename /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug-iphonesimulator/VoicingTrainer.build/Objects-normal/arm64/VoicingTrainer.swiftdoc /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug-iphonesimulator/VoicingTrainer.swiftmodule/arm64-apple-ios-simulator.swiftdoc

Copy /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug-iphonesimulator/VoicingTrainer.swiftmodule/arm64-apple-ios-simulator.abi.json /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug-iphonesimulator/VoicingTrainer.build/Objects-normal/arm64/VoicingTrainer.abi.json (in target 'VoicingTrainer' from project 'VoicingTrainer')
    cd /Users/<USER>/DEV/VoicingTrainer
    builtin-copy -exclude .DS_Store -exclude CVS -exclude .svn -exclude .git -exclude .hg -resolve-src-symlinks -rename /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug-iphonesimulator/VoicingTrainer.build/Objects-normal/arm64/VoicingTrainer.abi.json /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug-iphonesimulator/VoicingTrainer.swiftmodule/arm64-apple-ios-simulator.abi.json

Copy /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug-iphonesimulator/VoicingTrainer.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug-iphonesimulator/VoicingTrainer.build/Objects-normal/arm64/VoicingTrainer.swiftsourceinfo (in target 'VoicingTrainer' from project 'VoicingTrainer')
    cd /Users/<USER>/DEV/VoicingTrainer
    builtin-copy -exclude .DS_Store -exclude CVS -exclude .svn -exclude .git -exclude .hg -resolve-src-symlinks -rename /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug-iphonesimulator/VoicingTrainer.build/Objects-normal/arm64/VoicingTrainer.swiftsourceinfo /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug-iphonesimulator/VoicingTrainer.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo

SwiftCompile normal arm64 Compiling\ UserProgressionManager.swift,\ VoicingTrainerApp.swift,\ ChordGameManager.swift,\ MIDIManager.swift,\ CompactVoicingPicker.swift /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/Core/Managers/UserProgressionManager.swift /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/Core/App/VoicingTrainerApp.swift /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/Core/Managers/ChordGameManager.swift /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/Core/Managers/MIDIManager.swift /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/Game/Chord/CompactVoicingPicker.swift (in target 'VoicingTrainer' from project 'VoicingTrainer')
SwiftCompile normal arm64 /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/Core/Managers/UserProgressionManager.swift (in target 'VoicingTrainer' from project 'VoicingTrainer')
    cd /Users/<USER>/DEV/VoicingTrainer
    

SwiftCompile normal arm64 /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/Core/App/VoicingTrainerApp.swift (in target 'VoicingTrainer' from project 'VoicingTrainer')
    cd /Users/<USER>/DEV/VoicingTrainer
    

SwiftCompile normal arm64 /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/Core/Managers/ChordGameManager.swift (in target 'VoicingTrainer' from project 'VoicingTrainer')
    cd /Users/<USER>/DEV/VoicingTrainer
    

SwiftCompile normal arm64 /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/Core/Managers/MIDIManager.swift (in target 'VoicingTrainer' from project 'VoicingTrainer')
    cd /Users/<USER>/DEV/VoicingTrainer
    

SwiftCompile normal arm64 /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/Game/Chord/CompactVoicingPicker.swift (in target 'VoicingTrainer' from project 'VoicingTrainer')
    cd /Users/<USER>/DEV/VoicingTrainer
    

SwiftCompile normal arm64 Compiling\ MusicAppView.swift,\ ChordsView.swift,\ NoteGenerator.swift,\ ProgressionGameManager.swift,\ GeneratedAssetSymbols.swift /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/Core/App/MusicAppView.swift /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/Game/Chord/ChordsView.swift /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/Game/Note/NoteGenerator.swift /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/Core/Managers/ProgressionGameManager.swift /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug-iphonesimulator/VoicingTrainer.build/DerivedSources/GeneratedAssetSymbols.swift (in target 'VoicingTrainer' from project 'VoicingTrainer')
SwiftCompile normal arm64 /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/Core/App/MusicAppView.swift (in target 'VoicingTrainer' from project 'VoicingTrainer')
    cd /Users/<USER>/DEV/VoicingTrainer
    

SwiftCompile normal arm64 /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/Game/Chord/ChordsView.swift (in target 'VoicingTrainer' from project 'VoicingTrainer')
    cd /Users/<USER>/DEV/VoicingTrainer
    

SwiftCompile normal arm64 /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/Game/Note/NoteGenerator.swift (in target 'VoicingTrainer' from project 'VoicingTrainer')
    cd /Users/<USER>/DEV/VoicingTrainer
    

SwiftCompile normal arm64 /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/Core/Managers/ProgressionGameManager.swift (in target 'VoicingTrainer' from project 'VoicingTrainer')
    cd /Users/<USER>/DEV/VoicingTrainer
    

SwiftCompile normal arm64 /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug-iphonesimulator/VoicingTrainer.build/DerivedSources/GeneratedAssetSymbols.swift (in target 'VoicingTrainer' from project 'VoicingTrainer')
    cd /Users/<USER>/DEV/VoicingTrainer
    

SwiftCompile normal arm64 Compiling\ CountdownView.swift,\ StringNoteView.swift,\ RefactoredTargetNotesDisplay.swift,\ LevelStartView.swift,\ PowerManager.swift,\ PlatformDependant.swift /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/Game/Note/Views/CountdownView.swift /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/Game/Shared/StringNoteView.swift /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/Game/Shared/RefactoredTargetNotesDisplay.swift /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/Game/Note/Views/LevelStartView.swift /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/Core/Managers/PowerManager.swift /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/Core/Configuration/PlatformDependant.swift (in target 'VoicingTrainer' from project 'VoicingTrainer')
SwiftCompile normal arm64 /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/Game/Note/Views/CountdownView.swift (in target 'VoicingTrainer' from project 'VoicingTrainer')
    cd /Users/<USER>/DEV/VoicingTrainer
    

SwiftCompile normal arm64 /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/Game/Shared/StringNoteView.swift (in target 'VoicingTrainer' from project 'VoicingTrainer')
    cd /Users/<USER>/DEV/VoicingTrainer
    

SwiftCompile normal arm64 /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/Game/Shared/RefactoredTargetNotesDisplay.swift (in target 'VoicingTrainer' from project 'VoicingTrainer')
    cd /Users/<USER>/DEV/VoicingTrainer
    

SwiftCompile normal arm64 /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/Game/Note/Views/LevelStartView.swift (in target 'VoicingTrainer' from project 'VoicingTrainer')
    cd /Users/<USER>/DEV/VoicingTrainer
    

SwiftCompile normal arm64 /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/Core/Managers/PowerManager.swift (in target 'VoicingTrainer' from project 'VoicingTrainer')
    cd /Users/<USER>/DEV/VoicingTrainer
    

SwiftCompile normal arm64 /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/Core/Configuration/PlatformDependant.swift (in target 'VoicingTrainer' from project 'VoicingTrainer')
    cd /Users/<USER>/DEV/VoicingTrainer
    

SwiftCompile normal arm64 Compiling\ AchievementData.swift,\ SimpleNoteDisplay.swift,\ PianoKeyboardViewModel.swift,\ ParticleEffect.swift,\ ProgressionsView.swift /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/Game/Note/Models/AchievementData.swift /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/Game/Shared/SimpleNoteDisplay.swift /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/Game/Shared/PianoKeyboardViewModel.swift /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/Music/Audio/ParticleEffect.swift /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/UI/Views/ProgressionsView.swift (in target 'VoicingTrainer' from project 'VoicingTrainer')
SwiftCompile normal arm64 /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/Game/Note/Models/AchievementData.swift (in target 'VoicingTrainer' from project 'VoicingTrainer')
    cd /Users/<USER>/DEV/VoicingTrainer
    

SwiftCompile normal arm64 /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/Game/Shared/SimpleNoteDisplay.swift (in target 'VoicingTrainer' from project 'VoicingTrainer')
    cd /Users/<USER>/DEV/VoicingTrainer
    

SwiftCompile normal arm64 /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/Game/Shared/PianoKeyboardViewModel.swift (in target 'VoicingTrainer' from project 'VoicingTrainer')
    cd /Users/<USER>/DEV/VoicingTrainer
    

SwiftCompile normal arm64 /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/Music/Audio/ParticleEffect.swift (in target 'VoicingTrainer' from project 'VoicingTrainer')
    cd /Users/<USER>/DEV/VoicingTrainer
    

SwiftCompile normal arm64 /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/UI/Views/ProgressionsView.swift (in target 'VoicingTrainer' from project 'VoicingTrainer')
    cd /Users/<USER>/DEV/VoicingTrainer
    

SwiftDriverJobDiscovery normal arm64 Compiling ChordBrowserView.swift (in target 'VoicingTrainer' from project 'VoicingTrainer')

SwiftCompile normal arm64 Compiling\ AchievementManager.swift,\ WaveStringView.swift,\ NoteNameGenerator.swift,\ PianoKeyboardView.swift,\ NoteLevelData.swift,\ ChordPlayer.swift /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/Core/Managers/AchievementManager.swift /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/Game/Note/WaveStringView.swift /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/Music/Models/NoteNameGenerator.swift /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/Game/Shared/PianoKeyboardView.swift /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/Game/Note/Models/NoteLevelData.swift /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/Music/Audio/ChordPlayer.swift (in target 'VoicingTrainer' from project 'VoicingTrainer')

SwiftCompile normal arm64 /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/Core/Managers/AchievementManager.swift (in target 'VoicingTrainer' from project 'VoicingTrainer')
    cd /Users/<USER>/DEV/VoicingTrainer
    

SwiftCompile normal arm64 /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/Game/Note/WaveStringView.swift (in target 'VoicingTrainer' from project 'VoicingTrainer')
    cd /Users/<USER>/DEV/VoicingTrainer
    

SwiftCompile normal arm64 /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/Music/Models/NoteNameGenerator.swift (in target 'VoicingTrainer' from project 'VoicingTrainer')
    cd /Users/<USER>/DEV/VoicingTrainer
    

SwiftCompile normal arm64 /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/Game/Shared/PianoKeyboardView.swift (in target 'VoicingTrainer' from project 'VoicingTrainer')
    cd /Users/<USER>/DEV/VoicingTrainer
    

SwiftCompile normal arm64 /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/Game/Note/Models/NoteLevelData.swift (in target 'VoicingTrainer' from project 'VoicingTrainer')
    cd /Users/<USER>/DEV/VoicingTrainer
    

SwiftCompile normal arm64 /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/Music/Audio/ChordPlayer.swift (in target 'VoicingTrainer' from project 'VoicingTrainer')
    cd /Users/<USER>/DEV/VoicingTrainer
    

SwiftDriverJobDiscovery normal arm64 Compiling LevelClearView.swift (in target 'VoicingTrainer' from project 'VoicingTrainer')

SwiftCompile normal arm64 Compiling\ NoteGameManager.swift,\ ChordBadge.swift,\ ChordAnalyzer.swift,\ ChordsSelectView.swift,\ ChordVoicing.swift /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/Core/Managers/NoteGameManager.swift /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/UI/Shared/ChordBadge.swift /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/Music/Analysis/ChordAnalyzer.swift /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/Game/Chord/ChordsSelectView.swift /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/Music/Models/ChordVoicing.swift (in target 'VoicingTrainer' from project 'VoicingTrainer')

SwiftCompile normal arm64 /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/Core/Managers/NoteGameManager.swift (in target 'VoicingTrainer' from project 'VoicingTrainer')
    cd /Users/<USER>/DEV/VoicingTrainer
    

SwiftCompile normal arm64 /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/UI/Shared/ChordBadge.swift (in target 'VoicingTrainer' from project 'VoicingTrainer')
    cd /Users/<USER>/DEV/VoicingTrainer
    

SwiftCompile normal arm64 /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/Music/Analysis/ChordAnalyzer.swift (in target 'VoicingTrainer' from project 'VoicingTrainer')
    cd /Users/<USER>/DEV/VoicingTrainer
    

SwiftCompile normal arm64 /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/Game/Chord/ChordsSelectView.swift (in target 'VoicingTrainer' from project 'VoicingTrainer')
    cd /Users/<USER>/DEV/VoicingTrainer
    

SwiftCompile normal arm64 /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/Music/Models/ChordVoicing.swift (in target 'VoicingTrainer' from project 'VoicingTrainer')
    cd /Users/<USER>/DEV/VoicingTrainer
    

SwiftDriverJobDiscovery normal arm64 Compiling NotesView.swift (in target 'VoicingTrainer' from project 'VoicingTrainer')

SwiftCompile normal arm64 Compiling\ LevelSelectView.swift,\ NoteGameStateManager.swift,\ ProgressionReader.swift,\ SongsView.swift,\ SpriteKitParticleSystem.swift /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/Game/Note/Views/LevelSelectView.swift /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/Game/Shared/NoteGameStateManager.swift /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/Model/ProgressionReader.swift /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/UI/Views/SongsView.swift /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/Music/Audio/SpriteKitParticleSystem.swift (in target 'VoicingTrainer' from project 'VoicingTrainer')

SwiftCompile normal arm64 /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/Game/Note/Views/LevelSelectView.swift (in target 'VoicingTrainer' from project 'VoicingTrainer')
    cd /Users/<USER>/DEV/VoicingTrainer
    

SwiftCompile normal arm64 /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/Game/Shared/NoteGameStateManager.swift (in target 'VoicingTrainer' from project 'VoicingTrainer')
    cd /Users/<USER>/DEV/VoicingTrainer
    

SwiftCompile normal arm64 /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/Model/ProgressionReader.swift (in target 'VoicingTrainer' from project 'VoicingTrainer')
    cd /Users/<USER>/DEV/VoicingTrainer
    

SwiftCompile normal arm64 /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/UI/Views/SongsView.swift (in target 'VoicingTrainer' from project 'VoicingTrainer')
    cd /Users/<USER>/DEV/VoicingTrainer
    

SwiftCompile normal arm64 /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/Music/Audio/SpriteKitParticleSystem.swift (in target 'VoicingTrainer' from project 'VoicingTrainer')
    cd /Users/<USER>/DEV/VoicingTrainer
    

SwiftDriverJobDiscovery normal arm64 Compiling PowerDebugView.swift (in target 'VoicingTrainer' from project 'VoicingTrainer')

SwiftCompile normal arm64 Compiling\ SettingsView.swift,\ ChordNameParser.swift,\ SoundEffectManager.swift,\ ChordsParser.swift,\ GameConfig.swift,\ CompactGameStatus.swift /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/UI/Views/SettingsView.swift /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/Music/Analysis/ChordNameParser.swift /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/Core/Managers/SoundEffectManager.swift /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/Music/Models/ChordsParser.swift /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/Core/Configuration/GameConfig.swift /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/Game/Shared/CompactGameStatus.swift (in target 'VoicingTrainer' from project 'VoicingTrainer')
Command SwiftCompile failed with a nonzero exit code

** BUILD FAILED **


The following build commands failed:
	SwiftCompile normal arm64 /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/UI/Views/NewProgressionView.swift (in target 'VoicingTrainer' from project 'VoicingTrainer')
	SwiftCompile normal arm64 Compiling\ NewProgressionView.swift /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/UI/Views/NewProgressionView.swift (in target 'VoicingTrainer' from project 'VoicingTrainer')
	SwiftCompile normal arm64 Compiling\ SettingsView.swift,\ ChordNameParser.swift,\ SoundEffectManager.swift,\ ChordsParser.swift,\ GameConfig.swift,\ CompactGameStatus.swift /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/UI/Views/SettingsView.swift /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/Music/Analysis/ChordNameParser.swift /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/Core/Managers/SoundEffectManager.swift /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/Music/Models/ChordsParser.swift /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/Core/Configuration/GameConfig.swift /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/Game/Shared/CompactGameStatus.swift (in target 'VoicingTrainer' from project 'VoicingTrainer')
	Building project VoicingTrainer with scheme VoicingTrainer and configuration Debug
(4 failures)
