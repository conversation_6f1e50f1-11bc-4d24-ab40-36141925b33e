#!/bin/bash

echo "🧪 开始诊断和弦名称问题..."
echo "=================================="

cd /Users/<USER>/DEV/VoicingTrainer

# 1. 编译测试
echo "📦 编译测试中..."
xcodebuild -scheme VoicingTrainer -destination 'platform=macOS' build-for-testing > build_test.log 2>&1

if [ $? -ne 0 ]; then
    echo "❌ 编译失败，查看日志:"
    tail -20 build_test.log
    exit 1
fi

echo "✅ 编译成功"

# 2. 运行单元测试
echo "🧪 运行和弦相关测试..."
xcodebuild test -scheme VoicingTrainer -destination 'platform=macOS' -only-testing:VoicingTrainerTests/MIDIManagerTests > test_results.log 2>&1

echo "📊 测试结果:"
grep -E "(✅|❌|🔍)" test_results.log || echo "未找到测试输出标记"

# 3. 检查关键组件
echo ""
echo "🔍 检查关键组件状态:"
echo "ChordNameParser.swift 存在: $(ls VoicingTrainer/Music/Analysis/ChordNameParser.swift > /dev/null 2>&1 && echo "✅" || echo "❌")"
echo "MIDIManager.swift 存在: $(ls VoicingTrainer/Core/Managers/MIDIManager.swift > /dev/null 2>&1 && echo "✅" || echo "❌")"
echo "NotesView.swift 存在: $(ls VoicingTrainer/Game/Note/NotesView.swift > /dev/null 2>&1 && echo "✅" || echo "❌")"

# 4. 检查MIDIManager中的关键方法
echo ""
echo "🔍 检查MIDIManager中的updateChordInfo方法:"
grep -n -A 10 -B 2 "chordNames = names" VoicingTrainer/Core/Managers/MIDIManager.swift || echo "❌ 未找到chordNames赋值"

# 5. 检查NotesView中的使用
echo ""
echo "🔍 检查NotesView中的chordNames使用:"
grep -n -A 3 -B 3 "midiManager.chordNames" VoicingTrainer/Game/Note/NotesView.swift || echo "❌ 未找到chordNames使用"

echo ""
echo "🧪 诊断完成！"
echo "=================================="
echo "如果有问题，请检查:"
echo "1. MIDIManager.updateChordInfo() 方法是否正确调用 ChordNameParser.analyze"
echo "2. ChordNameParser.analyze() 方法是否返回正确结果"
echo "3. NotesView 是否正确绑定到 midiManager.chordNames"
echo ""
echo "💡 建议运行: 'sh run_chord_tests.sh' 来获取详细诊断信息" 