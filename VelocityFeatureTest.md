# MIDI Velocity 功能测试指南

## 功能概述

现在 NotesView 中的琴弦振动系统已经支持 MIDI velocity 参数：
- 轻按 MIDI 键盘：琴弦振动较弱
- 重按 MIDI 键盘：琴弦振动较强

## 技术实现

1. **MIDIManager** 增强：
   - 新增 `noteVelocities` 字典存储每个音符的 velocity 值
   - 新增 `getVelocity(for:)` 方法获取音符的 velocity
   - 在 noteOn/noteOff 时自动管理 velocity 数据

2. **NotesView** 更新：
   - `handleMIDINotesForStringEffect` 方法现在从 MIDIManager 获取真实 velocity
   - velocity (0-127) 映射到 intensity (1.0-10.0)
   - 支持原始琴弦效果和波形效果两种模式

3. **效果增强**：
   - **NoteStringView**: 使用 `note.intensity` 控制谐波振幅
   - **WaveStringView**: velocity 影响振幅、阻尼率、透明度和相位速度

## 测试步骤

1. **启动应用**：
   - 打开 VoicingTrainer
   - 进入 Notes 游戏模式

2. **测试原始琴弦效果**：
   - 确保琴弦效果已选中（不是波形效果）
   - 轻轻按下 MIDI 键盘按键
   - 观察琴弦振动是否较小
   - 用力按下 MIDI 键盘按键
   - 观察琴弦振动是否明显增强

3. **测试波形效果**：
   - 切换到波形效果模式
   - 重复上述测试
   - 注意波形的振幅、透明度和衰减速度变化

4. **查看调试输出**：
   - 检查控制台是否显示类似信息：
   ```
   🎸 触发琴弦效果 - 音符: 60, velocity: 45, 强度: 4.5
   🌊 触发波形效果 - 音符: 64, velocity: 100, 强度: 8.9
   ```

## 预期结果

- **轻按键** (velocity < 50): 琴弦振动微弱，intensity 约 1.0-4.9
- **中等力度** (velocity 50-100): 琴弦振动中等，intensity 约 4.9-8.0  
- **重按键** (velocity > 100): 琴弦振动强烈，intensity 约 8.0-10.0

## 故障排除

如果效果不明显：
1. 确认 MIDI 键盘已正确连接
2. 检查控制台输出中的 velocity 值
3. 尝试在不同的视觉效果模式间切换
4. 确认 `enableMIDIOutput` 设置正确

## 代码位置

- MIDIManager: `VoicingTrainer/Core/Managers/MIDIManager.swift`
- NotesView: `VoicingTrainer/Game/Note/NotesView.swift`
- StringNoteView: `VoicingTrainer/Game/Shared/StringNoteView.swift`
- WaveStringView: `VoicingTrainer/Game/Note/WaveStringView.swift` 