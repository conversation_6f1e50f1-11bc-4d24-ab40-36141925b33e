Command line invocation:
    /Applications/Xcode.app/Contents/Developer/usr/bin/xcodebuild test -scheme VoicingTrainer -destination platform=macOS "-only-testing:VoicingTrainerTests/MIDIManagerTests"

Resolve Package Graph


Resolved source packages:
  TimecodeKit: https://github.com/orchetect/TimecodeKit @ 2.3.3
  AudioKitEX: https://github.com/AudioKit/AudioKitEX @ 5.6.2
  SoundpipeAudioKit: https://github.com/AudioKit/SoundpipeAudioKit.git @ 5.7.1
  KissFFT: https://github.com/AudioKit/KissFFT @ 1.0.0
  MIDIKit: https://github.com/orchetect/MIDIKit @ 0.10.1
  AudioKit: https://github.com/AudioKit/AudioKit @ 5.6.5

ComputePackagePrebuildTargetDependencyGraph

Prepare packages

CreateBuildRequest

SendProjectDescription

CreateBuildOperation

ComputeTargetDependencyGraph
note: Building targets in dependency order
note: Target dependency graph (32 targets)
    Target 'VoicingTrainerUITests' in project 'VoicingTrainer'
        ➜ Explicit dependency on target 'VoicingTrainer' in project 'VoicingTrainer'
    Target 'VoicingTrainerTests' in project 'VoicingTrainer'
        ➜ Explicit dependency on target 'VoicingTrainer' in project 'VoicingTrainer'
    Target 'VoicingTrainer' in project 'VoicingTrainer'
        ➜ Explicit dependency on target 'MIDIKitControlSurfaces' in project 'MIDIKit'
        ➜ Explicit dependency on target 'AudioKitEX' in project 'AudioKitEX'
        ➜ Explicit dependency on target 'SoundpipeAudioKit' in project 'SoundpipeAudioKit'
        ➜ Explicit dependency on target 'MIDIKitIO' in project 'MIDIKit'
        ➜ Explicit dependency on target 'MIDIKit' in project 'MIDIKit'
        ➜ Explicit dependency on target 'MIDIKitSMF' in project 'MIDIKit'
        ➜ Explicit dependency on target 'AudioKit' in project 'AudioKit'
        ➜ Explicit dependency on target 'MIDIKitCore' in project 'MIDIKit'
    Target 'MIDIKitCore' in project 'MIDIKit'
        ➜ Explicit dependency on target 'MIDIKitCore' in project 'MIDIKit'
        ➜ Explicit dependency on target 'MIDIKitInternals' in project 'MIDIKit'
    Target 'MIDIKitSMF' in project 'MIDIKit'
        ➜ Explicit dependency on target 'MIDIKitSMF' in project 'MIDIKit'
        ➜ Explicit dependency on target 'MIDIKitInternals' in project 'MIDIKit'
        ➜ Explicit dependency on target 'MIDIKitCore' in project 'MIDIKit'
        ➜ Explicit dependency on target 'TimecodeKitCore' in project 'TimecodeKit'
    Target 'MIDIKit' in project 'MIDIKit'
        ➜ Explicit dependency on target 'MIDIKit' in project 'MIDIKit'
        ➜ Explicit dependency on target 'MIDIKitInternals' in project 'MIDIKit'
        ➜ Explicit dependency on target 'MIDIKitCore' in project 'MIDIKit'
        ➜ Explicit dependency on target 'MIDIKitIO' in project 'MIDIKit'
        ➜ Explicit dependency on target 'MIDIKitControlSurfaces' in project 'MIDIKit'
        ➜ Explicit dependency on target 'MIDIKitSMF' in project 'MIDIKit'
        ➜ Explicit dependency on target 'MIDIKitSync' in project 'MIDIKit'
        ➜ Explicit dependency on target 'MIDIKitUI' in project 'MIDIKit'
        ➜ Explicit dependency on target 'TimecodeKitCore' in project 'TimecodeKit'
    Target 'MIDIKit' in project 'MIDIKit'
        ➜ Explicit dependency on target 'MIDIKitInternals' in project 'MIDIKit'
        ➜ Explicit dependency on target 'MIDIKitCore' in project 'MIDIKit'
        ➜ Explicit dependency on target 'MIDIKitIO' in project 'MIDIKit'
        ➜ Explicit dependency on target 'MIDIKitControlSurfaces' in project 'MIDIKit'
        ➜ Explicit dependency on target 'MIDIKitSMF' in project 'MIDIKit'
        ➜ Explicit dependency on target 'MIDIKitSync' in project 'MIDIKit'
        ➜ Explicit dependency on target 'MIDIKitUI' in project 'MIDIKit'
        ➜ Explicit dependency on target 'TimecodeKitCore' in project 'TimecodeKit'
    Target 'MIDIKitUI' in project 'MIDIKit'
        ➜ Explicit dependency on target 'MIDIKitInternals' in project 'MIDIKit'
        ➜ Explicit dependency on target 'MIDIKitCore' in project 'MIDIKit'
        ➜ Explicit dependency on target 'MIDIKitIO' in project 'MIDIKit'
    Target 'MIDIKitSync' in project 'MIDIKit'
        ➜ Explicit dependency on target 'MIDIKitInternals' in project 'MIDIKit'
        ➜ Explicit dependency on target 'MIDIKitCore' in project 'MIDIKit'
        ➜ Explicit dependency on target 'MIDIKitIO' in project 'MIDIKit'
        ➜ Explicit dependency on target 'TimecodeKitCore' in project 'TimecodeKit'
    Target 'MIDIKitSMF' in project 'MIDIKit'
        ➜ Explicit dependency on target 'MIDIKitInternals' in project 'MIDIKit'
        ➜ Explicit dependency on target 'MIDIKitCore' in project 'MIDIKit'
        ➜ Explicit dependency on target 'TimecodeKitCore' in project 'TimecodeKit'
    Target 'TimecodeKitCore' in project 'TimecodeKit'
        ➜ Explicit dependency on target 'TimecodeKitCore' in project 'TimecodeKit'
    Target 'TimecodeKitCore' in project 'TimecodeKit' (no dependencies)
    Target 'MIDIKitIO' in project 'MIDIKit'
        ➜ Explicit dependency on target 'MIDIKitIO' in project 'MIDIKit'
        ➜ Explicit dependency on target 'MIDIKit_MIDIKitIO' in project 'MIDIKit'
        ➜ Explicit dependency on target 'MIDIKitInternals' in project 'MIDIKit'
        ➜ Explicit dependency on target 'MIDIKitCore' in project 'MIDIKit'
    Target 'SoundpipeAudioKit' in project 'SoundpipeAudioKit'
        ➜ Explicit dependency on target 'SoundpipeAudioKit' in project 'SoundpipeAudioKit'
        ➜ Explicit dependency on target 'Soundpipe' in project 'SoundpipeAudioKit'
        ➜ Explicit dependency on target 'CSoundpipeAudioKit' in project 'SoundpipeAudioKit'
        ➜ Explicit dependency on target 'KissFFT' in project 'KissFFT'
        ➜ Explicit dependency on target 'AudioKit' in project 'AudioKit'
        ➜ Explicit dependency on target 'AudioKitEX' in project 'AudioKitEX'
    Target 'SoundpipeAudioKit' in project 'SoundpipeAudioKit'
        ➜ Explicit dependency on target 'Soundpipe' in project 'SoundpipeAudioKit'
        ➜ Explicit dependency on target 'CSoundpipeAudioKit' in project 'SoundpipeAudioKit'
        ➜ Explicit dependency on target 'KissFFT' in project 'KissFFT'
        ➜ Explicit dependency on target 'AudioKit' in project 'AudioKit'
        ➜ Explicit dependency on target 'AudioKitEX' in project 'AudioKitEX'
    Target 'CSoundpipeAudioKit' in project 'SoundpipeAudioKit'
        ➜ Explicit dependency on target 'Soundpipe' in project 'SoundpipeAudioKit'
        ➜ Explicit dependency on target 'KissFFT' in project 'KissFFT'
        ➜ Explicit dependency on target 'AudioKit' in project 'AudioKit'
        ➜ Explicit dependency on target 'AudioKitEX' in project 'AudioKitEX'
    Target 'Soundpipe' in project 'SoundpipeAudioKit'
        ➜ Explicit dependency on target 'KissFFT' in project 'KissFFT'
    Target 'KissFFT' in project 'KissFFT'
        ➜ Explicit dependency on target 'KissFFT' in project 'KissFFT'
    Target 'KissFFT' in project 'KissFFT' (no dependencies)
    Target 'AudioKitEX' in project 'AudioKitEX'
        ➜ Explicit dependency on target 'AudioKitEX' in project 'AudioKitEX'
        ➜ Explicit dependency on target 'CAudioKitEX' in project 'AudioKitEX'
        ➜ Explicit dependency on target 'AudioKit' in project 'AudioKit'
    Target 'AudioKitEX' in project 'AudioKitEX'
        ➜ Explicit dependency on target 'CAudioKitEX' in project 'AudioKitEX'
        ➜ Explicit dependency on target 'AudioKit' in project 'AudioKit'
    Target 'AudioKit' in project 'AudioKit'
        ➜ Explicit dependency on target 'AudioKit' in project 'AudioKit'
        ➜ Explicit dependency on target 'AudioKit_AudioKit' in project 'AudioKit'
    Target 'AudioKit' in project 'AudioKit'
        ➜ Explicit dependency on target 'AudioKit_AudioKit' in project 'AudioKit'
    Target 'AudioKit_AudioKit' in project 'AudioKit' (no dependencies)
    Target 'CAudioKitEX' in project 'AudioKitEX' (no dependencies)
    Target 'MIDIKitControlSurfaces' in project 'MIDIKit'
        ➜ Explicit dependency on target 'MIDIKitControlSurfaces' in project 'MIDIKit'
        ➜ Explicit dependency on target 'MIDIKitInternals' in project 'MIDIKit'
        ➜ Explicit dependency on target 'MIDIKitCore' in project 'MIDIKit'
        ➜ Explicit dependency on target 'MIDIKitIO' in project 'MIDIKit'
    Target 'MIDIKitControlSurfaces' in project 'MIDIKit'
        ➜ Explicit dependency on target 'MIDIKitInternals' in project 'MIDIKit'
        ➜ Explicit dependency on target 'MIDIKitCore' in project 'MIDIKit'
        ➜ Explicit dependency on target 'MIDIKitIO' in project 'MIDIKit'
    Target 'MIDIKitIO' in project 'MIDIKit'
        ➜ Explicit dependency on target 'MIDIKit_MIDIKitIO' in project 'MIDIKit'
        ➜ Explicit dependency on target 'MIDIKitInternals' in project 'MIDIKit'
        ➜ Explicit dependency on target 'MIDIKitCore' in project 'MIDIKit'
    Target 'MIDIKit_MIDIKitIO' in project 'MIDIKit' (no dependencies)
    Target 'MIDIKitCore' in project 'MIDIKit'
        ➜ Explicit dependency on target 'MIDIKitInternals' in project 'MIDIKit'
    Target 'MIDIKitInternals' in project 'MIDIKit'
        ➜ Explicit dependency on target 'MIDIKit_MIDIKitInternals' in project 'MIDIKit'
    Target 'MIDIKit_MIDIKitInternals' in project 'MIDIKit' (no dependencies)

GatherProvisioningInputs

CreateBuildDescription

ExecuteExternalTool /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc --version

ExecuteExternalTool /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang -v -E -dM -arch x86_64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk -x c++ -c /dev/null

ExecuteExternalTool /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang -v -E -dM -arch x86_64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk -x c -c /dev/null

ExecuteExternalTool /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang -v -E -dM -arch x86_64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk -x objective-c++ -c /dev/null

ExecuteExternalTool /Applications/Xcode.app/Contents/Developer/usr/bin/actool --version --output-format xml1

ExecuteExternalTool /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang -v -E -dM -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk -x c -c /dev/null

ExecuteExternalTool /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang -v -E -dM -arch x86_64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk -x objective-c -c /dev/null

ExecuteExternalTool /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/ld -version_details

ReadFileContents /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/share/docc/features.json

Build description signature: 90d810601412a0a80fa00711f1f98e15
Build description path: /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/XCBuildData/90d810601412a0a80fa00711f1f98e15.xcbuilddata
ProcessInfoPlistFile /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/AudioKit_AudioKit.bundle/Contents/Info.plist /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/AudioKit.build/Debug/AudioKit_AudioKit.build/empty-AudioKit_AudioKit.plist (in target 'AudioKit_AudioKit' from project 'AudioKit')
    cd /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/SourcePackages/checkouts/AudioKit
    builtin-infoPlistUtility /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/AudioKit.build/Debug/AudioKit_AudioKit.build/empty-AudioKit_AudioKit.plist -producttype com.apple.product-type.bundle -expandbuildsettings -platform macosx -o /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/AudioKit_AudioKit.bundle/Contents/Info.plist

ClangStatCache /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang-stat-cache /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/macosx15.4-24E241-88b860576fb364319593bd8fb30666b0.sdkstatcache
    cd /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer.xcodeproj
    /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang-stat-cache /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk -o /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/macosx15.4-24E241-88b860576fb364319593bd8fb30666b0.sdkstatcache

ProcessInfoPlistFile /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/MIDIKit_MIDIKitInternals.bundle/Contents/Info.plist /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/MIDIKit.build/Debug/MIDIKit_MIDIKitInternals.build/empty-MIDIKit_MIDIKitInternals.plist (in target 'MIDIKit_MIDIKitInternals' from project 'MIDIKit')
    cd /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/SourcePackages/checkouts/MIDIKit
    builtin-infoPlistUtility /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/MIDIKit.build/Debug/MIDIKit_MIDIKitInternals.build/empty-MIDIKit_MIDIKitInternals.plist -producttype com.apple.product-type.bundle -expandbuildsettings -platform macosx -o /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/MIDIKit_MIDIKitInternals.bundle/Contents/Info.plist

ProcessInfoPlistFile /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/MIDIKit_MIDIKitIO.bundle/Contents/Info.plist /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/MIDIKit.build/Debug/MIDIKit_MIDIKitIO.build/empty-MIDIKit_MIDIKitIO.plist (in target 'MIDIKit_MIDIKitIO' from project 'MIDIKit')
    cd /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/SourcePackages/checkouts/MIDIKit
    builtin-infoPlistUtility /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/MIDIKit.build/Debug/MIDIKit_MIDIKitIO.build/empty-MIDIKit_MIDIKitIO.plist -producttype com.apple.product-type.bundle -expandbuildsettings -platform macosx -o /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/MIDIKit_MIDIKitIO.bundle/Contents/Info.plist

WriteAuxiliaryFile /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainer.build/Objects-normal/x86_64/VoicingTrainer.SwiftFileList (in target 'VoicingTrainer' from project 'VoicingTrainer')
    cd /Users/<USER>/DEV/VoicingTrainer
    write-file /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainer.build/Objects-normal/x86_64/VoicingTrainer.SwiftFileList

WriteAuxiliaryFile /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainer.build/Objects-normal/x86_64/VoicingTrainer.LinkFileList (in target 'VoicingTrainer' from project 'VoicingTrainer')
    cd /Users/<USER>/DEV/VoicingTrainer
    write-file /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainer.build/Objects-normal/x86_64/VoicingTrainer.LinkFileList

SwiftDriver VoicingTrainer normal x86_64 com.apple.xcode.tools.swift.compiler (in target 'VoicingTrainer' from project 'VoicingTrainer')
    cd /Users/<USER>/DEV/VoicingTrainer
    builtin-SwiftDriver -- /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc -module-name VoicingTrainer -Onone -enforce-exclusivity\=checked @/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainer.build/Objects-normal/x86_64/VoicingTrainer.SwiftFileList -DDEBUG -Xcc -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/GeneratedModuleMaps/CAudioKitEX.modulemap -Xcc -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/GeneratedModuleMaps/KissFFT.modulemap -Xcc -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/GeneratedModuleMaps/CSoundpipeAudioKit.modulemap -Xcc -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/GeneratedModuleMaps/Soundpipe.modulemap -enable-bare-slash-regex -enable-experimental-feature DebugDescriptionMacro -sdk /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk -target x86_64-apple-macos15.0 -g -module-cache-path /Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex -Xfrontend -serialize-debugging-options -profile-coverage-mapping -profile-generate -enable-testing -index-store-path /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Index.noindex/DataStore -swift-version 5 -I /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug -emit-localized-strings -emit-localized-strings-path /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainer.build/Objects-normal/x86_64 -c -j8 -enable-batch-mode -incremental -Xcc -ivfsstatcache -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/macosx15.4-24E241-88b860576fb364319593bd8fb30666b0.sdkstatcache -output-file-map /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainer.build/Objects-normal/x86_64/VoicingTrainer-OutputFileMap.json -use-frontend-parseable-output -save-temps -no-color-diagnostics -serialize-diagnostics -emit-dependencies -emit-module -emit-module-path /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainer.build/Objects-normal/x86_64/VoicingTrainer.swiftmodule -validate-clang-modules-once -clang-build-session-file /Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainer.build/swift-overrides.hmap -emit-const-values -Xfrontend -const-gather-protocols-file -Xfrontend /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainer.build/Objects-normal/x86_64/VoicingTrainer_const_extract_protocols.json -Xcc -iquote -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainer.build/VoicingTrainer-generated-files.hmap -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainer.build/VoicingTrainer-own-target-headers.hmap -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainer.build/VoicingTrainer-all-non-framework-target-headers.hmap -Xcc -ivfsoverlay -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainer-5f2ddef3a416ec04434590c5d76bba5c-VFS/all-product-headers.yaml -Xcc -iquote -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainer.build/VoicingTrainer-project-headers.hmap -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/SourcePackages/checkouts/AudioKitEX/Sources/CAudioKitEX/include -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/SourcePackages/checkouts/KissFFT/Sources/KissFFT/include -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/SourcePackages/checkouts/SoundpipeAudioKit/Sources/CSoundpipeAudioKit/include -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/SourcePackages/checkouts/SoundpipeAudioKit/Sources/Soundpipe/include -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/include -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainer.build/DerivedSources-normal/x86_64 -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainer.build/DerivedSources/x86_64 -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainer.build/DerivedSources -Xcc -DDEBUG\=1 -emit-objc-header -emit-objc-header-path /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainer.build/Objects-normal/x86_64/VoicingTrainer-Swift.h -working-directory /Users/<USER>/DEV/VoicingTrainer -experimental-emit-module-separately -disable-cmo

SwiftDriver\ Compilation\ Requirements VoicingTrainer normal x86_64 com.apple.xcode.tools.swift.compiler (in target 'VoicingTrainer' from project 'VoicingTrainer')
    cd /Users/<USER>/DEV/VoicingTrainer
    builtin-Swift-Compilation-Requirements -- /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc -module-name VoicingTrainer -Onone -enforce-exclusivity\=checked @/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainer.build/Objects-normal/x86_64/VoicingTrainer.SwiftFileList -DDEBUG -Xcc -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/GeneratedModuleMaps/CAudioKitEX.modulemap -Xcc -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/GeneratedModuleMaps/KissFFT.modulemap -Xcc -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/GeneratedModuleMaps/CSoundpipeAudioKit.modulemap -Xcc -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/GeneratedModuleMaps/Soundpipe.modulemap -enable-bare-slash-regex -enable-experimental-feature DebugDescriptionMacro -sdk /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk -target x86_64-apple-macos15.0 -g -module-cache-path /Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex -Xfrontend -serialize-debugging-options -profile-coverage-mapping -profile-generate -enable-testing -index-store-path /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Index.noindex/DataStore -swift-version 5 -I /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug -emit-localized-strings -emit-localized-strings-path /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainer.build/Objects-normal/x86_64 -c -j8 -enable-batch-mode -incremental -Xcc -ivfsstatcache -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/macosx15.4-24E241-88b860576fb364319593bd8fb30666b0.sdkstatcache -output-file-map /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainer.build/Objects-normal/x86_64/VoicingTrainer-OutputFileMap.json -use-frontend-parseable-output -save-temps -no-color-diagnostics -serialize-diagnostics -emit-dependencies -emit-module -emit-module-path /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainer.build/Objects-normal/x86_64/VoicingTrainer.swiftmodule -validate-clang-modules-once -clang-build-session-file /Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainer.build/swift-overrides.hmap -emit-const-values -Xfrontend -const-gather-protocols-file -Xfrontend /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainer.build/Objects-normal/x86_64/VoicingTrainer_const_extract_protocols.json -Xcc -iquote -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainer.build/VoicingTrainer-generated-files.hmap -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainer.build/VoicingTrainer-own-target-headers.hmap -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainer.build/VoicingTrainer-all-non-framework-target-headers.hmap -Xcc -ivfsoverlay -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainer-5f2ddef3a416ec04434590c5d76bba5c-VFS/all-product-headers.yaml -Xcc -iquote -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainer.build/VoicingTrainer-project-headers.hmap -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/SourcePackages/checkouts/AudioKitEX/Sources/CAudioKitEX/include -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/SourcePackages/checkouts/KissFFT/Sources/KissFFT/include -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/SourcePackages/checkouts/SoundpipeAudioKit/Sources/CSoundpipeAudioKit/include -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/SourcePackages/checkouts/SoundpipeAudioKit/Sources/Soundpipe/include -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/include -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainer.build/DerivedSources-normal/x86_64 -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainer.build/DerivedSources/x86_64 -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainer.build/DerivedSources -Xcc -DDEBUG\=1 -emit-objc-header -emit-objc-header-path /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainer.build/Objects-normal/x86_64/VoicingTrainer-Swift.h -working-directory /Users/<USER>/DEV/VoicingTrainer -experimental-emit-module-separately -disable-cmo

SwiftDriver\ Compilation VoicingTrainer normal x86_64 com.apple.xcode.tools.swift.compiler (in target 'VoicingTrainer' from project 'VoicingTrainer')
    cd /Users/<USER>/DEV/VoicingTrainer
    builtin-Swift-Compilation -- /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc -module-name VoicingTrainer -Onone -enforce-exclusivity\=checked @/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainer.build/Objects-normal/x86_64/VoicingTrainer.SwiftFileList -DDEBUG -Xcc -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/GeneratedModuleMaps/CAudioKitEX.modulemap -Xcc -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/GeneratedModuleMaps/KissFFT.modulemap -Xcc -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/GeneratedModuleMaps/CSoundpipeAudioKit.modulemap -Xcc -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/GeneratedModuleMaps/Soundpipe.modulemap -enable-bare-slash-regex -enable-experimental-feature DebugDescriptionMacro -sdk /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk -target x86_64-apple-macos15.0 -g -module-cache-path /Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex -Xfrontend -serialize-debugging-options -profile-coverage-mapping -profile-generate -enable-testing -index-store-path /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Index.noindex/DataStore -swift-version 5 -I /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug -emit-localized-strings -emit-localized-strings-path /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainer.build/Objects-normal/x86_64 -c -j8 -enable-batch-mode -incremental -Xcc -ivfsstatcache -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/macosx15.4-24E241-88b860576fb364319593bd8fb30666b0.sdkstatcache -output-file-map /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainer.build/Objects-normal/x86_64/VoicingTrainer-OutputFileMap.json -use-frontend-parseable-output -save-temps -no-color-diagnostics -serialize-diagnostics -emit-dependencies -emit-module -emit-module-path /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainer.build/Objects-normal/x86_64/VoicingTrainer.swiftmodule -validate-clang-modules-once -clang-build-session-file /Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainer.build/swift-overrides.hmap -emit-const-values -Xfrontend -const-gather-protocols-file -Xfrontend /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainer.build/Objects-normal/x86_64/VoicingTrainer_const_extract_protocols.json -Xcc -iquote -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainer.build/VoicingTrainer-generated-files.hmap -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainer.build/VoicingTrainer-own-target-headers.hmap -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainer.build/VoicingTrainer-all-non-framework-target-headers.hmap -Xcc -ivfsoverlay -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainer-5f2ddef3a416ec04434590c5d76bba5c-VFS/all-product-headers.yaml -Xcc -iquote -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainer.build/VoicingTrainer-project-headers.hmap -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/SourcePackages/checkouts/AudioKitEX/Sources/CAudioKitEX/include -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/SourcePackages/checkouts/KissFFT/Sources/KissFFT/include -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/SourcePackages/checkouts/SoundpipeAudioKit/Sources/CSoundpipeAudioKit/include -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/SourcePackages/checkouts/SoundpipeAudioKit/Sources/Soundpipe/include -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/include -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainer.build/DerivedSources-normal/x86_64 -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainer.build/DerivedSources/x86_64 -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainer.build/DerivedSources -Xcc -DDEBUG\=1 -emit-objc-header -emit-objc-header-path /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainer.build/Objects-normal/x86_64/VoicingTrainer-Swift.h -working-directory /Users/<USER>/DEV/VoicingTrainer -experimental-emit-module-separately -disable-cmo

Ld /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/VoicingTrainer.app/Contents/MacOS/VoicingTrainer.debug.dylib normal (in target 'VoicingTrainer' from project 'VoicingTrainer')
    cd /Users/<USER>/DEV/VoicingTrainer
    /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang -Xlinker -reproducible -target x86_64-apple-macos15.0 -dynamiclib -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk -O0 -L/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/EagerLinkingTBDs/Debug -L/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug -F/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/EagerLinkingTBDs/Debug -F/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -F/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -F/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -F/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -F/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -F/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -F/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -F/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -F/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -F/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -F/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -F/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -F/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -F/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -F/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -F/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -F/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug -filelist /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainer.build/Objects-normal/x86_64/VoicingTrainer.LinkFileList -install_name @rpath/VoicingTrainer.debug.dylib -Xlinker -rpath -Xlinker /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -Xlinker -rpath -Xlinker @executable_path/Frameworks -Xlinker -rpath -Xlinker @executable_path/../Frameworks -Xlinker -object_path_lto -Xlinker /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainer.build/Objects-normal/x86_64/VoicingTrainer_lto.o -rdynamic -Xlinker -no_deduplicate -Xlinker -debug_variant -Xlinker -dependency_info -Xlinker /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainer.build/Objects-normal/x86_64/VoicingTrainer_dependency_info.dat -fobjc-link-runtime -fprofile-instr-generate -L/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx -L/usr/lib/swift -Xlinker -add_ast_path -Xlinker /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainer.build/Objects-normal/x86_64/VoicingTrainer.swiftmodule -lc++ -Wl,-no_warn_duplicate_libraries -Wl,-no_warn_duplicate_libraries -Wl,-no_warn_duplicate_libraries -lc++ -Wl,-no_warn_duplicate_libraries -Wl,-no_warn_duplicate_libraries -Wl,-no_warn_duplicate_libraries -Wl,-no_warn_duplicate_libraries -Wl,-no_warn_duplicate_libraries -Wl,-no_warn_duplicate_libraries -Wl,-no_warn_duplicate_libraries -Wl,-no_warn_duplicate_libraries -Wl,-no_warn_duplicate_libraries -Wl,-no_warn_duplicate_libraries -Wl,-no_warn_duplicate_libraries -Wl,-no_warn_duplicate_libraries -Wl,-no_warn_duplicate_libraries -Xlinker -alias -Xlinker _main -Xlinker ___debug_main_executable_dylib_entry_point -Xlinker -no_adhoc_codesign -o /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/VoicingTrainer.app/Contents/MacOS/VoicingTrainer.debug.dylib -Xlinker -add_ast_path -Xlinker /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/MIDIKit.build/Debug/MIDIKitControlSurfaces.build/Objects-normal/x86_64/MIDIKitControlSurfaces.swiftmodule -Xlinker -add_ast_path -Xlinker /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/MIDIKit.build/Debug/MIDIKitInternals.build/Objects-normal/x86_64/MIDIKitInternals.swiftmodule -Xlinker -add_ast_path -Xlinker /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/MIDIKit.build/Debug/MIDIKitCore.build/Objects-normal/x86_64/MIDIKitCore.swiftmodule -Xlinker -add_ast_path -Xlinker /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/MIDIKit.build/Debug/MIDIKitIO.build/Objects-normal/x86_64/MIDIKitIO.swiftmodule -Xlinker -add_ast_path -Xlinker /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/AudioKitEX.build/Debug/AudioKitEX.build/Objects-normal/x86_64/AudioKitEX.swiftmodule -Xlinker -add_ast_path -Xlinker /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/AudioKit.build/Debug/AudioKit.build/Objects-normal/x86_64/AudioKit.swiftmodule -Xlinker -add_ast_path -Xlinker /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/SoundpipeAudioKit.build/Debug/SoundpipeAudioKit.build/Objects-normal/x86_64/SoundpipeAudioKit.swiftmodule -Xlinker -add_ast_path -Xlinker /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/MIDIKit.build/Debug/MIDIKit.build/Objects-normal/x86_64/MIDIKit.swiftmodule -Xlinker -add_ast_path -Xlinker /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/TimecodeKit.build/Debug/TimecodeKitCore.build/Objects-normal/x86_64/TimecodeKitCore.swiftmodule -Xlinker -add_ast_path -Xlinker /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/MIDIKit.build/Debug/MIDIKitSMF.build/Objects-normal/x86_64/MIDIKitSMF.swiftmodule -Xlinker -add_ast_path -Xlinker /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/MIDIKit.build/Debug/MIDIKitSync.build/Objects-normal/x86_64/MIDIKitSync.swiftmodule -Xlinker -add_ast_path -Xlinker /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/MIDIKit.build/Debug/MIDIKitUI.build/Objects-normal/x86_64/MIDIKitUI.swiftmodule

ConstructStubExecutorLinkFileList /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainer.build/VoicingTrainer-ExecutorLinkFileList-normal-x86_64.txt (in target 'VoicingTrainer' from project 'VoicingTrainer')
    cd /Users/<USER>/DEV/VoicingTrainer
    construct-stub-executor-link-file-list /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/VoicingTrainer.app/Contents/MacOS/VoicingTrainer.debug.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib/libPreviewsJITStubExecutor_no_swift_entry_point.a /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib/libPreviewsJITStubExecutor.a --output /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainer.build/VoicingTrainer-ExecutorLinkFileList-normal-x86_64.txt
note: Using stub executor library with Swift entry point. (in target 'VoicingTrainer' from project 'VoicingTrainer')

Ld /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/VoicingTrainer.app/Contents/MacOS/VoicingTrainer normal (in target 'VoicingTrainer' from project 'VoicingTrainer')
    cd /Users/<USER>/DEV/VoicingTrainer
    /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang -Xlinker -reproducible -target x86_64-apple-macos15.0 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk -O0 -L/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug -F/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -F/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -F/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -F/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -F/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -F/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -F/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -F/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -F/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -F/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -F/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -F/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -F/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -F/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -F/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -F/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -F/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug -Xlinker -rpath -Xlinker @executable_path -Xlinker -rpath -Xlinker /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -Xlinker -rpath -Xlinker @executable_path/Frameworks -Xlinker -rpath -Xlinker @executable_path/../Frameworks -rdynamic -Xlinker -no_deduplicate -Xlinker -debug_variant -e ___debug_blank_executor_main -Xlinker -sectcreate -Xlinker __TEXT -Xlinker __debug_dylib -Xlinker /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainer.build/VoicingTrainer-DebugDylibPath-normal-x86_64.txt -Xlinker -sectcreate -Xlinker __TEXT -Xlinker __debug_instlnm -Xlinker /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainer.build/VoicingTrainer-DebugDylibInstallName-normal-x86_64.txt -Xlinker -filelist -Xlinker /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainer.build/VoicingTrainer-ExecutorLinkFileList-normal-x86_64.txt /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/VoicingTrainer.app/Contents/MacOS/VoicingTrainer.debug.dylib -Xlinker -no_adhoc_codesign -o /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/VoicingTrainer.app/Contents/MacOS/VoicingTrainer

WriteAuxiliaryFile /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainerUITests.build/Objects-normal/x86_64/VoicingTrainerUITests.LinkFileList (in target 'VoicingTrainerUITests' from project 'VoicingTrainer')
    cd /Users/<USER>/DEV/VoicingTrainer
    write-file /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainerUITests.build/Objects-normal/x86_64/VoicingTrainerUITests.LinkFileList

WriteAuxiliaryFile /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainerUITests.build/Objects-normal/x86_64/VoicingTrainerUITests.SwiftConstValuesFileList (in target 'VoicingTrainerUITests' from project 'VoicingTrainer')
    cd /Users/<USER>/DEV/VoicingTrainer
    write-file /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainerUITests.build/Objects-normal/x86_64/VoicingTrainerUITests.SwiftConstValuesFileList

WriteAuxiliaryFile /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainerUITests.build/Objects-normal/x86_64/VoicingTrainerUITests.SwiftFileList (in target 'VoicingTrainerUITests' from project 'VoicingTrainer')
    cd /Users/<USER>/DEV/VoicingTrainer
    write-file /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainerUITests.build/Objects-normal/x86_64/VoicingTrainerUITests.SwiftFileList

WriteAuxiliaryFile /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainerTests.build/Objects-normal/x86_64/VoicingTrainerTests.SwiftFileList (in target 'VoicingTrainerTests' from project 'VoicingTrainer')
    cd /Users/<USER>/DEV/VoicingTrainer
    write-file /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainerTests.build/Objects-normal/x86_64/VoicingTrainerTests.SwiftFileList

WriteAuxiliaryFile /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainer.build/Objects-normal/x86_64/VoicingTrainer.SwiftConstValuesFileList (in target 'VoicingTrainer' from project 'VoicingTrainer')
    cd /Users/<USER>/DEV/VoicingTrainer
    write-file /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainer.build/Objects-normal/x86_64/VoicingTrainer.SwiftConstValuesFileList

WriteAuxiliaryFile /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainerTests.build/Objects-normal/x86_64/VoicingTrainerTests.SwiftConstValuesFileList (in target 'VoicingTrainerTests' from project 'VoicingTrainer')
    cd /Users/<USER>/DEV/VoicingTrainer
    write-file /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainerTests.build/Objects-normal/x86_64/VoicingTrainerTests.SwiftConstValuesFileList

WriteAuxiliaryFile /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainerTests.build/Objects-normal/x86_64/VoicingTrainerTests.LinkFileList (in target 'VoicingTrainerTests' from project 'VoicingTrainer')
    cd /Users/<USER>/DEV/VoicingTrainer
    write-file /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainerTests.build/Objects-normal/x86_64/VoicingTrainerTests.LinkFileList

ProcessInfoPlistFile /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/VoicingTrainer.app/Contents/Info.plist /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/Info.plist (in target 'VoicingTrainer' from project 'VoicingTrainer')
    cd /Users/<USER>/DEV/VoicingTrainer
    builtin-infoPlistUtility /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/Info.plist -producttype com.apple.product-type.application -genpkginfo /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/VoicingTrainer.app/Contents/PkgInfo -expandbuildsettings -platform macosx -additionalcontentfile /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainer.build/assetcatalog_generated_info.plist -scanforprivacyfile /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/VoicingTrainer.app/Contents/Resources/AudioKit_AudioKit.bundle -scanforprivacyfile /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/VoicingTrainer.app/Contents/Resources/MIDIKit_MIDIKitIO.bundle -scanforprivacyfile /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/VoicingTrainer.app/Contents/Resources/MIDIKit_MIDIKitInternals.bundle -o /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/VoicingTrainer.app/Contents/Info.plist
removing value "audio" for "UIBackgroundModes" - not supported on macOS
removing value "bluetooth-central" for "UIBackgroundModes" - not supported on macOS

CopySwiftLibs /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/VoicingTrainer.app (in target 'VoicingTrainer' from project 'VoicingTrainer')
    cd /Users/<USER>/DEV/VoicingTrainer
    builtin-swiftStdLibTool --copy --verbose --sign 6BA82849F60FEDF5AE63A9E9D429C6BE479E1636 --scan-executable /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/VoicingTrainer.app/Contents/MacOS/VoicingTrainer.debug.dylib --scan-folder /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/VoicingTrainer.app/Contents/Frameworks --scan-folder /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/VoicingTrainer.app/Contents/PlugIns --scan-folder /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/VoicingTrainer.app/Contents/Library/SystemExtensions --scan-folder /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/VoicingTrainer.app/Contents/Extensions --platform macosx --toolchain /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain --destination /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/VoicingTrainer.app/Contents/Frameworks --strip-bitcode --strip-bitcode-tool /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/bitcode_strip --emit-dependency-info /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainer.build/SwiftStdLibToolInputDependencies.dep --filter-for-swift-os

ExtractAppIntentsMetadata (in target 'VoicingTrainer' from project 'VoicingTrainer')
    cd /Users/<USER>/DEV/VoicingTrainer
    /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/appintentsmetadataprocessor --toolchain-dir /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain --module-name VoicingTrainer --sdk-root /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk --xcode-version 16E140 --platform-family macOS --deployment-target 15.0 --bundle-identifier com.SHZX.VoicingTutor --output /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/VoicingTrainer.app/Contents/Resources --target-triple x86_64-apple-macos15.0 --binary-file /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/VoicingTrainer.app/Contents/MacOS/VoicingTrainer --dependency-file /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainer.build/Objects-normal/x86_64/VoicingTrainer_dependency_info.dat --stringsdata-file /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainer.build/Objects-normal/x86_64/ExtractedAppShortcutsMetadata.stringsdata --source-file-list /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainer.build/Objects-normal/x86_64/VoicingTrainer.SwiftFileList --metadata-file-list /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainer.build/VoicingTrainer.DependencyMetadataFileList --static-metadata-file-list /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainer.build/VoicingTrainer.DependencyStaticMetadataFileList --swift-const-vals-list /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainer.build/Objects-normal/x86_64/VoicingTrainer.SwiftConstValuesFileList --compile-time-extraction --deployment-aware-processing --validate-assistant-intents --no-app-shortcuts-localization
2025-06-15 22:47:04.521 appintentsmetadataprocessor[17114:902742] Starting appintentsmetadataprocessor export
2025-06-15 22:47:04.525 appintentsmetadataprocessor[17114:902742] warning: Metadata extraction skipped. No AppIntents.framework dependency found.

SwiftDriver VoicingTrainerTests normal x86_64 com.apple.xcode.tools.swift.compiler (in target 'VoicingTrainerTests' from project 'VoicingTrainer')
    cd /Users/<USER>/DEV/VoicingTrainer
    builtin-SwiftDriver -- /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc -module-name VoicingTrainerTests -Onone -enforce-exclusivity\=checked @/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainerTests.build/Objects-normal/x86_64/VoicingTrainerTests.SwiftFileList -DDEBUG -Xcc -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/GeneratedModuleMaps/CAudioKitEX.modulemap -Xcc -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/GeneratedModuleMaps/KissFFT.modulemap -Xcc -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/GeneratedModuleMaps/CSoundpipeAudioKit.modulemap -Xcc -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/GeneratedModuleMaps/Soundpipe.modulemap -plugin-path /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/host/plugins/testing -enable-bare-slash-regex -enable-experimental-feature DebugDescriptionMacro -sdk /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk -target x86_64-apple-macos15.0 -g -module-cache-path /Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex -Xfrontend -serialize-debugging-options -profile-coverage-mapping -profile-generate -enable-testing -index-store-path /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Index.noindex/DataStore -swift-version 5 -I /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug -I /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug -F /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks -c -j8 -enable-batch-mode -incremental -Xcc -ivfsstatcache -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/macosx15.4-24E241-88b860576fb364319593bd8fb30666b0.sdkstatcache -output-file-map /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainerTests.build/Objects-normal/x86_64/VoicingTrainerTests-OutputFileMap.json -use-frontend-parseable-output -save-temps -no-color-diagnostics -serialize-diagnostics -emit-dependencies -emit-module -emit-module-path /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainerTests.build/Objects-normal/x86_64/VoicingTrainerTests.swiftmodule -validate-clang-modules-once -clang-build-session-file /Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainerTests.build/swift-overrides.hmap -emit-const-values -Xfrontend -const-gather-protocols-file -Xfrontend /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainerTests.build/Objects-normal/x86_64/VoicingTrainerTests_const_extract_protocols.json -Xcc -iquote -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainerTests.build/VoicingTrainerTests-generated-files.hmap -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainerTests.build/VoicingTrainerTests-own-target-headers.hmap -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainerTests.build/VoicingTrainerTests-all-non-framework-target-headers.hmap -Xcc -ivfsoverlay -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainer-5f2ddef3a416ec04434590c5d76bba5c-VFS/all-product-headers.yaml -Xcc -iquote -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainerTests.build/VoicingTrainerTests-project-headers.hmap -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/SourcePackages/checkouts/AudioKitEX/Sources/CAudioKitEX/include -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/SourcePackages/checkouts/KissFFT/Sources/KissFFT/include -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/SourcePackages/checkouts/SoundpipeAudioKit/Sources/CSoundpipeAudioKit/include -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/SourcePackages/checkouts/SoundpipeAudioKit/Sources/Soundpipe/include -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/include -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainerTests.build/DerivedSources-normal/x86_64 -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainerTests.build/DerivedSources/x86_64 -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainerTests.build/DerivedSources -Xcc -DDEBUG\=1 -emit-objc-header -emit-objc-header-path /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainerTests.build/Objects-normal/x86_64/VoicingTrainerTests-Swift.h -working-directory /Users/<USER>/DEV/VoicingTrainer -experimental-emit-module-separately -disable-cmo

SwiftDriver VoicingTrainerUITests normal x86_64 com.apple.xcode.tools.swift.compiler (in target 'VoicingTrainerUITests' from project 'VoicingTrainer')
    cd /Users/<USER>/DEV/VoicingTrainer
    builtin-SwiftDriver -- /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc -module-name VoicingTrainerUITests -Onone -enforce-exclusivity\=checked @/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainerUITests.build/Objects-normal/x86_64/VoicingTrainerUITests.SwiftFileList -DDEBUG -module-alias Testing\=_Testing_Unavailable -plugin-path /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/host/plugins/testing -enable-bare-slash-regex -enable-experimental-feature DebugDescriptionMacro -sdk /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk -target x86_64-apple-macos15.0 -g -module-cache-path /Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex -Xfrontend -serialize-debugging-options -profile-coverage-mapping -profile-generate -enable-testing -index-store-path /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Index.noindex/DataStore -swift-version 5 -I /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug -I /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug -F /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks -c -j8 -enable-batch-mode -incremental -Xcc -ivfsstatcache -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/macosx15.4-24E241-88b860576fb364319593bd8fb30666b0.sdkstatcache -output-file-map /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainerUITests.build/Objects-normal/x86_64/VoicingTrainerUITests-OutputFileMap.json -use-frontend-parseable-output -save-temps -no-color-diagnostics -serialize-diagnostics -emit-dependencies -emit-module -emit-module-path /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainerUITests.build/Objects-normal/x86_64/VoicingTrainerUITests.swiftmodule -validate-clang-modules-once -clang-build-session-file /Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainerUITests.build/swift-overrides.hmap -emit-const-values -Xfrontend -const-gather-protocols-file -Xfrontend /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainerUITests.build/Objects-normal/x86_64/VoicingTrainerUITests_const_extract_protocols.json -Xcc -iquote -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainerUITests.build/VoicingTrainerUITests-generated-files.hmap -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainerUITests.build/VoicingTrainerUITests-own-target-headers.hmap -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainerUITests.build/VoicingTrainerUITests-all-non-framework-target-headers.hmap -Xcc -ivfsoverlay -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainer-5f2ddef3a416ec04434590c5d76bba5c-VFS/all-product-headers.yaml -Xcc -iquote -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainerUITests.build/VoicingTrainerUITests-project-headers.hmap -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/include -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainerUITests.build/DerivedSources-normal/x86_64 -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainerUITests.build/DerivedSources/x86_64 -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainerUITests.build/DerivedSources -Xcc -DDEBUG\=1 -emit-objc-header -emit-objc-header-path /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainerUITests.build/Objects-normal/x86_64/VoicingTrainerUITests-Swift.h -working-directory /Users/<USER>/DEV/VoicingTrainer -experimental-emit-module-separately -disable-cmo

SwiftDriver\ Compilation\ Requirements VoicingTrainerUITests normal x86_64 com.apple.xcode.tools.swift.compiler (in target 'VoicingTrainerUITests' from project 'VoicingTrainer')
    cd /Users/<USER>/DEV/VoicingTrainer
    builtin-Swift-Compilation-Requirements -- /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc -module-name VoicingTrainerUITests -Onone -enforce-exclusivity\=checked @/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainerUITests.build/Objects-normal/x86_64/VoicingTrainerUITests.SwiftFileList -DDEBUG -module-alias Testing\=_Testing_Unavailable -plugin-path /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/host/plugins/testing -enable-bare-slash-regex -enable-experimental-feature DebugDescriptionMacro -sdk /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk -target x86_64-apple-macos15.0 -g -module-cache-path /Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex -Xfrontend -serialize-debugging-options -profile-coverage-mapping -profile-generate -enable-testing -index-store-path /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Index.noindex/DataStore -swift-version 5 -I /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug -I /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug -F /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks -c -j8 -enable-batch-mode -incremental -Xcc -ivfsstatcache -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/macosx15.4-24E241-88b860576fb364319593bd8fb30666b0.sdkstatcache -output-file-map /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainerUITests.build/Objects-normal/x86_64/VoicingTrainerUITests-OutputFileMap.json -use-frontend-parseable-output -save-temps -no-color-diagnostics -serialize-diagnostics -emit-dependencies -emit-module -emit-module-path /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainerUITests.build/Objects-normal/x86_64/VoicingTrainerUITests.swiftmodule -validate-clang-modules-once -clang-build-session-file /Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainerUITests.build/swift-overrides.hmap -emit-const-values -Xfrontend -const-gather-protocols-file -Xfrontend /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainerUITests.build/Objects-normal/x86_64/VoicingTrainerUITests_const_extract_protocols.json -Xcc -iquote -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainerUITests.build/VoicingTrainerUITests-generated-files.hmap -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainerUITests.build/VoicingTrainerUITests-own-target-headers.hmap -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainerUITests.build/VoicingTrainerUITests-all-non-framework-target-headers.hmap -Xcc -ivfsoverlay -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainer-5f2ddef3a416ec04434590c5d76bba5c-VFS/all-product-headers.yaml -Xcc -iquote -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainerUITests.build/VoicingTrainerUITests-project-headers.hmap -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/include -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainerUITests.build/DerivedSources-normal/x86_64 -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainerUITests.build/DerivedSources/x86_64 -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainerUITests.build/DerivedSources -Xcc -DDEBUG\=1 -emit-objc-header -emit-objc-header-path /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainerUITests.build/Objects-normal/x86_64/VoicingTrainerUITests-Swift.h -working-directory /Users/<USER>/DEV/VoicingTrainer -experimental-emit-module-separately -disable-cmo

SwiftDriver\ Compilation VoicingTrainerUITests normal x86_64 com.apple.xcode.tools.swift.compiler (in target 'VoicingTrainerUITests' from project 'VoicingTrainer')
    cd /Users/<USER>/DEV/VoicingTrainer
    builtin-Swift-Compilation -- /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc -module-name VoicingTrainerUITests -Onone -enforce-exclusivity\=checked @/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainerUITests.build/Objects-normal/x86_64/VoicingTrainerUITests.SwiftFileList -DDEBUG -module-alias Testing\=_Testing_Unavailable -plugin-path /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/host/plugins/testing -enable-bare-slash-regex -enable-experimental-feature DebugDescriptionMacro -sdk /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk -target x86_64-apple-macos15.0 -g -module-cache-path /Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex -Xfrontend -serialize-debugging-options -profile-coverage-mapping -profile-generate -enable-testing -index-store-path /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Index.noindex/DataStore -swift-version 5 -I /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug -I /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug -F /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks -c -j8 -enable-batch-mode -incremental -Xcc -ivfsstatcache -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/macosx15.4-24E241-88b860576fb364319593bd8fb30666b0.sdkstatcache -output-file-map /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainerUITests.build/Objects-normal/x86_64/VoicingTrainerUITests-OutputFileMap.json -use-frontend-parseable-output -save-temps -no-color-diagnostics -serialize-diagnostics -emit-dependencies -emit-module -emit-module-path /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainerUITests.build/Objects-normal/x86_64/VoicingTrainerUITests.swiftmodule -validate-clang-modules-once -clang-build-session-file /Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainerUITests.build/swift-overrides.hmap -emit-const-values -Xfrontend -const-gather-protocols-file -Xfrontend /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainerUITests.build/Objects-normal/x86_64/VoicingTrainerUITests_const_extract_protocols.json -Xcc -iquote -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainerUITests.build/VoicingTrainerUITests-generated-files.hmap -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainerUITests.build/VoicingTrainerUITests-own-target-headers.hmap -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainerUITests.build/VoicingTrainerUITests-all-non-framework-target-headers.hmap -Xcc -ivfsoverlay -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainer-5f2ddef3a416ec04434590c5d76bba5c-VFS/all-product-headers.yaml -Xcc -iquote -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainerUITests.build/VoicingTrainerUITests-project-headers.hmap -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/include -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainerUITests.build/DerivedSources-normal/x86_64 -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainerUITests.build/DerivedSources/x86_64 -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainerUITests.build/DerivedSources -Xcc -DDEBUG\=1 -emit-objc-header -emit-objc-header-path /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainerUITests.build/Objects-normal/x86_64/VoicingTrainerUITests-Swift.h -working-directory /Users/<USER>/DEV/VoicingTrainer -experimental-emit-module-separately -disable-cmo

SwiftDriver\ Compilation\ Requirements VoicingTrainerTests normal x86_64 com.apple.xcode.tools.swift.compiler (in target 'VoicingTrainerTests' from project 'VoicingTrainer')
    cd /Users/<USER>/DEV/VoicingTrainer
    builtin-Swift-Compilation-Requirements -- /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc -module-name VoicingTrainerTests -Onone -enforce-exclusivity\=checked @/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainerTests.build/Objects-normal/x86_64/VoicingTrainerTests.SwiftFileList -DDEBUG -Xcc -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/GeneratedModuleMaps/CAudioKitEX.modulemap -Xcc -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/GeneratedModuleMaps/KissFFT.modulemap -Xcc -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/GeneratedModuleMaps/CSoundpipeAudioKit.modulemap -Xcc -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/GeneratedModuleMaps/Soundpipe.modulemap -plugin-path /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/host/plugins/testing -enable-bare-slash-regex -enable-experimental-feature DebugDescriptionMacro -sdk /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk -target x86_64-apple-macos15.0 -g -module-cache-path /Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex -Xfrontend -serialize-debugging-options -profile-coverage-mapping -profile-generate -enable-testing -index-store-path /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Index.noindex/DataStore -swift-version 5 -I /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug -I /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug -F /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks -c -j8 -enable-batch-mode -incremental -Xcc -ivfsstatcache -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/macosx15.4-24E241-88b860576fb364319593bd8fb30666b0.sdkstatcache -output-file-map /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainerTests.build/Objects-normal/x86_64/VoicingTrainerTests-OutputFileMap.json -use-frontend-parseable-output -save-temps -no-color-diagnostics -serialize-diagnostics -emit-dependencies -emit-module -emit-module-path /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainerTests.build/Objects-normal/x86_64/VoicingTrainerTests.swiftmodule -validate-clang-modules-once -clang-build-session-file /Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainerTests.build/swift-overrides.hmap -emit-const-values -Xfrontend -const-gather-protocols-file -Xfrontend /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainerTests.build/Objects-normal/x86_64/VoicingTrainerTests_const_extract_protocols.json -Xcc -iquote -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainerTests.build/VoicingTrainerTests-generated-files.hmap -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainerTests.build/VoicingTrainerTests-own-target-headers.hmap -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainerTests.build/VoicingTrainerTests-all-non-framework-target-headers.hmap -Xcc -ivfsoverlay -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainer-5f2ddef3a416ec04434590c5d76bba5c-VFS/all-product-headers.yaml -Xcc -iquote -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainerTests.build/VoicingTrainerTests-project-headers.hmap -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/SourcePackages/checkouts/AudioKitEX/Sources/CAudioKitEX/include -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/SourcePackages/checkouts/KissFFT/Sources/KissFFT/include -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/SourcePackages/checkouts/SoundpipeAudioKit/Sources/CSoundpipeAudioKit/include -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/SourcePackages/checkouts/SoundpipeAudioKit/Sources/Soundpipe/include -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/include -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainerTests.build/DerivedSources-normal/x86_64 -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainerTests.build/DerivedSources/x86_64 -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainerTests.build/DerivedSources -Xcc -DDEBUG\=1 -emit-objc-header -emit-objc-header-path /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainerTests.build/Objects-normal/x86_64/VoicingTrainerTests-Swift.h -working-directory /Users/<USER>/DEV/VoicingTrainer -experimental-emit-module-separately -disable-cmo

SwiftDriver\ Compilation VoicingTrainerTests normal x86_64 com.apple.xcode.tools.swift.compiler (in target 'VoicingTrainerTests' from project 'VoicingTrainer')
    cd /Users/<USER>/DEV/VoicingTrainer
    builtin-Swift-Compilation -- /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc -module-name VoicingTrainerTests -Onone -enforce-exclusivity\=checked @/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainerTests.build/Objects-normal/x86_64/VoicingTrainerTests.SwiftFileList -DDEBUG -Xcc -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/GeneratedModuleMaps/CAudioKitEX.modulemap -Xcc -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/GeneratedModuleMaps/KissFFT.modulemap -Xcc -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/GeneratedModuleMaps/CSoundpipeAudioKit.modulemap -Xcc -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/GeneratedModuleMaps/Soundpipe.modulemap -plugin-path /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/host/plugins/testing -enable-bare-slash-regex -enable-experimental-feature DebugDescriptionMacro -sdk /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk -target x86_64-apple-macos15.0 -g -module-cache-path /Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex -Xfrontend -serialize-debugging-options -profile-coverage-mapping -profile-generate -enable-testing -index-store-path /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Index.noindex/DataStore -swift-version 5 -I /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug -I /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug -F /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks -c -j8 -enable-batch-mode -incremental -Xcc -ivfsstatcache -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/macosx15.4-24E241-88b860576fb364319593bd8fb30666b0.sdkstatcache -output-file-map /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainerTests.build/Objects-normal/x86_64/VoicingTrainerTests-OutputFileMap.json -use-frontend-parseable-output -save-temps -no-color-diagnostics -serialize-diagnostics -emit-dependencies -emit-module -emit-module-path /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainerTests.build/Objects-normal/x86_64/VoicingTrainerTests.swiftmodule -validate-clang-modules-once -clang-build-session-file /Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainerTests.build/swift-overrides.hmap -emit-const-values -Xfrontend -const-gather-protocols-file -Xfrontend /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainerTests.build/Objects-normal/x86_64/VoicingTrainerTests_const_extract_protocols.json -Xcc -iquote -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainerTests.build/VoicingTrainerTests-generated-files.hmap -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainerTests.build/VoicingTrainerTests-own-target-headers.hmap -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainerTests.build/VoicingTrainerTests-all-non-framework-target-headers.hmap -Xcc -ivfsoverlay -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainer-5f2ddef3a416ec04434590c5d76bba5c-VFS/all-product-headers.yaml -Xcc -iquote -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainerTests.build/VoicingTrainerTests-project-headers.hmap -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/SourcePackages/checkouts/AudioKitEX/Sources/CAudioKitEX/include -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/SourcePackages/checkouts/KissFFT/Sources/KissFFT/include -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/SourcePackages/checkouts/SoundpipeAudioKit/Sources/CSoundpipeAudioKit/include -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/SourcePackages/checkouts/SoundpipeAudioKit/Sources/Soundpipe/include -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/include -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainerTests.build/DerivedSources-normal/x86_64 -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainerTests.build/DerivedSources/x86_64 -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainerTests.build/DerivedSources -Xcc -DDEBUG\=1 -emit-objc-header -emit-objc-header-path /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainerTests.build/Objects-normal/x86_64/VoicingTrainerTests-Swift.h -working-directory /Users/<USER>/DEV/VoicingTrainer -experimental-emit-module-separately -disable-cmo

ProcessInfoPlistFile /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/VoicingTrainer.app/Contents/PlugIns/VoicingTrainerTests.xctest/Contents/Info.plist /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainerTests.build/empty-VoicingTrainerTests.plist (in target 'VoicingTrainerTests' from project 'VoicingTrainer')
    cd /Users/<USER>/DEV/VoicingTrainer
    builtin-infoPlistUtility /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainerTests.build/empty-VoicingTrainerTests.plist -producttype com.apple.product-type.bundle.unit-test -expandbuildsettings -platform macosx -scanforprivacyfile /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/VoicingTrainer.app/Contents/PlugIns/VoicingTrainerTests.xctest/Contents/Resources/AudioKit_AudioKit.bundle -scanforprivacyfile /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/VoicingTrainer.app/Contents/PlugIns/VoicingTrainerTests.xctest/Contents/Resources/MIDIKit_MIDIKitIO.bundle -scanforprivacyfile /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/VoicingTrainer.app/Contents/PlugIns/VoicingTrainerTests.xctest/Contents/Resources/MIDIKit_MIDIKitInternals.bundle -o /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/VoicingTrainer.app/Contents/PlugIns/VoicingTrainerTests.xctest/Contents/Info.plist

Ld /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/VoicingTrainer.app/Contents/PlugIns/VoicingTrainerTests.xctest/Contents/MacOS/VoicingTrainerTests normal (in target 'VoicingTrainerTests' from project 'VoicingTrainer')
    cd /Users/<USER>/DEV/VoicingTrainer
    /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang -Xlinker -reproducible -target x86_64-apple-macos15.0 -bundle -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk -O0 -L/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/EagerLinkingTBDs/Debug -L/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug -L/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib -F/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/EagerLinkingTBDs/Debug -F/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -F/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -F/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -F/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -F/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -F/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -F/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -F/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -F/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -F/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -F/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -F/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -F/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -F/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -F/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -F/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -F/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug -iframework /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks -filelist /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainerTests.build/Objects-normal/x86_64/VoicingTrainerTests.LinkFileList -Xlinker -rpath -Xlinker /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/PackageFrameworks -Xlinker -rpath -Xlinker @loader_path/../Frameworks -Xlinker -rpath -Xlinker @executable_path/../Frameworks -bundle_loader /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/VoicingTrainer.app/Contents/MacOS/VoicingTrainer -Xlinker -object_path_lto -Xlinker /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainerTests.build/Objects-normal/x86_64/VoicingTrainerTests_lto.o -rdynamic -Xlinker -no_deduplicate -Xlinker -debug_variant -Xlinker -dependency_info -Xlinker /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainerTests.build/Objects-normal/x86_64/VoicingTrainerTests_dependency_info.dat -fobjc-link-runtime -fprofile-instr-generate -L/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx -L/usr/lib/swift -Xlinker -add_ast_path -Xlinker /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainerTests.build/Objects-normal/x86_64/VoicingTrainerTests.swiftmodule -lc++ -Wl,-no_warn_duplicate_libraries -Wl,-no_warn_duplicate_libraries -Wl,-no_warn_duplicate_libraries -lc++ -Wl,-no_warn_duplicate_libraries -Wl,-no_warn_duplicate_libraries -Wl,-no_warn_duplicate_libraries -Wl,-no_warn_duplicate_libraries -Wl,-no_warn_duplicate_libraries -Wl,-no_warn_duplicate_libraries -Wl,-no_warn_duplicate_libraries -Wl,-no_warn_duplicate_libraries -Wl,-no_warn_duplicate_libraries -Wl,-no_warn_duplicate_libraries -Wl,-no_warn_duplicate_libraries -Wl,-no_warn_duplicate_libraries -Wl,-no_warn_duplicate_libraries -framework XCTest -lXCTestSwiftSupport /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/VoicingTrainer.app/Contents/MacOS/VoicingTrainer.debug.dylib -Xlinker -no_adhoc_codesign -o /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/VoicingTrainer.app/Contents/PlugIns/VoicingTrainerTests.xctest/Contents/MacOS/VoicingTrainerTests

ExtractAppIntentsMetadata (in target 'VoicingTrainerTests' from project 'VoicingTrainer')
    cd /Users/<USER>/DEV/VoicingTrainer
    /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/appintentsmetadataprocessor --toolchain-dir /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain --module-name VoicingTrainerTests --sdk-root /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk --xcode-version 16E140 --platform-family macOS --deployment-target 15.0 --bundle-identifier com.yourcompany.VoicingTrainerTests --output /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/VoicingTrainer.app/Contents/PlugIns/VoicingTrainerTests.xctest/Contents/Resources --target-triple x86_64-apple-macos15.0 --binary-file /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/VoicingTrainer.app/Contents/PlugIns/VoicingTrainerTests.xctest/Contents/MacOS/VoicingTrainerTests --dependency-file /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainerTests.build/Objects-normal/x86_64/VoicingTrainerTests_dependency_info.dat --stringsdata-file /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainerTests.build/Objects-normal/x86_64/ExtractedAppShortcutsMetadata.stringsdata --source-file-list /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainerTests.build/Objects-normal/x86_64/VoicingTrainerTests.SwiftFileList --metadata-file-list /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainerTests.build/VoicingTrainerTests.DependencyMetadataFileList --static-metadata-file-list /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainerTests.build/VoicingTrainerTests.DependencyStaticMetadataFileList --swift-const-vals-list /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainerTests.build/Objects-normal/x86_64/VoicingTrainerTests.SwiftConstValuesFileList --compile-time-extraction --deployment-aware-processing --validate-assistant-intents --no-app-shortcuts-localization
2025-06-15 22:47:05.009 appintentsmetadataprocessor[17117:902759] Starting appintentsmetadataprocessor export
2025-06-15 22:47:05.012 appintentsmetadataprocessor[17117:902759] warning: Metadata extraction skipped. No AppIntents.framework dependency found.

CopySwiftLibs /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/VoicingTrainer.app/Contents/PlugIns/VoicingTrainerTests.xctest (in target 'VoicingTrainerTests' from project 'VoicingTrainer')
    cd /Users/<USER>/DEV/VoicingTrainer
    builtin-swiftStdLibTool --copy --verbose --sign - --scan-executable /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/VoicingTrainer.app/Contents/PlugIns/VoicingTrainerTests.xctest/Contents/MacOS/VoicingTrainerTests --scan-folder /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/VoicingTrainer.app/Contents/PlugIns/VoicingTrainerTests.xctest/Contents/Frameworks --scan-folder /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/VoicingTrainer.app/Contents/PlugIns/VoicingTrainerTests.xctest/Contents/PlugIns --scan-folder /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/VoicingTrainer.app/Contents/PlugIns/VoicingTrainerTests.xctest/Contents/Library/SystemExtensions --scan-folder /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/VoicingTrainer.app/Contents/PlugIns/VoicingTrainerTests.xctest/Contents/Extensions --platform macosx --toolchain /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain --destination /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/VoicingTrainer.app/Contents/PlugIns/VoicingTrainerTests.xctest/Contents/Frameworks --strip-bitcode --scan-executable /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib/libXCTestSwiftSupport.dylib --strip-bitcode-tool /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/bitcode_strip --emit-dependency-info /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainerTests.build/SwiftStdLibToolInputDependencies.dep --filter-for-swift-os

CodeSign /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/VoicingTrainer.app/Contents/PlugIns/VoicingTrainerTests.xctest (in target 'VoicingTrainerTests' from project 'VoicingTrainer')
    cd /Users/<USER>/DEV/VoicingTrainer
    
    Signing Identity:     "Sign to Run Locally"
    
    /usr/bin/codesign --force --sign - --timestamp\=none --generate-entitlement-der /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/VoicingTrainer.app/Contents/PlugIns/VoicingTrainerTests.xctest

CodeSign /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/VoicingTrainer.app/Contents/MacOS/VoicingTrainer.debug.dylib (in target 'VoicingTrainer' from project 'VoicingTrainer')
    cd /Users/<USER>/DEV/VoicingTrainer
    
    Signing Identity:     "Apple Development: <EMAIL> (MVJ5355FR9)"
    
    /usr/bin/codesign --force --sign 6BA82849F60FEDF5AE63A9E9D429C6BE479E1636 --timestamp\=none --generate-entitlement-der /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/VoicingTrainer.app/Contents/MacOS/VoicingTrainer.debug.dylib

CodeSign /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/VoicingTrainer.app/Contents/MacOS/__preview.dylib (in target 'VoicingTrainer' from project 'VoicingTrainer')
    cd /Users/<USER>/DEV/VoicingTrainer
    
    Signing Identity:     "Apple Development: <EMAIL> (MVJ5355FR9)"
    
    /usr/bin/codesign --force --sign 6BA82849F60FEDF5AE63A9E9D429C6BE479E1636 --timestamp\=none --generate-entitlement-der /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/VoicingTrainer.app/Contents/MacOS/__preview.dylib
/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/VoicingTrainer.app/Contents/MacOS/__preview.dylib: replacing existing signature

CodeSign /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/VoicingTrainer.app (in target 'VoicingTrainer' from project 'VoicingTrainer')
    cd /Users/<USER>/DEV/VoicingTrainer
    
    Signing Identity:     "Apple Development: <EMAIL> (MVJ5355FR9)"
    
    /usr/bin/codesign --force --sign 6BA82849F60FEDF5AE63A9E9D429C6BE479E1636 --entitlements /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainer.build/VoicingTrainer.app.xcent --timestamp\=none --generate-entitlement-der /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/VoicingTrainer.app

Validate /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/VoicingTrainer.app (in target 'VoicingTrainer' from project 'VoicingTrainer')
    cd /Users/<USER>/DEV/VoicingTrainer
    builtin-validationUtility /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/VoicingTrainer.app -no-validate-extension -infoplist-subpath Contents/Info.plist

RegisterWithLaunchServices /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/VoicingTrainer.app (in target 'VoicingTrainer' from project 'VoicingTrainer')
    cd /Users/<USER>/DEV/VoicingTrainer
    /System/Library/Frameworks/CoreServices.framework/Versions/Current/Frameworks/LaunchServices.framework/Versions/Current/Support/lsregister -f -R -trusted /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/VoicingTrainer.app

Ld /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/VoicingTrainerUITests-Runner.app/Contents/PlugIns/VoicingTrainerUITests.xctest/Contents/MacOS/VoicingTrainerUITests normal (in target 'VoicingTrainerUITests' from project 'VoicingTrainer')
    cd /Users/<USER>/DEV/VoicingTrainer
    /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang -Xlinker -reproducible -target x86_64-apple-macos15.0 -bundle -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk -O0 -L/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/EagerLinkingTBDs/Debug -L/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug -L/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib -F/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/EagerLinkingTBDs/Debug -F/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug -iframework /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks -filelist /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainerUITests.build/Objects-normal/x86_64/VoicingTrainerUITests.LinkFileList -Xlinker -rpath -Xlinker @loader_path/../Frameworks -Xlinker -object_path_lto -Xlinker /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainerUITests.build/Objects-normal/x86_64/VoicingTrainerUITests_lto.o -rdynamic -Xlinker -no_deduplicate -Xlinker -debug_variant -Xlinker -dependency_info -Xlinker /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainerUITests.build/Objects-normal/x86_64/VoicingTrainerUITests_dependency_info.dat -fobjc-link-runtime -fprofile-instr-generate -L/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx -L/usr/lib/swift -Xlinker -add_ast_path -Xlinker /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainerUITests.build/Objects-normal/x86_64/VoicingTrainerUITests.swiftmodule -framework XCTest -lXCTestSwiftSupport -Xlinker -no_adhoc_codesign -o /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/VoicingTrainerUITests-Runner.app/Contents/PlugIns/VoicingTrainerUITests.xctest/Contents/MacOS/VoicingTrainerUITests

ProcessInfoPlistFile /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/VoicingTrainerUITests-Runner.app/Contents/PlugIns/VoicingTrainerUITests.xctest/Contents/Info.plist /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainerUITests.build/empty-VoicingTrainerUITests.plist (in target 'VoicingTrainerUITests' from project 'VoicingTrainer')
    cd /Users/<USER>/DEV/VoicingTrainer
    builtin-infoPlistUtility /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainerUITests.build/empty-VoicingTrainerUITests.plist -producttype com.apple.product-type.bundle.ui-testing -expandbuildsettings -platform macosx -additionalcontentfile /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainerUITests.build/ProductTypeInfoPlistAdditions.plist -o /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/VoicingTrainerUITests-Runner.app/Contents/PlugIns/VoicingTrainerUITests.xctest/Contents/Info.plist

CopySwiftLibs /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/VoicingTrainerUITests-Runner.app/Contents/PlugIns/VoicingTrainerUITests.xctest (in target 'VoicingTrainerUITests' from project 'VoicingTrainer')
    cd /Users/<USER>/DEV/VoicingTrainer
    builtin-swiftStdLibTool --copy --verbose --sign - --scan-executable /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/VoicingTrainerUITests-Runner.app/Contents/PlugIns/VoicingTrainerUITests.xctest/Contents/MacOS/VoicingTrainerUITests --scan-folder /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/VoicingTrainerUITests-Runner.app/Contents/PlugIns/VoicingTrainerUITests.xctest/Contents/Frameworks --scan-folder /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/VoicingTrainerUITests-Runner.app/Contents/PlugIns/VoicingTrainerUITests.xctest/Contents/PlugIns --scan-folder /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/VoicingTrainerUITests-Runner.app/Contents/PlugIns/VoicingTrainerUITests.xctest/Contents/Library/SystemExtensions --scan-folder /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/VoicingTrainerUITests-Runner.app/Contents/PlugIns/VoicingTrainerUITests.xctest/Contents/Extensions --platform macosx --toolchain /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain --destination /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/VoicingTrainerUITests-Runner.app/Contents/PlugIns/VoicingTrainerUITests.xctest/Contents/Frameworks --strip-bitcode --scan-executable /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib/libXCTestSwiftSupport.dylib --strip-bitcode-tool /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/bitcode_strip --emit-dependency-info /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainerUITests.build/SwiftStdLibToolInputDependencies.dep --filter-for-swift-os

ExtractAppIntentsMetadata (in target 'VoicingTrainerUITests' from project 'VoicingTrainer')
    cd /Users/<USER>/DEV/VoicingTrainer
    /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/appintentsmetadataprocessor --toolchain-dir /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain --module-name VoicingTrainerUITests --sdk-root /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk --xcode-version 16E140 --platform-family macOS --deployment-target 15.0 --bundle-identifier com.yourcompany.VoicingTrainerUITests --output /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/VoicingTrainerUITests-Runner.app/Contents/PlugIns/VoicingTrainerUITests.xctest/Contents/Resources --target-triple x86_64-apple-macos15.0 --binary-file /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/VoicingTrainerUITests-Runner.app/Contents/PlugIns/VoicingTrainerUITests.xctest/Contents/MacOS/VoicingTrainerUITests --dependency-file /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainerUITests.build/Objects-normal/x86_64/VoicingTrainerUITests_dependency_info.dat --stringsdata-file /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainerUITests.build/Objects-normal/x86_64/ExtractedAppShortcutsMetadata.stringsdata --source-file-list /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainerUITests.build/Objects-normal/x86_64/VoicingTrainerUITests.SwiftFileList --metadata-file-list /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainerUITests.build/VoicingTrainerUITests.DependencyMetadataFileList --static-metadata-file-list /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainerUITests.build/VoicingTrainerUITests.DependencyStaticMetadataFileList --swift-const-vals-list /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainerUITests.build/Objects-normal/x86_64/VoicingTrainerUITests.SwiftConstValuesFileList --compile-time-extraction --deployment-aware-processing --validate-assistant-intents --no-app-shortcuts-localization
2025-06-15 22:47:05.930 appintentsmetadataprocessor[17125:902825] Starting appintentsmetadataprocessor export
2025-06-15 22:47:05.934 appintentsmetadataprocessor[17125:902825] warning: Metadata extraction skipped. No AppIntents.framework dependency found.

CodeSign /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/VoicingTrainerUITests-Runner.app/Contents/PlugIns/VoicingTrainerUITests.xctest (in target 'VoicingTrainerUITests' from project 'VoicingTrainer')
    cd /Users/<USER>/DEV/VoicingTrainer
    
    Signing Identity:     "Sign to Run Locally"
    
    /usr/bin/codesign --force --sign - --entitlements /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainerUITests.build/VoicingTrainerUITests.xctest.xcent --timestamp\=none --generate-entitlement-der /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/VoicingTrainerUITests-Runner.app/Contents/PlugIns/VoicingTrainerUITests.xctest

2025-06-15 22:47:08.934780+0800 VoicingTrainer[17127:902862] ApplePersistenceIgnoreState: Existing state will not be touched. New state will be written to /var/folders/2r/205x5g_d7q59fmlb5b89lp_w0000gn/T/com.SHZX.VoicingTutor.savedState
Failed to load config, using defaults
2025-06-15 22:47:09.070856+0800 VoicingTrainer[17127:902862] [midi] MIDI.swift:init():52:Initializing MIDI (MIDI.swift:init():52)
2025-06-15 22:47:09.084907+0800 VoicingTrainer[17127:902862] [midi] MIDI.swift:init():52:Initializing MIDI (MIDI.swift:init():52)
2025-06-15 22:47:09.126632+0800 VoicingTrainer[17127:902862] [plugin] AddInstanceForFactory: No factory registered for id <CFUUID 0x600000ccbcc0> F8BB1C28-BAE8-11D6-9C31-00039315CD46
✅ SoundFont loaded successfully
✅ AudioEngine started successfully
🎹   [0] 蓝牙
No achievement data found, creating default data
Loaded 13 note exercise levels
[]
Auto-selected last unlocked level: exercise 1
[]
2025-06-15 22:47:09.616434+0800 VoicingTrainer[17127:902862] Errors found! Invalidating cache...
2025-06-15 22:47:09.627961+0800 VoicingTrainer[17127:902862] Unable to open mach-O at path: /AppleInternal/Library/BuildRoots/1c8f7852-1ca9-11f0-b28b-226177e5bb69/Library/Caches/com.apple.xbs/Binaries/RenderBox/install/TempContent/Root/System/Library/PrivateFrameworks/RenderBox.framework/Versions/A/Resources/default.metallib  Error:2
2025-06-15 22:47:09.636325+0800 VoicingTrainer[17127:903533] Errors found! Invalidating cache...
[]
Test Suite 'Selected tests' started at 2025-06-15 22:47:09.961.
Test Suite 'VoicingTrainerTests.xctest' started at 2025-06-15 22:47:09.961.
Test Suite 'MIDIManagerTests' started at 2025-06-15 22:47:09.961.
Test Case '-[VoicingTrainerTests.MIDIManagerTests testChordNameParser_DominantSeventh]' started.
2025-06-15 22:47:09.963011+0800 VoicingTrainer[17127:902862] [midi] MIDI.swift:init():52:Initializing MIDI (MIDI.swift:init():52)
✅ SoundFont loaded successfully
✅ AudioEngine started successfully
🎹   [0] 蓝牙
✅ 属七和弦测试通过: ["C7"]
Test Case '-[VoicingTrainerTests.MIDIManagerTests testChordNameParser_DominantSeventh]' passed (1.018 seconds).
Test Case '-[VoicingTrainerTests.MIDIManagerTests testChordNameParser_EmptyInput]' started.
2025-06-15 22:47:10.982004+0800 VoicingTrainer[17127:902862] [midi] MIDI.swift:init():52:Initializing MIDI (MIDI.swift:init():52)
✅ SoundFont loaded successfully
✅ AudioEngine started successfully
🎹   [0] 蓝牙
✅ 空输入测试通过
Test Case '-[VoicingTrainerTests.MIDIManagerTests testChordNameParser_EmptyInput]' passed (1.016 seconds).
Test Case '-[VoicingTrainerTests.MIDIManagerTests testChordNameParser_MajorChord]' started.
2025-06-15 22:47:11.999301+0800 VoicingTrainer[17127:902862] [midi] MIDI.swift:init():52:Initializing MIDI (MIDI.swift:init():52)
✅ SoundFont loaded successfully
✅ AudioEngine started successfully
🎹   [0] 蓝牙
✅ 大三和弦测试通过: ["C"]
Test Case '-[VoicingTrainerTests.MIDIManagerTests testChordNameParser_MajorChord]' passed (1.019 seconds).
Test Case '-[VoicingTrainerTests.MIDIManagerTests testChordNameParser_MinorChord]' started.
2025-06-15 22:47:13.017937+0800 VoicingTrainer[17127:902862] [midi] MIDI.swift:init():52:Initializing MIDI (MIDI.swift:init():52)
✅ SoundFont loaded successfully
✅ AudioEngine started successfully
🎹   [0] 蓝牙
✅ 小三和弦测试通过: ["D#6/C", "Cm"]
Test Case '-[VoicingTrainerTests.MIDIManagerTests testChordNameParser_MinorChord]' passed (1.016 seconds).
Test Case '-[VoicingTrainerTests.MIDIManagerTests testChordNameParser_Performance]' started.
2025-06-15 22:47:14.034687+0800 VoicingTrainer[17127:902862] [midi] MIDI.swift:init():52:Initializing MIDI (MIDI.swift:init():52)
✅ SoundFont loaded successfully
✅ AudioEngine started successfully
🎹   [0] 蓝牙
[]
[]
[]
/Users/<USER>/DEV/VoicingTrainer/VoicingTrainerTests/MIDIManagerTests.swift:146: Test Case '-[VoicingTrainerTests.MIDIManagerTests testChordNameParser_Performance]' measured [Time, seconds] average: 0.009, relative standard deviation: 1.297%, values: [0.009277, 0.009140, 0.009187, 0.009240, 0.009149, 0.009221, 0.009276, 0.009438, 0.009465, 0.009469], performanceMetricID:com.apple.XCTPerformanceMetric_WallClockTime, baselineName: "", baselineAverage: , polarity: prefers smaller, maxPercentRegression: 10.000%, maxPercentRelativeStandardDeviation: 10.000%, maxRegression: 0.100, maxStandardDeviation: 0.100
✅ 和弦解析性能测试通过
Test Case '-[VoicingTrainerTests.MIDIManagerTests testChordNameParser_Performance]' passed (1.512 seconds).
Test Case '-[VoicingTrainerTests.MIDIManagerTests testChordNameParser_SingleNote]' started.
2025-06-15 22:47:15.545318+0800 VoicingTrainer[17127:902862] [midi] MIDI.swift:init():52:Initializing MIDI (MIDI.swift:init():52)
✅ SoundFont loaded successfully
✅ AudioEngine started successfully
🎹   [0] 蓝牙
✅ 单音符测试通过: ["C4"]
Test Case '-[VoicingTrainerTests.MIDIManagerTests testChordNameParser_SingleNote]' passed (1.009 seconds).
Test Case '-[VoicingTrainerTests.MIDIManagerTests testDebugMIDIManagerState]' started.
2025-06-15 22:47:16.557118+0800 VoicingTrainer[17127:902862] [midi] MIDI.swift:init():52:Initializing MIDI (MIDI.swift:init():52)
✅ SoundFont loaded successfully
✅ AudioEngine started successfully
🎹   [0] 蓝牙
🔧 MIDIManager状态调试:
  pressedNotes: []
  chordNames: []
  noteNames: 
  chordName: 
Test Case '-[VoicingTrainerTests.MIDIManagerTests testDebugMIDIManagerState]' passed (2.020 seconds).
Test Case '-[VoicingTrainerTests.MIDIManagerTests testMIDIManager_ChordNameClearing]' started.
2025-06-15 22:47:18.577691+0800 VoicingTrainer[17127:902862] [midi] MIDI.swift:init():52:Initializing MIDI (MIDI.swift:init():52)
✅ SoundFont loaded successfully
✅ AudioEngine started successfully
🎹   [0] 蓝牙
note60 on velocity:100 - stored before pressedNotes update
🔧 代码变更记录: MIDIManager.updateChordInfo (总变更: 1)
play Chord:C4
  按下C4后:
VoicingTrainerTests/MIDIManagerTests.swift:195: Fatal error: Unexpectedly found nil while implicitly unwrapping an Optional value
2025-06-15 22:47:19.610710+0800 VoicingTrainer[17127:902862] VoicingTrainerTests/MIDIManagerTests.swift:195: Fatal error: Unexpectedly found nil while implicitly unwrapping an Optional value

💣 [91mProgram crashed: Signal 4: Backtracing from 0x7ff81a3de0e5...[0m
[0K💣 [91mProgram crashed: Illegal instruction at 0x00007ff81a3de0e5[39m

Thread 0 crashed:

  [90m0[39m [32m0x00007ff81a3de0e5[39m [96mclosure #1 in closure #1 in closure #1 in _assertionFailure(_:_:file:line:flags:)[39m[37m + 309[39m in [35mlibswiftCore.dylib[39m
  [90m1[39m [32m0x00007ff81a3dded5[39m [96mclosure #1 in closure #1 in _assertionFailure(_:_:file:line:flags:)[39m[37m + 325[39m in [35mlibswiftCore.dylib[39m
  [90m2[39m [32m0x00007ff81a3ddd4c[39m [96mclosure #1 in _assertionFailure(_:_:file:line:flags:)[39m[37m + 380[39m in [35mlibswiftCore.dylib[39m
  [90m3[39m [32m0x00007ff81a3dd985[39m [96m_assertionFailure(_:_:file:line:flags:)[39m[37m + 277[39m in [35mlibswiftCore.dylib[39m
  [90m4[39m [32m0x00007ff807c2d9fc[39m [96m_dispatch_client_callout[39m[37m + 6[39m in [35mlibdispatch.dylib[39m
  [90m5[39m [32m0x00007ff807c1b0c3[39m [96m_dispatch_continuation_pop[39m[37m + 518[39m in [35mlibdispatch.dylib[39m
  [90m6[39m [32m0x00007ff807c2a7fa[39m [96m_dispatch_source_invoke[39m[37m + 2207[39m in [35mlibdispatch.dylib[39m
  [90m7[39m [32m0x00007ff807c47e6f[39m [96m_dispatch_main_queue_drain.cold.5[39m[37m + 587[39m in [35mlibdispatch.dylib[39m
  [90m8[39m [32m0x00007ff807c23f66[39m [96m_dispatch_main_queue_drain[39m[37m + 121[39m in [35mlibdispatch.dylib[39m
  [90m9[39m [32m0x00007ff807c23edf[39m [96m_dispatch_main_queue_callback_4CF[39m[37m + 31[39m in [35mlibdispatch.dylib[39m
 [90m10[39m [32m0x00007ff807ee7868[39m [96m__CFRUNLOOP_IS_SERVICING_THE_MAIN_DISPATCH_QUEUE__[39m[37m + 9[39m in [35mCoreFoundation[39m
 [90m11[39m [32m0x00007ff807eaa5e6[39m [96m__CFRunLoopRun[39m[37m + 2511[39m in [35mCoreFoundation[39m
 [90m12[39m [32m0x00007ff807ea95d0[39m [96mCFRunLoopRunSpecific[39m[37m + 536[39m in [35mCoreFoundation[39m
 [90m13[39m [32m0x0000000109503554[39m [96m+[XCTWaiter _synchronouslyWaitForTimeInterval:][39m[37m + 274[39m in [35mXCTestCore[39m
 [90m14[39m [32m0x0000000109503d6d[39m [96m-[XCTWaiter _performWait:manager:completionHandler:][39m[37m + 970[39m in [35mXCTestCore[39m
 [90m15[39m [32m0x00000001095017a7[39m [96m-[XCTWaiter _waitForExpectations:timeout:enforceOrder:synchronously:completionHandler:][39m[37m + 625[39m in [35mXCTestCore[39m
 [90m16[39m [32m0x00000001095014d1[39m [96m-[XCTWaiter waitForExpectations:timeout:enforceOrder:][39m[37m + 104[39m in [35mXCTestCore[39m
 [90m17[39m [32m0x00000001094e030d[39m [96m-[XCTestCase(AsynchronousTesting) waitForExpectationsWithTimeout:handler:][39m[37m + 220[39m in [35mXCTestCore[39m
 [90m18[39m [96mMIDIManagerTests.testMIDIManager_ChordNameClearing()[39m[37m + 1403[39m in [35mVoicingTrainerTests[39m at [33m/Users/<USER>/DEV/VoicingTrainer/VoicingTrainerTests/MIDIManagerTests.swift:139:9[39m

  [90m 137[39m│         }
  [90m 138[39m│         
  [48;5;234m[97m[90m 139[97m│         waitForExpectations(timeout: 1.0, handler: nil)[39m[49m
  [90m    [39m│         [91m▲[39m
  [90m 140[39m│         print("✅ 和弦名称清除测试通过")
  [90m 141[39m│     }

 [90m19[39m [32m0x00007ff807e8cfec[39m [96m__invoking___[39m[37m + 140[39m in [35mCoreFoundation[39m
 [90m20[39m [32m0x00007ff807e8ceb2[39m [96m-[NSInvocation invoke][39m[37m + 302[39m in [35mCoreFoundation[39m
 [90m21[39m [32m0x00000001094f73eb[39m [96m+[XCTFailableInvocation invokeStandardConventionInvocation:completion:][39m[37m + 66[39m in [35mXCTestCore[39m
 [90m22[39m [32m0x00000001094f73a7[39m [96m__90+[XCTFailableInvocation invokeInvocation:withTestMethodConvention:lastObservedErrorIssue:]_block_invoke_3[39m[37m + 20[39m in [35mXCTestCore[39m
 [90m23[39m [32m0x00000001094f6c00[39m [96m__81+[XCTFailableInvocation invokeWithAsynchronousWait:lastObservedErrorIssue:block:]_block_invoke[39m[37m + 317[39m in [35mXCTestCore[39m
 [90m24[39m [32m0x0000000109574872[39m [96m__49+[XCTSwiftErrorObservation observeErrorsInBlock:]_block_invoke[39m[37m + 16[39m in [35mXCTestCore[39m
 [90m25[39m [32m0x000000010957ca52[39m [96mclosure #1 in static XCTSwiftErrorObservation._observeErrors(in:)[39m[37m + 34[39m in [35mXCTestCore[39m
 [90m26[39m [32m0x00007ffd128e235c[39m [96mTaskLocal.withValue<A>(_:operation:file:line:)[39m[37m + 108[39m in [35mlibswift_Concurrency.dylib[39m
 [90m27[39m [32m0x000000010957c906[39m [96mstatic XCTSwiftErrorObservation._observeErrors(in:)[39m[37m + 438[39m in [35mXCTestCore[39m
 [90m28[39m [32m0x0000000109574779[39m [96m+[XCTSwiftErrorObservation observeErrorsInBlock:][39m[37m + 183[39m in [35mXCTestCore[39m
 [90m29[39m [32m0x00000001094f69ac[39m [96m+[XCTFailableInvocation invokeWithAsynchronousWait:lastObservedErrorIssue:block:][39m[37m + 203[39m in [35mXCTestCore[39m
 [90m30[39m [32m0x00000001094f7104[39m [96m+[XCTFailableInvocation invokeInvocation:withTestMethodConvention:lastObservedErrorIssue:][39m[37m + 438[39m in [35mXCTestCore[39m
 [90m31[39m [32m0x00000001094f746f[39m [96m+[XCTFailableInvocation invokeInvocation:lastObservedErrorIssue:][39m[37m + 64[39m in [35mXCTestCore[39m
 [90m32[39m [32m0x0000000109568c42[39m [96m-[XCTestCase invokeTestMethod:][39m[37m + 68[39m in [35mXCTestCore[39m
 [90m33[39m [32m0x000000010956968a[39m [96m__24-[XCTestCase invokeTest]_block_invoke_2.312[39m[37m + 61[39m in [35mXCTestCore[39m
 [90m34[39m [32m0x00000001095486ec[39m [96m-[XCTMemoryChecker _assertInvalidObjectsDeallocatedAfterScope:][39m[37m + 60[39m in [35mXCTestCore[39m
 [90m35[39m [32m0x000000010956ce7a[39m [96m-[XCTestCase assertInvalidObjectsDeallocatedAfterScope:][39m[37m + 112[39m in [35mXCTestCore[39m
 [90m36[39m [32m0x000000010956951a[39m [96m__24-[XCTestCase invokeTest]_block_invoke.294[39m[37m + 453[39m in [35mXCTestCore[39m
 [90m37[39m [32m0x000000010954e0d4[39m [96m-[XCTestCase(XCTIssueHandling) _caughtUnhandledDeveloperExceptionPermittingControlFlowInterruptions:caughtInterruptionException:whileExecutingBlock:][39m[37m + 174[39m in [35mXCTestCore[39m
 [90m38[39m [32m0x0000000109568f22[39m [96m-[XCTestCase invokeTest][39m[37m + 680[39m in [35mXCTestCore[39m
 [90m39[39m [32m0x000000010956af91[39m [96m__26-[XCTestCase performTest:]_block_invoke.417[39m[37m + 38[39m in [35mXCTestCore[39m
 [90m40[39m [32m0x000000010954e0d4[39m [96m-[XCTestCase(XCTIssueHandling) _caughtUnhandledDeveloperExceptionPermittingControlFlowInterruptions:caughtInterruptionException:whileExecutingBlock:][39m[37m + 174[39m in [35mXCTestCore[39m
[90m...[39m [32m[39m
 [90m85[39m [32m0x00007ff807eaba65[39m [96m__CFRunLoopDoSource1[39m[37m + 539[39m in [35mCoreFoundation[39m
 [90m86[39m [32m0x00007ff807eaa6d7[39m [96m__CFRunLoopRun[39m[37m + 2752[39m in [35mCoreFoundation[39m
 [90m87[39m [32m0x00007ff807ea95d0[39m [96mCFRunLoopRunSpecific[39m[37m + 536[39m in [35mCoreFoundation[39m
 [90m88[39m [32m0x00007ff813da00d4[39m [96mRunCurrentEventLoopInMode[39m[37m + 281[39m in [35mHIToolbox[39m
 [90m89[39m [32m0x00007ff813da2f97[39m [96mReceiveNextEventCommon[39m[37m + 499[39m in [35mHIToolbox[39m
 [90m90[39m [32m0x00007ff813f2c19a[39m [96m_BlockUntilNextEventMatchingListInModeWithFilter[39m[37m + 63[39m in [35mHIToolbox[39m
 [90m91[39m [32m0x00007ff80b87ae2d[39m [96m_DPSNextEvent[39m[37m + 912[39m in [35mAppKit[39m
 [90m92[39m [32m0x00007ff80c308d27[39m [96m-[NSApplication(NSEventRouting) _nextEventMatchingEventMask:untilDate:inMode:dequeue:][39m[37m + 1263[39m in [35mAppKit[39m
 [90m93[39m [32m0x00007ff80b86bf19[39m [96m-[NSApplication run][39m[37m + 610[39m in [35mAppKit[39m
 [90m94[39m [32m0x00007ff80b83f085[39m [96mNSApplicationMain[39m[37m + 803[39m in [35mAppKit[39m
 [90m95[39m [32m0x00007ff917ceafb1[39m [96m<unknown>[39m[37m + 161[39m in [35mSwiftUI[39m
 [90m96[39m [32m0x00007ff91803e2e8[39m [96m<unknown>[39m[37m + 104[39m in [35mSwiftUI[39m
 [90m97[39m [32m0x00007ff9182d51ab[39m [96m<unknown>[39m[37m + 139[39m in [35mSwiftUI[39m

Backtrace took 6.91s


Press space to interact, D to debug, or any other key to quit (30s) 
Press space to interact, D to debug, or any other key to quit (29s) 
Press space to interact, D to debug, or any other key to quit (28s) 
Press space to interact, D to debug, or any other key to quit (27s) 
Press space to interact, D to debug, or any other key to quit (26s) 
Press space to interact, D to debug, or any other key to quit (25s) 
Press space to interact, D to debug, or any other key to quit (24s) 
Press space to interact, D to debug, or any other key to quit (23s) 
Press space to interact, D to debug, or any other key to quit (22s) 
Press space to interact, D to debug, or any other key to quit (21s) 
Press space to interact, D to debug, or any other key to quit (20s) 
Press space to interact, D to debug, or any other key to quit (19s) 
Press space to interact, D to debug, or any other key to quit (18s) 
Press space to interact, D to debug, or any other key to quit (17s) 
Press space to interact, D to debug, or any other key to quit (16s) 
Press space to interact, D to debug, or any other key to quit (15s) 
Press space to interact, D to debug, or any other key to quit (14s) 
Press space to interact, D to debug, or any other key to quit (13s) 
Press space to interact, D to debug, or any other key to quit (12s) 
Press space to interact, D to debug, or any other key to quit (11s) 
Press space to interact, D to debug, or any other key to quit (10s) 
Press space to interact, D to debug, or any other key to quit (9s) 
Press space to interact, D to debug, or any other key to quit (8s) 
Press space to interact, D to debug, or any other key to quit (7s) 
Press space to interact, D to debug, or any other key to quit (6s) 
Press space to interact, D to debug, or any other key to quit (5s) 
Press space to interact, D to debug, or any other key to quit (4s) 
Press space to interact, D to debug, or any other key to quit (3s) 
Press space to interact, D to debug, or any other key to quit (2s) 
Press space to interact, D to debug, or any other key to quit (1s) 
[0K2025-06-15 22:48:12.398579+0800 VoicingTrainer[17140:905092] ApplePersistenceIgnoreState: Existing state will not be touched. New state will be written to /var/folders/2r/205x5g_d7q59fmlb5b89lp_w0000gn/T/com.SHZX.VoicingTutor.savedState
Failed to load config, using defaults
2025-06-15 22:48:12.494952+0800 VoicingTrainer[17140:905092] [midi] MIDI.swift:init():52:Initializing MIDI (MIDI.swift:init():52)
2025-06-15 22:48:12.506572+0800 VoicingTrainer[17140:905092] [midi] MIDI.swift:init():52:Initializing MIDI (MIDI.swift:init():52)
2025-06-15 22:48:12.526862+0800 VoicingTrainer[17140:905092] [plugin] AddInstanceForFactory: No factory registered for id <CFUUID 0x600003b63c20> F8BB1C28-BAE8-11D6-9C31-00039315CD46
✅ SoundFont loaded successfully
✅ AudioEngine started successfully
🎹   [0] 蓝牙
No achievement data found, creating default data
Loaded 13 note exercise levels
[]
Auto-selected last unlocked level: exercise 1
[]
2025-06-15 22:48:12.945038+0800 VoicingTrainer[17140:905092] Unable to open mach-O at path: /AppleInternal/Library/BuildRoots/1c8f7852-1ca9-11f0-b28b-226177e5bb69/Library/Caches/com.apple.xbs/Binaries/RenderBox/install/TempContent/Root/System/Library/PrivateFrameworks/RenderBox.framework/Versions/A/Resources/default.metallib  Error:2
[]

Restarting after unexpected exit, crash, or test timeout; summary will include totals from previous launches.

Test Suite 'Selected tests' started at 2025-06-15 22:48:13.183.
Test Suite 'VoicingTrainerTests.xctest' started at 2025-06-15 22:48:13.183.
Test Suite 'MIDIManagerTests' started at 2025-06-15 22:48:13.183.
Test Case '-[VoicingTrainerTests.MIDIManagerTests testMIDIManager_MajorChordName]' started.
2025-06-15 22:48:13.184205+0800 VoicingTrainer[17140:905092] [midi] MIDI.swift:init():52:Initializing MIDI (MIDI.swift:init():52)
✅ SoundFont loaded successfully
✅ AudioEngine started successfully
🎹   [0] 蓝牙
note60 on velocity:100 - stored before pressedNotes update
🔧 代码变更记录: MIDIManager.updateChordInfo (总变更: 2)
play Chord:C4
note64 on velocity:100 - stored before pressedNotes update
🔧 代码变更记录: MIDIManager.updateChordInfo (总变更: 3)
play Chord:Unknown
note67 on velocity:100 - stored before pressedNotes update
🔧 代码变更记录: MIDIManager.updateChordInfo (总变更: 4)
play Chord:C
[]
🔍 大三和弦调试信息:
  pressedNotes: []
  chordNames: []
  noteNames: 
/Users/<USER>/DEV/VoicingTrainer/VoicingTrainerTests/MIDIManagerTests.swift:101: error: -[VoicingTrainerTests.MIDIManagerTests testMIDIManager_MajorChordName] : XCTAssertEqual failed: ("0") is not equal to ("3") - 应该有三个按下的音符
/Users/<USER>/DEV/VoicingTrainer/VoicingTrainerTests/MIDIManagerTests.swift:102: error: -[VoicingTrainerTests.MIDIManagerTests testMIDIManager_MajorChordName] : XCTAssertFalse failed - 和弦名称数组不应该为空
/Users/<USER>/DEV/VoicingTrainer/VoicingTrainerTests/MIDIManagerTests.swift:103: error: -[VoicingTrainerTests.MIDIManagerTests testMIDIManager_MajorChordName] : XCTAssertTrue failed - 应该识别为C和弦
✅ 大三和弦和弦名称测试通过
Test Case '-[VoicingTrainerTests.MIDIManagerTests testMIDIManager_MajorChordName]' failed (1.374 seconds).
Test Case '-[VoicingTrainerTests.MIDIManagerTests testMIDIManager_MultipleNotesOnOff]' started.
2025-06-15 22:48:14.558560+0800 VoicingTrainer[17140:905092] [midi] MIDI.swift:init():52:Initializing MIDI (MIDI.swift:init():52)
✅ SoundFont loaded successfully
✅ AudioEngine started successfully
🎹   [0] 蓝牙
note60 on velocity:100 - stored before pressedNotes update
🔧 代码变更记录: MIDIManager.updateChordInfo (总变更: 5)
🧪 测试提醒: 建议运行单元测试！
   已累积 5 次代码变更
   尚未记录测试运行
play Chord:C4
note64 on velocity:100 - stored before pressedNotes update
🔧 代码变更记录: MIDIManager.updateChordInfo (总变更: 6)
🧪 测试提醒: 建议运行单元测试！
   已累积 6 次代码变更
   尚未记录测试运行
play Chord:Unknown
🔧 代码变更记录: MIDIManager.updateChordInfo (总变更: 7)
🧪 测试提醒: 建议运行单元测试！
   已累积 7 次代码变更
   尚未记录测试运行
play Chord:E4
note67 on velocity:100 - stored before pressedNotes update
🔧 代码变更记录: MIDIManager.updateChordInfo (总变更: 8)
🧪 测试提醒: 建议运行单元测试！
   已累积 8 次代码变更
   尚未记录测试运行
play Chord:Unknown
[]
🔧 代码变更记录: MIDIManager.updateChordInfo (总变更: 9)
🧪 测试提醒: 建议运行单元测试！
   已累积 9 次代码变更
   尚未记录测试运行
play Chord:G4
🔍 多音符操作调试信息:
  pressedNotes: []
  chordNames: []
/Users/<USER>/DEV/VoicingTrainer/VoicingTrainerTests/MIDIManagerTests.swift:171: error: -[VoicingTrainerTests.MIDIManagerTests testMIDIManager_MultipleNotesOnOff] : XCTAssertEqual failed: ("0") is not equal to ("1") - 应该只剩一个音符
/Users/<USER>/DEV/VoicingTrainer/VoicingTrainerTests/MIDIManagerTests.swift:172: error: -[VoicingTrainerTests.MIDIManagerTests testMIDIManager_MultipleNotesOnOff] : XCTAssertTrue failed - 应该只剩G音符
✅ 多音符操作测试通过
Test Case '-[VoicingTrainerTests.MIDIManagerTests testMIDIManager_MultipleNotesOnOff]' failed (1.213 seconds).
Test Case '-[VoicingTrainerTests.MIDIManagerTests testMIDIManager_SingleNoteChordName]' started.
2025-06-15 22:48:15.771873+0800 VoicingTrainer[17140:905092] [midi] MIDI.swift:init():52:Initializing MIDI (MIDI.swift:init():52)
✅ SoundFont loaded successfully
✅ AudioEngine started successfully
🎹   [0] 蓝牙
note60 on velocity:100 - stored before pressedNotes update
🔧 代码变更记录: MIDIManager.updateChordInfo (总变更: 10)
🧪 测试提醒: 建议运行单元测试！
   已累积 10 次代码变更
   尚未记录测试运行
play Chord:C4
[]
🔍 调试信息:
  pressedNotes: []
  chordNames: []
  noteNames: 
/Users/<USER>/DEV/VoicingTrainer/VoicingTrainerTests/MIDIManagerTests.swift:74: error: -[VoicingTrainerTests.MIDIManagerTests testMIDIManager_SingleNoteChordName] : XCTAssertEqual failed: ("0") is not equal to ("1") - 应该有一个按下的音符
/Users/<USER>/DEV/VoicingTrainer/VoicingTrainerTests/MIDIManagerTests.swift:75: error: -[VoicingTrainerTests.MIDIManagerTests testMIDIManager_SingleNoteChordName] : XCTAssertTrue failed - 应该包含C4音符
/Users/<USER>/DEV/VoicingTrainer/VoicingTrainerTests/MIDIManagerTests.swift:76: error: -[VoicingTrainerTests.MIDIManagerTests testMIDIManager_SingleNoteChordName] : XCTAssertFalse failed - 和弦名称数组不应该为空
/Users/<USER>/DEV/VoicingTrainer/VoicingTrainerTests/MIDIManagerTests.swift:77: error: -[VoicingTrainerTests.MIDIManagerTests testMIDIManager_SingleNoteChordName] : XCTAssertTrue failed - 应该包含C音符名称
✅ 单音符和弦名称测试通过
Test Case '-[VoicingTrainerTests.MIDIManagerTests testMIDIManager_SingleNoteChordName]' failed (1.143 seconds).
Test Suite 'MIDIManagerTests' failed at 2025-06-15 22:48:16.914.
	 Executed 3 tests, with 9 failures (0 unexpected) in 3.730 (3.731) seconds
Test Suite 'VoicingTrainerTests.xctest' failed at 2025-06-15 22:48:16.914.
	 Executed 3 tests, with 9 failures (0 unexpected) in 3.730 (3.731) seconds
Test Suite 'Selected tests' failed at 2025-06-15 22:48:16.915.
	 Executed 3 tests, with 9 failures (0 unexpected) in 3.730 (3.732) seconds
2025-06-15 22:48:21.925 xcodebuild[17058:902274] [MT] IDETestOperationsObserverDebug: 75.931 elapsed -- Testing started completed.
2025-06-15 22:48:21.925 xcodebuild[17058:902274] [MT] IDETestOperationsObserverDebug: 0.000 sec, +0.000 sec -- start
2025-06-15 22:48:21.925 xcodebuild[17058:902274] [MT] IDETestOperationsObserverDebug: 75.931 sec, +75.931 sec -- end

Test session results, code coverage, and logs:
	/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Logs/Test/Test-VoicingTrainer-2025.06.15_22-46-52-+0800.xcresult

Failing tests:
	MIDIManagerTests.testMIDIManager_ChordNameClearing()
	MIDIManagerTests.testMIDIManager_MajorChordName()
	MIDIManagerTests.testMIDIManager_MajorChordName()
	MIDIManagerTests.testMIDIManager_MajorChordName()
	MIDIManagerTests.testMIDIManager_MultipleNotesOnOff()
	MIDIManagerTests.testMIDIManager_MultipleNotesOnOff()
	MIDIManagerTests.testMIDIManager_SingleNoteChordName()
	MIDIManagerTests.testMIDIManager_SingleNoteChordName()
	MIDIManagerTests.testMIDIManager_SingleNoteChordName()
	MIDIManagerTests.testMIDIManager_SingleNoteChordName()

** TEST FAILED **

Testing started
