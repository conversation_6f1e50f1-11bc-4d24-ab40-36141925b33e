//
//  DataProcessingUtils.swift
//  VoicingTrainerTests
//
//  Created by <PERSON> on 2025/5/26.
//  数据处理工具类 - 包含音符解析、和弦分析、音符生成等核心功能
//

import Foundation

// MARK: - 音符解析工具

/**
 * 音符名称解析器
 * 将音符名称字符串（如"C3", "F#4", "Bb2"）转换为MIDI音符号
 */
struct NoteParser {
    
    /// 音符名称到半音数的映射
    private static let noteNames = ["C": 0, "D": 2, "E": 4, "F": 5, "G": 7, "A": 9, "B": 11]
    
    /**
     * 解析音符名称为MIDI音符号
     * - Parameter noteString: 音符名称字符串，如"C3", "F#4", "Bb2"
     * - Returns: MIDI音符号 (0-127)
     * 
     * 示例:
     * - "C3" -> 48
     * - "C4" -> 60 (Middle C)
     * - "C#3" -> 49
     * - "Bb3" -> 58
     */
    static func parseNoteToMidi(_ noteString: String) -> Int {
        let noteString = noteString.uppercased()
        
        var noteIndex = 0
        var octave = 0
        var currentIndex = noteString.startIndex
        
        // 解析音符名称 (C, D, E, F, G, A, B)
        if currentIndex < noteString.endIndex {
            let noteName = String(noteString[currentIndex])
            if let baseNote = noteNames[noteName] {
                noteIndex = baseNote
                currentIndex = noteString.index(after: currentIndex)
            }
        }
        
        // 解析升降号 (#, b)
        if currentIndex < noteString.endIndex {
            let modifier = noteString[currentIndex]
            if modifier == "#" {
                noteIndex += 1
                currentIndex = noteString.index(after: currentIndex)
            } else if modifier == "B" {  // 降号用B表示
                noteIndex -= 1
                currentIndex = noteString.index(after: currentIndex)
            }
        }
        
        // 解析八度
        if currentIndex < noteString.endIndex {
            let octaveString = String(noteString[currentIndex...])
            octave = Int(octaveString) ?? 3
        }
        
        // 计算MIDI音符号：(octave + 1) * 12 + noteIndex
        // 标准：C4 = 60, 所以C3 = 48
        return (octave + 1) * 12 + noteIndex
    }
}

// MARK: - 音符名称生成器

/**
 * 音符名称生成器
 * 将MIDI音符号转换为音符名称字符串
 */
struct NoteNameGenerator {
    
    /// 音符名称数组
    private static let noteNames = ["C", "C#", "D", "D#", "E", "F", "F#", "G", "G#", "A", "A#", "B"]
    
    /**
     * 将MIDI音符号转换为音符名称
     * - Parameter note: MIDI音符号 (0-127)
     * - Returns: 音符名称字符串，如"C4", "F#3"
     * 
     * 示例:
     * - 48 -> "C3"
     * - 60 -> "C4"
     * - 61 -> "C#4"
     */
    static func getNoteName(note: UInt8) -> String {
        let octave = Int(note / 12) - 1
        let noteIndex = Int(note % 12)
        return "\(noteNames[noteIndex])\(octave)"
    }
    
    /**
     * 将MIDI音符号转换为音符名称（Int版本）
     */
    static func getNoteName(note: Int) -> String {
        return getNoteName(note: UInt8(note))
    }
}

// MARK: - 五度圈工具

/**
 * 五度圈工具类
 * 用于和弦游戏中的调性循环和根音计算
 */
struct CircleOfFifthsUtils {
    
    /// 五度圈反向顺序：C, F, Bb, Eb, Ab, Db, Gb, B, E, A, D, G
    /// 这个顺序用于和弦游戏中的12个调的循环
    static let reversedOrder: [Int] = [0, 5, 10, 3, 8, 1, 6, 11, 4, 9, 2, 7]
    
    /**
     * 根据五度圈索引获取根音MIDI值
     * - Parameters:
     *   - index: 五度圈索引 (0-11)
     *   - baseOctave: 基础八度 (默认为3)
     * - Returns: 根音的MIDI值
     */
    static func getRootNote(for index: Int, baseOctave: Int = 3) -> Int {
        let rootPitch = reversedOrder[index % 12]
        return baseOctave * 12 + rootPitch
    }
    
    /**
     * 根据五度圈索引获取根音名称
     * - Parameter index: 五度圈索引 (0-11)
     * - Returns: 根音名称，如"C", "F", "Bb"
     */
    static func getRootName(for index: Int) -> String {
        let noteNames = ["C", "Db", "D", "Eb", "E", "F", "Gb", "G", "Ab", "A", "Bb", "B"]
        let rootPitch = reversedOrder[index % 12]
        return noteNames[rootPitch]
    }
}

// MARK: - 音符生成器

/**
 * 随机音符生成器
 * 根据游戏配置生成随机音符，支持白键限制
 */
class RandomNoteGenerator {
    
    /// 游戏配置
    private let loKey: Int
    private let hiKey: Int
    private let whiteKeyOnly: Bool
    
    /**
     * 初始化音符生成器
     * - Parameters:
     *   - loKey: 最低音符MIDI值
     *   - hiKey: 最高音符MIDI值
     *   - whiteKeyOnly: 是否只生成白键音符
     */
    init(loKey: Int, hiKey: Int, whiteKeyOnly: Bool) {
        self.loKey = loKey
        self.hiKey = hiKey
        self.whiteKeyOnly = whiteKeyOnly
    }
    
    /**
     * 检查是否为白键
     * - Parameter midiNote: MIDI音符号
     * - Returns: 是否为白键
     */
    private func isWhiteKey(_ midiNote: Int) -> Bool {
        let noteClass = midiNote % 12
        return [0, 2, 4, 5, 7, 9, 11].contains(noteClass)  // C, D, E, F, G, A, B
    }
    
    /**
     * 获取指定范围内的所有白键
     * - Parameters:
     *   - loKey: 最低音符
     *   - hiKey: 最高音符
     * - Returns: 白键MIDI值数组
     */
    private func getWhiteKeysInRange(loKey: Int, hiKey: Int) -> [Int] {
        return (loKey...hiKey).filter { isWhiteKey($0) }
    }
    
    /**
     * 生成随机音符
     * - Returns: 随机音符的MIDI值
     */
    func generateRandomNote() -> Int {
        let range = loKey...hiKey
        if whiteKeyOnly {
            let whiteKeys = getWhiteKeysInRange(loKey: loKey, hiKey: hiKey)
            return whiteKeys.randomElement() ?? Int.random(in: range)
        } else {
            return Int.random(in: range)
        }
    }
}

// MARK: - 和弦分析器

/**
 * 和弦分析器
 * 分析MIDI音符组合，识别和弦类型
 */
class ChordAnalyzerUtils {
    
    /// 音符名称数组
    private let noteNames = ["C", "C#", "D", "D#", "E", "F", "F#", "G", "G#", "A", "A#", "B"]
    
    /// 和弦类型定义：音程集合 -> 和弦名称
    private let chordTypes: [(intervals: Set<Int>, names: [String])] = [
        // 基础三和弦
        (Set([0, 4, 7]), ["", "maj"]),                    // 大三和弦
        (Set([0, 3, 7]), ["m", "min"]),                   // 小三和弦
        (Set([0, 4, 8]), ["aug", "+"]),                   // 增三和弦
        (Set([0, 3, 6]), ["dim", "°"]),                   // 减三和弦
        
        // 七和弦
        (Set([0, 4, 7, 10]), ["7"]),                      // 属七和弦
        (Set([0, 4, 7, 11]), ["maj7", "M7"]),            // 大七和弦
        (Set([0, 3, 7, 10]), ["m7", "min7"]),            // 小七和弦
        (Set([0, 3, 7, 11]), ["mM7", "minmaj7"]),        // 小大七和弦
        (Set([0, 3, 6, 9]), ["dim7", "°7"]),             // 减七和弦
        (Set([0, 3, 6, 10]), ["m7b5", "ø"]),             // 半减七和弦
        
        // 扩展和弦
        (Set([0, 4, 7, 11, 14]), ["maj9"]),              // 大九和弦
        (Set([0, 4, 7, 10, 14]), ["9"]),                 // 属九和弦
        (Set([0, 3, 7, 10, 14]), ["m9", "min9"]),        // 小九和弦
        
        // 挂留和弦
        (Set([0, 5, 7]), ["sus4"]),                      // 挂四和弦
        (Set([0, 2, 7]), ["sus2"]),                      // 挂二和弦
        
        // 加音和弦
        (Set([0, 4, 7, 14]), ["add9"]),                  // 加九和弦
        (Set([0, 3, 7, 14]), ["madd9"]),                 // 小加九和弦
    ]
    
    /**
     * 分析和弦并返回可能的和弦名称
     * - Parameter midiNotes: MIDI音符数组
     * - Returns: 可能的和弦名称数组
     */
    func analyze(midiNotes: [Int]) -> [String] {
        guard !midiNotes.isEmpty else { return [] }
        
        // 将MIDI音符转换为音高类（0-11）
        let pitchClasses = Set(midiNotes.map { $0 % 12 })
        guard !pitchClasses.isEmpty else { return [] }
        
        var possibleChords: [(root: Int, type: String)] = []
        
        // 尝试每个音符作为根音
        for potentialRoot in pitchClasses {
            // 标准化音程（相对于根音）
            let normalizedIntervals = Set(pitchClasses.map { ($0 - potentialRoot + 12) % 12 })
            
            // 检查是否匹配预定义的和弦类型
            for (intervals, names) in chordTypes {
                if isChordMatch(normalizedIntervals: normalizedIntervals, chordIntervals: intervals) {
                    for name in names {
                        possibleChords.append((root: potentialRoot, type: name))
                    }
                }
            }
        }
        
        // 如果没有找到匹配的和弦，尝试分析不完整和弦
        if possibleChords.isEmpty {
            possibleChords.append(contentsOf: analyzeIncompleteChord(pitchClasses: pitchClasses))
        }
        
        // 转换为和弦名称
        return possibleChords.map { noteNames[$0.root] + $0.type }
    }
    
    /**
     * 检查音程集合是否匹配和弦类型
     * - Parameters:
     *   - normalizedIntervals: 标准化的音程集合
     *   - chordIntervals: 和弦类型的音程集合
     * - Returns: 是否匹配
     */
    private func isChordMatch(normalizedIntervals: Set<Int>, chordIntervals: Set<Int>) -> Bool {
        // 根音必须存在
        guard normalizedIntervals.contains(0) else { return false }
        
        // 检查必要音程是否存在
        let hasEssentialIntervals = chordIntervals.isSubset(of: normalizedIntervals)
        
        // 检查是否有不属于和弦的音
        let hasInvalidIntervals = normalizedIntervals.contains { interval in
            !chordIntervals.contains(interval)
        }
        
        return hasEssentialIntervals && !hasInvalidIntervals
    }
    
    /**
     * 分析不完整和弦
     * - Parameter pitchClasses: 音高类集合
     * - Returns: 可能的和弦根音和类型
     */
    private func analyzeIncompleteChord(pitchClasses: Set<Int>) -> [(root: Int, type: String)] {
        var results: [(root: Int, type: String)] = []
        
        // 如果只有两个音，尝试分析为省略五音的三和弦
        if pitchClasses.count == 2 {
            let sortedPitches = Array(pitchClasses).sorted()
            let interval = (sortedPitches[1] - sortedPitches[0] + 12) % 12
            
            if interval == 4 {
                results.append((root: sortedPitches[0], type: ""))  // 大三和弦（无五音）
            } else if interval == 3 {
                results.append((root: sortedPitches[0], type: "m")) // 小三和弦（无五音）
            }
        }
        
        return results
    }
}

// MARK: - 和弦配置数据模型

/**
 * 和弦配置数据模型
 * 用于测试和弦配置的解析和计算
 */
struct ChordVoicingData: Codable, Identifiable, Hashable {
    let id: String
    let name: String
    let type: String
    let base_note: String
    let intervals: [Int]
    let description: String
    
    /// 计算基础音符的MIDI值
    var baseMidiNote: Int {
        return NoteParser.parseNoteToMidi(base_note)
    }
} 