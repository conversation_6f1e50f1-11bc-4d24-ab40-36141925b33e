import XCTest
@testable import VoicingTrainer

/// MIDIManager 单元测试
/// 专注于测试和弦名称解析功能和MIDI事件处理
class MIDIManagerTests: XCTestCase {
    
    var midiManager: MIDIManager!
    
    override func setUp() {
        super.setUp()
        midiManager = MIDIManager()
        // 等待初始化完成
        sleep(1)
    }
    
    override func tearDown() {
        midiManager?.resetAllNotes()
        midiManager = nil
        super.tearDown()
    }
    
    // MARK: - ChordNameParser 测试
    
    func testChordNameParser_SingleNote() {
        let result = ChordNameParser.analyze(midiNotes: [60]) // C4
        XCTAssertFalse(result.isEmpty, "单音符应该返回音符名称")
        XCTAssertTrue(result.first?.contains("C") == true, "应该包含C音符")
        print("✅ 单音符测试通过: \(result)")
    }
    
    func testChordNameParser_MajorChord() {
        let result = ChordNameParser.analyze(midiNotes: [60, 64, 67]) // C E G
        XCTAssertFalse(result.isEmpty, "大三和弦应该返回和弦名称")
        XCTAssertTrue(result.contains { $0.contains("C") }, "应该识别为C和弦")
        print("✅ 大三和弦测试通过: \(result)")
    }
    
    func testChordNameParser_MinorChord() {
        let result = ChordNameParser.analyze(midiNotes: [60, 63, 67]) // C Eb G
        XCTAssertFalse(result.isEmpty, "小三和弦应该返回和弦名称")
        XCTAssertTrue(result.contains { $0.contains("C") && $0.contains("m") }, "应该识别为Cm和弦")
        print("✅ 小三和弦测试通过: \(result)")
    }
    
    func testChordNameParser_DominantSeventh() {
        let result = ChordNameParser.analyze(midiNotes: [60, 64, 67, 70]) // C E G Bb
        XCTAssertFalse(result.isEmpty, "属七和弦应该返回和弦名称")
        XCTAssertTrue(result.contains { $0.contains("C") && $0.contains("7") }, "应该识别为C7和弦")
        print("✅ 属七和弦测试通过: \(result)")
    }
    
    func testChordNameParser_EmptyInput() {
        let result = ChordNameParser.analyze(midiNotes: [])
        XCTAssertTrue(result.isEmpty, "空输入应该返回空数组")
        print("✅ 空输入测试通过")
    }
    
    // MARK: - MIDIManager 和弦名称更新测试
    
    func testMIDIManager_SingleNoteChordName() {
        let expectation = self.expectation(description: "Single note chord name")
        
        // 模拟按下单个音符
        midiManager.noteOn(60, velocity: 100)
        
        // 等待异步更新完成
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            print("🔍 调试信息:")
            print("  pressedNotes: \(self.midiManager.pressedNotes)")
            print("  chordNames: \(self.midiManager.chordNames)")
            print("  noteNames: \(self.midiManager.noteNames)")
            
            XCTAssertEqual(self.midiManager.pressedNotes.count, 1, "应该有一个按下的音符")
            XCTAssertTrue(self.midiManager.pressedNotes.contains(60), "应该包含C4音符")
            XCTAssertFalse(self.midiManager.chordNames.isEmpty, "和弦名称数组不应该为空")
            XCTAssertTrue(self.midiManager.chordNames.first?.contains("C") == true, "应该包含C音符名称")
            
            expectation.fulfill()
        }
        
        waitForExpectations(timeout: 1.0, handler: nil)
        print("✅ 单音符和弦名称测试通过")
    }
    
    func testMIDIManager_MajorChordName() {
        let expectation = self.expectation(description: "Major chord name")
        
        // 模拟按下大三和弦音符
        midiManager.noteOn(60, velocity: 100) // C
        midiManager.noteOn(64, velocity: 100) // E
        midiManager.noteOn(67, velocity: 100) // G
        
        // 等待异步更新完成
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
            print("🔍 大三和弦调试信息:")
            print("  pressedNotes: \(self.midiManager.pressedNotes)")
            print("  chordNames: \(self.midiManager.chordNames)")
            print("  noteNames: \(self.midiManager.noteNames)")
            
            XCTAssertEqual(self.midiManager.pressedNotes.count, 3, "应该有三个按下的音符")
            XCTAssertFalse(self.midiManager.chordNames.isEmpty, "和弦名称数组不应该为空")
            XCTAssertTrue(self.midiManager.chordNames.contains { $0.contains("C") }, "应该识别为C和弦")
            
            expectation.fulfill()
        }
        
        waitForExpectations(timeout: 1.0, handler: nil)
        print("✅ 大三和弦和弦名称测试通过")
    }
    
    func testMIDIManager_ChordNameClearing() {
        let expectation = self.expectation(description: "Chord name clearing")
        
        // 先按下和弦
        midiManager.noteOn(60, velocity: 100)
        midiManager.noteOn(64, velocity: 100)
        midiManager.noteOn(67, velocity: 100)
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            XCTAssertFalse(self.midiManager.chordNames.isEmpty, "应该有和弦名称")
            
            // 然后释放所有音符
            self.midiManager.resetAllNotes()
            
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                print("🔍 清除后调试信息:")
                print("  pressedNotes: \(self.midiManager.pressedNotes)")
                print("  chordNames: \(self.midiManager.chordNames)")
                print("  noteNames: \(self.midiManager.noteNames)")
                
                XCTAssertTrue(self.midiManager.pressedNotes.isEmpty, "按下的音符应该为空")
                // 注意：chordNames 可能不会被清空，这取决于实现
                
                expectation.fulfill()
            }
        }
        
        waitForExpectations(timeout: 1.0, handler: nil)
        print("✅ 和弦名称清除测试通过")
    }
    
    // MARK: - 性能测试
    
    func testChordNameParser_Performance() {
        measure {
            for _ in 0..<100 {
                _ = ChordNameParser.analyze(midiNotes: [60, 64, 67, 70])
            }
        }
        print("✅ 和弦解析性能测试通过")
    }
    
    // MARK: - 边界情况测试
    
    func testMIDIManager_MultipleNotesOnOff() {
        let expectation = self.expectation(description: "Multiple notes on/off")
        
        // 快速按下和释放多个音符
        midiManager.noteOn(60, velocity: 100)
        midiManager.noteOn(64, velocity: 100)
        midiManager.noteOff(60)
        midiManager.noteOn(67, velocity: 100)
        midiManager.noteOff(64)
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
            print("🔍 多音符操作调试信息:")
            print("  pressedNotes: \(self.midiManager.pressedNotes)")
            print("  chordNames: \(self.midiManager.chordNames)")
            
            XCTAssertEqual(self.midiManager.pressedNotes.count, 1, "应该只剩一个音符")
            XCTAssertTrue(self.midiManager.pressedNotes.contains(67), "应该只剩G音符")
            
            expectation.fulfill()
        }
        
        waitForExpectations(timeout: 1.0, handler: nil)
        print("✅ 多音符操作测试通过")
    }
    
    // MARK: - 调试辅助方法
    
    func testDebugMIDIManagerState() {
        print("🔧 MIDIManager状态调试:")
        print("  pressedNotes: \(midiManager.pressedNotes)")
        print("  chordNames: \(midiManager.chordNames)")
        print("  noteNames: \(midiManager.noteNames)")
        print("  chordName: \(midiManager.chordName)")
        
        // 测试一个简单的和弦
        midiManager.noteOn(60, velocity: 100)
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            print("  按下C4后:")
            print("    pressedNotes: \(self.midiManager.pressedNotes)")
            print("    chordNames: \(self.midiManager.chordNames)")
            print("    noteNames: \(self.midiManager.noteNames)")
        }
        
        sleep(1) // 给异步操作时间完成
    }
}

// MARK: - 测试运行器扩展

extension MIDIManagerTests {
    
    /// 运行所有和弦相关测试
    static func runChordTests() {
        print("🧪 开始运行MIDIManager和弦测试...")
        
        let testSuite = XCTestSuite(name: "MIDIManager Chord Tests")
        let testCase = MIDIManagerTests()
        
        // 手动运行关键测试
        testCase.setUp()
        testCase.testChordNameParser_SingleNote()
        testCase.testChordNameParser_MajorChord()
        testCase.testChordNameParser_MinorChord()
        testCase.testDebugMIDIManagerState()
        testCase.tearDown()
        
        print("🧪 MIDIManager和弦测试完成")
    }
} 