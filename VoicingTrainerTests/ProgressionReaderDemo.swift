import XCTest
import Foundation
@testable import VoicingTrainer

/// ProgressionReader重构后的演示用例
final class ProgressionReaderDemo: XCTestCase {
    
    /// 演示如何使用重构后的ProgressionReader
    func testProgressionReaderDemo() throws {
        print("🎵 ProgressionReader重构演示")
        print("=" * 50)
        
        // 1. 创建ProgressionReader实例
        let progressionReader = ProgressionReader()
        print("✅ 创建ProgressionReader实例")
        
        // 2. 加载demo.progression文件
        progressionReader.loadProgression("demo")
        print("✅ 尝试加载demo.progression文件")
        
        // 等待加载完成
        let expectation = XCTestExpectation(description: "Load progression")
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
            expectation.fulfill()
        }
        wait(for: [expectation], timeout: 1.0)
        
        // 3. 验证加载结果
        if let progression = progressionReader.currentProgression {
            print("✅ 成功加载和弦进行:")
            print("   名称: \(progression.name)")
            print("   风格: \(progression.style ?? "未指定")")
            print("   调性: \(progression.key ?? "未指定")")
            print("   和弦数量: \(progression.chords.count)")
            
            // 4. 显示原始和弦信息
            print("\n🎼 原始和弦进行:")
            for (index, _) in progression.chords.enumerated() {
                let chordName = progressionReader.getChordName(at: index)
                let midiNotes = progressionReader.getChordMIDINotes(at: index)
                print("   \(index + 1). \(chordName) - MIDI音符: \(midiNotes)")
            }
            
            // 5. 演示移调功能
            print("\n🎵 移调演示:")
            
            // 向上移调2个半音（全音）
            progressionReader.transposeFromBase(2)
            print("   向上移调2个半音:")
            for (index, _) in progression.chords.enumerated() {
                let chordName = progressionReader.getChordName(at: index)
                let midiNotes = progressionReader.getChordMIDINotes(at: index)
                print("     \(index + 1). \(chordName) - MIDI音符: \(midiNotes)")
            }
            
            // 向下移调3个半音
            progressionReader.transposeFromBase(-3)
            print("   向下移调3个半音:")
            for (index, _) in progression.chords.enumerated() {
                let chordName = progressionReader.getChordName(at: index)
                let midiNotes = progressionReader.getChordMIDINotes(at: index)
                print("     \(index + 1). \(chordName) - MIDI音符: \(midiNotes)")
            }
            
            // 回到原调
            progressionReader.transposeFromBase(0)
            print("   回到原调:")
            for (index, _) in progression.chords.enumerated() {
                let chordName = progressionReader.getChordName(at: index)
                let midiNotes = progressionReader.getChordMIDINotes(at: index)
                print("     \(index + 1). \(chordName) - MIDI音符: \(midiNotes)")
            }
            
            // 6. 演示便捷方法
            print("\n🔧 便捷方法演示:")
            print("   所有和弦名称: \(progressionReader.chordNames)")
            print("   和弦数量: \(progressionReader.chordCount)")
            print("   所有MIDI音符: \(progressionReader.allChordMIDINotes)")
            
            print("\n🎉 演示完成！重构后的ProgressionReader功能正常")
            
        } else {
            print("❌ 未能加载和弦进行")
            if let errorMessage = progressionReader.errorMessage {
                print("   错误信息: \(errorMessage)")
            }
        }
        
        print("=" * 50)
    }
    
    /// 演示集成播放用例
    func testIntegrationWithMIDIManager() throws {
        print("🎵 ProgressionReader与MIDIManager集成演示")
        print("=" * 50)
        
        // 这里可以添加与MIDIManager的集成演示
        // 在实际应用中，你可以这样使用：
        
        /*
        // 1. 创建必要的管理器
        let midiManager = MIDIManager()
        let progressionReader = ProgressionReader()
        
        // 2. 加载和弦进行
        progressionReader.loadProgression("demo")
        
        // 3. 等待加载完成后播放
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            if progressionReader.chordCount > 0 {
                // 播放第一个和弦
                let firstChordNotes = progressionReader.getChordMIDINotes(at: 0)
                for note in firstChordNotes {
                    midiManager.playListenNote(note, velocity: 100)
                }
                
                // 1.5秒后停止
                DispatchQueue.main.asyncAfter(deadline: .now() + 1.5) {
                    for note in firstChordNotes {
                        midiManager.stopListenNote(note)
                    }
                }
            }
        }
        */
        
        print("✅ 集成演示代码结构完整")
        print("   - 使用ProgressionReader加载和弦进行")
        print("   - 获取MIDI音符")
        print("   - 通过MIDIManager播放音符")
        print("=" * 50)
    }
}

// MARK: - 辅助扩展

private extension String {
    static func *(lhs: String, rhs: Int) -> String {
        return String(repeating: lhs, count: rhs)
    }
}

/// ProgressionReader 功能演示
func demonstrateProgressionReader() {
    print("🚀 开始 ProgressionReader 演示")
    
    let progressionReader = ProgressionReader()
    
    // 等待扫描完成
    DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
        print("📋 发现的和弦进行文件: \(progressionReader.availableProgressions)")
        
        // 加载演示文件
        progressionReader.loadProgression("demo")
        
        // 等待加载完成
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            if let progression = progressionReader.currentProgression {
                print("✅ 成功加载: \(progression.name)")
                print("   调性: \(progression.key ?? "未指定")")
                print("   和弦数量: \(progression.chords.count)")
                
                // 测试获取和弦信息
                for (index, chord) in progression.chords.enumerated() {
                    let name = chord.getChordName()
                    let notes = chord.getMIDINotes()
                    print("   和弦 \(index + 1): \(name) - \(notes)")
                }
                
                // 测试移调功能
                print("\n🎵 测试移调功能（+5半音）")
                for (index, chord) in progression.chords.enumerated() {
                    let name = chord.getChordName(transpositionSemitones: 5)
                    let notes = chord.getMIDINotes(transpositionSemitones: 5)
                    print("   移调后和弦 \(index + 1): \(name) - \(notes)")
                }
                
                print("✅ ProgressionReader 演示完成")
                
                // 测试 ProgressionGameManager 集成
                testProgressionGameManagerIntegration(progression: progression)
            } else {
                print("❌ 未能加载和弦进行")
            }
        }
    }
}

/// 测试 ProgressionGameManager 集成
func testProgressionGameManagerIntegration(progression: Progression) {
    print("\n🎮 开始 ProgressionGameManager 集成测试")
    
    // 创建测试用的 MIDIManager 和 GameConfig
    let midiManager = MIDIManager()
    let gameConfig = GameConfig(
        gameSettings: GameSettings(
            responseTime: 3.0,
            chordsWaitInterval: 5.0,
            showTargetNotes: true,
            particleEffects: true
        ),
        practiceSettings: PracticeSettings(
            selectedPracticeGroup: .major,
            availablePracticeGroups: []
        )
    )
    
    let progressionGameManager = ProgressionGameManager(midiManager: midiManager, gameConfig: gameConfig)
    
    // 测试直接使用 progression 对象
    print("🎮 测试开始游戏...")
    progressionGameManager.startGame(
        with: progression,
        playbackType: .block_chord,
        bpm: 100,
        keepHighlighted: true,
        endlessMode: false,
        practiceMode: false,
        foreverWait: true
    )
    
    // 检查游戏状态
    print("🎮 游戏状态: \(progressionGameManager.gameState)")
    print("🎮 当前进行: \(progressionGameManager.currentProgression?.name ?? "未设置")")
    print("🎮 总和弦数: \(progressionGameManager.totalChordCount)")
    
    // 测试移调功能（练习模式）
    print("\n🎵 测试练习模式（移调功能）...")
    progressionGameManager.stopGame()
    
    progressionGameManager.startGame(
        with: progression,
        playbackType: .block_chord,
        bpm: 100,
        keepHighlighted: true,
        endlessMode: false,
        practiceMode: true,  // 启用练习模式
        foreverWait: true
    )
    
    print("🎮 练习模式游戏状态: \(progressionGameManager.gameState)")
    print("🎮 练习模式当前进行: \(progressionGameManager.currentProgression?.name ?? "未设置")")
    
    print("✅ ProgressionGameManager 集成测试完成")
} 
