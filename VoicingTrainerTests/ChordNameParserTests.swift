//
//  ChordNameParserTests.swift
//  VoicingTrainerTests
//
//  Created by AI Assistant on 2025/07/06.
//

import XCTest
@testable import VoicingTrainer

/// ChordNameParser 专门的单元测试类
/// 测试和弦名称解析的各种情况
class ChordNameParserTests: XCTestCase {

    override func setUpWithError() throws {
        // 每个测试前执行的设置
        print("🧪 开始 ChordNameParser 测试")
    }

    override func tearDownWithError() throws {
        // 每个测试后执行的清理
        print("🧪 结束 ChordNameParser 测试")
    }

    // MARK: - 基本三和弦测试
    
    func testMajorChord() {
        let result = ChordNameParser.analyze(midiNotes: [60, 64, 67]) // C E G
        XCTAssertFalse(result.isEmpty, "大三和弦应该返回和弦名称")
        XCTAssertTrue(result.contains { $0.contains("C") }, "应该识别为C和弦")
        XCTAssertTrue(result.contains("CM") || result.contains("C"), "应该包含CM或C")
        print("✅ 大三和弦测试通过: \(result)")
    }
    
    func testMinorChord() {
        let result = ChordNameParser.analyze(midiNotes: [60, 63, 67]) // C Eb G
        XCTAssertFalse(result.isEmpty, "小三和弦应该返回和弦名称")
        XCTAssertTrue(result.contains { $0.contains("C") && $0.contains("m") }, "应该识别为Cm和弦")
        print("✅ 小三和弦测试通过: \(result)")
    }
    
    func testDiminishedChord() {
        let result = ChordNameParser.analyze(midiNotes: [60, 63, 66]) // C Eb Gb
        XCTAssertFalse(result.isEmpty, "减三和弦应该返回和弦名称")
        XCTAssertTrue(result.contains { $0.contains("C") && ($0.contains("dim") || $0.contains("o")) }, "应该识别为Cdim或Co和弦")
        print("✅ 减三和弦测试通过: \(result)")
    }
    
    func testAugmentedChord() {
        let result = ChordNameParser.analyze(midiNotes: [60, 64, 68]) // C E G#
        XCTAssertFalse(result.isEmpty, "增三和弦应该返回和弦名称")
        XCTAssertTrue(result.contains { $0.contains("C") && ($0.contains("aug") || $0.contains("+")) }, "应该识别为Caug或C+和弦")
        print("✅ 增三和弦测试通过: \(result)")
    }
    
    // MARK: - 七和弦测试
    
    func testDominantSeventhChord() {
        let result = ChordNameParser.analyze(midiNotes: [60, 64, 67, 70]) // C E G Bb
        XCTAssertFalse(result.isEmpty, "属七和弦应该返回和弦名称")
        XCTAssertTrue(result.contains { $0.contains("C") && $0.contains("7") }, "应该识别为C7和弦")
        print("✅ 属七和弦测试通过: \(result)")
    }
    
    func testMajorSeventhChord() {
        let result = ChordNameParser.analyze(midiNotes: [60, 64, 67, 71]) // C E G B
        XCTAssertFalse(result.isEmpty, "大七和弦应该返回和弦名称")
        XCTAssertTrue(result.contains { $0.contains("C") && ($0.contains("M7") || $0.contains("maj7")) }, "应该识别为CM7或Cmaj7和弦")
        print("✅ 大七和弦测试通过: \(result)")
    }
    
    func testMinorSeventhChord() {
        let result = ChordNameParser.analyze(midiNotes: [60, 63, 67, 70]) // C Eb G Bb
        XCTAssertFalse(result.isEmpty, "小七和弦应该返回和弦名称")
        XCTAssertTrue(result.contains { $0.contains("C") && $0.contains("m7") }, "应该识别为Cm7和弦")
        print("✅ 小七和弦测试通过: \(result)")
    }
    
    func testHalfDiminishedSeventhChord() {
        let result = ChordNameParser.analyze(midiNotes: [60, 63, 66, 70]) // C Eb Gb Bb
        XCTAssertFalse(result.isEmpty, "半减七和弦应该返回和弦名称")
        XCTAssertTrue(result.contains { $0.contains("C") && ($0.contains("m7b5") || $0.contains("ø")) }, "应该识别为Cm7b5或Cø和弦")
        print("✅ 半减七和弦测试通过: \(result)")
    }
    
    func testDiminishedSeventhChord() {
        let result = ChordNameParser.analyze(midiNotes: [60, 63, 66, 69]) // C Eb Gb A
        XCTAssertFalse(result.isEmpty, "减七和弦应该返回和弦名称")
        XCTAssertTrue(result.contains { $0.contains("C") && ($0.contains("dim7") || $0.contains("o7")) }, "应该识别为Cdim7或Co7和弦")
        print("✅ 减七和弦测试通过: \(result)")
    }
    
    // MARK: - 挂留和弦测试
    
    func testSus4Chord() {
        let result = ChordNameParser.analyze(midiNotes: [60, 65, 67]) // C F G
        XCTAssertFalse(result.isEmpty, "sus4和弦应该返回和弦名称")
        XCTAssertTrue(result.contains { $0.contains("C") && $0.contains("sus") }, "应该识别为Csus和弦")
        print("✅ sus4和弦测试通过: \(result)")
    }
    
    func testSus2Chord() {
        let result = ChordNameParser.analyze(midiNotes: [60, 62, 67]) // C D G
        XCTAssertFalse(result.isEmpty, "sus2和弦应该返回和弦名称")
        XCTAssertTrue(result.contains { $0.contains("C") && $0.contains("sus2") }, "应该识别为Csus2和弦")
        print("✅ sus2和弦测试通过: \(result)")
    }
    
    // MARK: - 九和弦测试
    
    func testMajorNinthChord() {
        let result = ChordNameParser.analyze(midiNotes: [60, 62, 64, 67, 71]) // C D E G B
        XCTAssertFalse(result.isEmpty, "大九和弦应该返回和弦名称")
        XCTAssertTrue(result.contains { $0.contains("C") && ($0.contains("M9") || $0.contains("maj9")) }, "应该识别为CM9或Cmaj9和弦")
        print("✅ 大九和弦测试通过: \(result)")
    }
    
    func testMinorNinthChord() {
        let result = ChordNameParser.analyze(midiNotes: [60, 62, 63, 67, 70]) // C D Eb G Bb
        XCTAssertFalse(result.isEmpty, "小九和弦应该返回和弦名称")
        XCTAssertTrue(result.contains { $0.contains("C") && $0.contains("m9") }, "应该识别为Cm9和弦")
        print("✅ 小九和弦测试通过: \(result)")
    }
    
    // MARK: - 转位和弦测试
    
    func testFirstInversion() {
        let result = ChordNameParser.analyze(midiNotes: [64, 67, 72]) // E G C
        XCTAssertFalse(result.isEmpty, "第一转位应该返回和弦名称")
        XCTAssertTrue(result.contains { $0.contains("C") && $0.contains("/E") }, "应该识别为转位和弦")
        print("✅ 第一转位测试通过: \(result)")
    }
    
    func testSecondInversion() {
        let result = ChordNameParser.analyze(midiNotes: [67, 72, 76]) // G C E
        XCTAssertFalse(result.isEmpty, "第二转位应该返回和弦名称")
        XCTAssertTrue(result.contains { $0.contains("C") && $0.contains("/G") }, "应该识别为转位和弦")
        print("✅ 第二转位测试通过: \(result)")
    }
    
    // MARK: - 边界情况测试
    
    func testSingleNote() {
        let result = ChordNameParser.analyze(midiNotes: [60]) // C
        XCTAssertFalse(result.isEmpty, "单音符应该返回音符名称")
        XCTAssertTrue(result.first?.contains("C") == true, "应该包含C音符")
        print("✅ 单音符测试通过: \(result)")
    }
    
    func testEmptyInput() {
        let result = ChordNameParser.analyze(midiNotes: [])
        XCTAssertTrue(result.isEmpty, "空输入应该返回空数组")
        print("✅ 空输入测试通过: \(result)")
    }
    
    func testUnknownChord() {
        let result = ChordNameParser.analyze(midiNotes: [60, 61, 62]) // C C# D
        // 这是一个不常见的音符组合，可能返回Unknown
        XCTAssertFalse(result.isEmpty, "即使是不常见的组合也应该返回一些结果")
        print("✅ 不常见组合测试通过: \(result)")
    }
    
    // MARK: - 不同八度测试
    
    func testDifferentOctaves() {
        let result1 = ChordNameParser.analyze(midiNotes: [48, 52, 55]) // C3 E3 G3
        let result2 = ChordNameParser.analyze(midiNotes: [60, 64, 67]) // C4 E4 G4
        let result3 = ChordNameParser.analyze(midiNotes: [72, 76, 79]) // C5 E5 G5
        
        XCTAssertFalse(result1.isEmpty, "C3和弦应该被识别")
        XCTAssertFalse(result2.isEmpty, "C4和弦应该被识别")
        XCTAssertFalse(result3.isEmpty, "C5和弦应该被识别")
        
        // 所有八度的相同和弦应该得到相同的根音识别
        XCTAssertTrue(result1.contains { $0.contains("C") }, "C3应该被识别为C和弦")
        XCTAssertTrue(result2.contains { $0.contains("C") }, "C4应该被识别为C和弦")
        XCTAssertTrue(result3.contains { $0.contains("C") }, "C5应该被识别为C和弦")
        
        print("✅ 不同八度测试通过:")
        print("  C3: \(result1)")
        print("  C4: \(result2)")
        print("  C5: \(result3)")
    }
    
    // MARK: - 复合和弦测试
    
    func testComplexChord() {
        let result = ChordNameParser.analyze(midiNotes: [60, 64, 67, 70, 74]) // C E G Bb D
        XCTAssertFalse(result.isEmpty, "复合和弦应该返回和弦名称")
        print("✅ 复合和弦测试通过: \(result)")
    }
    
    // MARK: - 性能测试
    
    func testPerformance() {
        measure {
            for _ in 0..<1000 {
                _ = ChordNameParser.analyze(midiNotes: [60, 64, 67, 70])
            }
        }
        print("✅ 性能测试通过")
    }
    
    // MARK: - 特殊情况测试
    
    func testUserReportedIssue() {
        // 测试用户报告的问题：C4 60, E4 64, G4 67 返回 Unknown
        let result = ChordNameParser.analyze(midiNotes: [60, 64, 67])
        XCTAssertFalse(result.isEmpty, "用户报告的问题：C4 E4 G4应该被识别")
        XCTAssertFalse(result.contains("Unknown"), "不应该返回Unknown")
        XCTAssertTrue(result.contains { $0.contains("C") }, "应该识别为C和弦")
        print("✅ 用户报告问题验证通过: \(result)")
    }
    
    // MARK: - 调试方法
    
    func testDebugOutput() {
        print("🔍 测试调试输出:")
        
        let testCases = [
            ([60, 64, 67], "C Major"),
            ([60, 63, 67], "C Minor"),
            ([60, 64, 67, 70], "C7"),
            ([60, 64, 67, 71], "C Major 7"),
            ([60, 65, 67], "C sus4"),
            ([60, 62, 67], "C sus2"),
            ([60, 63, 66], "C dim"),
            ([60, 64, 68], "C aug")
        ]
        
        for (midiNotes, description) in testCases {
            let result = ChordNameParser.analyze(midiNotes: midiNotes)
            print("  \(description): \(midiNotes) -> \(result)")
        }
    }
    
    // MARK: - 静态测试运行器
    
    /// 运行所有和弦解析测试
    static func runAllTests() {
        print("🧪 开始运行所有ChordNameParser测试...")
        
        let testInstance = ChordNameParserTests()
        testInstance.testMajorChord()
        testInstance.testMinorChord()
        testInstance.testDominantSeventhChord()
        testInstance.testMajorSeventhChord()
        testInstance.testMinorSeventhChord()
        testInstance.testSus4Chord()
        testInstance.testSus2Chord()
        testInstance.testUserReportedIssue()
        testInstance.testDifferentOctaves()
        testInstance.testDebugOutput()
        
        print("🧪 所有ChordNameParser测试完成")
    }
} 