//
//  SimpleTestRunner.swift
//  VoicingTrainerTests
//
//  Created by <PERSON> on 2025/5/26.
//  简单的测试运行脚本 - 可以在命令行中运行基本测试
//

import Foundation

/**
 * 简单测试运行器
 * 提供基础的测试功能验证，不依赖于Testing框架
 */
class SimpleTestRunner {
    
    /**
     * 运行所有基础测试
     */
    static func runBasicTests() {
        print("🎹 VoicingTrainer 基础功能测试开始")
        print("=" * 50)
        
        var totalTests = 0
        var passedTests = 0
        
        // 测试音符解析
        print("\n🎵 测试音符解析功能...")
        let (parseTotal, parsePassed) = testNoteParsingBasic()
        totalTests += parseTotal
        passedTests += parsePassed
        
        // 测试音符名称生成
        print("\n🎼 测试音符名称生成...")
        let (nameTotal, namePassed) = testNoteNameGeneration()
        totalTests += nameTotal
        passedTests += namePassed
        
        // 测试五度圈工具
        print("\n🔄 测试五度圈工具...")
        let (circleTotal, circlePassed) = testCircleOfFifths()
        totalTests += circleTotal
        passedTests += circlePassed
        
        // 测试音符生成器
        print("\n🎲 测试音符生成器...")
        let (generatorTotal, generatorPassed) = testNoteGenerator()
        totalTests += generatorTotal
        passedTests += generatorPassed
        
        // 测试和弦分析器
        print("\n🎹 测试和弦分析器...")
        let (analyzerTotal, analyzerPassed) = testChordAnalyzer()
        totalTests += analyzerTotal
        passedTests += analyzerPassed
        
        // 输出总结
        print("\n" + "=" * 50)
        print("📊 测试总结:")
        print("   总测试数: \(totalTests)")
        print("   通过测试: \(passedTests)")
        print("   失败测试: \(totalTests - passedTests)")
        print("   通过率: \(String(format: "%.1f", Double(passedTests) / Double(totalTests) * 100))%")
        
        if passedTests == totalTests {
            print("✅ 所有测试通过！")
        } else {
            print("❌ 有测试失败，请检查代码")
        }
    }
    
    // MARK: - 具体测试方法
    
    private static func testNoteParsingBasic() -> (total: Int, passed: Int) {
        let testCases = [
            ("C3", 48),
            ("C4", 60),
            ("C#3", 49),
            ("DB3", 49),
            ("F#4", 66),
            ("BB3", 58)
        ]
        
        var passed = 0
        
        for (noteString, expectedMidi) in testCases {
            let actualMidi = NoteParser.parseNoteToMidi(noteString)
            let success = actualMidi == expectedMidi
            let status = success ? "✅" : "❌"
            print("  \(status) \(noteString) -> \(actualMidi) (期望: \(expectedMidi))")
            if success { passed += 1 }
        }
        
        return (testCases.count, passed)
    }
    
    private static func testNoteNameGeneration() -> (total: Int, passed: Int) {
        let testCases = [
            (48, "C3"),
            (60, "C4"),
            (49, "C#3"),
            (61, "C#4"),
            (72, "C5")
        ]
        
        var passed = 0
        
        for (midiNote, expectedName) in testCases {
            let actualName = NoteNameGenerator.getNoteName(note: midiNote)
            let success = actualName == expectedName
            let status = success ? "✅" : "❌"
            print("  \(status) \(midiNote) -> \(actualName) (期望: \(expectedName))")
            if success { passed += 1 }
        }
        
        return (testCases.count, passed)
    }
    
    private static func testCircleOfFifths() -> (total: Int, passed: Int) {
        let expectedNames = ["C", "F", "Bb", "Eb", "Ab", "Db", "Gb", "B", "E", "A", "D", "G"]
        var passed = 0
        
        for (index, expectedName) in expectedNames.enumerated() {
            let actualName = CircleOfFifthsUtils.getRootName(for: index)
            let success = actualName == expectedName
            let status = success ? "✅" : "❌"
            print("  \(status) 索引 \(index) -> \(actualName) (期望: \(expectedName))")
            if success { passed += 1 }
        }
        
        return (expectedNames.count, passed)
    }
    
    private static func testNoteGenerator() -> (total: Int, passed: Int) {
        var passed = 0
        var total = 0
        
        // 测试白键限制
        let whiteKeyGenerator = RandomNoteGenerator(loKey: 60, hiKey: 72, whiteKeyOnly: true)
        let whiteKeys = [60, 62, 64, 65, 67, 69, 71, 72]
        
        var allWhiteKeys = true
        for _ in 0..<50 {
            let note = whiteKeyGenerator.generateRandomNote()
            if !whiteKeys.contains(note) {
                allWhiteKeys = false
                break
            }
        }
        
        total += 1
        let whiteKeyStatus = allWhiteKeys ? "✅" : "❌"
        print("  \(whiteKeyStatus) 白键限制测试")
        if allWhiteKeys { passed += 1 }
        
        // 测试范围限制
        let rangeGenerator = RandomNoteGenerator(loKey: 60, hiKey: 65, whiteKeyOnly: false)
        var allInRange = true
        for _ in 0..<50 {
            let note = rangeGenerator.generateRandomNote()
            if note < 60 || note > 65 {
                allInRange = false
                break
            }
        }
        
        total += 1
        let rangeStatus = allInRange ? "✅" : "❌"
        print("  \(rangeStatus) 范围限制测试")
        if allInRange { passed += 1 }
        
        return (total, passed)
    }
    
    private static func testChordAnalyzer() -> (total: Int, passed: Int) {
        let analyzer = ChordAnalyzerUtils()
        
        let testChords = [
            ([60, 64, 67], "C", "C大三和弦"),
            ([60, 63, 67], "Cm", "C小三和弦"),
            ([60, 64, 67, 71], "Cmaj7", "C大七和弦"),
            ([60, 64, 67, 70], "C7", "C属七和弦"),
            ([60, 65, 67], "Csus4", "C挂四和弦")
        ]
        
        var passed = 0
        
        for (notes, expectedChord, description) in testChords {
            let result = analyzer.analyze(midiNotes: notes)
            let found = result.contains { $0.contains(expectedChord) }
            let success = found
            let status = success ? "✅" : "❌"
            print("  \(status) \(description): \(result.joined(separator: ", "))")
            if success { passed += 1 }
        }
        
        return (testChords.count, passed)
    }
}

// MARK: - 字符串扩展
// 注意：String扩展已在TestRunner.swift中定义

// MARK: - 使用说明

/**
 * 使用方法：
 * 在代码中调用 SimpleTestRunner.runBasicTests() 来运行测试
 * 
 * 示例：
 * SimpleTestRunner.runBasicTests()
 */

// 注意：这个文件设计为在Xcode中作为类使用，不是独立脚本
// 要运行测试，请在代码中调用 SimpleTestRunner.runBasicTests() 