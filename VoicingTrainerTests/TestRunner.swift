//
//  TestRunner.swift
//  VoicingTrainerTests
//
//  Created by <PERSON> on 2025/5/26.
//  测试运行器 - 提供便捷的测试运行和结果输出功能
//

import Foundation

/**
 * 测试运行器
 * 提供便捷的方法来运行所有测试并输出结果
 */
class TestRunner {
    
    /**
     * 运行所有数据处理相关的测试
     * 这个方法可以在命令行中调用来验证所有功能
     */
    static func runAllTests() {
        print("🎹 VoicingTrainer 数据处理功能测试开始")
        print("=" * 50)
        
        // 测试音符解析功能
        print("\n🎵 测试音符解析功能...")
        testNoteParsingFunctions()
        
        // 测试音符名称生成功能
        print("\n🎼 测试音符名称生成功能...")
        testNoteNameGeneration()
        
        // 测试五度圈工具
        print("\n🔄 测试五度圈工具...")
        testCircleOfFifthsUtils()
        
        // 测试音符生成器
        print("\n🎲 测试音符生成器...")
        testRandomNoteGenerator()
        
        // 测试和弦分析器
        print("\n🎹 测试和弦分析器...")
        testChordAnalyzer()
        
        // 测试和弦配置
        print("\n⚙️ 测试和弦配置...")
        testChordVoicingData()
        
        // 运行集成测试
        print("\n🔗 运行集成测试...")
        testIntegration()
        
        print("\n" + "=" * 50)
        print("✅ 所有测试完成！")
    }
    
    // MARK: - 具体测试方法
    
    private static func testNoteParsingFunctions() {
        let testCases = [
            ("C3", 48),
            ("C4", 60),
            ("C#3", 49),
            ("DB3", 49),
            ("F#4", 66),
            ("BB3", 58)
        ]
        
        for (noteString, expectedMidi) in testCases {
            let actualMidi = NoteParser.parseNoteToMidi(noteString)
            let status = actualMidi == expectedMidi ? "✅" : "❌"
            print("  \(status) \(noteString) -> \(actualMidi) (期望: \(expectedMidi))")
        }
    }
    
    private static func testNoteNameGeneration() {
        let testCases = [
            (48, "C3"),
            (60, "C4"),
            (49, "C#3"),
            (61, "C#4"),
            (72, "C5")
        ]
        
        for (midiNote, expectedName) in testCases {
            let actualName = NoteNameGenerator.getNoteName(note: midiNote)
            let status = actualName == expectedName ? "✅" : "❌"
            print("  \(status) \(midiNote) -> \(actualName) (期望: \(expectedName))")
        }
    }
    
    private static func testCircleOfFifthsUtils() {
        let expectedNames = ["C", "F", "Bb", "Eb", "Ab", "Db", "Gb", "B", "E", "A", "D", "G"]
        
        for (index, expectedName) in expectedNames.enumerated() {
            let actualName = CircleOfFifthsUtils.getRootName(for: index)
            let status = actualName == expectedName ? "✅" : "❌"
            print("  \(status) 索引 \(index) -> \(actualName) (期望: \(expectedName))")
        }
    }
    
    private static func testRandomNoteGenerator() {
        // 测试白键限制
        let whiteKeyGenerator = RandomNoteGenerator(loKey: 60, hiKey: 72, whiteKeyOnly: true)
        let whiteKeys = [60, 62, 64, 65, 67, 69, 71, 72]
        
        var allWhiteKeys = true
        for _ in 0..<50 {
            let note = whiteKeyGenerator.generateRandomNote()
            if !whiteKeys.contains(note) {
                allWhiteKeys = false
                break
            }
        }
        
        let whiteKeyStatus = allWhiteKeys ? "✅" : "❌"
        print("  \(whiteKeyStatus) 白键限制测试")
        
        // 测试范围限制
        let rangeGenerator = RandomNoteGenerator(loKey: 60, hiKey: 65, whiteKeyOnly: false)
        var allInRange = true
        for _ in 0..<50 {
            let note = rangeGenerator.generateRandomNote()
            if note < 60 || note > 65 {
                allInRange = false
                break
            }
        }
        
        let rangeStatus = allInRange ? "✅" : "❌"
        print("  \(rangeStatus) 范围限制测试")
    }
    
    private static func testChordAnalyzer() {
        let analyzer = ChordAnalyzerUtils()
        
        let testChords = [
            ([60, 64, 67], "C", "C大三和弦"),
            ([60, 63, 67], "Cm", "C小三和弦"),
            ([60, 64, 67, 71], "Cmaj7", "C大七和弦"),
            ([60, 64, 67, 70], "C7", "C属七和弦"),
            ([60, 65, 67], "Csus4", "C挂四和弦")
        ]
        
        for (notes, expectedChord, description) in testChords {
            let result = analyzer.analyze(midiNotes: notes)
            let found = result.contains { $0.contains(expectedChord) }
            let status = found ? "✅" : "❌"
            print("  \(status) \(description): \(result.joined(separator: ", "))")
        }
    }
    
    private static func testChordVoicingData() {
        let testVoicings = [
            ChordVoicingData(id: "test1", name: "Test C3", type: "major", base_note: "C3", intervals: [0, 4, 7], description: "Test"),
            ChordVoicingData(id: "test2", name: "Test C4", type: "major", base_note: "C4", intervals: [0, 4, 7], description: "Test"),
            ChordVoicingData(id: "test3", name: "Test F#2", type: "major", base_note: "F#2", intervals: [0, 4, 7], description: "Test")
        ]
        
        let expectedMidi = [48, 60, 42]  // C3, C4, F#2
        
        for (index, voicing) in testVoicings.enumerated() {
            let actualMidi = voicing.baseMidiNote
            let expected = expectedMidi[index]
            let status = actualMidi == expected ? "✅" : "❌"
            print("  \(status) \(voicing.base_note) -> \(actualMidi) (期望: \(expected))")
        }
    }
    
    private static func testIntegration() {
        // 测试完整的和弦游戏流程
        let voicing = ChordVoicingData(
            id: "integration_test",
            name: "Integration Test Triad",
            type: "major",
            base_note: "C3",
            intervals: [0, 4, 7],
            description: "Integration test"
        )
        
        let analyzer = ChordAnalyzerUtils()
        var allTestsPassed = true
        
        // 测试前3个调
        for rootIndex in 0..<3 {
            let baseRootNote = voicing.baseMidiNote
            let rootPitchClass = CircleOfFifthsUtils.reversedOrder[rootIndex % 12]
            let basePitchClass = baseRootNote % 12
            
            let intervalDifference = (rootPitchClass - basePitchClass + 12) % 12
            let rootNote = baseRootNote + intervalDifference
            
            let chordNotes = voicing.intervals.map { rootNote + $0 }
            let analyzedChords = analyzer.analyze(midiNotes: chordNotes)
            
            if analyzedChords.isEmpty {
                allTestsPassed = false
                break
            }
            
            let expectedRootName = CircleOfFifthsUtils.getRootName(for: rootIndex)
            let hasCorrectRoot = analyzedChords.contains { $0.hasPrefix(expectedRootName) }
            
            if !hasCorrectRoot {
                allTestsPassed = false
                break
            }
        }
        
        let status = allTestsPassed ? "✅" : "❌"
        print("  \(status) 完整和弦游戏流程测试")
    }
}

// MARK: - 字符串扩展

extension String {
    static func *(lhs: String, rhs: Int) -> String {
        return String(repeating: lhs, count: rhs)
    }
}

// MARK: - 命令行入口

/**
 * 命令行测试入口
 * 可以通过以下方式运行：
 * 在Xcode中运行测试，或者调用 TestRunner.runAllTests()
 */ 