//
//  FullUITests.swift
//  FullUITests
//
//  Created by <PERSON> on 2025/5/26.
//  FullUI项目的单元测试 - 测试音符解析、和弦分析、音符生成等核心功能
//

import Testing
import Foundation

// MARK: - 音符解析测试

struct NoteParsingTests {
    
    /**
     * 测试基础音符解析功能
     * 验证常见音符名称到MIDI值的转换是否正确
     */
    @Test("基础音符解析测试")
    func testBasicNoteParsing() async throws {
        // 测试基础音符
        #expect(NoteParser.parseNoteToMidi("C3") == 48)
        #expect(NoteParser.parseNoteToMidi("C4") == 60)  // Middle C
        #expect(NoteParser.parseNoteToMidi("C5") == 72)
        
        // 测试其他音符
        #expect(NoteParser.parseNoteToMidi("D3") == 50)
        #expect(NoteParser.parseNoteToMidi("E3") == 52)
        #expect(NoteParser.parseNoteToMidi("F3") == 53)
        #expect(NoteParser.parseNoteToMidi("G3") == 55)
        #expect(NoteParser.parseNoteToMidi("A3") == 57)
        #expect(NoteParser.parseNoteToMidi("B3") == 59)
    }
    
    /**
     * 测试升号音符解析
     * 验证带升号的音符名称解析是否正确
     */
    @Test("升号音符解析测试")
    func testSharpNoteParsing() async throws {
        #expect(NoteParser.parseNoteToMidi("C#3") == 49)
        #expect(NoteParser.parseNoteToMidi("D#3") == 51)
        #expect(NoteParser.parseNoteToMidi("F#3") == 54)
        #expect(NoteParser.parseNoteToMidi("G#3") == 56)
        #expect(NoteParser.parseNoteToMidi("A#3") == 58)
    }
    
    /**
     * 测试降号音符解析
     * 验证带降号的音符名称解析是否正确
     */
    @Test("降号音符解析测试")
    func testFlatNoteParsing() async throws {
        #expect(NoteParser.parseNoteToMidi("DB3") == 49)  // Db = C#
        #expect(NoteParser.parseNoteToMidi("EB3") == 51)  // Eb = D#
        #expect(NoteParser.parseNoteToMidi("GB3") == 54)  // Gb = F#
        #expect(NoteParser.parseNoteToMidi("AB3") == 56)  // Ab = G#
        #expect(NoteParser.parseNoteToMidi("BB3") == 58)  // Bb = A#
    }
    
    /**
     * 测试不同八度的音符解析
     * 验证八度变化对MIDI值的影响
     */
    @Test("八度音符解析测试")
    func testOctaveNoteParsing() async throws {
        // C音符在不同八度
        #expect(NoteParser.parseNoteToMidi("C0") == 12)
        #expect(NoteParser.parseNoteToMidi("C1") == 24)
        #expect(NoteParser.parseNoteToMidi("C2") == 36)
        #expect(NoteParser.parseNoteToMidi("C3") == 48)
        #expect(NoteParser.parseNoteToMidi("C4") == 60)
        #expect(NoteParser.parseNoteToMidi("C5") == 72)
        #expect(NoteParser.parseNoteToMidi("C6") == 84)
        #expect(NoteParser.parseNoteToMidi("C7") == 96)
    }
    
    /**
     * 测试大小写不敏感
     * 验证音符名称的大小写处理
     */
    @Test("大小写不敏感测试")
    func testCaseInsensitive() async throws {
        #expect(NoteParser.parseNoteToMidi("c3") == 48)
        #expect(NoteParser.parseNoteToMidi("C3") == 48)
        #expect(NoteParser.parseNoteToMidi("c#3") == 49)
        #expect(NoteParser.parseNoteToMidi("C#3") == 49)
    }
}

// MARK: - 音符名称生成测试

struct NoteNameGenerationTests {
    
    /**
     * 测试MIDI值到音符名称的转换
     * 验证MIDI值正确转换为音符名称字符串
     */
    @Test("MIDI到音符名称转换测试")
    func testMidiToNoteName() async throws {
        // 测试基础音符
        #expect(NoteNameGenerator.getNoteName(note: 48) == "C3")
        #expect(NoteNameGenerator.getNoteName(note: 60) == "C4")  // Middle C
        #expect(NoteNameGenerator.getNoteName(note: 72) == "C5")
        
        // 测试升号音符
        #expect(NoteNameGenerator.getNoteName(note: 49) == "C#3")
        #expect(NoteNameGenerator.getNoteName(note: 61) == "C#4")
        
        // 测试其他音符
        #expect(NoteNameGenerator.getNoteName(note: 50) == "D3")
        #expect(NoteNameGenerator.getNoteName(note: 52) == "E3")
        #expect(NoteNameGenerator.getNoteName(note: 53) == "F3")
    }
    
    /**
     * 测试UInt8和Int版本的一致性
     * 验证两个版本的函数返回相同结果
     */
    @Test("UInt8和Int版本一致性测试")
    func testUInt8IntConsistency() async throws {
        for midi in 0...127 {
            let uint8Result = NoteNameGenerator.getNoteName(note: UInt8(midi))
            let intResult = NoteNameGenerator.getNoteName(note: midi)
            #expect(uint8Result == intResult)
        }
    }
}

// MARK: - 五度圈工具测试

struct CircleOfFifthsTests {
    
    /**
     * 测试五度圈顺序
     * 验证五度圈的反向顺序是否正确
     */
    @Test("五度圈顺序测试")
    func testCircleOfFifthsOrder() async throws {
        let expectedOrder = [0, 5, 10, 3, 8, 1, 6, 11, 4, 9, 2, 7]  // C, F, Bb, Eb, Ab, Db, Gb, B, E, A, D, G
        #expect(CircleOfFifthsUtils.reversedOrder == expectedOrder)
    }
    
    /**
     * 测试根音名称获取
     * 验证五度圈索引对应的根音名称
     */
    @Test("根音名称获取测试")
    func testRootNameGeneration() async throws {
        let expectedNames = ["C", "F", "Bb", "Eb", "Ab", "Db", "Gb", "B", "E", "A", "D", "G"]
        
        for (index, expectedName) in expectedNames.enumerated() {
            let actualName = CircleOfFifthsUtils.getRootName(for: index)
            #expect(actualName == expectedName, "Index \(index): expected \(expectedName), got \(actualName)")
        }
    }
    
    /**
     * 测试根音MIDI值计算
     * 验证五度圈索引对应的MIDI值计算
     */
    @Test("根音MIDI值计算测试")
    func testRootNoteCalculation() async throws {
        // 测试C3八度（baseOctave = 3）
        #expect(CircleOfFifthsUtils.getRootNote(for: 0, baseOctave: 3) == 48)  // C3
        #expect(CircleOfFifthsUtils.getRootNote(for: 1, baseOctave: 3) == 53)  // F3
        #expect(CircleOfFifthsUtils.getRootNote(for: 2, baseOctave: 3) == 58)  // Bb3
        
        // 测试C4八度（baseOctave = 4）
        #expect(CircleOfFifthsUtils.getRootNote(for: 0, baseOctave: 4) == 60)  // C4
        #expect(CircleOfFifthsUtils.getRootNote(for: 1, baseOctave: 4) == 65)  // F4
    }
    
    /**
     * 测试索引循环
     * 验证超出范围的索引是否正确循环
     */
    @Test("索引循环测试")
    func testIndexWrapping() async throws {
        // 测试索引12应该等于索引0
        #expect(CircleOfFifthsUtils.getRootName(for: 12) == CircleOfFifthsUtils.getRootName(for: 0))
        #expect(CircleOfFifthsUtils.getRootName(for: 13) == CircleOfFifthsUtils.getRootName(for: 1))
        
        // 测试负索引（虽然实际使用中不会出现）
        #expect(CircleOfFifthsUtils.getRootNote(for: -1, baseOctave: 3) == CircleOfFifthsUtils.getRootNote(for: 11, baseOctave: 3))
    }
}

// MARK: - 音符生成器测试

struct RandomNoteGeneratorTests {
    
    /**
     * 测试音符范围限制
     * 验证生成的音符是否在指定范围内
     */
    @Test("音符范围限制测试")
    func testNoteRangeConstraints() async throws {
        let generator = RandomNoteGenerator(loKey: 60, hiKey: 72, whiteKeyOnly: false)
        
        // 生成100个音符，验证都在范围内
        for _ in 0..<100 {
            let note = generator.generateRandomNote()
            #expect(note >= 60 && note <= 72, "Generated note \(note) is out of range [60, 72]")
        }
    }
    
    /**
     * 测试白键限制
     * 验证白键模式下只生成白键音符
     */
    @Test("白键限制测试")
    func testWhiteKeyOnly() async throws {
        let generator = RandomNoteGenerator(loKey: 60, hiKey: 72, whiteKeyOnly: true)
        let whiteKeys = [60, 62, 64, 65, 67, 69, 71, 72]  // C4, D4, E4, F4, G4, A4, B4, C5
        
        // 生成100个音符，验证都是白键
        for _ in 0..<100 {
            let note = generator.generateRandomNote()
            #expect(whiteKeys.contains(note), "Generated note \(note) is not a white key")
        }
    }
    
    /**
     * 测试全音符模式
     * 验证全音符模式下可以生成黑键
     */
    @Test("全音符模式测试")
    func testAllNotesMode() async throws {
        let generator = RandomNoteGenerator(loKey: 60, hiKey: 65, whiteKeyOnly: false)
        var generatedNotes = Set<Int>()
        
        // 生成足够多的音符，应该包含黑键
        for _ in 0..<1000 {
            let note = generator.generateRandomNote()
            generatedNotes.insert(note)
        }
        
        // 验证包含黑键（61是C#4，63是D#4）
        let hasBlackKeys = generatedNotes.contains(61) || generatedNotes.contains(63)
        #expect(hasBlackKeys, "All notes mode should generate black keys")
    }
}

// MARK: - 和弦分析器测试

struct ChordAnalyzerTests {
    
    /**
     * 测试基础三和弦识别
     * 验证大三和弦和小三和弦的识别
     */
    @Test("基础三和弦识别测试")
    func testBasicTriadRecognition() async throws {
        let analyzer = ChordAnalyzerUtils()
        
        // C大三和弦 (C, E, G)
        let cMajor = analyzer.analyze(midiNotes: [60, 64, 67])
        #expect(cMajor.contains("C"), "Should recognize C major chord")
        
        // C小三和弦 (C, Eb, G)
        let cMinor = analyzer.analyze(midiNotes: [60, 63, 67])
        #expect(cMinor.contains("Cm"), "Should recognize C minor chord")
        
        // F大三和弦 (F, A, C)
        let fMajor = analyzer.analyze(midiNotes: [65, 69, 72])
        #expect(fMajor.contains("F"), "Should recognize F major chord")
    }
    
    /**
     * 测试七和弦识别
     * 验证各种七和弦的识别
     */
    @Test("七和弦识别测试")
    func testSeventhChordRecognition() async throws {
        let analyzer = ChordAnalyzerUtils()
        
        // C大七和弦 (C, E, G, B)
        let cMaj7 = analyzer.analyze(midiNotes: [60, 64, 67, 71])
        #expect(cMaj7.contains("Cmaj7") || cMaj7.contains("CM7"), "Should recognize C major 7th chord")
        
        // C属七和弦 (C, E, G, Bb)
        let c7 = analyzer.analyze(midiNotes: [60, 64, 67, 70])
        #expect(c7.contains("C7"), "Should recognize C dominant 7th chord")
        
        // C小七和弦 (C, Eb, G, Bb)
        let cm7 = analyzer.analyze(midiNotes: [60, 63, 67, 70])
        #expect(cm7.contains("Cm7"), "Should recognize C minor 7th chord")
    }
    
    /**
     * 测试挂留和弦识别
     * 验证sus2和sus4和弦的识别
     */
    @Test("挂留和弦识别测试")
    func testSuspendedChordRecognition() async throws {
        let analyzer = ChordAnalyzerUtils()
        
        // Csus4 (C, F, G)
        let csus4 = analyzer.analyze(midiNotes: [60, 65, 67])
        #expect(csus4.contains("Csus4"), "Should recognize C sus4 chord")
        
        // Csus2 (C, D, G)
        let csus2 = analyzer.analyze(midiNotes: [60, 62, 67])
        #expect(csus2.contains("Csus2"), "Should recognize C sus2 chord")
    }
    
    /**
     * 测试转位和弦识别
     * 验证和弦转位的识别能力
     */
    @Test("转位和弦识别测试")
    func testInversionRecognition() async throws {
        let analyzer = ChordAnalyzerUtils()
        
        // C大三和弦的不同转位
        let rootPosition = analyzer.analyze(midiNotes: [60, 64, 67])  // C, E, G
        let firstInversion = analyzer.analyze(midiNotes: [64, 67, 72])  // E, G, C
        let secondInversion = analyzer.analyze(midiNotes: [67, 72, 76])  // G, C, E
        
        // 所有转位都应该被识别为某种形式的C和弦
        #expect(!rootPosition.isEmpty, "Root position should be recognized")
        #expect(!firstInversion.isEmpty, "First inversion should be recognized")
        #expect(!secondInversion.isEmpty, "Second inversion should be recognized")
    }
    
    /**
     * 测试空输入处理
     * 验证空输入的处理
     */
    @Test("空输入处理测试")
    func testEmptyInputHandling() async throws {
        let analyzer = ChordAnalyzerUtils()
        
        let emptyResult = analyzer.analyze(midiNotes: [])
        #expect(emptyResult.isEmpty, "Empty input should return empty result")
    }
}

// MARK: - 和弦配置测试

struct ChordVoicingTests {
    
    /**
     * 测试和弦配置数据模型
     * 验证和弦配置的解析和计算
     */
    @Test("和弦配置数据模型测试")
    func testChordVoicingDataModel() async throws {
        // 创建测试和弦配置
        let triad = ChordVoicingData(
            id: "test_triad",
            name: "Test Major Triad",
            type: "major",
            base_note: "C3",
            intervals: [0, 4, 7],
            description: "Test chord"
        )
        
        // 验证base_note解析
        #expect(triad.baseMidiNote == 48, "C3 should be MIDI note 48")
        
        // 测试不同的base_note
        let triadC4 = ChordVoicingData(
            id: "test_triad_c4",
            name: "Test Major Triad C4",
            type: "major",
            base_note: "C4",
            intervals: [0, 4, 7],
            description: "Test chord"
        )
        
        #expect(triadC4.baseMidiNote == 60, "C4 should be MIDI note 60")
    }
    
    /**
     * 测试和弦计算逻辑
     * 验证基于base_note和intervals的和弦音符计算
     */
    @Test("和弦计算逻辑测试")
    func testChordCalculationLogic() async throws {
        let triad = ChordVoicingData(
            id: "test_triad",
            name: "Test Major Triad",
            type: "major",
            base_note: "C3",
            intervals: [0, 4, 7],
            description: "Test chord"
        )
        
        // 模拟和弦游戏中的计算逻辑
        for currentRootIndex in 0..<3 {
            let baseRootNote = triad.baseMidiNote
            let rootPitchClass = CircleOfFifthsUtils.reversedOrder[currentRootIndex % 12]
            let basePitchClass = baseRootNote % 12
            
            let intervalDifference = (rootPitchClass - basePitchClass + 12) % 12
            let rootNote = baseRootNote + intervalDifference
            
            let chordNotes = triad.intervals.map { rootNote + $0 }
            
            // 验证和弦音符数量正确
            #expect(chordNotes.count == 3, "Triad should have 3 notes")
            
            // 验证音符在合理范围内
            for note in chordNotes {
                #expect(note >= 0 && note <= 127, "MIDI note \(note) should be in valid range")
            }
        }
    }
}

// MARK: - 集成测试

struct IntegrationTests {
    
    /**
     * 测试音符解析和名称生成的往返转换
     * 验证解析和生成函数的一致性
     */
    @Test("音符解析和生成往返测试")
    func testNoteParsingRoundTrip() async throws {
        let testNotes = ["C3", "C#3", "D3", "Eb3", "E3", "F3", "F#3", "G3", "Ab3", "A3", "Bb3", "B3", "C4"]
        
        for noteString in testNotes {
            let midiValue = NoteParser.parseNoteToMidi(noteString)
            let generatedName = NoteNameGenerator.getNoteName(note: midiValue)
            
            // 注意：降号会被转换为升号，所以需要特殊处理
            let normalizedInput = noteString.replacingOccurrences(of: "B", with: "#")
                .replacingOccurrences(of: "E#", with: "F")
                .replacingOccurrences(of: "B#", with: "C")
            
            if !normalizedInput.contains("b") {
                #expect(generatedName.uppercased() == normalizedInput.uppercased(), 
                       "Round trip failed: \(noteString) -> \(midiValue) -> \(generatedName)")
            }
        }
    }
    
    /**
     * 测试完整的和弦游戏流程
     * 验证从和弦配置到音符生成的完整流程
     */
    @Test("完整和弦游戏流程测试")
    func testCompleteChordGameFlow() async throws {
        // 创建测试和弦配置
        let testVoicings = [
            ChordVoicingData(id: "triad", name: "Major Triad", type: "major", base_note: "C3", intervals: [0, 4, 7], description: "Test"),
            ChordVoicingData(id: "seventh", name: "Major 7th", type: "maj7", base_note: "C2", intervals: [0, 4, 7, 11], description: "Test")
        ]
        
        let analyzer = ChordAnalyzerUtils()
        
        for voicing in testVoicings {
            // 模拟游戏中的12个调
            for rootIndex in 0..<12 {
                let baseRootNote = voicing.baseMidiNote
                let rootPitchClass = CircleOfFifthsUtils.reversedOrder[rootIndex % 12]
                let basePitchClass = baseRootNote % 12
                
                let intervalDifference = (rootPitchClass - basePitchClass + 12) % 12
                let rootNote = baseRootNote + intervalDifference
                
                let chordNotes = voicing.intervals.map { rootNote + $0 }
                
                // 验证生成的和弦可以被分析器识别
                let analyzedChords = analyzer.analyze(midiNotes: chordNotes)
                #expect(!analyzedChords.isEmpty, "Generated chord should be recognizable by analyzer")
                
                // 验证根音名称正确
                let expectedRootName = CircleOfFifthsUtils.getRootName(for: rootIndex)
                let hasCorrectRoot = analyzedChords.contains { chord in
                    chord.hasPrefix(expectedRootName)
                }
                #expect(hasCorrectRoot, "Analyzed chord should have correct root: \(expectedRootName)")
            }
        }
    }
}

// MARK: - 性能测试

struct PerformanceTests {
    
    /**
     * 测试音符解析性能
     * 验证大量音符解析的性能
     */
    @Test("音符解析性能测试")
    func testNoteParsingPerformance() async throws {
        let testNotes = ["C3", "C#3", "D3", "Eb3", "E3", "F3", "F#3", "G3", "Ab3", "A3", "Bb3", "B3"]
        
        let startTime = Date()
        
        // 解析10000次
        for _ in 0..<10000 {
            for note in testNotes {
                _ = NoteParser.parseNoteToMidi(note)
            }
        }
        
        let endTime = Date()
        let duration = endTime.timeIntervalSince(startTime)
        
        // 应该在1秒内完成
        #expect(duration < 1.0, "Note parsing should complete within 1 second, took \(duration)s")
    }
    
    /**
     * 测试和弦分析性能
     * 验证大量和弦分析的性能
     */
    @Test("和弦分析性能测试")
    func testChordAnalysisPerformance() async throws {
        let analyzer = ChordAnalyzerUtils()
        let testChords = [
            [60, 64, 67],      // C major
            [60, 63, 67],      // C minor
            [60, 64, 67, 71],  // C maj7
            [60, 64, 67, 70],  // C7
            [60, 65, 67],      // C sus4
        ]
        
        let startTime = Date()
        
        // 分析1000次
        for _ in 0..<1000 {
            for chord in testChords {
                _ = analyzer.analyze(midiNotes: chord)
            }
        }
        
        let endTime = Date()
        let duration = endTime.timeIntervalSince(startTime)
        
        // 应该在1秒内完成
        #expect(duration < 1.0, "Chord analysis should complete within 1 second, took \(duration)s")
    }
}
