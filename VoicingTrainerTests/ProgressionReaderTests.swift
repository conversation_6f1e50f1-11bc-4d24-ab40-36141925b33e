import XCTest
import Foundation
@testable import VoicingTrainer

/// ProgressionReader重构后的单元测试
final class ProgressionReaderTests: XCTestCase {
    
    var progressionReader: ProgressionReader!
    var documentsURL: URL!
    var userProgressionsURL: URL!
    
    override func setUpWithError() throws {
        // 初始化测试环境
        progressionReader = ProgressionReader()
        
        // 设置测试目录
        let fileManager = FileManager.default
        documentsURL = fileManager.urls(for: .documentDirectory, in: .userDomainMask).first!
        userProgressionsURL = documentsURL.appendingPathComponent("UserProgressions")
        
        // 确保测试目录存在
        if !fileManager.fileExists(atPath: userProgressionsURL.path) {
            try fileManager.createDirectory(at: userProgressionsURL, withIntermediateDirectories: true)
        }
        
        // 创建测试用的demo.progression文件
        try createTestProgressionFile()
    }
    
    override func tearDownWithError() throws {
        // 清理测试环境
        progressionReader = nil
        
        // 删除测试文件
        let fileManager = FileManager.default
        let testFileURL = userProgressionsURL.appendingPathComponent("test-demo.progression")
        if fileManager.fileExists(atPath: testFileURL.path) {
            try fileManager.removeItem(at: testFileURL)
        }
    }
    
    /// 创建测试用的和弦进行文件
    private func createTestProgressionFile() throws {
        let testProgression = """
        {
          "progression": {
            "name": "Test Jazz 2-5-1",
            "style": "Jazz", 
            "key": "CM",
            "chords": [
              {
                "suffix": "m7",
                "root": "D3",
                "notes": [0, 3, 7, 10]
              },
              {
                "suffix": "7",
                "root": "G3", 
                "notes": [0, 4, 7, 10]
              },
              {
                "suffix": "M7",
                "root": "C3",
                "notes": [0, 4, 7, 11]
              }
            ]
          }
        }
        """
        
        let testFileURL = userProgressionsURL.appendingPathComponent("test-demo.progression")
        try testProgression.write(to: testFileURL, atomically: true, encoding: .utf8)
        
        // 刷新可用文件列表
        progressionReader.refreshAvailableProgressions()
    }
    
    // MARK: - 测试加载功能
    
    func testLoadProgression() throws {
        // 测试加载和弦进行
        progressionReader.loadProgression("test-demo")
        
        // 等待异步加载完成
        let expectation = XCTestExpectation(description: "Load progression")
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            expectation.fulfill()
        }
        wait(for: [expectation], timeout: 1.0)
        
        // 验证加载结果
        XCTAssertNotNil(progressionReader.currentProgression, "应该成功加载和弦进行")
        XCTAssertEqual(progressionReader.currentProgression?.name, "Test Jazz 2-5-1")
        XCTAssertEqual(progressionReader.currentProgression?.key, "CM")
        XCTAssertEqual(progressionReader.currentProgression?.chords.count, 3)
        XCTAssertNil(progressionReader.errorMessage, "不应该有错误信息")
    }
    
    func testLoadNonExistentProgression() throws {
        // 测试加载不存在的文件
        progressionReader.loadProgression("non-existent")
        
        // 等待异步处理完成
        let expectation = XCTestExpectation(description: "Load non-existent progression")
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            expectation.fulfill()
        }
        wait(for: [expectation], timeout: 1.0)
        
        // 验证错误处理
        XCTAssertNotNil(progressionReader.errorMessage, "应该有错误信息")
        XCTAssertTrue(progressionReader.errorMessage!.contains("文件不存在"), "错误信息应该提示文件不存在")
    }
    
    // MARK: - 测试移调功能
    
    func testTransposition() throws {
        // 先加载和弦进行
        progressionReader.loadProgression("test-demo")
        
        let expectation = XCTestExpectation(description: "Load for transposition test")
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            expectation.fulfill()
        }
        wait(for: [expectation], timeout: 1.0)
        
        // 获取原始和弦名称
        let originalChordNames = progressionReader.chordNames
        XCTAssertEqual(originalChordNames, ["Dm7", "G7", "CM7"], "原始和弦名称应该正确")
        
        // 测试向上移调2个半音（全音）
        progressionReader.transposeFromBase(2)
        let transposedUpChordNames = progressionReader.chordNames
        XCTAssertEqual(transposedUpChordNames, ["Em7", "A7", "DM7"], "向上移调2半音后和弦名称应该正确")
        
        // 测试向下移调3个半音
        progressionReader.transposeFromBase(-3)
        let transposedDownChordNames = progressionReader.chordNames
        XCTAssertEqual(transposedDownChordNames, ["Bm7", "E7", "AM7"], "向下移调3半音后和弦名称应该正确")
        
        // 测试回到原调
        progressionReader.transposeFromBase(0)
        let backToOriginalChordNames = progressionReader.chordNames
        XCTAssertEqual(backToOriginalChordNames, ["Dm7", "G7", "CM7"], "回到原调后和弦名称应该正确")
    }
    
    func testTranspositionOutOfRange() throws {
        // 先加载和弦进行
        progressionReader.loadProgression("test-demo")
        
        let expectation = XCTestExpectation(description: "Load for range test")
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            expectation.fulfill()
        }
        wait(for: [expectation], timeout: 1.0)
        
        // 测试超出范围的移调（应该被拒绝）
        progressionReader.transposeFromBase(12)  // 超出范围
        progressionReader.transposeFromBase(-12) // 超出范围
        
        // 验证和弦名称没有改变（移调被拒绝）
        let chordNames = progressionReader.chordNames
        XCTAssertEqual(chordNames, ["Dm7", "G7", "CM7"], "超出范围的移调应该被拒绝")
    }
    
    // MARK: - 测试MIDI音符获取
    
    func testGetMIDINotes() throws {
        // 先加载和弦进行
        progressionReader.loadProgression("test-demo")
        
        let expectation = XCTestExpectation(description: "Load for MIDI test")
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            expectation.fulfill()
        }
        wait(for: [expectation], timeout: 1.0)
        
        // 测试获取第一个和弦的MIDI音符
        let firstChordNotes = progressionReader.getChordMIDINotes(at: 0)
        // D3 = 50, 加上音程偏移 [0, 3, 7, 10]
        let expectedFirstChordNotes = [50, 53, 57, 60]
        XCTAssertEqual(firstChordNotes, expectedFirstChordNotes, "第一个和弦的MIDI音符应该正确")
        
        // 测试移调后的MIDI音符
        progressionReader.transposeFromBase(2)
        let transposedFirstChordNotes = progressionReader.getChordMIDINotes(at: 0)
        let expectedTransposedNotes = [52, 55, 59, 62] // 加2个半音
        XCTAssertEqual(transposedFirstChordNotes, expectedTransposedNotes, "移调后的MIDI音符应该正确")
        
        // 测试所有和弦的MIDI音符
        let allChordNotes = progressionReader.allChordMIDINotes
        XCTAssertEqual(allChordNotes.count, 3, "应该有3个和弦的音符")
        XCTAssertEqual(allChordNotes[0], expectedTransposedNotes, "第一个和弦音符应该匹配")
    }
    
    // MARK: - 测试和弦信息获取
    
    func testChordInformation() throws {
        // 先加载和弦进行
        progressionReader.loadProgression("test-demo")
        
        let expectation = XCTestExpectation(description: "Load for chord info test")
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            expectation.fulfill()
        }
        wait(for: [expectation], timeout: 1.0)
        
        // 测试和弦数量
        XCTAssertEqual(progressionReader.chordCount, 3, "和弦数量应该为3")
        
        // 测试单个和弦名称获取
        XCTAssertEqual(progressionReader.getChordName(at: 0), "Dm7", "第一个和弦名称应该正确")
        XCTAssertEqual(progressionReader.getChordName(at: 1), "G7", "第二个和弦名称应该正确")
        XCTAssertEqual(progressionReader.getChordName(at: 2), "CM7", "第三个和弦名称应该正确")
        
        // 测试超出索引范围
        XCTAssertEqual(progressionReader.getChordName(at: 3), "", "超出范围的索引应该返回空字符串")
        XCTAssertEqual(progressionReader.getChordMIDINotes(at: 3), [], "超出范围的索引应该返回空数组")
    }
    
    // MARK: - 性能测试
    
    func testPerformanceLoadProgression() throws {
        measure {
            progressionReader.loadProgression("test-demo")
        }
    }
    
    func testPerformanceTransposition() throws {
        // 先加载和弦进行
        progressionReader.loadProgression("test-demo")
        
        let expectation = XCTestExpectation(description: "Load for performance test")
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            expectation.fulfill()
        }
        wait(for: [expectation], timeout: 1.0)
        
        measure {
            for i in -11...11 {
                progressionReader.transposeFromBase(i)
                _ = progressionReader.chordNames
                _ = progressionReader.allChordMIDINotes
            }
        }
    }
}

// MARK: - 播放测试扩展（集成测试）

extension ProgressionReaderTests {
    
    /// 集成测试：使用MIDIManager播放demo progression
    func testPlayDemoProgression() throws {
        // 这是一个集成测试，需要MIDIManager
        // 在实际应用中，您可以通过这种方式测试完整的播放流程
        
        // 注意：这个测试需要MIDIManager实例，在单元测试中可能需要Mock
        // 这里提供一个基础的结构示例
        
        // 1. 加载和弦进行
        progressionReader.loadProgression("test-demo")
        
        let expectation = XCTestExpectation(description: "Load for play test")
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            expectation.fulfill()
        }
        wait(for: [expectation], timeout: 1.0)
        
        // 2. 验证加载成功
        XCTAssertNotNil(progressionReader.currentProgression)
        
        // 3. 获取第一个和弦的MIDI音符
        let firstChordNotes = progressionReader.getChordMIDINotes(at: 0)
        XCTAssertFalse(firstChordNotes.isEmpty, "第一个和弦应该有音符")
        
        // 4. 验证MIDI音符在有效范围内
        for note in firstChordNotes {
            XCTAssertTrue(note >= 0 && note <= 127, "MIDI音符应该在有效范围内 (0-127)")
        }
        
        print("🎵 集成测试完成 - Demo progression 加载和音符获取成功")
        print("   和弦进行: \(progressionReader.currentProgression?.name ?? "Unknown")")
        print("   第一个和弦: \(progressionReader.getChordName(at: 0))")
        print("   第一个和弦音符: \(firstChordNotes)")
    }
} 