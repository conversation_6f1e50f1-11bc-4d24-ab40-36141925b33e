# FullUI 单元测试文档

## 概述

本目录包含 FullUI 钢琴学习应用的完整单元测试套件，主要测试音符解析、和弦分析、音符生成等核心数据处理功能。

## 文件结构

```
FullUITests/
├── README.md                 # 本文档
├── DataProcessingUtils.swift # 数据处理工具类
├── FullUITests.swift        # 完整的单元测试套件
├── TestRunner.swift         # 测试运行器
└── SimpleTestRunner.swift   # 简单测试运行器（不依赖Testing框架）
```

## 测试覆盖范围

### 1. 音符解析测试 (NoteParsingTests)
- ✅ 基础音符解析 (C3, C4, C5 等)
- ✅ 升号音符解析 (C#3, D#3, F#3 等)
- ✅ 降号音符解析 (Db3, Eb3, Gb3 等)
- ✅ 不同八度音符解析 (C0-C7)
- ✅ 大小写不敏感处理

### 2. 音符名称生成测试 (NoteNameGenerationTests)
- ✅ MIDI值到音符名称转换
- ✅ UInt8和Int版本一致性验证

### 3. 五度圈工具测试 (CircleOfFifthsTests)
- ✅ 五度圈顺序验证
- ✅ 根音名称获取
- ✅ 根音MIDI值计算
- ✅ 索引循环处理

### 4. 音符生成器测试 (RandomNoteGeneratorTests)
- ✅ 音符范围限制
- ✅ 白键限制功能
- ✅ 全音符模式验证

### 5. 和弦分析器测试 (ChordAnalyzerTests)
- ✅ 基础三和弦识别 (大三、小三和弦)
- ✅ 七和弦识别 (大七、属七、小七和弦)
- ✅ 挂留和弦识别 (sus2, sus4)
- ✅ 转位和弦识别
- ✅ 空输入处理

### 6. 和弦配置测试 (ChordVoicingTests)
- ✅ 和弦配置数据模型
- ✅ 和弦计算逻辑验证

### 7. 集成测试 (IntegrationTests)
- ✅ 音符解析和生成往返转换
- ✅ 完整和弦游戏流程测试

### 8. 性能测试 (PerformanceTests)
- ✅ 音符解析性能测试
- ✅ 和弦分析性能测试

## 运行测试

### 方法1: 使用 Xcode
1. 在 Xcode 中打开 `FullUI.xcodeproj`
2. 选择 `FullUITests` target
3. 按 `Cmd+U` 运行所有测试
4. 查看测试结果面板

### 方法2: 使用命令行 (Swift Testing)
```bash
# 在项目根目录运行
swift test
```

### 方法3: 使用简单测试运行器（推荐）
```swift
// 在Xcode中或Playground中运行
SimpleTestRunner.runBasicTests()
```

这个方法不依赖Testing框架，可以快速验证核心功能。

### 方法4: 使用完整测试运行器
```swift
// 在代码中调用
TestRunner.runAllTests()
```

### 方法5: 使用 xcodebuild
```bash
# 在项目根目录运行
xcodebuild test -scheme FullUITests -destination 'platform=iOS Simulator,name=iPhone 16'
```

## 核心数据处理功能

### 音符解析 (NoteParser)
```swift
// 将音符名称转换为MIDI值
let midiNote = NoteParser.parseNoteToMidi("C4")  // 返回 60
let sharpNote = NoteParser.parseNoteToMidi("F#3") // 返回 54
let flatNote = NoteParser.parseNoteToMidi("Bb3")  // 返回 58
```

### 音符名称生成 (NoteNameGenerator)
```swift
// 将MIDI值转换为音符名称
let noteName = NoteNameGenerator.getNoteName(note: 60)  // 返回 "C4"
let sharpName = NoteNameGenerator.getNoteName(note: 61) // 返回 "C#4"
```

### 五度圈工具 (CircleOfFifthsUtils)
```swift
// 获取五度圈中的根音名称
let rootName = CircleOfFifthsUtils.getRootName(for: 0)  // 返回 "C"
let rootNote = CircleOfFifthsUtils.getRootNote(for: 0, baseOctave: 3) // 返回 48 (C3)
```

### 随机音符生成 (RandomNoteGenerator)
```swift
// 创建音符生成器
let generator = RandomNoteGenerator(loKey: 60, hiKey: 72, whiteKeyOnly: true)
let randomNote = generator.generateRandomNote()  // 返回60-72范围内的白键
```

### 和弦分析 (ChordAnalyzerUtils)
```swift
// 分析和弦
let analyzer = ChordAnalyzerUtils()
let chordNames = analyzer.analyze(midiNotes: [60, 64, 67])  // 返回 ["C"]
```

## 测试数据示例

### 音符解析测试数据
| 输入 | 期望MIDI值 | 说明 |
|------|-----------|------|
| C3   | 48        | 基础音符 |
| C4   | 60        | Middle C |
| C#3  | 49        | 升号音符 |
| Db3  | 49        | 降号音符 |
| F#4  | 66        | 高八度升号 |

### 和弦分析测试数据
| MIDI音符 | 期望和弦 | 说明 |
|----------|----------|------|
| [60,64,67] | C | C大三和弦 |
| [60,63,67] | Cm | C小三和弦 |
| [60,64,67,71] | Cmaj7 | C大七和弦 |
| [60,65,67] | Csus4 | C挂四和弦 |

## 错误处理

测试套件包含以下错误处理验证：
- 空输入处理
- 无效音符名称处理
- 超出范围的MIDI值处理
- 不完整和弦识别

## 性能基准

- 音符解析：10,000次操作应在1秒内完成
- 和弦分析：1,000次操作应在1秒内完成

## 贡献指南

添加新测试时请遵循以下规范：
1. 使用描述性的测试名称
2. 添加详细的注释说明测试目的
3. 包含边界条件测试
4. 验证错误处理逻辑
5. 更新本README文档

## 依赖关系

测试套件依赖以下框架：
- Swift Testing Framework
- Foundation

## 版本历史

- v1.0.0: 初始版本，包含所有核心功能测试
- 支持音符解析、和弦分析、五度圈工具等功能的完整测试覆盖 