[0;34m📅 [17:57:45] 检查Git状态...[0m
[1;33m⚠️  发现未提交的更改，建议先提交后再运行完整测试[0m
[0;34m📅 [17:57:45] 最近的提交记录:[0m
67c6898 解决了和弦进行的状态问题，可以有效练习，看到效果了
0cc876b 和弦进行无尽练习黑带感
3a9edcc 最新加入的功能：音效
4674f86 和弦进行练习功能初步完成。加入了和弦进行练习音效
f6bb433 Add .gitignore and clean up repository - Add comprehensive Xcode .gitignore to exclude build artifacts - Remove DerivedData/ from tracking (15,152 files) - Remove xcuserdata/ personal settings from tracking - Clean up repository structure for better performance
[0;34m📅 [17:57:45] 🏃‍♂️ 开始快速健康检查...[0m
🧪 开始诊断和弦名称问题...
==================================
📦 编译测试中...
❌ 编译失败，查看日志:
    builtin-copy -exclude .DS_Store -exclude CVS -exclude .svn -exclude .git -exclude .hg -resolve-src-symlinks /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/MIDIKit_MIDIKitIO.bundle /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/VoicingTrainer.app/Contents/Resources

Copy /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/VoicingTrainer.app/Contents/Resources/AudioKit_AudioKit.bundle /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/AudioKit_AudioKit.bundle (in target 'VoicingTrainer' from project 'VoicingTrainer')
    cd /Users/<USER>/DEV/VoicingTrainer
    builtin-copy -exclude .DS_Store -exclude CVS -exclude .svn -exclude .git -exclude .hg -resolve-src-symlinks /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/AudioKit_AudioKit.bundle /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/VoicingTrainer.app/Contents/Resources

ProcessInfoPlistFile /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/VoicingTrainer.app/Contents/Info.plist /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/Info.plist (in target 'VoicingTrainer' from project 'VoicingTrainer')
    cd /Users/<USER>/DEV/VoicingTrainer
    builtin-infoPlistUtility /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/Info.plist -producttype com.apple.product-type.application -genpkginfo /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/VoicingTrainer.app/Contents/PkgInfo -expandbuildsettings -platform macosx -additionalcontentfile /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/VoicingTrainer.build/Debug/VoicingTrainer.build/assetcatalog_generated_info.plist -scanforprivacyfile /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/VoicingTrainer.app/Contents/Resources/AudioKit_AudioKit.bundle -scanforprivacyfile /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/VoicingTrainer.app/Contents/Resources/MIDIKit_MIDIKitIO.bundle -scanforprivacyfile /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/VoicingTrainer.app/Contents/Resources/MIDIKit_MIDIKitInternals.bundle -o /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Products/Debug/VoicingTrainer.app/Contents/Info.plist
removing value "audio" for "UIBackgroundModes" - not supported on macOS
removing value "bluetooth-central" for "UIBackgroundModes" - not supported on macOS

** TEST BUILD FAILED **


The following build commands failed:
	SwiftCompile normal x86_64 Compiling\ RefactoredTargetNotesDisplay.swift,\ ParticleEffect.swift,\ LevelStartView.swift,\ NotesView.swift,\ TestReminder.swift,\ ProgressionsView.swift,\ SpriteKitParticleSystem.swift /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/Game/Shared/RefactoredTargetNotesDisplay.swift /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/Music/Audio/ParticleEffect.swift /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/Game/Note/Views/LevelStartView.swift /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/Game/Note/NotesView.swift /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/Core/Configuration/TestReminder.swift /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/UI/Views/ProgressionsView.swift /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/Music/Audio/SpriteKitParticleSystem.swift (in target 'VoicingTrainer' from project 'VoicingTrainer')
	SwiftCompile normal x86_64 /Users/<USER>/DEV/VoicingTrainer/VoicingTrainer/Core/Configuration/TestReminder.swift (in target 'VoicingTrainer' from project 'VoicingTrainer')
	Building project VoicingTrainer for testing with scheme VoicingTrainer
(3 failures)
[0;31m❌ 快速健康检查失败，查看 daily_test_20250615.log 了解详情[0m
[0;31m❌ 快速检查失败，停止后续测试[0m
[0;34m📅 [18:01:37] 检查Git状态...[0m
[1;33m⚠️  发现未提交的更改，建议先提交后再运行完整测试[0m
[0;34m📅 [18:01:38] 最近的提交记录:[0m
67c6898 解决了和弦进行的状态问题，可以有效练习，看到效果了
0cc876b 和弦进行无尽练习黑带感
3a9edcc 最新加入的功能：音效
4674f86 和弦进行练习功能初步完成。加入了和弦进行练习音效
f6bb433 Add .gitignore and clean up repository - Add comprehensive Xcode .gitignore to exclude build artifacts - Remove DerivedData/ from tracking (15,152 files) - Remove xcuserdata/ personal settings from tracking - Clean up repository structure for better performance
[0;34m📅 [18:01:38] 🏃‍♂️ 开始快速健康检查...[0m
🧪 开始诊断和弦名称问题...
==================================
📦 编译测试中...
❌ 编译失败，查看日志:

ExecuteExternalTool /Applications/Xcode.app/Contents/Developer/usr/bin/actool --version --output-format xml1

ExecuteExternalTool /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang -v -E -dM -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk -x c -c /dev/null

ExecuteExternalTool /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang -v -E -dM -arch x86_64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk -x objective-c -c /dev/null

ExecuteExternalTool /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/ld -version_details

ReadFileContents /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/share/docc/features.json

Build description signature: 8032dc4d4779cc1a6c9a86f5bfa62837
Build description path: /Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/XCBuildData/8032dc4d4779cc1a6c9a86f5bfa62837.xcbuilddata
error: unable to attach DB: error: accessing build database "/Users/<USER>/Library/Developer/Xcode/DerivedData/VoicingTrainer-eznmmjstiivvdiaotxoeobfeorus/Build/Intermediates.noindex/XCBuildData/build.db": database is locked Possibly there are two concurrent builds running in the same filesystem location.
** TEST BUILD FAILED **


The following build commands failed:
	Building project VoicingTrainer for testing with scheme VoicingTrainer
(1 failure)
[0;31m❌ 快速健康检查失败，查看 daily_test_20250615.log 了解详情[0m
[0;31m❌ 快速检查失败，停止后续测试[0m
[0;34m📅 [22:45:34] 检查Git状态...[0m
[1;33m⚠️  发现未提交的更改，建议先提交后再运行完整测试[0m
[0;34m📅 [22:45:35] 最近的提交记录:[0m
4a94d50 修正电源管理问题
67c6898 解决了和弦进行的状态问题，可以有效练习，看到效果了
0cc876b 和弦进行无尽练习黑带感
3a9edcc 最新加入的功能：音效
4674f86 和弦进行练习功能初步完成。加入了和弦进行练习音效
[0;34m📅 [22:45:35] 🏃‍♂️ 开始快速健康检查...[0m
🧪 开始诊断和弦名称问题...
==================================
📦 编译测试中...
✅ 编译成功
🧪 运行和弦相关测试...
📊 测试结果:
✅ SoundFont loaded successfully
✅ AudioEngine started successfully
✅ SoundFont loaded successfully
✅ AudioEngine started successfully
✅ 属七和弦测试通过: ["C7"]
✅ SoundFont loaded successfully
✅ AudioEngine started successfully
✅ 空输入测试通过
✅ SoundFont loaded successfully
✅ AudioEngine started successfully
✅ 大三和弦测试通过: ["C"]
✅ SoundFont loaded successfully
✅ AudioEngine started successfully
✅ 小三和弦测试通过: ["D#6/C", "Cm"]
✅ SoundFont loaded successfully
✅ AudioEngine started successfully
✅ 和弦解析性能测试通过
✅ SoundFont loaded successfully
✅ AudioEngine started successfully
✅ 单音符测试通过: ["C4"]
✅ SoundFont loaded successfully
✅ AudioEngine started successfully
✅ SoundFont loaded successfully
✅ AudioEngine started successfully
  [90m 140[39m│         print("✅ 和弦名称清除测试通过")
✅ SoundFont loaded successfully
✅ AudioEngine started successfully
✅ SoundFont loaded successfully
✅ AudioEngine started successfully
🔍 大三和弦调试信息:
✅ 大三和弦和弦名称测试通过
✅ SoundFont loaded successfully
✅ AudioEngine started successfully
🔍 多音符操作调试信息:
✅ 多音符操作测试通过
✅ SoundFont loaded successfully
✅ AudioEngine started successfully
🔍 调试信息:
✅ 单音符和弦名称测试通过

🔍 检查关键组件状态:
ChordNameParser.swift 存在: ✅
MIDIManager.swift 存在: ✅
NotesView.swift 存在: ✅

🔍 检查MIDIManager中的updateChordInfo方法:
590-        
591-        // 更新和弦名称数组
592:        chordNames = names
593-        
594-        // 🔧 Debug: 添加调试信息以追踪和弦名称更新
595-        if debugMIDI {
596-            print("🎵 updateChordInfo: pressedNotes=\(sorted), chordNames=\(names)")
597-        }
598-        
599-        if !names.isEmpty
600-        {
601-            /*
602-            names.forEach{

🔍 检查NotesView中的chordNames使用:
265-    }
266-    
267-    var chordNameView:some View {
268:        //if !midiManager.chordNames.isEmpty {
269-            VStack(spacing: 4) {
270-                // 第一个和弦名称最大
271:                let _ = print(midiManager.chordNames)
272:                Text(midiManager.chordNames.first ?? "")
273-                    .font(.title)
274-                    .fontWeight(.bold)
275-                    .foregroundColor(.primary)
276-                
277-                // 其他和弦名称显示在下面，字体略小
278:                if midiManager.chordNames.count > 1 {
279:                    ForEach(Array(midiManager.chordNames.dropFirst().enumerated()), id: \.offset) { index, chordName in
280-                        Text(chordName)
281-                            .font(.title2)
282-                            .fontWeight(.medium)

🧪 诊断完成！
==================================
如果有问题，请检查:
1. MIDIManager.updateChordInfo() 方法是否正确调用 ChordNameParser.analyze
2. ChordNameParser.analyze() 方法是否返回正确结果
3. NotesView 是否正确绑定到 midiManager.chordNames

💡 建议运行: 'sh run_chord_tests.sh' 来获取详细诊断信息
[0;32m✅ 快速健康检查通过[0m
[0;32m✅ 快速检查完成！[0m
