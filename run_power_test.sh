#!/bin/bash

# VoicingTrainer iOS 功耗测试脚本
# 使用方法: ./run_power_test.sh [device_name]

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 默认设备
DEVICE_NAME=${1:-"iPhone 16 Pro"}

echo -e "${BLUE}🔋 VoicingTrainer 功耗测试脚本${NC}"
echo -e "${BLUE}================================${NC}"

# 检查Xcode是否安装
if ! command -v xcodebuild &> /dev/null; then
    echo -e "${RED}❌ 错误: 未找到 Xcode 命令行工具${NC}"
    echo "请安装 Xcode 并运行: xcode-select --install"
    exit 1
fi

# 检查项目文件
if [ ! -f "VoicingTrainer.xcodeproj/project.pbxproj" ]; then
    echo -e "${RED}❌ 错误: 未找到 VoicingTrainer.xcodeproj${NC}"
    echo "请在项目根目录运行此脚本"
    exit 1
fi

echo -e "${YELLOW}📱 目标设备: ${DEVICE_NAME}${NC}"

# 列出可用的模拟器
echo -e "${BLUE}📋 可用的iOS模拟器:${NC}"
xcrun simctl list devices iOS | grep -E "iPhone|iPad" | head -10

echo ""
echo -e "${YELLOW}⚙️  开始构建应用...${NC}"

# 构建应用
if xcodebuild -project VoicingTrainer.xcodeproj \
    -scheme VoicingTrainer \
    -destination "platform=iOS Simulator,name=${DEVICE_NAME}" \
    -configuration Debug \
    build > build.log 2>&1; then
    echo -e "${GREEN}✅ 构建成功${NC}"
else
    echo -e "${RED}❌ 构建失败，查看 build.log 获取详细信息${NC}"
    tail -20 build.log
    exit 1
fi

echo -e "${YELLOW}🚀 启动应用进行功耗测试...${NC}"

# 启动模拟器中的应用
if xcodebuild -project VoicingTrainer.xcodeproj \
    -scheme VoicingTrainer \
    -destination "platform=iOS Simulator,name=${DEVICE_NAME}" \
    -configuration Debug \
    test-without-building > test.log 2>&1; then
    echo -e "${GREEN}✅ 应用启动成功${NC}"
else
    echo -e "${YELLOW}⚠️  测试启动可能失败，但应用应该已经安装${NC}"
fi

echo ""
echo -e "${BLUE}📊 功耗测试指南:${NC}"
echo "1. 在模拟器中打开 VoicingTrainer 应用"
echo "2. 进入 '功耗监控' 标签页"
echo "3. 点击 '开始测试' 按钮"
echo "4. 观察实时功耗指标"
echo "5. 等待测试完成（60秒）"
echo "6. 查看测试结果和建议"

echo ""
echo -e "${BLUE}🔧 高级测试选项:${NC}"
echo "- 使用 Instruments 进行详细分析:"
echo "  instruments -t 'Energy Log' -D energy_trace.trace VoicingTrainer"
echo ""
echo "- 监控CPU使用率:"
echo "  instruments -t 'Activity Monitor' -D activity_trace.trace VoicingTrainer"
echo ""
echo "- 查看内存使用:"
echo "  instruments -t 'Allocations' -D memory_trace.trace VoicingTrainer"

echo ""
echo -e "${GREEN}🎯 测试完成后，请查看以下文件:${NC}"
echo "- PowerTestScript.md - 详细测试指南"
echo "- VoicingTrainer_DevRules.md - 开发规则和经验"
echo "- build.log - 构建日志"
echo "- test.log - 测试日志"

echo ""
echo -e "${BLUE}💡 提示:${NC}"
echo "- 在真实设备上测试功耗更准确"
echo "- 确保设备电量充足（>50%）"
echo "- 关闭其他后台应用以获得准确结果"
echo "- 定期进行功耗测试以监控性能变化"

echo ""
echo -e "${GREEN}✨ 功耗测试环境准备完成！${NC}" 