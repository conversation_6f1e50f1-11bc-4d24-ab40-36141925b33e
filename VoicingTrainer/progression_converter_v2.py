#!/usr/bin/env python3
"""
和弦进行文件格式转换工具 v2.0
改进版：正确处理Slash和弦的移调

新格式支持：
- chordType: 纯和弦类型（不包含低音）
- bassNote: 低音音符（相对于进行根音的偏移）
- rootOffset: 和弦根音偏移
"""

import json
import os
import sys
from pathlib import Path
import re

class ProgressionConverterV2:
    def __init__(self):
        # 音符名称映射
        self.note_names = ["C", "C#", "D", "D#", "E", "F", "F#", "G", "G#", "A", "A#", "B"]
        self.note_to_pitch_class = {name: i for i, name in enumerate(self.note_names)}
        
    def midi_to_pitch_class(self, midi_note):
        """将MIDI音符转换为音高类别 (0-11)"""
        return midi_note % 12
    
    def note_name_to_pitch_class(self, note_name):
        """将音符名称转换为音高类别"""
        note_name = note_name.upper()
        if note_name in self.note_to_pitch_class:
            return self.note_to_pitch_class[note_name]
        return 0  # 默认为C
    
    def pitch_class_to_note_name(self, pitch_class):
        """将音高类别转换为音符名称"""
        return self.note_names[pitch_class % 12]
    
    def calculate_root_offset(self, progression_root, chord_root_midi):
        """计算和弦根音相对于进行根音的偏移"""
        progression_root_pc = self.note_name_to_pitch_class(progression_root)
        chord_root_pc = self.midi_to_pitch_class(chord_root_midi)
        
        # 计算半音偏移
        offset = (chord_root_pc - progression_root_pc) % 12
        return offset
    
    def parse_slash_chord(self, chord_name):
        """解析Slash和弦
        返回: (root_note, chord_type, bass_note)
        例如: 'C#dim/E' -> ('C#', 'dim', 'E')
              'G/B' -> ('G', 'maj', 'B')
              'C' -> ('C', 'maj', None)
        """
        # 分离低音部分
        if '/' in chord_name:
            chord_part, bass_part = chord_name.split('/', 1)
            bass_note = bass_part.strip()
        else:
            chord_part = chord_name
            bass_note = None
        
        # 解析和弦主体部分
        pattern = r'^([A-G][#b]?)(.*)$'
        match = re.match(pattern, chord_part)
        
        if match:
            root_note = match.group(1)
            chord_type = match.group(2) if match.group(2) else "maj"
        else:
            root_note = chord_part[0] if chord_part else "C"
            chord_type = chord_part[1:] if len(chord_part) > 1 else "maj"
        
        return root_note, chord_type, bass_note
    
    def calculate_intervals(self, midi_notes, root_midi):
        """计算音程数组"""
        if not midi_notes:
            return []
        
        # 找到最低音符作为基准
        base_note = min(midi_notes)
        
        # 计算相对音程
        intervals = []
        for note in sorted(midi_notes):
            interval = note - base_note
            intervals.append(interval)
        
        return intervals
    
    def infer_chord_root(self, chord_name, midi_notes):
        """推断和弦根音"""
        root_note_name, chord_type, bass_note = self.parse_slash_chord(chord_name)
        
        # 将根音名称转换为音高类别
        root_pc = self.note_name_to_pitch_class(root_note_name)
        
        # 在MIDI音符中查找匹配的根音
        for midi_note in midi_notes:
            if self.midi_to_pitch_class(midi_note) == root_pc:
                return midi_note
        
        # 如果没找到，使用最低音符
        return min(midi_notes) if midi_notes else 60
    
    def convert_chord(self, chord_data, progression_root):
        """转换单个和弦"""
        chord_name = chord_data["name"]
        midi_notes = chord_data["notes"]
        
        # 解析Slash和弦
        root_note_name, chord_type, bass_note = self.parse_slash_chord(chord_name)
        
        # 推断和弦根音
        chord_root_midi = self.infer_chord_root(chord_name, midi_notes)
        
        # 计算根音偏移
        root_offset = self.calculate_root_offset(progression_root, chord_root_midi)
        
        # 计算音程
        intervals = self.calculate_intervals(midi_notes, chord_root_midi)
        
        # 构建和弦对象
        chord_obj = {
            "role": chord_data["role"],
            "chordType": chord_type,
            "rootOffset": root_offset,
            "intervals": intervals
        }
        
        # 如果有低音，计算低音偏移
        if bass_note:
            bass_pc = self.note_name_to_pitch_class(bass_note)
            progression_root_pc = self.note_name_to_pitch_class(progression_root)
            bass_offset = (bass_pc - progression_root_pc) % 12
            chord_obj["bassOffset"] = bass_offset
        
        return chord_obj
    
    def convert_progression(self, old_data):
        """转换整个和弦进行"""
        progression = old_data["progression"]
        
        # 提取基本信息
        name = progression["name"]
        root_note = progression["rootNote"]
        
        # 转换和弦
        new_chords = []
        for chord in progression["chords"]:
            new_chord = self.convert_chord(chord, root_note)
            new_chords.append(new_chord)
        
        # 构建新格式
        new_progression = {
            "progression": {
                "name": name,
                "rootNote": root_note,
                "chords": new_chords
            }
        }
        
        return new_progression
    
    def convert_file(self, input_file, output_file):
        """转换单个文件"""
        try:
            print(f"转换: {input_file} -> {output_file}")
            
            # 读取原文件
            with open(input_file, 'r', encoding='utf-8') as f:
                old_data = json.load(f)
            
            # 转换格式
            new_data = self.convert_progression(old_data)
            
            # 写入新文件
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(new_data, f, indent=2, ensure_ascii=False)
            
            print(f"✅ 转换成功")
            return True
            
        except Exception as e:
            print(f"❌ 转换失败: {e}")
            return False
    
    def convert_directory(self, input_dir, output_dir):
        """转换整个目录"""
        input_path = Path(input_dir)
        output_path = Path(output_dir)
        
        # 确保输出目录存在
        output_path.mkdir(parents=True, exist_ok=True)
        
        # 查找所有.progression文件
        progression_files = list(input_path.glob("*.progression"))
        
        if not progression_files:
            print(f"❌ 在 {input_dir} 中没有找到 .progression 文件")
            return False
        
        print(f"找到 {len(progression_files)} 个和弦进行文件")
        
        success_count = 0
        for file_path in progression_files:
            output_file = output_path / file_path.name
            if self.convert_file(file_path, output_file):
                success_count += 1
        
        print(f"\n转换完成: {success_count}/{len(progression_files)} 个文件成功")
        return success_count == len(progression_files)

def main():
    converter = ProgressionConverterV2()
    
    # 获取当前脚本所在目录
    script_dir = Path(__file__).parent
    
    # 设置输入和输出目录
    input_dir = script_dir / "Resources" / "Data" / "progressions"
    output_dir = script_dir / "Resources" / "Data" / "progressions_v2"
    
    print("🎵 和弦进行文件格式转换工具 v2.0")
    print("✨ 新功能：正确处理Slash和弦移调")
    print("=" * 50)
    print(f"输入目录: {input_dir}")
    print(f"输出目录: {output_dir}")
    print()
    
    if not input_dir.exists():
        print(f"❌ 输入目录不存在: {input_dir}")
        return
    
    # 执行转换
    success = converter.convert_directory(input_dir, output_dir)
    
    if success:
        print("\n🎉 所有文件转换完成！")
        print(f"新格式文件保存在: {output_dir}")
        print("\n新格式特点:")
        print("✅ chordType: 纯和弦类型")
        print("✅ bassOffset: 低音偏移（仅Slash和弦）")
        print("✅ rootOffset: 和弦根音偏移")
        print("✅ intervals: 音程关系")
    else:
        print("\n❌ 转换过程中出现错误，请检查日志。")

if __name__ == "__main__":
    main() 