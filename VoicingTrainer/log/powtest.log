Failed to load config, using defaults
MIDI.swift:init():52:Initializing MIDI (MIDI.swift:init():52)
MIDI.swift:init():52:Initializing MIDI (MIDI.swift:init():52)
✅ SoundFont loaded successfully
✅ AudioEngine started successfully
🎹   [0] Session 1
🎹   [1] Chordio
🎹 PowerManager - MIDI active: true
No achievement data found, creating default data
Loaded 13 note exercise levels
Auto-selected last unlocked level: exercise 1
🔋 PowerManager - Battery: 100%, State: UIDeviceBatteryState(rawValue: 1)
App is being debugged, do not track this hang
Hang detected: 0.42s (debugger attached, not reporting)
🔋 PowerManager - CPU: 158.0%, Memory: 2.1%, Efficiency: 0.0%
⚠️ PowerManager - Low efficiency detected: 0.0%
💡 PowerManager - Enabling power saving mode
🎹 MIDIManager - Enabling power saving mode
🔋 开始功耗测试...
📊 样本 1/12:
时间: 12:04:20
电池: 100.0%
CPU: 158.0%
内存: 2.1%
热状态: 正常
后台: 否
效率: 0.0%
------------------------------
ProgressView initialized with an out-of-bounds progress value. The value will be clamped to the range of `0...total`.
🔋 PowerManager - CPU: 174.4%, Memory: 2.3%, Efficiency: 0.0%
⚠️ PowerManager - Low efficiency detected: 0.0%
💡 PowerManager - Enabling power saving mode
🎹 MIDIManager - Enabling power saving mode
ProgressView initialized with an out-of-bounds progress value. The value will be clamped to the range of `0...total`.
📊 样本 2/12:
时间: 12:04:25
电池: 100.0%
CPU: 174.4%
内存: 2.3%
热状态: 正常
后台: 否
效率: 0.0%
------------------------------
ProgressView initialized with an out-of-bounds progress value. The value will be clamped to the range of `0...total`.
🔋 PowerManager - CPU: 174.5%, Memory: 2.3%, Efficiency: 0.0%
⚠️ PowerManager - Low efficiency detected: 0.0%
💡 PowerManager - Enabling power saving mode
🎹 MIDIManager - Enabling power saving mode
ProgressView initialized with an out-of-bounds progress value. The value will be clamped to the range of `0...total`.
📊 样本 3/12:
时间: 12:04:30
电池: 100.0%
CPU: 174.5%
内存: 2.3%
热状态: 正常
后台: 否
效率: 0.0%
------------------------------
ProgressView initialized with an out-of-bounds progress value. The value will be clamped to the range of `0...total`.
🔋 PowerManager - CPU: 174.5%, Memory: 2.3%, Efficiency: 0.0%
⚠️ PowerManager - Low efficiency detected: 0.0%
💡 PowerManager - Enabling power saving mode
🎹 MIDIManager - Enabling power saving mode
ProgressView initialized with an out-of-bounds progress value. The value will be clamped to the range of `0...total`.
📊 样本 4/12:
时间: 12:04:35
电池: 100.0%
CPU: 174.5%
内存: 2.3%
热状态: 正常
后台: 否
效率: 0.0%
------------------------------
ProgressView initialized with an out-of-bounds progress value. The value will be clamped to the range of `0...total`.
🔋 PowerManager - CPU: 174.1%, Memory: 2.3%, Efficiency: 0.0%
⚠️ PowerManager - Low efficiency detected: 0.0%
💡 PowerManager - Enabling power saving mode
🎹 MIDIManager - Enabling power saving mode
ProgressView initialized with an out-of-bounds progress value. The value will be clamped to the range of `0...total`.
📊 样本 5/12:
时间: 12:04:40
电池: 100.0%
CPU: 174.1%
内存: 2.3%
热状态: 正常
后台: 否
效率: 0.0%
------------------------------
ProgressView initialized with an out-of-bounds progress value. The value will be clamped to the range of `0...total`.
🔋 PowerManager - CPU: 174.0%, Memory: 2.3%, Efficiency: 0.0%
⚠️ PowerManager - Low efficiency detected: 0.0%
💡 PowerManager - Enabling power saving mode
🎹 MIDIManager - Enabling power saving mode
ProgressView initialized with an out-of-bounds progress value. The value will be clamped to the range of `0...total`.
📊 样本 6/12:
时间: 12:04:45
电池: 100.0%
CPU: 174.0%
内存: 2.3%
热状态: 正常
后台: 否
效率: 0.0%
------------------------------
ProgressView initialized with an out-of-bounds progress value. The value will be clamped to the range of `0...total`.
🔋 PowerManager - CPU: 174.1%, Memory: 2.3%, Efficiency: 0.0%
⚠️ PowerManager - Low efficiency detected: 0.0%
💡 PowerManager - Enabling power saving mode
🎹 MIDIManager - Enabling power saving mode
ProgressView initialized with an out-of-bounds progress value. The value will be clamped to the range of `0...total`.
📊 样本 7/12:
时间: 12:04:50
电池: 100.0%
CPU: 174.1%
内存: 2.3%
热状态: 正常
后台: 否
效率: 0.0%
------------------------------
ProgressView initialized with an out-of-bounds progress value. The value will be clamped to the range of `0...total`.
🔋 PowerManager - CPU: 174.1%, Memory: 2.3%, Efficiency: 0.0%
⚠️ PowerManager - Low efficiency detected: 0.0%
💡 PowerManager - Enabling power saving mode
🎹 MIDIManager - Enabling power saving mode
ProgressView initialized with an out-of-bounds progress value. The value will be clamped to the range of `0...total`.
📊 样本 8/12:
时间: 12:04:55
电池: 100.0%
CPU: 174.1%
内存: 2.3%
热状态: 正常
后台: 否
效率: 0.0%
------------------------------
ProgressView initialized with an out-of-bounds progress value. The value will be clamped to the range of `0...total`.
🔋 PowerManager - CPU: 174.0%, Memory: 2.3%, Efficiency: 0.0%
⚠️ PowerManager - Low efficiency detected: 0.0%
💡 PowerManager - Enabling power saving mode
🎹 MIDIManager - Enabling power saving mode
ProgressView initialized with an out-of-bounds progress value. The value will be clamped to the range of `0...total`.
📊 样本 9/12:
时间: 12:05:00
电池: 100.0%
CPU: 174.0%
内存: 2.3%
热状态: 正常
后台: 否
效率: 0.0%
------------------------------
ProgressView initialized with an out-of-bounds progress value. The value will be clamped to the range of `0...total`.
🔋 PowerManager - CPU: 174.1%, Memory: 2.3%, Efficiency: 0.0%
⚠️ PowerManager - Low efficiency detected: 0.0%
💡 PowerManager - Enabling power saving mode
🎹 MIDIManager - Enabling power saving mode
ProgressView initialized with an out-of-bounds progress value. The value will be clamped to the range of `0...total`.
📊 样本 10/12:
时间: 12:05:05
电池: 100.0%
CPU: 174.1%
内存: 2.3%
热状态: 正常
后台: 否
效率: 0.0%
------------------------------
ProgressView initialized with an out-of-bounds progress value. The value will be clamped to the range of `0...total`.
🔋 PowerManager - CPU: 174.1%, Memory: 2.3%, Efficiency: 0.0%
⚠️ PowerManager - Low efficiency detected: 0.0%
💡 PowerManager - Enabling power saving mode
🎹 MIDIManager - Enabling power saving mode
ProgressView initialized with an out-of-bounds progress value. The value will be clamped to the range of `0...total`.
📊 样本 11/12:
时间: 12:05:10
电池: 100.0%
CPU: 174.1%
内存: 2.3%
热状态: 正常
后台: 否
效率: 0.0%
------------------------------
ProgressView initialized with an out-of-bounds progress value. The value will be clamped to the range of `0...total`.
🔋 PowerManager - CPU: 174.1%, Memory: 2.3%, Efficiency: 0.0%
⚠️ PowerManager - Low efficiency detected: 0.0%
💡 PowerManager - Enabling power saving mode
🎹 MIDIManager - Enabling power saving mode
ProgressView initialized with an out-of-bounds progress value. The value will be clamped to the range of `0...total`.
📊 样本 12/12:
时间: 12:05:15
电池: 100.0%
CPU: 174.1%
内存: 2.3%
热状态: 正常
后台: 否
效率: 0.0%
------------------------------
ProgressView initialized with an out-of-bounds progress value. The value will be clamped to the range of `0...total`.
🔋 PowerManager - CPU: 174.1%, Memory: 2.3%, Efficiency: 0.0%
⚠️ PowerManager - Low efficiency detected: 0.0%
💡 PowerManager - Enabling power saving mode
🎹 MIDIManager - Enabling power saving mode
ProgressView initialized with an out-of-bounds progress value. The value will be clamped to the range of `0...total`.
📊 样本 13/12:
时间: 12:05:20
电池: 100.0%
CPU: 174.1%
内存: 2.3%
热状态: 正常
后台: 否
效率: 0.0%
------------------------------
ProgressView initialized with an out-of-bounds progress value. The value will be clamped to the range of `0...total`.
ProgressView initialized with an out-of-bounds progress value. The value will be clamped to the range of `0...total`.
✅ 功耗测试完成!
📊 测试报告:
总样本数: 13
前台样本: 13
后台样本: 0

📈 平均指标:
电池消耗: 100.00%
CPU使用: 172.9%
内存使用: 2.3%
功耗效率: 0.0%

📈 峰值指标:
最高CPU: 174.5%
最高内存: 2.3%
最低效率: 0.0%

🏆 功耗等级: D (不合格) ❌
💾 测试结果已保存: PowerTest_2025-06-15_12-05-20.json
ProgressView initialized with an out-of-bounds progress value. The value will be clamped to the range of `0...total`.
🔋 PowerManager - CPU: 174.5%, Memory: 2.3%, Efficiency: 0.0%
⚠️ PowerManager - Low efficiency detected: 0.0%
💡 PowerManager - Enabling power saving mode
🎹 MIDIManager - Enabling power saving mode
ProgressView initialized with an out-of-bounds progress value. The value will be clamped to the range of `0...total`.
🔋 PowerManager - CPU: 174.5%, Memory: 2.3%, Efficiency: 0.0%
⚠️ PowerManager - Low efficiency detected: 0.0%
💡 PowerManager - Enabling power saving mode
🎹 MIDIManager - Enabling power saving mode
ProgressView initialized with an out-of-bounds progress value. The value will be clamped to the range of `0...total`.
🔋 PowerManager - CPU: 174.5%, Memory: 2.3%, Efficiency: 0.0%
⚠️ PowerManager - Low efficiency detected: 0.0%
💡 PowerManager - Enabling power saving mode
🎹 MIDIManager - Enabling power saving mode
ProgressView initialized with an out-of-bounds progress value. The value will be clamped to the range of `0...total`.
🔋 PowerManager - CPU: 174.5%, Memory: 2.3%, Efficiency: 0.0%
⚠️ PowerManager - Low efficiency detected: 0.0%
💡 PowerManager - Enabling power saving mode
🎹 MIDIManager - Enabling power saving mode
ProgressView initialized with an out-of-bounds progress value. The value will be clamped to the range of `0...total`.
🔋 PowerManager - CPU: 174.5%, Memory: 2.3%, Efficiency: 0.0%
⚠️ PowerManager - Low efficiency detected: 0.0%
💡 PowerManager - Enabling power saving mode
🎹 MIDIManager - Enabling power saving mode
ProgressView initialized with an out-of-bounds progress value. The value will be clamped to the range of `0...total`.
🔋 PowerManager - CPU: 174.5%, Memory: 2.3%, Efficiency: 0.0%
⚠️ PowerManager - Low efficiency detected: 0.0%
💡 PowerManager - Enabling power saving mode
🎹 MIDIManager - Enabling power saving mode
ProgressView initialized with an out-of-bounds progress value. The value will be clamped to the range of `0...total`.
🔋 PowerManager - CPU: 174.5%, Memory: 2.3%, Efficiency: 0.0%
⚠️ PowerManager - Low efficiency detected: 0.0%
💡 PowerManager - Enabling power saving mode
🎹 MIDIManager - Enabling power saving mode
ProgressView initialized with an out-of-bounds progress value. The value will be clamped to the range of `0...total`.
🔋 PowerManager - CPU: 174.5%, Memory: 2.3%, Efficiency: 0.0%
⚠️ PowerManager - Low efficiency detected: 0.0%
💡 PowerManager - Enabling power saving mode
🎹 MIDIManager - Enabling power saving mode
ProgressView initialized with an out-of-bounds progress value. The value will be clamped to the range of `0...total`.
🔋 PowerManager - CPU: 174.5%, Memory: 2.3%, Efficiency: 0.0%
⚠️ PowerManager - Low efficiency detected: 0.0%
💡 PowerManager - Enabling power saving mode
🎹 MIDIManager - Enabling power saving mode
ProgressView initialized with an out-of-bounds progress value. The value will be clamped to the range of `0...total`.
🔋 PowerManager - CPU: 174.5%, Memory: 2.3%, Efficiency: 0.0%
⚠️ PowerManager - Low efficiency detected: 0.0%
💡 PowerManager - Enabling power saving mode
🎹 MIDIManager - Enabling power saving mode
ProgressView initialized with an out-of-bounds progress value. The value will be clamped to the range of `0...total`.
🔋 PowerManager - CPU: 174.5%, Memory: 2.3%, Efficiency: 0.0%
⚠️ PowerManager - Low efficiency detected: 0.0%
💡 PowerManager - Enabling power saving mode
🎹 MIDIManager - Enabling power saving mode
ProgressView initialized with an out-of-bounds progress value. The value will be clamped to the range of `0...total`.
🔋 PowerManager - CPU: 174.5%, Memory: 2.3%, Efficiency: 0.0%
⚠️ PowerManager - Low efficiency detected: 0.0%
💡 PowerManager - Enabling power saving mode
🎹 MIDIManager - Enabling power saving mode
ProgressView initialized with an out-of-bounds progress value. The value will be clamped to the range of `0...total`.
🔋 PowerManager - CPU: 174.5%, Memory: 2.3%, Efficiency: 0.0%
⚠️ PowerManager - Low efficiency detected: 0.0%
💡 PowerManager - Enabling power saving mode
🎹 MIDIManager - Enabling power saving mode
ProgressView initialized with an out-of-bounds progress value. The value will be clamped to the range of `0...total`.
🔋 PowerManager - CPU: 174.5%, Memory: 2.3%, Efficiency: 0.0%
⚠️ PowerManager - Low efficiency detected: 0.0%
💡 PowerManager - Enabling power saving mode
🎹 MIDIManager - Enabling power saving mode
ProgressView initialized with an out-of-bounds progress value. The value will be clamped to the range of `0...total`.
🔋 PowerManager - CPU: 174.5%, Memory: 2.3%, Efficiency: 0.0%
⚠️ PowerManager - Low efficiency detected: 0.0%
💡 PowerManager - Enabling power saving mode
🎹 MIDIManager - Enabling power saving mode
ProgressView initialized with an out-of-bounds progress value. The value will be clamped to the range of `0...total`.
🔋 PowerManager - CPU: 174.5%, Memory: 2.3%, Efficiency: 0.0%
⚠️ PowerManager - Low efficiency detected: 0.0%
💡 PowerManager - Enabling power saving mode
🎹 MIDIManager - Enabling power saving mode
ProgressView initialized with an out-of-bounds progress value. The value will be clamped to the range of `0...total`.
🔋 PowerManager - CPU: 174.5%, Memory: 2.3%, Efficiency: 0.0%
⚠️ PowerManager - Low efficiency detected: 0.0%
💡 PowerManager - Enabling power saving mode
🎹 MIDIManager - Enabling power saving mode
ProgressView initialized with an out-of-bounds progress value. The value will be clamped to the range of `0...total`.
🔋 PowerManager - CPU: 174.5%, Memory: 2.3%, Efficiency: 0.0%
⚠️ PowerManager - Low efficiency detected: 0.0%
💡 PowerManager - Enabling power saving mode
🎹 MIDIManager - Enabling power saving mode
ProgressView initialized with an out-of-bounds progress value. The value will be clamped to the range of `0...total`.
🔋 PowerManager - CPU: 174.5%, Memory: 2.3%, Efficiency: 0.0%
⚠️ PowerManager - Low efficiency detected: 0.0%
💡 PowerManager - Enabling power saving mode
🎹 MIDIManager - Enabling power saving mode
ProgressView initialized with an out-of-bounds progress value. The value will be clamped to the range of `0...total`.