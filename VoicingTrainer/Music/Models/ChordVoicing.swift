import Foundation

// MARK: - Chord Voicing Data Models

struct ChordVoicing: Codable, Identifiable, Hashable {
    let id: String
    let name: String
    let type: String
    let base_note: String  // 新增：基础音符，如"C3"
    let intervals: [Int]
    let description: String
    let suffix: String?    // 新增：和弦后缀名称，如"maj7 (原位)"
    
    // 计算基础音符的MIDI值
    var baseMidiNote: Int {
        return parseNoteToMidi(base_note)
    }
}

// MARK: - Note Parsing Helper

func parseNoteToMidi(_ noteString: String) -> Int {
    let noteNames = ["C": 0, "D": 2, "E": 4, "F": 5, "G": 7, "A": 9, "B": 11]
    let noteString = noteString.uppercased()
    
    var noteIndex = 0
    var octave = 0
    var currentIndex = noteString.startIndex
    
    // 解析音符名称
    if currentIndex < noteString.endIndex {
        let noteName = String(noteString[currentIndex])
        if let baseNote = noteNames[noteName] {
            noteIndex = baseNote
            currentIndex = noteString.index(after: currentIndex)
        }
    }
    
    // 解析升降号
    if currentIndex < noteString.endIndex {
        let modifier = noteString[currentIndex]
        if modifier == "#" {
            noteIndex += 1
            currentIndex = noteString.index(after: currentIndex)
        } else if modifier == "B" {
            noteIndex -= 1
            currentIndex = noteString.index(after: currentIndex)
        }
    }
    
    // 解析八度
    if currentIndex < noteString.endIndex {
        let octaveString = String(noteString[currentIndex...])
        octave = Int(octaveString) ?? 3
    }
    
    // 计算MIDI音符号：(octave + 1) * 12 + noteIndex
    // C4 = 60, 所以C3 = 48
    return (octave + 1) * 12 + noteIndex
}

// MARK: - Chord Voicing Manager

class ChordVoicingManager: ObservableObject {
    @Published var voicings: [ChordVoicing] = []
    @Published var selectedVoicing: ChordVoicing?
    
    init() {
        loadVoicings()
    }
    
    private func loadVoicings() {
        guard let url = Bundle.main.url(forResource: "voicings", withExtension: "json"),
              let data = try? Data(contentsOf: url),
              let voicings = try? JSONDecoder().decode([ChordVoicing].self, from: data) else {
            print("Failed to load voicings.json")
            return
        }
        
        self.voicings = voicings
        self.selectedVoicing = voicings.first
    }
}

// MARK: - Circle of Fifths Helper

struct CircleOfFifths {
    // 五度圈反向顺序：C, F, Bb, Eb, Ab, Db, Gb, B, E, A, D, G
    static let reversedOrder: [Int] = [0, 5, 10, 3, 8, 1, 6, 11, 4, 9, 2, 7]
    
    static func getRootNote(for index: Int, baseOctave: Int = 3) -> Int {
        let rootPitch = reversedOrder[index % 12]
        return baseOctave * 12 + rootPitch
    }
    
    static func getRootName(for index: Int) -> String {
        let noteNames = ["C", "Db", "D", "Eb", "E", "F", "Gb", "G", "Ab", "A", "Bb", "B"]
        let rootPitch = reversedOrder[index % 12]
        return noteNames[rootPitch]
    }
} 
