//
//  ChordsParser.swift
//  FullUI
//
//  Created by <PERSON> Li on 2025/5/26.
//  和弦数据解析器 - 解析三级结构的和弦数据
//

import Foundation

// MARK: - 数据模型

/**
 * 和弦配置（第三级）- 匹配原始数据结构
 */
struct OriginalChordVoicing: Identifiable, Codable {
    let name: String
    let suffix: String?  // 新增：和弦后缀名称
    let intervals: [Int]
    let root_note: String
    
    // 为了兼容性，生成一个 ID
    var id: String {
        return name.replacingOccurrences(of: " ", with: "_").lowercased()
    }
    
    // 转换为现有的 ChordVoicing 格式
    func toChordVoicing() -> ChordVoicing {
        return ChordVoicing(
            id: self.id,
            name: self.name,
            type: "chord", // 添加默认类型
            base_note: self.root_note,
            intervals: self.intervals,
            description: self.name,
            suffix: self.suffix ?? self.name  // 使用suffix字段，如果没有则使用name
        )
    }
}

/**
 * 和弦类型（第二级）- 匹配原始数据结构
 */
struct OriginalChordType: Identifiable, Codable, Hashable {
    let name: String
    let intervals: [Int]
    let voicings: [OriginalChordVoicing]
    
    // 为了兼容性，生成一个 ID
    var id: String {
        return name.replacingOccurrences(of: " ", with: "_").lowercased()
    }
    
    // Hashable conformance
    func hash(into hasher: inout Hasher) {
        hasher.combine(id)
    }
    
    static func == (lhs: OriginalChordType, rhs: OriginalChordType) -> Bool {
        lhs.id == rhs.id
    }
    
    // 转换为现有的 ChordType 格式
    func toChordType() -> ChordType {
        return ChordType(
            id: self.id,
            name: self.name,
            symbol: "", // 原始数据中没有符号
            description: self.name,
            voicings: self.voicings.map { $0.toChordVoicing() }
        )
    }
}

/**
 * 和弦分类（第一级）- 匹配原始数据结构
 */
struct OriginalChordCategory: Identifiable, Codable, Hashable {
    let name: String
    let chords: [String: OriginalChordType]
    
    // 为了兼容性，生成一个 ID
    var id: String {
        return name.replacingOccurrences(of: " ", with: "_").lowercased()
    }
    
    // Hashable conformance
    func hash(into hasher: inout Hasher) {
        hasher.combine(id)
    }
    
    static func == (lhs: OriginalChordCategory, rhs: OriginalChordCategory) -> Bool {
        lhs.id == rhs.id
    }
    
    // 转换为现有的 ChordCategory 格式
    func toChordCategory() -> ChordCategory {
        // 定义和弦类型的显示顺序
        let chordTypeOrder = ["major", "minor", "diminished", "augmented", "major_7", "dominant_7", "minor_7", "sus2", "sus4"]
        
        // 按照预定义顺序排列和弦类型
        let orderedChordTypes: [ChordType] = chordTypeOrder.compactMap { key in
            guard let value = chords[key] else { return nil }
            let chordType = value.toChordType()
            // 使用字典的 key 作为更准确的 ID
            return ChordType(
                id: key,
                name: chordType.name,
                symbol: chordType.symbol,
                description: chordType.description,
                voicings: chordType.voicings // 保持voicing的原始顺序
            )
        }
        
        return ChordCategory(
            id: self.id,
            name: self.name,
            description: self.name,
            chordTypes: orderedChordTypes
        )
    }
}

/**
 * 根数据结构 - 匹配原始数据结构
 */
struct OriginalChordData: Codable {
    let categories: [String: OriginalChordCategory]
}

// MARK: - 新格式数据结构（用于兼容性）

/**
 * 和弦分类（第一级）- 新格式
 */
struct ChordCategory: Identifiable, Codable, Hashable {
    let id: String
    let name: String
    let description: String
    let chordTypes: [ChordType]
    
    // Hashable conformance
    func hash(into hasher: inout Hasher) {
        hasher.combine(id)
    }
    
    static func == (lhs: ChordCategory, rhs: ChordCategory) -> Bool {
        lhs.id == rhs.id
    }
}

/**
 * 和弦类型（第二级）- 新格式
 */
struct ChordType: Identifiable, Codable, Hashable {
    let id: String
    let name: String
    let symbol: String
    let description: String
    let voicings: [ChordVoicing]
    
    // Hashable conformance
    func hash(into hasher: inout Hasher) {
        hasher.combine(id)
    }
    
    static func == (lhs: ChordType, rhs: ChordType) -> Bool {
        lhs.id == rhs.id
    }
}

/**
 * 根数据结构 - 新格式
 */
struct ChordData: Codable {
    let categories: [ChordCategory]
}

// MARK: - 和弦数据解析器

/**
 * 和弦数据解析器
 * 负责从JSON文件加载和解析三级结构的和弦数据
 */
class ChordsParser: ObservableObject {
    @Published var categories: [ChordCategory] = []
    @Published var isLoading: Bool = false
    @Published var errorMessage: String?
    
    init() {
        loadChords()
    }
    
    /**
     * 从JSON文件加载和弦数据
     */
    func loadChords() {
        isLoading = true
        errorMessage = nil
        
        // 首先尝试加载 chords.txt（原始格式）
        if let url = Bundle.main.url(forResource: "chords", withExtension: "json") {
            loadOriginalFormat(from: url)
        } else if let url = Bundle.main.url(forResource: "trial", withExtension: "json") {
            loadNewFormat(from: url)
        } else {
            errorMessage = "无法找到 trial.json 或 chords.json 文件"
            print("无法找到和弦数据文件==========")
            isLoading = false
        }
    }
    
    /**
     * 加载原始格式的数据（chords.txt）
     */
    private func loadOriginalFormat(from url: URL) {
        do {
            let data = try Data(contentsOf: url)
            let originalData = try JSONDecoder().decode(OriginalChordData.self, from: data)
            
            DispatchQueue.main.async {
                // 按照JSON文件中的预定义顺序显示分类
                let categoryOrder = ["triads", "seventh_chords", "other_chords"]
                
                // 转换为新格式，保持原始顺序
                self.categories = categoryOrder.compactMap { key in
                    guard let value = originalData.categories[key] else { return nil }
                    let category = value.toChordCategory()
                    // 使用字典的 key 作为更准确的 ID
                    return ChordCategory(
                        id: key,
                        name: category.name,
                        description: category.description,
                        chordTypes: category.chordTypes // 保持和弦类型的原始顺序
                    )
                }
                
                self.isLoading = false
                print("✅ 成功加载原始格式 \(originalData.categories.count) 个和弦分类")
                
                // 调试信息
                for category in self.categories {
                    print("📁 分类: \(category.name) (\(category.chordTypes.count) 种类型)")
                    for chordType in category.chordTypes {
                        print("  🎵 类型: \(chordType.name) (\(chordType.voicings.count) 个配置)")
                    }
                }
            }
        } catch {
            DispatchQueue.main.async {
                self.errorMessage = "解析原始格式和弦数据失败: \(error.localizedDescription)"
                self.isLoading = false
                print("❌ 解析原始格式和弦数据失败: \(error)")
            }
        }
    }
    
    /**
     * 加载新格式的数据（chords.json）
     */
    private func loadNewFormat(from url: URL) {
        do {
            let data = try Data(contentsOf: url)
            let chordData = try JSONDecoder().decode(ChordData.self, from: data)
            
            DispatchQueue.main.async {
                self.categories = chordData.categories
                self.isLoading = false
                print("✅ 成功加载新格式 \(chordData.categories.count) 个和弦分类")
                
                // 调试信息
                for category in chordData.categories {
                    print("📁 分类: \(category.name) (\(category.chordTypes.count) 种类型)")
                    for chordType in category.chordTypes {
                        print("  🎵 类型: \(chordType.name) (\(chordType.voicings.count) 个配置)")
                    }
                }
            }
        } catch {
            DispatchQueue.main.async {
                self.errorMessage = "解析新格式和弦数据失败: \(error.localizedDescription)"
                self.isLoading = false
                print("❌ 解析新格式和弦数据失败: \(error)")
            }
        }
    }
    
    /**
     * 获取所有和弦配置的扁平列表（兼容现有代码）
     */
    func getAllVoicings() -> [ChordVoicing] {
        var allVoicings: [ChordVoicing] = []
        
        for category in categories {
            for chordType in category.chordTypes {
                allVoicings.append(contentsOf: chordType.voicings)
            }
        }
        
        return allVoicings
    }
    
    /**
     * 根据ID查找特定的和弦配置
     */
    func findVoicing(by id: String) -> ChordVoicing? {
        for category in categories {
            for chordType in category.chordTypes {
                if let voicing = chordType.voicings.first(where: { $0.id == id }) {
                    return voicing
                }
            }
        }
        return nil
    }
    
    /**
     * 获取分类路径信息（用于显示面包屑导航）
     */
    func getPath(for voicing: ChordVoicing) -> (category: ChordCategory, chordType: ChordType)? {
        for category in categories {
            for chordType in category.chordTypes {
                if chordType.voicings.contains(where: { $0.id == voicing.id }) {
                    return (category, chordType)
                }
            }
        }
        return nil
    }
} 
