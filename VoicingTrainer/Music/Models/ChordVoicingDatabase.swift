import Foundation

// MARK: - Helpers

struct AnyCodingKey: CodingKey {
    let stringValue: String
    let intValue: Int?
    
    init?(stringValue: String) {
        self.stringValue = stringValue
        self.intValue = nil
    }
    
    init?(intValue: Int) {
        self.stringValue = String(intValue)
        self.intValue = intValue
    }
}

// MARK: - New Chord Voicing Data Models

struct ChordVoicingItem: Codable, Identifiable, Hashable {
    let id: String
    let name: String
    let suffix: String
    let description: String
    let intervals: [Int]
    let root_note: String
    let tags: ChordTags
    let metadata: [String: Any]
    
    // Custom coding keys to handle the dynamic metadata
    private enum CodingKeys: String, CodingKey {
        case id, name, suffix, description, intervals, root_note, tags, metadata
    }
    
    // Regular initializer
    init(id: String, name: String, suffix: String, description: String, intervals: [Int], root_note: String, tags: ChordTags, metadata: [String: Any]) {
        self.id = id
        self.name = name
        self.suffix = suffix
        self.description = description
        self.intervals = intervals
        self.root_note = root_note
        self.tags = tags
        self.metadata = metadata
    }
    
    // Custom decoder to handle dynamic metadata
    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        id = try container.decode(String.self, forKey: .id)
        name = try container.decode(String.self, forKey: .name)
        suffix = try container.decode(String.self, forKey: .suffix)
        description = try container.decode(String.self, forKey: .description)
        intervals = try container.decode([Int].self, forKey: .intervals)
        root_note = try container.decode(String.self, forKey: .root_note)
        tags = try container.decode(ChordTags.self, forKey: .tags)
        
        // Decode metadata as a flexible dictionary
        if let metadataContainer = try? container.nestedContainer(keyedBy: AnyCodingKey.self, forKey: .metadata) {
            var metadataDict: [String: Any] = [:]
            for key in metadataContainer.allKeys {
                if let intValue = try? metadataContainer.decode(Int.self, forKey: key) {
                    metadataDict[key.stringValue] = intValue
                } else if let stringValue = try? metadataContainer.decode(String.self, forKey: key) {
                    metadataDict[key.stringValue] = stringValue
                } else if let arrayValue = try? metadataContainer.decode([String].self, forKey: key) {
                    metadataDict[key.stringValue] = arrayValue
                }
            }
            metadata = metadataDict
        } else {
            metadata = [:]
        }
    }
    
    // Custom encoder
    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        try container.encode(id, forKey: .id)
        try container.encode(name, forKey: .name)
        try container.encode(suffix, forKey: .suffix)
        try container.encode(description, forKey: .description)
        try container.encode(intervals, forKey: .intervals)
        try container.encode(root_note, forKey: .root_note)
        try container.encode(tags, forKey: .tags)
        
        // For now, we'll encode a simplified metadata structure
        var metadataContainer = container.nestedContainer(keyedBy: AnyCodingKey.self, forKey: .metadata)
        for (key, value) in metadata {
            let codingKey = AnyCodingKey(stringValue: key)!
            if let intValue = value as? Int {
                try metadataContainer.encode(intValue, forKey: codingKey)
            } else if let stringValue = value as? String {
                try metadataContainer.encode(stringValue, forKey: codingKey)
            } else if let arrayValue = value as? [String] {
                try metadataContainer.encode(arrayValue, forKey: codingKey)
            }
        }
    }
    
    // Hashable conformance
    static func == (lhs: ChordVoicingItem, rhs: ChordVoicingItem) -> Bool {
        return lhs.id == rhs.id
    }
    
    func hash(into hasher: inout Hasher) {
        hasher.combine(id)
    }
    
    // 计算基础音符的MIDI值
    var baseMidiNote: Int {
        return parseNoteToMidi(root_note)
    }
    
    // 转换为兼容的ChordVoicing格式
    func toChordVoicing() -> ChordVoicing {
        return ChordVoicing(
            id: self.id,
            name: self.name,
            type: "chord",
            base_note: self.root_note,
            intervals: self.intervals,
            description: self.description,
            suffix: self.suffix
        )
    }
}

struct ChordTags: Codable, Hashable {
    let quality: String
    let structure: String
    let inversion: String
    let complexity: String
    let family: [String]
}

struct ChordMetadata: Codable, Hashable {
    let difficulty: Int
    let common_progressions: [String]
    let jazz_usage: String
}

struct CategoryDefinition: Codable {
    let name: String
    let description: String
    let categories: [String: CategoryInfo]
}

struct CategoryInfo: Codable {
    let name: String
    let description: String
    let color: String?
    let filter: [String: String]
}

// MARK: - Practice Group Models (新增)

struct PracticeGroup: Codable, Identifiable, Hashable {
    let id: String
    let name: String
    let description: String
    let family: String          // 对应 "major", "minor" 等
    let structure: String       // 对应 "triad", "seventh", "extended"
    let complexity: String      // 对应 "basic", "intermediate", "advanced", "expert"
    let hands: String          // 对应 "one_hand", "two_hands"
    let voicing_ids: [String]  // 包含的voicing id列表
    let practice_type: String  // "single" 或 "family"
    let priceType: String?     // 价格类型：free 或 paid
    
    // 计算属性：获取价格类型枚举
    var priceTypeEnum: PriceType {
        return PriceType.from(priceType)
    }
    
    // 检查是否可用
    func isAvailable() -> Bool {
        return PurchaseManager.shared.isItemAvailable(priceType: priceTypeEnum)
    }
    
    // 获取锁定描述
    func getLockDescription() -> String? {
        return PurchaseManager.shared.getLockDescription(priceType: priceTypeEnum)
    }
    
    private enum CodingKeys: String, CodingKey {
        case id, name, description, family, structure, complexity, hands, voicing_ids, practice_type
        case priceType = "price_type"
    }
    
    // Hashable conformance
    static func == (lhs: PracticeGroup, rhs: PracticeGroup) -> Bool {
        return lhs.id == rhs.id
    }
    
    func hash(into hasher: inout Hasher) {
        hasher.combine(id)
    }
}

struct ChordVoicingDatabase: Codable {
    let chord_database: [ChordVoicingItem]
    let category_definitions: [String: CategoryDefinition]
    let practice_patterns: [String: ChordDatabasePracticePattern]
    let practice_groups: [PracticeGroup]? // 新增：可选字段，向下兼容
}

struct ChordDatabasePracticePattern: Codable {
    let name: String
    let description: String
    let offset: Int
    let useCircleOfFifths: Bool
}

// MARK: - Chord Voicing Database Manager

class ChordVoicingDatabaseManager: ObservableObject {
    @Published var database: ChordVoicingDatabase?
    @Published var selectedVoicing: ChordVoicing?
    
    // B分类法相关
    @Published var qualityCategories: [QualityCategory] = []
    @Published var structureCategories: [StructureCategory] = []
    @Published var complexityCategories: [ComplexityCategory] = []
    
    // 新增：Practice Groups 支持
    @Published var practiceGroups: [PracticeGroup] = []
    @Published var selectedPracticeGroup: PracticeGroup?
    @Published var selectedPracticeGroupVoicings: [ChordVoicing] = []
    
    init() {
        loadDatabase()
    }
    
    private func loadDatabase() {
        guard let url = Bundle.main.url(forResource: "chord_voicing", withExtension: "json"),
              let data = try? Data(contentsOf: url),
              let database = try? JSONDecoder().decode(ChordVoicingDatabase.self, from: data) else {
            print("❌ Failed to load chord_voicing.json")
            return
        }
        
        self.database = database
        setupCategories()
        
        // 新增：加载practice_groups
        self.practiceGroups = database.practice_groups ?? []
        print("✅ 加载了 \(self.practiceGroups.count) 个练习组")
        
        // 设置默认选择的和弦
        if let firstChord = database.chord_database.first {
            self.selectedVoicing = firstChord.toChordVoicing()
        }
        
        print("✅ Loaded chord_voicing.json with \(database.chord_database.count) chords")
    }
    
    private func setupCategories() {
        guard let database = database else { return }
        
        // 设置音质分类
        setupQualityCategories(database: database)
        
        // 设置结构分类
        setupStructureCategories(database: database)
        
        // 设置复杂度分类
        setupComplexityCategories(database: database)
    }
    
    private func setupQualityCategories(database: ChordVoicingDatabase) {
        guard let byQuality = database.category_definitions["by_quality"] else { return }
        
        var categories: [QualityCategory] = []
        
        for (key, categoryInfo) in byQuality.categories {
            let chords = database.chord_database.filter { $0.tags.quality == key }
            let category = QualityCategory(
                id: key,
                name: categoryInfo.name,
                description: categoryInfo.description,
                color: categoryInfo.color ?? "#4A90E2",
                chords: chords
            )
            categories.append(category)
        }
        
        // 按优先级排序：major, minor, dominant, suspended, diminished, augmented
        let priority = ["major", "minor", "dominant", "suspended", "diminished", "augmented"]
        categories.sort { lhs, rhs in
            let lhsIndex = priority.firstIndex(of: lhs.id) ?? Int.max
            let rhsIndex = priority.firstIndex(of: rhs.id) ?? Int.max
            return lhsIndex < rhsIndex
        }
        
        self.qualityCategories = categories
    }
    
    private func setupStructureCategories(database: ChordVoicingDatabase) {
        guard let byStructure = database.category_definitions["by_structure"] else { return }
        
        var categories: [StructureCategory] = []
        
        for (key, categoryInfo) in byStructure.categories {
            let chords = database.chord_database.filter { $0.tags.structure == key }
            let category = StructureCategory(
                id: key,
                name: categoryInfo.name,
                description: categoryInfo.description,
                chords: chords
            )
            categories.append(category)
        }
        
        // 按结构复杂度排序
        let priority = ["triad", "seventh", "extended"]
        categories.sort { lhs, rhs in
            let lhsIndex = priority.firstIndex(of: lhs.id) ?? Int.max
            let rhsIndex = priority.firstIndex(of: rhs.id) ?? Int.max
            return lhsIndex < rhsIndex
        }
        
        self.structureCategories = categories
    }
    
    private func setupComplexityCategories(database: ChordVoicingDatabase) {
        guard let byComplexity = database.category_definitions["by_complexity"] else { return }
        
        var categories: [ComplexityCategory] = []
        
        for (key, categoryInfo) in byComplexity.categories {
            let chords = database.chord_database.filter { $0.tags.complexity == key }
            let category = ComplexityCategory(
                id: key,
                name: categoryInfo.name,
                description: categoryInfo.description,
                chords: chords
            )
            categories.append(category)
        }
        
        // 按难度排序
        let priority = ["basic", "intermediate", "advanced", "expert"]
        categories.sort { lhs, rhs in
            let lhsIndex = priority.firstIndex(of: lhs.id) ?? Int.max
            let rhsIndex = priority.firstIndex(of: rhs.id) ?? Int.max
            return lhsIndex < rhsIndex
        }
        
        self.complexityCategories = categories
    }
    
    // MARK: - 查询方法
    
    func getChordsInQuality(_ quality: String) -> [ChordVoicingItem] {
        return database?.chord_database.filter { $0.tags.quality == quality } ?? []
    }
    
    func getChordsInStructure(_ structure: String) -> [ChordVoicingItem] {
        return database?.chord_database.filter { $0.tags.structure == structure } ?? []
    }
    
    func getChordsInComplexity(_ complexity: String) -> [ChordVoicingItem] {
        return database?.chord_database.filter { $0.tags.complexity == complexity } ?? []
    }
    
    func getChordsByQualityAndStructure(quality: String, structure: String) -> [ChordVoicingItem] {
        return database?.chord_database.filter { 
            $0.tags.quality == quality && $0.tags.structure == structure 
        } ?? []
    }
    
    // MARK: - Practice Group Methods
    
    func selectPracticeGroup(_ practiceGroup: PracticeGroup) {
        selectedPracticeGroup = practiceGroup
        
        // 获取对应的voicings
        guard let database = database else {
            selectedPracticeGroupVoicings = []
            return
        }
        
        let voicings = practiceGroup.voicing_ids.compactMap { voicingId in
            database.chord_database.first { $0.id == voicingId }?.toChordVoicing()
        }
        
        selectedPracticeGroupVoicings = voicings
        
        print("🎯 选择练习组: \(practiceGroup.name)")
        print("🎯 包含voicings: \(voicings.map { $0.name })")
        
        // 清除单个voicing选择
        selectedVoicing = nil
    }
    
    func clearPracticeGroupSelection() {
        selectedPracticeGroup = nil
        selectedPracticeGroupVoicings = []
    }
}

// MARK: - Category Data Models

struct QualityCategory: Identifiable, Hashable {
    let id: String
    let name: String
    let description: String
    let color: String
    let chords: [ChordVoicingItem]
    
    var chordCount: Int { chords.count }
}

struct StructureCategory: Identifiable, Hashable {
    let id: String
    let name: String
    let description: String
    let chords: [ChordVoicingItem]
    
    var chordCount: Int { chords.count }
}

struct ComplexityCategory: Identifiable, Hashable {
    let id: String
    let name: String
    let description: String
    let chords: [ChordVoicingItem]
    
    var chordCount: Int { chords.count }
}

// MARK: - Grouped Chord Data

struct GroupedChordData: Identifiable {
    let id: String
    let structureName: String
    let complexityGroups: [ComplexityGroup]
}

struct ComplexityGroup: Identifiable {
    let id: String
    let complexityName: String
    let difficulty: Int
    let chords: [ChordVoicingItem]
    
    var chordCount: Int { chords.count }
} 
