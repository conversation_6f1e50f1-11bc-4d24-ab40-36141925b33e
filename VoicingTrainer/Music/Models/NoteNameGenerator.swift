//
//  NoteNameGenerator.swift
//  FullUI
//
//  Created by <PERSON> on 2025/5/26.
//  统一的音符名称生成工具 - 整合所有重复的 getNoteName 函数
//

import Foundation

/**
 * 音符名称生成器
 * 统一处理 MIDI 音符号到音符名称的转换
 */
struct NoteNameGenerator {
    
    // MARK: - 音符名称映射
    
    private static let noteNames = ["C", "Db", "D", "Eb", "E", "F", "F#", "G", "Ab", "A", "Bb", "B"]
    
    // MARK: - 公共接口
    
    /**
     * 将 MIDI 音符号转换为音符名称（UInt8 版本）
     * @param note MIDI 音符号 (0-127)
     * @return 音符名称，格式为 "C4", "C#4" 等
     */
    static func getNoteName(note: UInt8) -> String {
        return getNoteName(note: Int(note))
    }
    
    /**
     * 将 MIDI 音符号转换为音符名称（Int 版本）
     * @param note MIDI 音符号 (0-127)
     * @return 音符名称，格式为 "C4", "C#4" 等
     */
    static func getNoteName(note: Int) -> String {
        // 确保音符号在有效范围内
        guard note >= 0 && note <= 127 else {
            return "Invalid"
        }
        
        let noteIndex = note % 12
        let octave = (note / 12) - 1
        
        return "\(noteNames[noteIndex])\(octave)"
    }
    
    /**
     * 获取音符名称（不包含八度信息）
     * @param note MIDI 音符号 (0-127)
     * @return 音符名称，格式为 "C", "C#" 等
     */
    static func getNoteNameOnly(note: Int) -> String {
        guard note >= 0 && note <= 127 else {
            return "Invalid"
        }
        
        let noteIndex = note % 12
        return noteNames[noteIndex]
    }
    
    /**
     * 获取八度信息
     * @param note MIDI 音符号 (0-127)
     * @return 八度数字
     */
    static func getOctave(note: Int) -> Int {
        guard note >= 0 && note <= 127 else {
            return -1
        }
        
        return (note / 12) - 1
    }
    
    /**
     * 将音符名称转换为 MIDI 音符号
     * @param noteName 音符名称，格式为 "C4", "C#4" 等
     * @return MIDI 音符号，如果解析失败返回 nil
     */
    static func getMIDINote(from noteName: String) -> Int? {
        // 解析音符名称，例如 "C4", "C#4", "Bb3"
        let trimmed = noteName.trimmingCharacters(in: .whitespaces)
        
        // 提取八度数字
        guard let lastChar = trimmed.last,
              let octave = Int(String(lastChar)) else {
            return nil
        }
        
        // 提取音符名称部分
        let noteNamePart = String(trimmed.dropLast())
        
        // 查找音符索引
        guard let noteIndex = noteNames.firstIndex(of: noteNamePart) else {
            return nil
        }
        
        // 计算 MIDI 音符号
        return (octave + 1) * 12 + noteIndex
    }
    
    /**
     * 批量转换音符数组
     * @param notes MIDI 音符号数组
     * @return 音符名称数组
     */
    static func getNoteNames(notes: [Int]) -> [String] {
        return notes.map { getNoteName(note: $0) }
    }
    
    /**
     * 将音符数组转换为字符串（用空格分隔）
     * @param notes MIDI 音符号数组
     * @return 音符名称字符串，例如 "C4 E4 G4"
     */
    static func getNotesString(notes: [Int]) -> String {
        return getNoteNames(notes: notes).joined(separator: " ")
    }
}

// MARK: - 扩展支持

extension NoteNameGenerator {
    
    /**
     * 检查是否为黑键
     * @param note MIDI 音符号
     * @return 是否为黑键
     */
    static func isBlackKey(note: Int) -> Bool {
        let noteIndex = note % 12
        return [1, 3, 6, 8, 10].contains(noteIndex) // Db, Eb, F#, Ab, Bb
    }
    
    /**
     * 检查是否为白键
     * @param note MIDI 音符号
     * @return 是否为白键
     */
    static func isWhiteKey(note: Int) -> Bool {
        return !isBlackKey(note: note)
    }
    
    /**
     * 获取音符的颜色类型
     * @param note MIDI 音符号
     * @return "black" 或 "white"
     */
    static func getKeyColor(note: Int) -> String {
        return isBlackKey(note: note) ? "black" : "white"
    }
} 
