//
//  ChordNameGenerator.swift
//  VoicingTrainer
//
//  Created by AI Assistant on 2025/6/19.
//

import Foundation

/// 动态和弦名称生成器
/// 根据新的数据格式生成移调后的和弦名称
class ChordNameGenerator {
    
    // MARK: - 属性
    
    /// 音符名称数组
    private let noteNames = ["C", "C#", "D", "D#", "E", "F", "F#", "G", "G#", "A", "A#", "B"]
    
    /// 音符名称到音高类别的映射
    private lazy var noteToPitchClass: [String: Int] = {
        var mapping: [String: Int] = [:]
        for (index, name) in noteNames.enumerated() {
            mapping[name] = index
        }
        
        // 添加降号音符的映射
        mapping["DB"] = 1   // C#
        mapping["EB"] = 3   // D#
        mapping["GB"] = 6   // F#
        mapping["AB"] = 8   // G#
        mapping["BB"] = 10  // A#
        
        return mapping
    }()
    
    // MARK: - 初始化
    
    init() {
        // 初始化完成
    }
    
    // MARK: - 公共方法
    
    /// 将音符名称转换为音高类别
    /// - Parameter noteName: 音符名称 (如 "C", "F#")
    /// - Returns: 音高类别 (0-11)
    func noteNameToPitchClass(_ noteName: String) -> Int {
        let upperName = noteName.uppercased()
        return noteToPitchClass[upperName] ?? 0
    }
    
    /// 解析包含八度信息的根音（如"C3"）
    /// - Parameter rootNote: 根音字符串 (如"C", "C3", "F#4")
    /// - Returns: (音高类别, 八度, MIDI音符号)
    func parseRootNoteWithOctave(_ rootNote: String) -> (pitchClass: Int, octave: Int, midiNote: Int) {
        let upperRootNote = rootNote.uppercased()
        
        // 匹配模式：音符名称 + 可选的八度数字
        // 支持升号(#)和降号(B，因为使用了uppercased())
        let pattern = "^([A-G][#B]?)(\\d*)$"
        let regex = try! NSRegularExpression(pattern: pattern)
        
        if let match = regex.firstMatch(in: upperRootNote, range: NSRange(upperRootNote.startIndex..., in: upperRootNote)) {
            let noteNameRange = Range(match.range(at: 1), in: upperRootNote)!
            let octaveRange = Range(match.range(at: 2), in: upperRootNote)
            
            let noteName = String(upperRootNote[noteNameRange])
            let octaveString = octaveRange != nil && !octaveRange!.isEmpty ? String(upperRootNote[octaveRange!]) : ""
            
            let pitchClass = noteToPitchClass[noteName] ?? 0
            let octave = Int(octaveString) ?? 4  // 默认使用八度4
            
            // 标准MIDI计算：C1=24, C2=36, C3=48, C4=60
            // 公式：MIDI = 12 * (八度 + 1) + 音高类别
            let midiNote = 12 * (octave + 1) + pitchClass
            
            return (pitchClass: pitchClass, octave: octave, midiNote: midiNote)
        }
        
        // 如果解析失败，返回默认值
        let pitchClass = noteToPitchClass[upperRootNote] ?? 0
        let octave = 4
        let midiNote = 12 * (octave + 1) + pitchClass  // 使用相同的公式
        
        return (pitchClass: pitchClass, octave: octave, midiNote: midiNote)
    }
    
    /// 将音高类别转换为音符名称
    /// - Parameter pitchClass: 音高类别 (0-11)
    /// - Returns: 音符名称
    func pitchClassToNoteName(_ pitchClass: Int) -> String {
        return noteNames[pitchClass % 12]
    }
    
    /// 生成移调后的和弦名称
    /// - Parameters:
    ///   - progressionRoot: 和弦进行的根音 (支持"C"或"C3"格式)
    ///   - chordData: 和弦数据 (新格式)
    ///   - transpositionSemitones: 移调半音数
    /// - Returns: 移调后的和弦名称
    func generateChordName(
        progressionRoot: String,
        chordData: [String: Any],
        transpositionSemitones: Int = 0
    ) -> String {
        // 解析progressionRoot，获取音高类别（忽略八度信息，因为和弦名称不包含八度）
        let (oldRootPC, _, _) = parseRootNoteWithOctave(progressionRoot)
        let newRootPC = (oldRootPC + transpositionSemitones) % 12
        
        // 计算和弦根音
        let rootOffset = chordData["rootOffset"] as? Int ?? 0
        let chordRootPC = (newRootPC + rootOffset) % 12
        let chordRootName = pitchClassToNoteName(chordRootPC)
        
        // 获取和弦类型
        let chordType = chordData["chordType"] as? String ?? ""
        
        // 构建基本和弦名称
        var chordName = "\(chordRootName)\(chordType)"
        
        // 如果有低音，添加Slash和弦部分
        if let bassOffset = chordData["bassOffset"] as? Int {
            let bassPC = (newRootPC + bassOffset) % 12
            let bassName = pitchClassToNoteName(bassPC)
            chordName = "\(chordName)/\(bassName)"
        }
        
        return chordName
    }
    
    /// 生成整个和弦进行的移调名称
    /// - Parameters:
    ///   - progressionData: 和弦进行数据 (新格式)
    ///   - transpositionSemitones: 移调半音数
    /// - Returns: 移调后的和弦名称列表
    func generateProgressionNames(
        progressionData: [String: Any],
        transpositionSemitones: Int = 0
    ) -> [String] {
        guard let progression = progressionData["progression"] as? [String: Any],
              let progressionRoot = progression["rootNote"] as? String,
              let chords = progression["chords"] as? [[String: Any]] else {
            return []
        }
        
        var chordNames: [String] = []
        for chord in chords {
            let name = generateChordName(
                progressionRoot: progressionRoot,
                chordData: chord,
                transpositionSemitones: transpositionSemitones
            )
            chordNames.append(name)
        }
        
        return chordNames
    }
    
    /// 基于ChordInfo结构体生成和弦名称
    /// - Parameters:
    ///   - progressionRoot: 和弦进行的根音
    ///   - chordInfo: ChordInfo结构体
    ///   - transpositionSemitones: 移调半音数
    /// - Returns: 移调后的和弦名称
    func generateChordName(
        progressionRoot: String,
        chordInfo: ChordInfo,
        transpositionSemitones: Int = 0
    ) -> String {
        // 重构后的版本：直接使用ChordInfo的getChordName方法
        return chordInfo.getChordName(transpositionSemitones: transpositionSemitones)
    }
    
    /// 计算移调后的MIDI音符
    /// - Parameters:
    ///   - progressionRoot: 和弦进行的根音 (支持"C"或"C3"格式)
    ///   - chordInfo: ChordInfo结构体
    ///   - transpositionSemitones: 移调半音数
    ///   - baseOctave: 基准八度 (默认4，但会被progressionRoot中的八度覆盖)
    /// - Returns: 移调后的MIDI音符数组
    func calculateTransposedMIDINotes(
        progressionRoot: String,
        chordInfo: ChordInfo,
        transpositionSemitones: Int = 0,
        baseOctave: Int = 4
    ) -> [Int] {
        // 重构后的版本：直接使用ChordInfo的getMIDINotes方法
        return chordInfo.getMIDINotes(transpositionSemitones: transpositionSemitones)
    }
    
    /// 从直接指定的根音生成和弦名称（最新格式）
    /// - Parameters:
    ///   - root: 根音字符串 (如"D3", "G2", "C3")
    ///   - chordType: 和弦类型 (如"m9", "7", "6")
    ///   - transpositionSemitones: 移调半音数
    ///   - bassOffset: 低音偏移（可选）
    /// - Returns: 和弦名称
    func generateChordNameFromRoot(
        root: String,
        chordType: String,
        transpositionSemitones: Int = 0,
        bassOffset: Int? = nil
    ) -> String {
        // 解析根音
        let (rootPC, _, _) = parseRootNoteWithOctave(root)
        
        // 计算移调后的根音
        let transposedRootPC = (rootPC + transpositionSemitones) % 12
        let transposedRootName = pitchClassToNoteName(transposedRootPC)
        
        // 构建基本和弦名称
        var chordName = "\(transposedRootName)\(chordType)"
        
        // 处理低音（如果有）
        if let bassOffset = bassOffset {
            let bassPC = (transposedRootPC + bassOffset) % 12
            let bassName = pitchClassToNoteName(bassPC)
            chordName += "/\(bassName)"
        }
        
        return chordName
    }
    
    /// 从直接指定的根音计算MIDI音符（最新格式）
    /// - Parameters:
    ///   - root: 根音字符串 (如"D3", "G2", "C3")
    ///   - intervals: 音程数组
    ///   - transpositionSemitones: 移调半音数
    /// - Returns: MIDI音符数组
    func calculateMIDINotesFromRoot(
        root: String,
        intervals: [Int],
        transpositionSemitones: Int = 0
    ) -> [Int] {
        // 解析根音（包含八度信息）
        let (_, octave, rootMIDI) = parseRootNoteWithOctave(root)
        
        // 计算移调后的根音MIDI值
        let transposedRootMIDI = rootMIDI + transpositionSemitones
        
        // 基于移调后的根音和音程计算所有MIDI音符
        let midiNotes = intervals.map { interval in
            transposedRootMIDI + interval
        }
        
        return midiNotes
    }
}

// MARK: - 扩展：测试和调试

extension ChordNameGenerator {
    
    /// 测试和弦名称生成
    /// - Parameter verbose: 是否输出详细信息
    static func runTests(verbose: Bool = false) {
        let generator = ChordNameGenerator()
        
        if verbose {
            print("🎵 ChordNameGenerator 测试")
            print("=" + String(repeating: "=", count: 49))
        }
        
        // 测试数据 - 使用B3作为根音
        let testData: [String: Any] = [
            "progression": [
                "name": "Test Progression",
                "rootNote": "B3",
                "chords": [
                    [
                        "role": "Root",
                        "chordType": "min",
                        "rootOffset": 0,
                        "intervals": [0, 3, 7, 12, 15]
                    ],
                    [
                        "role": "Normal",
                        "chordType": "dim",
                        "rootOffset": 2,
                        "intervals": [0, 12, 15, 21, 24],
                        "bassOffset": 5
                    ],
                    [
                        "role": "Normal",
                        "chordType": "7",
                        "rootOffset": 7,
                        "intervals": [0, 8, 12, 15, 18],
                        "bassOffset": 11
                    ]
                ]
            ]
        ]
        
        // 生成原始和弦名称
        let originalNames = generator.generateProgressionNames(
            progressionData: testData,
            transpositionSemitones: 0
        )
        
        if verbose {
            print("原始进行 (B3调):")
            for (index, name) in originalNames.enumerated() {
                print("  \(index + 1). \(name)")
            }
            
            // 测试八度解析功能
            print("\n🎵 测试八度解析:")
            let testRootNotes = ["C", "C3", "F#4", "Bb2", "G#5"]
            for rootNote in testRootNotes {
                let (pc, octave, midi) = generator.parseRootNoteWithOctave(rootNote)
                print("  \(rootNote) → 音高类别: \(pc), 八度: \(octave), MIDI: \(midi)")
            }
            print()
        }
        
        // 测试五度圈反向移调
        let circleOfFifthsOffsets = [0, -5, -10, -3, -8, -1, -6, -11, -4, -9, -2, -7]
        let keys = ["B", "E", "A", "D", "G", "C", "F", "A#", "D#", "G#", "C#", "F#"]
        
        for (offset, key) in zip(circleOfFifthsOffsets, keys) {
            let transposedNames = generator.generateProgressionNames(
                progressionData: testData,
                transpositionSemitones: offset
            )
            
            if verbose {
                let chordStr = transposedNames.joined(separator: " - ")
                print("\(key)调 (移调 \(offset >= 0 ? "+" : "")\(offset) 半音): \(chordStr)")
            }
        }
    }
} 