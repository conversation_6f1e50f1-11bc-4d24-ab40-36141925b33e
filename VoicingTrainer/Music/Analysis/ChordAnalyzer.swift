import Foundation

/// Swift implementation of chord analysis from MIDI notes
/// Based on the Python pychord library
class ChordAnalyzer {
    
    // MARK: - Constants
    
    /// Note names mapped to their chromatic values
    private static let noteValueDict: [String: Int] = [
        "C": 0, "C#": 1, "Db": 1, "D": 2, "D#": 3, "Eb": 3,
        "E": 4, "F": 5, "F#": 6, "Gb": 6, "G": 7, "G#": 8,
        "Ab": 8, "A": 9, "A#": 10, "Bb": 10, "B": 11, "Cb": 11
    ]
    
    /// Chromatic values mapped to note names (preferring sharps)
    private static let valueNoteDict: [Int: String] = [
        0: "C", 1: "C#", 2: "D", 3: "D#", 4: "E", 5: "F",
        6: "F#", 7: "G", 8: "G#", 9: "A", 10: "A#", 11: "B"
    ]
    
    /// Chord quality definitions (name, components) - matching Python pychord exactly
    private static let defaultQualities: [(String, [Int])] = [
        // 2 notes
        ("5", [0, 7]),
        ("no5", [0, 4]),
        ("omit5", [0, 4]),
        ("m(no5)", [0, 3]),
        ("m(omit5)", [0, 3]),
        // 3 notes
        ("", [0, 4, 7]),
        ("maj", [0, 4, 7]),
        ("m", [0, 3, 7]),
        ("min", [0, 3, 7]),
        ("-", [0, 3, 7]),
        ("dim", [0, 3, 6]),
        ("(b5)", [0, 4, 6]),
        ("aug", [0, 4, 8]),
        ("sus2", [0, 2, 7]),
        ("sus4", [0, 5, 7]),
        ("sus", [0, 5, 7]),
        // 4 notes
        ("6", [0, 4, 7, 9]),
        ("6b5", [0, 4, 6, 9]),
        ("6-5", [0, 4, 6, 9]),
        ("7", [0, 4, 7, 10]),
        ("7-5", [0, 4, 6, 10]),
        ("7b5", [0, 4, 6, 10]),
        ("7+5", [0, 4, 8, 10]),
        ("7#5", [0, 4, 8, 10]),
        ("7sus4", [0, 5, 7, 10]),
        ("m6", [0, 3, 7, 9]),
        ("m7", [0, 3, 7, 10]),
        ("m7-5", [0, 3, 6, 10]),
        ("m7b5", [0, 3, 6, 10]),
        ("m7+5", [0, 3, 8, 10]),
        ("m7#5", [0, 3, 8, 10]),
        ("dim6", [0, 3, 6, 8]),
        ("dim7", [0, 3, 6, 9]),
        ("M7", [0, 4, 7, 11]),
        ("maj7", [0, 4, 7, 11]),
        ("maj7+5", [0, 4, 8, 11]),
        ("M7+5", [0, 4, 8, 11]),
        ("mmaj7", [0, 3, 7, 11]),
        ("mM7", [0, 3, 7, 11]),
        ("add4", [0, 4, 5, 7]),
        ("majadd4", [0, 4, 5, 7]),
        ("Madd4", [0, 4, 5, 7]),
        ("madd4", [0, 3, 5, 7]),
        ("add9", [0, 4, 7, 14]),
        ("majadd9", [0, 4, 7, 14]),
        ("Madd9", [0, 4, 7, 14]),
        ("madd9", [0, 3, 7, 14]),
        ("sus4add9", [0, 5, 7, 14]),
        ("sus4add2", [0, 2, 5, 7]),
        ("2", [0, 4, 7, 14]),
        ("add11", [0, 4, 7, 17]),
        ("4", [0, 4, 7, 17]),
        // 5 notes
        ("m69", [0, 3, 7, 9, 14]),
        ("69", [0, 4, 7, 9, 14]),
        ("9", [0, 4, 7, 10, 14]),
        ("m9", [0, 3, 7, 10, 14]),
        ("M9", [0, 4, 7, 11, 14]),
        ("maj9", [0, 4, 7, 11, 14]),
        ("9sus4", [0, 5, 7, 10, 14]),
        ("7-9", [0, 4, 7, 10, 13]),
        ("7b9", [0, 4, 7, 10, 13]),
        ("7(b9)", [0, 4, 7, 10, 13]),
        ("7+9", [0, 4, 7, 10, 15]),
        ("7#9", [0, 4, 7, 10, 15]),
        ("9-5", [0, 4, 6, 10, 14]),
        ("9b5", [0, 4, 6, 10, 14]),
        ("9+5", [0, 4, 8, 10, 14]),
        ("9#5", [0, 4, 8, 10, 14]),
        ("7#9b5", [0, 4, 6, 10, 15]),
        ("7#9#5", [0, 4, 8, 10, 15]),
        ("m7b9b5", [0, 3, 6, 10, 13]),
        ("7b9b5", [0, 4, 6, 10, 13]),
        ("7b9#5", [0, 4, 8, 10, 13]),
        ("11", [0, 7, 10, 14, 17]),
        ("7+11", [0, 4, 7, 10, 18]),
        ("7#11", [0, 4, 7, 10, 18]),
        ("maj7+11", [0, 4, 7, 11, 18]),
        ("M7+11", [0, 4, 7, 11, 18]),
        ("maj7#11", [0, 4, 7, 11, 18]),
        ("M7#11", [0, 4, 7, 11, 18]),
        ("7b9#9", [0, 4, 7, 10, 13, 15]),
        ("7b9#11", [0, 4, 7, 10, 13, 18]),
        ("7#9#11", [0, 4, 7, 10, 15, 18]),
        ("7-13", [0, 4, 7, 10, 20]),
        ("7b13", [0, 4, 7, 10, 20]),
        ("m7add11", [0, 3, 7, 10, 17]),
        ("maj7add11", [0, 4, 7, 11, 17]),
        ("M7add11", [0, 4, 7, 11, 17]),
        ("mmaj7add11", [0, 3, 7, 11, 17]),
        ("mM7add11", [0, 3, 7, 11, 17]),
        // 6 notes
        ("7b9b13", [0, 4, 7, 10, 13, 17, 20]),
        ("9+11", [0, 4, 7, 10, 14, 18]),
        ("9#11", [0, 4, 7, 10, 14, 18]),
        ("m11", [0, 3, 7, 10, 14, 17]),
        ("13", [0, 4, 7, 10, 14, 21]),
        ("13-9", [0, 4, 7, 10, 13, 21]),
        ("13b9", [0, 4, 7, 10, 13, 21]),
        ("13+9", [0, 4, 7, 10, 15, 21]),
        ("13#9", [0, 4, 7, 10, 15, 21]),
        ("13+11", [0, 4, 7, 10, 18, 21]),
        ("13#11", [0, 4, 7, 10, 18, 21]),
        ("maj13", [0, 4, 7, 11, 14, 21]),
        ("M13", [0, 4, 7, 11, 14, 21]),
        ("maj7add13", [0, 4, 7, 9, 11, 14]),
        ("M7add13", [0, 4, 7, 9, 11, 14])
    ]
    
    private static let qualityDict: [[Int]: String] = {
        var dict: [[Int]: String] = [:]
        for (name, components) in defaultQualities {
            dict[components] = name
        }
        return dict
    }()
    
    // MARK: - Public Methods
    
    /// Analyze MIDI notes and return possible chord names
    /// - Parameter midiNotes: Array of MIDI note numbers (0-127)
    /// - Returns: Array of possible chord names
    static func analyze(midiNotes: [Int]) -> [String] {
        guard !midiNotes.isEmpty else { return [] }
        
        // Convert MIDI notes to note names (sorted from lowest to highest)
        let sortedMidiNotes = midiNotes.sorted()
        let noteNames = midiNotesToNoteNames(midiNotes: sortedMidiNotes)
        
        // Find chords from note names using the same algorithm as Python pychord
        return findChordsFromNotes(notes: noteNames)
    }
    
    // MARK: - Private Methods
    
    /// Convert MIDI note numbers to note names
    private static func midiNotesToNoteNames(midiNotes: [Int]) -> [String] {
        return midiNotes.map { midiNote in
            let chromaticValue = midiNote % 12
            return valueNoteDict[chromaticValue] ?? "C"
        }
    }
    
    /// Convert note name to chromatic value
    private static func noteToValue(_ note: String) -> Int {
        return noteValueDict[note] ?? 0
    }
    
    /// Find possible chords from note names - exactly matching Python algorithm
    private static func findChordsFromNotes(notes: [String]) -> [String] {
        guard !notes.isEmpty else { return [] }
        
        let bassNote = notes[0]
        var chords: [String] = []
        
        // Get all rotated note combinations
        let allRotatedNotes = getAllRotatedNotes(notes: notes)
        
        for rotatedNotes in allRotatedNotes {
            let rotatedRoot = rotatedNotes[0]
            let positions = notesToPositions(notes: rotatedNotes, root: rotatedRoot)
            
            // Find quality from positions
            if let quality = findQualityFromComponents(components: positions) {
                let chordName: String
                if rotatedRoot == bassNote {
                    chordName = "\(rotatedRoot)\(quality)"
                } else {
                    chordName = "\(rotatedRoot)\(quality)/\(bassNote)"
                }
                chords.append(chordName)
            }
        }
        
        return chords
    }
    
    /// Get all rotated combinations of notes
    private static func getAllRotatedNotes(notes: [String]) -> [[String]] {
        var rotatedNotesList: [[String]] = []
        
        for i in 0..<notes.count {
            let rotated = Array(notes[i..<notes.count]) + Array(notes[0..<i])
            rotatedNotesList.append(rotated)
        }
        
        return rotatedNotesList
    }
    
    /// Convert notes to positions relative to root - exactly matching Python algorithm
    private static func notesToPositions(notes: [String], root: String) -> [Int] {
        let rootPos = noteToValue(root)
        var currentPos = rootPos
        var positions: [Int] = []
        
        for note in notes {
            var notePos = noteToValue(note)
            
            // Handle octave wrapping - this is the key part matching Python
            if notePos < currentPos {
                notePos += 12 * ((currentPos - notePos) / 12 + 1)
            }
            
            positions.append(notePos - rootPos)
            currentPos = notePos
        }
        
        return positions
    }
    
    /// Find chord quality from component positions
    private static func findQualityFromComponents(components: [Int]) -> String? {
        return qualityDict[components]
    }
}

// MARK: - Test Cases

#if DEBUG
extension ChordAnalyzer {
    /// Test the chord analyzer with predefined test cases
    static func runTests() {
        let testCases: [(name: String, midiNotes: [Int], expected: String)] = [
            ("C major", [60, 64, 67], "C"),
            ("C minor", [60, 63, 67], "Cm"),
            ("C7", [60, 64, 67, 70], "C7"),
            ("C major 7", [60, 64, 67, 71], "M7"),  // Python uses M7, not maj7
            ("C minor 7", [60, 63, 67, 70], "Cm7"),
            ("C major 9", [60, 64, 67, 71, 74], "M9"),  // Python uses M9
            ("C13 (might not match)", [60, 64, 67, 70, 74, 77], "13")
        ]
        
        print("Running ChordAnalyzer Tests:")
        print(String(repeating: "=", count: 40))
        
        for testCase in testCases {
            let result = analyze(midiNotes: testCase.midiNotes)
            let resultString = result.joined(separator: ", ")
            let passed = result.contains { $0.contains(testCase.expected) }
            
            print("\(testCase.name): \(testCase.midiNotes)")
            print("Expected: contains \(testCase.expected)")
            print("Got: \(resultString)")
            print("Status: \(passed ? "✅ PASS" : "❌ FAIL")")
            print(String(repeating: "-", count: 30))
        }
    }
}
#endif 