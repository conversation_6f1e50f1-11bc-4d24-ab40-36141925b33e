import Foundation

// MARK: - 音符和音程定义
struct ChordParserNote {
    let name: String
    let midiNumber: Int
    let octave: Int
    
    init(midiNumber: Int) {
        self.midiNumber = midiNumber
        self.octave = midiNumber / 12 - 1
        
        let noteNames = ["C", "C#", "D", "D#", "E", "F", "F#", "G", "G#", "A", "A#", "B"]
        let noteIndex = midiNumber % 12
        self.name = noteNames[noteIndex]
    }
    
    var pitchClass: Int {
        return midiNumber % 12
    }
}

struct ChordParserType {
    let name: String
    let intervals: [Int]  // 以半音为单位的音程
    let aliases: [String]
}

// MARK: - ChordNameParser 主类
class ChordNameParser {
    
    // 基本音程定义（以半音为单位）
    /*
    private static let intervals: [String: Int] = [
        "P1": 0,   // 纯一度 Perfect unison
        "m2": 1,   // 小二度 Minor second
        "M2": 2,   // 大二度 Major second
        "m3": 3,   // 小三度 Minor third
        "M3": 4,   // 大三度 Major third
        "P4": 5,   // 纯四度 Perfect fourth
        "TT": 6,   // 三全音 Tritone
        "P5": 7,   // 纯五度 Perfect fifth
        "m6": 8,   // 小六度 Minor sixth
        "M6": 9,   // 大六度 Major sixth
        "m7": 10,  // 小七度 Minor seventh
        "M7": 11,  // 大七度 Major seventh
        "P8": 12   // 纯八度 Perfect octave
    ]
     */
    private static let intervalsBySemitones: [Int: String] = [
        // 简单音程（0-12半音）
        0: "P1",            // 纯一度 Perfect unison
        1: "minor 2nd(H)",     // 小二度 Minor second
        2: "major 2nd(W)",     // 大二度 Major second
        3: "minor 3rd",     // 小三度 Minor third
        4: "major 3rd",     // 大三度 Major third
        5: "perfect 4th",   // 纯四度 Perfect fourth
        6: "tritone",       // 三全音 Tritone (A4/d5)
        7: "perfect 5th",   // 纯五度 Perfect fifth
        8: "minor 6th",     // 小六度 Minor sixth
        9: "major 6th",     // 大六度 Major sixth
        10: "minor 7th",    // 小七度 Minor seventh
        11: "major 7th",    // 大七度 Major seventh
        12: "perfect octave", // 纯八度 Perfect octave
        
        // 复合音程（13-24半音）
        13: "minor 9th",    // 小九度 = 八度 + 小七度
        14: "major 9th",    // 大九度 = 八度 + 大七度
        15: "minor 10th",   // 小十度 = 八度 + 小三度
        16: "major 10th",   // 大十度 = 八度 + 大三度
        17: "perfect 11th", // 纯十一度 = 八度 + 纯四度
        18: "augmented 11th", // 增十一度 = 八度 + 增四度
        19: "perfect 12th", // 纯十二度 = 八度 + 纯五度
        20: "minor 13th",   // 小十三度 = 八度 + 小六度
        21: "major 13th",   // 大十三度 = 八度 + 大六度
        22: "minor 14th",   // 小十四度 = 八度 + 小七度
        23: "major 14th",   // 大十四度 = 八度 + 大七度
        24: "double octave" // 双八度 = 两个纯八度
        
        // 注：超过24半音的音程理论上可以继续扩展（如25=minor 16th等）
    ]
    // 和弦类型定义 - chordTypes
    private static let chordTypes: [ChordParserType] = [
        
        //音程
        /*
        ChordParserType(name: "minor 2nd", intervals: [1], aliases: ["minor 2nd","half step"]),
        ChordParserType(name: "major 2nd", intervals: [2], aliases: ["major 2nd","whole step"]),
        ChordParserType(name: "minor 3rd", intervals: [3], aliases: ["minor 3rd"]),
        ChordParserType(name: "major 3rd", intervals: [4], aliases: ["major 3rd"]),
        ChordParserType(name: "perfect 4th", intervals: [5], aliases: ["perfect 4th"]),
        ChordParserType(name: "tritone", intervals: [6], aliases: ["tritone"]),
        ChordParserType(name: "perfect 5th", intervals: [7], aliases: ["perfect 5th"]),
        ChordParserType(name: "minor 6th", intervals: [8], aliases: ["minor 6th"]),
        ChordParserType(name: "major 6th", intervals: [9], aliases: ["major 6th"]),
        ChordParserType(name: "minor 7th", intervals: [10], aliases: ["minor 7th"]),
        ChordParserType(name: "major 7th", intervals: [11], aliases: ["major 7th"]),
        ChordParserType(name: "major octave", intervals: [12], aliases: ["octave"]),
        */
        // 基本三和弦 (按使用频率排序以优化匹配)
        ChordParserType(name: "major", intervals: [4, 7], aliases: ["M", "maj"]),
        ChordParserType(name: "minor", intervals: [3, 7], aliases: ["m", "min", "-"]),
        
        // 七和弦
        ChordParserType(name: "major7", intervals: [4, 7, 11], aliases: ["M7"]),
        ChordParserType(name: "major7b5", intervals: [4, 6, 11], aliases: ["M7b5"]),
        ChordParserType(name: "dominant7", intervals: [4, 7, 10], aliases: ["7", "dom7"]),
        ChordParserType(name: "minor7", intervals: [3, 7, 10], aliases: ["m7", "min7", "-7"]),
        ChordParserType(name: "half-diminished7", intervals: [3, 6, 10], aliases: ["m7b5", "ø7", "ø"]),
        ChordParserType(name: "diminished7", intervals: [3, 6, 9], aliases: ["dim7", "o7"]),
        ChordParserType(name: "minor-major7", intervals: [3, 7, 11], aliases: ["mM7", "minormajor7"]),
        ChordParserType(name: "augmented7", intervals: [4, 8, 10], aliases: ["aug7", "+7"]),
        ChordParserType(name: "augmented-major7", intervals: [4, 8, 11], aliases: ["augmaj7", "+maj7", "+M7"]),
        
        // 其他三和弦
        ChordParserType(name: "diminished", intervals: [3, 6], aliases: ["dim", "o"]),
        ChordParserType(name: "augmented", intervals: [4, 8], aliases: ["aug", "+"]),
        
        // 挂留和弦
        ChordParserType(name: "sus4", intervals: [5, 7], aliases: ["sus"]),
        ChordParserType(name: "sus2", intervals: [2, 7], aliases: []),
        
        // 六和弦
        ChordParserType(name: "major6", intervals: [4, 7, 9], aliases: ["6", "add6"]),
        ChordParserType(name: "major69", intervals: [2, 4, 7, 9], aliases: ["6/9"]),
        ChordParserType(name: "minor6", intervals: [3, 7, 9], aliases: ["m6"]),
        
        // 九和弦53 56 60 63 67
        //ChordParserType(name: "major9", intervals: [4, 7, 11, 14], aliases: ["maj9", "M9"]),
        ChordParserType(name: "major9", intervals: [2, 4, 7, 11], aliases: ["M9","maj9"]),
        //ChordParserType(name: "minor9", intervals: [3, 7, 10, 14], aliases: ["m9", "min9", "-9"]),
        ChordParserType(name: "minor9", intervals: [2,3, 7, 10], aliases: ["m9", "min9", "-9"]),
        ChordParserType(name: "minor9(11)", intervals: [2,3,5,7,10], aliases: ["m9/11", "min9(11)", "-9/11"]),
        
        //属和弦
        ChordParserType(name: "dominant9", intervals: [4,7,10,14], aliases: ["9"]),
        ChordParserType(name: "dominant913", intervals: [2,4,7,9,10], aliases: ["9/13"]),
        ChordParserType(name: "dominant7b9", intervals: [1,4,7,10], aliases: ["7b9"]),
        
        // add和弦
        ChordParserType(name: "add9", intervals: [4, 7, 14], aliases: []),
        ChordParserType(name: "add2", intervals: [2, 4, 7], aliases: []),
        ChordParserType(name: "minor-add9", intervals: [3, 7, 14], aliases: ["madd9"]),
        
        // 五度和弦
        ChordParserType(name: "power", intervals: [7], aliases: ["5"])
    ]
    
    
    static func getIntervalName(forSemitones semitones: Int) -> String? {
        
        guard semitones >= 0 && semitones <= 24 else {
               return nil
        }
        
        return intervalsBySemitones[semitones]
        
        
    }
    // MARK: - 主要分析方法
    /// 分析 MIDI 音符并返回可能的和弦名称
    /// - Parameter midiNotes: MIDI 音符号数组
    /// - Returns: 可能的和弦名称数组
    static func analyze(midiNotes: [Int]) -> [String] {
        guard !midiNotes.isEmpty else { return [] }
        
        if midiNotes.count == 1{
            return NoteNameGenerator.getNoteNames(notes:midiNotes)
        }
        //c
        if midiNotes.count == 2{
            let interval = abs(midiNotes[1] - midiNotes[0])
            
            let root = NoteNameGenerator.getNoteName(note: midiNotes[0])
            
            let intervalName = (intervalsBySemitones[interval] ?? "beyond double octave") + " from " + root
            
            return [intervalName]
        }
        
        // 转换为 ChordParserNote 对象
        let notes = midiNotes.map { ChordParserNote(midiNumber: $0) }
        
        // 获取所有可能的根音
        var results: [String] = []
        
        // 尝试每个音符作为根音
        for (index, _) in notes.enumerated() {
            if let chordName = detectChordWithRoot(notes: notes, rootIndex: index) {
                let inversionSuffix = index == 0 ? "" : "/\(notes[0].name)"
                results.append(chordName + inversionSuffix)
            }
        }
        
        // 去重
        let uniqueResults = Array(Set(results))
        
        // 🔧 智能排序：优先显示更常用的和弦名称
        let sortedResults = smartSortChordNames(uniqueResults)
        
        return sortedResults.isEmpty ? ["Unknown"] : sortedResults
    }
    
    // MARK: - 私有辅助方法
    
    /// 以指定根音检测和弦
    private static func detectChordWithRoot(notes: [ChordParserNote], rootIndex: Int) -> String? {
        let rootNote = notes[rootIndex]
        
        // 重新排列音符，将根音放在第一位
        var reorderedNotes = notes
        if rootIndex != 0 {
            reorderedNotes = Array(notes[rootIndex...]) + Array(notes[..<rootIndex])
        }
        
        // 计算相对于根音的音程
        let intervals = calculateIntervals(notes: reorderedNotes)
        
        // 🔍 强制调试输出以诊断问题
        #if DEBUG
        // 🔧 BugFix: 临时强制启用调试输出以诊断和弦识别问题
        print("🎵 ChordNameParser Debug (Root: \(rootNote.name)):")
        for note in notes {
            print("  Note: \(note.name) (MIDI: \(note.midiNumber))")
        }
        print("  Intervals: \(intervals)")
        print("  Attempting to match intervals: \(intervals)")
        #endif
        
        // 匹配和弦类型
        if let chordType = matchChordType(intervals: intervals) {
            return rootNote.name + chordType
        }
        
        return nil
    }
    
    /// 计算音程数组
    private static func calculateIntervals(notes: [ChordParserNote]) -> [Int] {
        guard notes.count > 1 else { return [] }
        
        let rootPitchClass = notes[0].pitchClass
        var intervals: [Int] = []
        
        for i in 1..<notes.count {
            let interval = (notes[i].pitchClass - rootPitchClass + 12) % 12
            if interval != 0 { // 排除重复的根音
                intervals.append(interval)
            }
        }
        
        // 去重并排序
        let uniqueIntervals = Array(Set(intervals)).sorted()
        return uniqueIntervals
    }
    
    /// 匹配和弦类型
    private static func matchChordType(intervals: [Int]) -> String? {
        #if DEBUG
        print("🎵 matchChordType: 尝试匹配音程 \(intervals)")
        #endif
        
        // 完全匹配
        for chordType in chordTypes {
            if intervals == chordType.intervals {
                #if DEBUG
                print("🎵 匹配成功！完全匹配: \(chordType.name) (intervals: \(chordType.intervals))")
                #endif
                return chordType.aliases.first ?? chordType.name
            }
        }
        
        #if DEBUG
        print("🎵 完全匹配失败，尝试部分匹配...")
        #endif
        
        // 部分匹配（允许省略某些音，但保留核心音程）
        for chordType in chordTypes {
            if isSubset(intervals, of: chordType.intervals) && intervals.count >= 2 {
                #if DEBUG
                print("🎵 匹配成功！部分匹配: \(chordType.name) (target: \(chordType.intervals), actual: \(intervals))")
                #endif
                let suffix = chordType.aliases.first ?? chordType.name
                return suffix
            }
        }
        
        #if DEBUG
        print("🎵 匹配失败！没有找到匹配的和弦类型")
        print("🎵 可用的和弦类型前5个:")
        for (index, chordType) in chordTypes.prefix(5).enumerated() {
            print("  \(index + 1). \(chordType.name): \(chordType.intervals)")
        }
        #endif
        
        return nil
    }
    
    /// 检查是否为子集
    private static func isSubset(_ subset: [Int], of superset: [Int]) -> Bool {
        return subset.allSatisfy { superset.contains($0) }
    }
    
    // MARK: - 智能排序方法
    
    /// 获取和弦优先级（数字越小优先级越高）
    /// - Parameter chordName: 和弦名称
    /// - Returns: 优先级数字
    private static func getChordPriority(_ chordName: String) -> Int {
        let baseChord = chordName.contains("/") ? String(chordName.split(separator: "/")[0]) : chordName
        
        // 优先级1: 基本三和弦（C, Dm, Em, CM, Am等）
        if isBasicTriad(baseChord) {
            return 1
        }
        
        // 优先级2: 常用七和弦（Dm7, C7, CM7等）
        if isBasicSeventhChord(baseChord) {
            return 2
        }
        
        // 优先级3: 六和弦（C6, Dm6等）
        if baseChord.hasSuffix("6") || baseChord.hasSuffix("m6") {
            return 3
        }
        
        // 优先级4: 九和弦（C9, Dm9等）
        if baseChord.hasSuffix("9") || baseChord.hasSuffix("m9") || baseChord.hasSuffix("M9") {
            return 4
        }
        
        // 优先级5: 挂留和弦（Csus4, Csus2等）
        if baseChord.contains("sus") {
            return 5
        }
        
        // 优先级6: add和弦（Cadd9等）
        if baseChord.contains("add") {
            return 6
        }
        
        // 优先级7: 复杂和弦（aug, dim, 复杂的七和弦等）
        if baseChord.contains("aug") || baseChord.contains("dim") || baseChord.contains("ø") || baseChord.contains("o") {
            return 7
        }
        
        // 优先级8: 其他复杂和弦
        return 8
    }
    
    /// 检查是否为基本三和弦
    private static func isBasicTriad(_ chord: String) -> Bool {
        // 移除根音部分，检查剩余的和弦类型
        let rootPattern = "^[A-G][#b]?"
        let remainingPart = chord.replacingOccurrences(of: rootPattern, with: "", options: .regularExpression)
        
        // 基本三和弦的后缀：空字符串(大三和弦)、"m"(小三和弦)、"M"(大三和弦)
        return remainingPart.isEmpty || remainingPart == "m" || remainingPart == "M"
    }
    
    /// 检查是否为基本七和弦
    private static func isBasicSeventhChord(_ chord: String) -> Bool {
        // 常见的七和弦后缀
        let seventhSuffixes = ["7", "m7", "M7", "maj7"]
        return seventhSuffixes.contains { chord.hasSuffix($0) }
    }
    
    /// 智能排序和弦名称：优先显示更常用的写法
    /// - Parameter chordNames: 待排序的和弦名称数组
    /// - Returns: 排序后的和弦名称数组
    private static func smartSortChordNames(_ chordNames: [String]) -> [String] {
        guard !chordNames.isEmpty else { return [] }
        
        // 如果只有一个和弦名，直接返回
        if chordNames.count == 1 {
            return chordNames
        }
        
        #if DEBUG
        print("🎵 智能排序前: \(chordNames)")
        #endif
        
        // 按优先级排序
        let sorted = chordNames.sorted { chord1, chord2 in
            // 规则1: 不包含斜杠的优先（基本和弦 > 转位和弦）
            let hasSlash1 = chord1.contains("/")
            let hasSlash2 = chord2.contains("/")
            
            if hasSlash1 != hasSlash2 {
                return !hasSlash1 // 不包含斜杠的排前面
            }
            
            // 规则2: 和弦类型优先级（基本和弦 > 七和弦 > 复杂和弦）
            let priority1 = getChordPriority(chord1)
            let priority2 = getChordPriority(chord2)
            
            if priority1 != priority2 {
                return priority1 < priority2 // 优先级数字越小越优先
            }
            
            // 规则3: 长度优先（更简单的优先）
            if chord1.count != chord2.count {
                return chord1.count < chord2.count
            }
            
            // 规则4: 字母顺序
            return chord1 < chord2
        }
        
        #if DEBUG
        print("🎵 智能排序后: \(sorted)")
        #endif
        
        return sorted
    }
    
    // MARK: - 辅助分析方法
    
    /// 获取和弦的详细信息
    /// - Parameter midiNotes: MIDI 音符号数组
    /// - Returns: 详细的和弦分析结果
    static func analyzeDetailed(midiNotes: [Int]) -> ChordParserAnalysis? {
        guard !midiNotes.isEmpty else { return nil }
        
        let notes = midiNotes.map { ChordParserNote(midiNumber: $0) }
        let intervals = calculateIntervals(notes: notes)
        
        if let chordType = chordTypes.first(where: { $0.intervals == intervals }) {
            return ChordParserAnalysis(
                rootNote: notes[0].name,
                chordType: chordType.name,
                intervals: intervals,
                notes: notes.map { $0.name },
                quality: getChordQuality(chordType: chordType)
            )
        }
        
        return nil
    }
    
    /// 获取和弦性质
    private static func getChordQuality(chordType: ChordParserType) -> String {
        if chordType.name.contains("major") {
            return "major"
        } else if chordType.name.contains("minor") {
            return "minor"
        } else if chordType.name.contains("diminished") {
            return "diminished"
        } else if chordType.name.contains("augmented") {
            return "augmented"
        } else {
            return "other"
        }
    }
}

// MARK: - 数据结构
struct ChordParserAnalysis {
    let rootNote: String
    let chordType: String
    let intervals: [Int]
    let notes: [String]
    let quality: String
} 
