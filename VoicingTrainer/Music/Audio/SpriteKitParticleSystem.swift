//
//  SpriteKitParticleSystem.swift
//  FullUI
//
//  Created by <PERSON> on 2025/5/26.
//  基于SpriteKit的音名爆炸粒子系统
//

import Foundation
import SpriteKit
import SwiftUI
#if os(iOS)
import UIKit

extension UIBezierPath {
    convenience init(string: String, attributes: [NSAttributedString.Key: Any]) {
        self.init()
        let attributedString = NSAttributedString(string: string, attributes: attributes)
        let line = CTLineCreateWithAttributedString(attributedString)
        let runs = CTLineGetGlyphRuns(line) as! [CTRun]
        
        for run in runs {
            let attributes = CTRunGetAttributes(run) as! [NSAttributedString.Key: Any]
            let font = attributes[.font] as! UIFont
            
            let glyphCount = CTRunGetGlyphCount(run)
            let glyphs = UnsafeMutablePointer<CGGlyph>.allocate(capacity: glyphCount)
            let positions = UnsafeMutablePointer<CGPoint>.allocate(capacity: glyphCount)
            
            CTRunGetGlyphs(run, CFRangeMake(0, glyphCount), glyphs)
            CTRunGetPositions(run, CFRangeMake(0, glyphCount), positions)
            
            let path = CGMutablePath()
            for i in 0..<glyphCount {
                if let letterPath = font.getPath(for: glyphs[i], at: positions[i]) {
                    path.addPath(letterPath)
                }
            }
            
            self.append(UIBezierPath(cgPath: path))
            
            glyphs.deallocate()
            positions.deallocate()
        }
    }
}

extension UIFont {
    func getPath(for glyph: CGGlyph, at position: CGPoint) -> CGPath? {
        let ctFont = CTFontCreateWithName(fontName as CFString, pointSize, nil)
        var transform = CGAffineTransform(translationX: position.x, y: position.y)
        return CTFontCreatePathForGlyph(ctFont, glyph, &transform)
    }
}

#else

import AppKit

extension NSBezierPath {
    var cgPath: CGPath {
        let path = CGMutablePath()
        var points = [CGPoint](repeating: .zero, count: 3)
        
        for i in 0..<elementCount {
            let type = element(at: i, associatedPoints: &points)
            switch type {
            case .moveTo:
                path.move(to: points[0])
            case .lineTo:
                path.addLine(to: points[0])
            case .curveTo:
                path.addCurve(to: points[2], control1: points[0], control2: points[1])
            case .closePath:
                path.closeSubpath()
            default:
                break
            }
        }
        
        return path
    }
    
    convenience init(text: String, attributes: [NSAttributedString.Key: Any]) {
        self.init()
        let attributedString = NSAttributedString(string: text, attributes: attributes)
        let line = CTLineCreateWithAttributedString(attributedString)
        let runs = CTLineGetGlyphRuns(line) as! [CTRun]
        
        for run in runs {
            let attributes = CTRunGetAttributes(run) as! [NSAttributedString.Key: Any]
            let font = attributes[.font] as! NSFont
            
            let glyphCount = CTRunGetGlyphCount(run)
            let glyphs = UnsafeMutablePointer<CGGlyph>.allocate(capacity: glyphCount)
            let positions = UnsafeMutablePointer<CGPoint>.allocate(capacity: glyphCount)
            
            CTRunGetGlyphs(run, CFRangeMake(0, glyphCount), glyphs)
            CTRunGetPositions(run, CFRangeMake(0, glyphCount), positions)
            
            for i in 0..<glyphCount {
                if let letterPath = font.getPath(for: glyphs[i], at: positions[i]) {
                    self.append(NSBezierPath(cgPath: letterPath))
                }
            }
            
            glyphs.deallocate()
            positions.deallocate()
        }
    }
}

extension NSFont {
    func getPath(for glyph: CGGlyph, at position: CGPoint) -> CGPath? {
        let ctFont = CTFontCreateWithName(fontName as CFString, pointSize, nil)
        var transform = CGAffineTransform(translationX: position.x, y: position.y)
        return CTFontCreatePathForGlyph(ctFont, glyph, &transform)
    }
}

#endif

// MARK: - 音名爆炸场景

/**
 * 音名爆炸SpriteKit场景
 * 负责处理音名的爆炸粒子效果
 */
class NoteExplodeScene: SKScene {
    
    // 配置参数
    private let noteFontSize: CGFloat = 48
    private let gravity: CGFloat = -10 // 减少重力，让粒子飞得更久
    private let explosionForceRange: ClosedRange<CGFloat> = 200...400 // 增加爆炸力
    private let particleLifetime: TimeInterval = 4.0 // 增加生命周期
    
    // 计分相关
    private var cornerStarNode: SKSpriteNode?
    private var rightNoteCount: Int = 0
    
    // 音符图标位置
    private var musicNoteIconPosition: CGPoint = .zero
    
    override func didMove(to view: SKView) {
        super.didMove(to: view)
        print("🎮 SpriteKit场景已加载，大小: \(size)")
        
        // 🔧 坐标系调试：检查场景大小
        print("🔍 场景大小详细信息:")
        print("   宽度: \(size.width)")
        print("   高度: \(size.height)")
        print("   视图边界: \(view.bounds)")
        print("   视图大小: \(view.bounds.size)")
        
        backgroundColor = .clear
        physicsWorld.gravity = CGVector(dx: 0, dy: gravity)
        
        // 设置场景大小为视图大小
        size = view.bounds.size
        print("🎬 SpriteKit场景初始化，大小: \(size)")
        
        // 设置场景边界
        let border = SKPhysicsBody(edgeLoopFrom: frame)
        physicsBody = border
        physicsBody?.friction = 0.3
        physicsBody?.restitution = 0.2
        
        // 🔧 注意：不在这里绘制调试圆圈，因为场景大小可能还是0
        // 调试圆圈将在updateSceneSize中绘制
        
        // 🔧 新增：延迟检查机制，定期检查场景大小
        //startSizeCheckTimer()
        
        // 延迟创建星星，确保场景大小已正确设置
        DispatchQueue.main.async {
            self.setupCornerStar()
        }
    }
    
    /**
     * 启动大小检查定时器
     */
    private func startSizeCheckTimer() {
        let checkAction = SKAction.run { [weak self] in
            guard let self = self else { return }
            
            print("⏰ 定期检查场景大小: \(self.size)")
            
            if self.size.width > 0 && self.size.height > 0 {
                print("✅ 场景大小已有效，绘制调试元素")
                self.drawCoordinateDebugElements()
                
                // 移除定时器
                self.removeAction(forKey: "sizeCheckTimer")
                print("🛑 停止大小检查定时器")
            }
        }
        
        let waitAction = SKAction.wait(forDuration: 0.5) // 每0.5秒检查一次
        let repeatAction = SKAction.repeatForever(SKAction.sequence([checkAction, waitAction]))
        
        run(repeatAction, withKey: "sizeCheckTimer")
        print("⏰ 启动大小检查定时器")
    }
    
    /**
     * 更新场景大小时调用
     */
    func updateSceneSize(_ newSize: CGSize) {
        size = newSize
        print("📐 场景大小更新: \(newSize)")
        
        // 🔧 只有当场景大小有效时才绘制调试圆圈
        //if newSize.width > 0 && newSize.height > 0 {
        //    drawCoordinateDebugElements()
        //}
        
        // 重新设置星星位置
        if cornerStarNode == nil {
            setupCornerStar()
        } else {
            updateCornerStarPosition()
        }
    }
    
    /**
     * 绘制坐标系调试元素
     */
    private func drawCoordinateDebugElements() {
        // 🧹 先清除之前的调试元素
        /*
        enumerateChildNodes(withName: "*Origin") { node, _ in
            node.removeFromParent()
        }
        enumerateChildNodes(withName: "corner_*") { node, _ in
            node.removeFromParent()
        }
        
        
        
        print("🔧 开始绘制坐标系调试元素，场景大小: \(size)")
        
        // 1. 红色圆 - SpriteKit坐标系原点 (0,0) 在左下角
        let spriteKitOrigin = SKShapeNode(circleOfRadius: 30) // 半径30
        spriteKitOrigin.fillColor = .red
        spriteKitOrigin.strokeColor = NSColor(red: 0.5, green: 0, blue: 0, alpha: 1.0) // 深红色
        spriteKitOrigin.lineWidth = 3
        spriteKitOrigin.position = CGPoint(x: 0, y: 0) // SpriteKit原点
        spriteKitOrigin.zPosition = 1000
        spriteKitOrigin.name = "spriteKitOrigin"
        addChild(spriteKitOrigin)
        print("🔴 红色圆（SpriteKit原点）: (0, 0)")
        
        // 2. 绿色圆 - SwiftUI坐标系原点 (0,0) 转换后的位置
        let swiftUIOriginInSpriteKit = CGPoint(x: 0, y: size.height) // SwiftUI (0,0) 在SpriteKit中的位置
        let swiftUIOrigin = SKShapeNode(circleOfRadius: 20) // 半径20，更小便于区分
        swiftUIOrigin.fillColor = .green
        swiftUIOrigin.strokeColor = NSColor(red: 0, green: 0.5, blue: 0, alpha: 1.0) // 深绿色
        swiftUIOrigin.lineWidth = 3
        swiftUIOrigin.position = swiftUIOriginInSpriteKit
        swiftUIOrigin.zPosition = 1001 // 稍高一层
        swiftUIOrigin.name = "swiftUIOrigin"
        addChild(swiftUIOrigin)
        print("🟢 绿色圆（SwiftUI原点转换）: \(swiftUIOriginInSpriteKit)")
        
        // 🔧 详细位置信息
        print("📏 坐标系详细对比:")
        print("   🔴 红色圆实际位置: \(spriteKitOrigin.position)")
        print("   🟢 绿色圆实际位置: \(swiftUIOrigin.position)")
        print("   📐 两圆距离: \(sqrt(pow(swiftUIOrigin.position.x - spriteKitOrigin.position.x, 2) + pow(swiftUIOrigin.position.y - spriteKitOrigin.position.y, 2)))")
        
        // 🔧 添加四个角落的参考点，便于理解坐标系
        let corners = [
            ("左下角", CGPoint(x: 0, y: 0)),
            ("右下角", CGPoint(x: size.width, y: 0)),
            ("左上角", CGPoint(x: 0, y: size.height)),
            ("右上角", CGPoint(x: size.width, y: size.height))
        ]
        
        for (name, point) in corners {
            let cornerDot = SKShapeNode(circleOfRadius: 5)
            cornerDot.fillColor = .blue
            cornerDot.strokeColor = .white
            cornerDot.lineWidth = 1
            cornerDot.position = point
            cornerDot.zPosition = 999
            cornerDot.name = "corner_\(name)"
            addChild(cornerDot)
            print("🔵 \(name): \(point)")
        }
        
        print("📍 坐标系总结：")
        print("   🔴 红色圆 = SpriteKit原点 (0,0) - 应该在左下角")
        print("   🟢 绿色圆 = SwiftUI原点 (0,0) 转换到SpriteKit - 应该在左上角")
        print("   🔵 蓝色点 = 四个角落参考点")
        print("   ✅ 现在场景大小有效：\(size)")
         */
    }
    
    /**
     * 更新右下角星星位置
     */
    private func updateCornerStarPosition() {
        guard let cornerStar = cornerStarNode else { return }
        
        let margin: CGFloat = 80
        let starPosition = CGPoint(
            x: size.width - margin,
            y: margin
        )
        cornerStar.position = starPosition
        print("⭐ 星星位置更新: \(starPosition)")
    }
    
    /**
     * 设置右下角的星星
     */
    private func setupCornerStar() {
        // 加载星星图片
        var starTexture: SKTexture?
        
        // 尝试加载star图片
        starTexture = SKTexture(imageNamed: "star")
        if starTexture != nil {
            print("✅ 从star加载成功")
        } else {
            starTexture = SKTexture(imageNamed: "Assets/star")
            if starTexture != nil {
                print("✅ 从Assets/star加载成功")
            } else {
                print("❌ 无法加载star图片")
                return
            }
        }
        
        guard let texture = starTexture else { return }
        
        // 创建星星节点
        cornerStarNode = SKSpriteNode(texture: texture)
        cornerStarNode?.size = CGSize(width: 50, height: 50)
        
        // 设置位置在右下角
        let margin: CGFloat = 80 // 距离边缘的距离
        let starPosition = CGPoint(
            x: size.width - margin,
            y: margin  // SpriteKit坐标系：原点在左下角，所以这是距离底部的距离
        )
        cornerStarNode?.position = starPosition
        
        print("⭐ 星星位置计算Miles:\(size.width)")
        print("   场景大小: \(size)")
        print("   边距: \(margin)")
        print("   星星位置: \(starPosition)")
        cornerStarNode?.zPosition = 100
        cornerStarNode?.name = "cornerStar"
        
        if let cornerStar = cornerStarNode {
            addChild(cornerStar)
            print("⭐ 右下角星星已创建，位置: \(cornerStar.position)")
        }
    }
    
    /**
     * 获取文字的路径
     * - Parameters:
     *   - text: 要转换的文字
     *   - position: 文字位置
     *   - color: 文字颜色
     * - Returns: 文字的CGPath
     */
    private func getLetterPath(for text: String, at position: CGPoint, color: Color) -> CGPath {
        #if os(iOS)
        let font = UIFont.boldSystemFont(ofSize: noteFontSize)
        let attributes: [NSAttributedString.Key: Any] = [.font: font]
        let path = UIBezierPath(string: text, attributes: attributes)
        #else
        let font = NSFont.boldSystemFont(ofSize: noteFontSize)
        let attributes: [NSAttributedString.Key: Any] = [.font: font]
        let path = NSBezierPath(text: text, attributes: attributes)
        #endif
        
        // 让路径的中心和position一致
        let bounds = path.cgPath.boundingBox
        let offset = CGPoint(x: position.x - bounds.midX, y: position.y - bounds.midY)
        var transform = CGAffineTransform(translationX: offset.x, y: offset.y)
        return path.cgPath.copy(using: &transform) ?? path.cgPath
    }
    
    /**
     * 触发音名爆炸效果
     * - Parameters:
     *   - noteName: 音名字符串 (如 "C", "F#")
     *   - position: 爆炸位置 (SwiftUI坐标系)
     *   - color: 粒子颜色
     */
    func explodeNote(_ noteName: String, at position: CGPoint, color: Color) {
        print("🎆 ===== 粒子爆炸坐标调试 =====")
        print("🎵 音名: \(noteName)")
        print("📱 接收到的相对于EnhancedTargetNotesDisplay的坐标: \(position)")
        print("📐 当前SpriteKit场景大小: \(size)")
        
        // 转换SwiftUI坐标到SpriteKit坐标
        let spriteKitPosition = convertSwiftUIToSpriteKit(position)
        print("🔄 转换后的SpriteKit坐标: \(spriteKitPosition)")
        
        // 验证转换是否合理
        if spriteKitPosition.x < 0 || spriteKitPosition.x > size.width ||
           spriteKitPosition.y < 0 || spriteKitPosition.y > size.height {
            print("⚠️  警告: 转换后的坐标超出场景边界!")
            print("   X: \(spriteKitPosition.x) (应在 0-\(size.width))")
            print("   Y: \(spriteKitPosition.y) (应在 0-\(size.height))")
        }
        
        // 简化实现：直接创建圆形粒子，不依赖文字路径
        let particleCount = 20 // 固定粒子数量
        
        // 转换SwiftUI Color到SKColor
        #if os(iOS)
        let skColor = UIColor(color)
        #else
        let skColor = NSColor(color)
        #endif
        
        print("🎨 使用颜色: \(skColor)")
        
        // 在转换后的位置周围创建粒子
        for i in 0..<particleCount {
            let angle = (CGFloat(i) / CGFloat(particleCount)) * 2 * .pi
            let radius: CGFloat = 20
            let particlePosition = CGPoint(
                x: spriteKitPosition.x + cos(angle) * radius,
                y: spriteKitPosition.y + sin(angle) * radius
            )
            
            // 打印第一个粒子的位置作为参考
            if i == 0 {
                print("🔵 第一个粒子的SpriteKit坐标: \(particlePosition)")
            }
            
            createSimpleParticle(at: particlePosition, color: skColor)
        }
        
        print("✅ 创建了 \(particleCount) 个粒子")
        
        // 创建飞行星星效果
        createFlyingStar(from: spriteKitPosition)
        
        print("🎆 ===== 调试信息结束 =====")
    }
    
    /**
     * 将SwiftUI坐标转换为SpriteKit坐标
     * SwiftUI: 原点在左上角，Y轴向下，相对于NoteDisplayContainer
     * SpriteKit: 原点在左下角，Y轴向上，相对于场景
     * 
     * 🔧 修复：正确处理相对坐标转换，不需要复杂的场景大小计算
     */
    private func convertSwiftUIToSpriteKit(_ relativePosition: CGPoint) -> CGPoint {
        print("🔧 坐标转换调试:")
        print("   📱 输入相对坐标: \(relativePosition)")
        print("   📏 SpriteKit场景大小: \(size)")
        
        // 🧪 临时测试：直接使用原始坐标，不进行转换
        // 这样可以测试原始坐标是否正确
        print("   🧪 测试：直接使用原始坐标")
        //let result = relativePosition
        
        /* 原始转换逻辑 - 先注释掉*/
        let spriteKitX = relativePosition.x
        let spriteKitY = size.height - relativePosition.y
        let result = CGPoint(x: spriteKitX, y: spriteKitY)
        
        
        print("   🔄 转换结果: \(result)")
        
        return result
    }
    
    /**
     * 创建简化的圆形粒子
     * - Parameters:
     *   - position: 粒子位置
     *   - color: 粒子颜色
     */
    private func createSimpleParticle(at position: CGPoint, color: SKColor) {
        // 创建更大的圆形粒子，更容易看见
        let particle = SKShapeNode(circleOfRadius: 4)
        particle.fillColor = color
        particle.strokeColor = .white
        particle.lineWidth = 2
        particle.position = position
        particle.zPosition = 1000 // 确保在最顶层
        
        // 添加物理体
        particle.physicsBody = SKPhysicsBody(circleOfRadius: 4)
        particle.physicsBody?.isDynamic = true
        particle.physicsBody?.mass = 0.05 // 减少质量，飞得更远
        
        // 设置爆炸力
        let explosionForce = CGFloat.random(in: explosionForceRange)
        let angle = CGFloat.random(in: 0...(2 * .pi))
        let dx = _math.cos(angle) * explosionForce
        let dy = _math.sin(angle) * explosionForce
        
        particle.physicsBody?.velocity = CGVector(dx: dx, dy: dy)
        particle.physicsBody?.angularVelocity = CGFloat.random(in: -5...5)
        
        // 设置阻尼
        particle.physicsBody?.linearDamping = 0.1 // 减少阻尼，飞得更远
        particle.physicsBody?.angularDamping = 0.2
        
        addChild(particle)
        
        // 粒子生命周期动画 - 延长可见时间
        let fadeDelay = SKAction.wait(forDuration: 2.0) // 2秒后才开始淡出
        let fadeOut = SKAction.fadeOut(withDuration: particleLifetime - 2.0)
        let remove = SKAction.removeFromParent()
        let sequence = SKAction.sequence([fadeDelay, fadeOut, remove])
        particle.run(sequence)
    }
    
    /**
     * 创建单个粒子（原版本，保留备用）
     * - Parameters:
     *   - position: 粒子位置
     *   - size: 粒子大小
     *   - color: 粒子颜色
     */
    private func createParticle(at position: CGPoint, size: CGFloat, color: SKColor) {
        // 创建粒子形状（小正方形）
        
        print("建立粒子:\(position)")
        
        let rect = CGRect(x: 0, y: 0, width: size, height: size)
        let particle = SKShapeNode(rect: rect)
        particle.fillColor = color
        particle.strokeColor = .clear
        particle.position = position
        
        // 添加物理体
        particle.physicsBody = SKPhysicsBody(rectangleOf: CGSize(width: size, height: size))
        particle.physicsBody?.isDynamic = true
        particle.physicsBody?.mass = 0.1
        
        // 设置爆炸力
        let explosionForce = CGFloat.random(in: explosionForceRange)
        let angle = CGFloat.random(in: 0...(2 * .pi))
        let dx = cos(angle) * explosionForce
        let dy = sin(angle) * explosionForce
        
        particle.physicsBody?.velocity = CGVector(dx: dx, dy: dy)
        particle.physicsBody?.angularVelocity = CGFloat.random(in: -5...5)
        
        // 设置阻尼
        particle.physicsBody?.linearDamping = 0.3
        particle.physicsBody?.angularDamping = 0.5
        
        addChild(particle)
        
        // 粒子生命周期动画
        let fadeDelay = SKAction.wait(forDuration: 0.5)
        let fadeOut = SKAction.fadeOut(withDuration: particleLifetime - 0.5)
        let remove = SKAction.removeFromParent()
        let sequence = SKAction.sequence([fadeDelay, fadeOut, remove])
        particle.run(sequence)
    }
    
    /**
     * 创建飞行星星效果
     * - Parameter fromPosition: 起始位置
     */
    private func createFlyingStar(from fromPosition: CGPoint) {
        // 加载星星图片
        var starTexture: SKTexture?
        starTexture = SKTexture(imageNamed: "star")
        if starTexture == nil {
            starTexture = SKTexture(imageNamed: "Assets/star")
            if starTexture == nil {
                print("❌ 无法创建飞行星星：无法加载star图片")
                return
            }
        }
        
        guard let texture = starTexture else { return }
        
        // 创建飞行星星
        let flyingStar = SKSpriteNode(texture: texture)
        flyingStar.size = CGSize(width: 30, height: 30) // 稍小一些
        flyingStar.position = fromPosition
        flyingStar.zPosition = 200
        flyingStar.name = "flyingStar"
        
        addChild(flyingStar)
        
        // 计算飞行到右上角音符图标的路径
        // 注意：这里需要将SwiftUI的右上角音符图标位置转换为SpriteKit坐标
        let targetPosition = calculateRightTopNoteIconPosition()
        
        
        let distance = sqrt(pow(targetPosition.x - fromPosition.x, 2) + pow(targetPosition.y - fromPosition.y, 2))
        let duration = TimeInterval(distance / 600) // 飞行速度：600点/秒
        
        // 创建飞行动画
        let moveAction = SKAction.move(to: targetPosition, duration: duration)
        moveAction.timingMode = .easeOut
        
        // 旋转动画
        let rotateAction = SKAction.rotate(byAngle: .pi * 4, duration: duration)
        
        // 缩放动画（飞行过程中稍微变大）
        let scaleUp = SKAction.scale(to: 1.2, duration: duration * 0.5)
        let scaleDown = SKAction.scale(to: 1.0, duration: duration * 0.5)
        let scaleSequence = SKAction.sequence([scaleUp, scaleDown])
        
        // 组合动画
        let flyGroup = SKAction.group([moveAction, rotateAction, scaleSequence])
        
        // 到达后的效果
        let arrivalEffect = SKAction.run { [weak self] in
            self?.onStarArrival()
            // 可以在这里添加到达右上角音符图标时的闪烁效果
        }
        
        // 移除飞行星星
        let remove = SKAction.removeFromParent()
        
        // 完整动画序列
        let fullSequence = SKAction.sequence([flyGroup, arrivalEffect, remove])
        flyingStar.run(fullSequence)
        
        print("⭐ 飞行星星已创建，从 \(fromPosition) 飞向 \(targetPosition)")
    }
    
    /**
     * 设置音符图标位置（从SwiftUI传入的全局坐标）
     */
    func setMusicNoteIconPosition(_ globalPosition: CGPoint) {
        // 将全局坐标转换为SpriteKit坐标
        let spriteKitPosition = convertSwiftUIToSpriteKit(globalPosition)
        musicNoteIconPosition = spriteKitPosition
        
        print("🎵 更新音符图标位置:")
        print("   SwiftUI全局坐标: \(globalPosition)")
        print("   SpriteKit坐标: \(spriteKitPosition)")
        print("   场景大小: \(size)")
    }
    
    /**
     * 计算右上角音符图标的SpriteKit坐标位置
     * 这个位置对应SwiftUI中ChordsView右上角compactGameStatusView中的第一个音符图标
     * 如果有精确位置就使用精确位置，否则使用估算位置
     */
    private func calculateRightTopNoteIconPosition() -> CGPoint {
        // 如果有精确的音符图标位置，就使用它
        if musicNoteIconPosition != .zero {
            return musicNoteIconPosition
        }
        
        // 否则使用估算位置（向后兼容）
        // 根据ChordsView.swift中compactGameStatusView的布局推算：
        // compactGameStatusView位于右上角，padding(.horizontal, 12) + padding(.vertical, 8)
        // 第一个HStack(spacing: 4)中的Image(systemName: "music.note")
        
        // 右上角音符图标的SwiftUI坐标（相对于整个场景）
        let rightMargin: CGFloat = 12 + 8 + 20 // padding + HStack内边距 + 图标估算宽度的一半
        let topMargin: CGFloat = 8 + 10 + 20   // padding + 估算高度 + 图标估算高度的一半
        
        let swiftUIX = size.width - rightMargin
        let swiftUIY = topMargin
        
        // 转换为SpriteKit坐标（原点在左下角，Y轴向上）
        let spriteKitX = swiftUIX
        let spriteKitY = size.height - swiftUIY
        
        let targetPosition = CGPoint(x: spriteKitX, y: spriteKitY)
        
        print("🎯 使用估算的音符图标位置:")
        print("   SwiftUI位置: (\(swiftUIX), \(swiftUIY))")
        print("   SpriteKit位置: \(targetPosition)")
        print("   场景大小: \(size)")
        
        return targetPosition
    }
    
    /**
     * 星星到达时的处理
     */
    private func onStarArrival() {
        rightNoteCount += 1
        print("⭐ 正确音符计数: \(rightNoteCount)")
        
        // 这里可以添加更多到达效果，比如粒子爆炸等
    }
    
    /**
     * 显示Perfect效果
     * - Parameters:
     *   - ratioA: 起始缩放比例
     *   - ratioB: 结束缩放比例
     */
    func showPerfectEffect(fromScale ratioA: CGFloat = 1.0, toScale ratioB: CGFloat = 1.5) {
        // 加载Perfect图片
        var perfectTexture: SKTexture?
        perfectTexture = SKTexture(imageNamed: "perfect")
        if perfectTexture != nil {
            print("✅ 从perfect加载成功")
        } else {
            perfectTexture = SKTexture(imageNamed: "Assets/perfect")
            if perfectTexture != nil {
                print("✅ 从Assets/perfect加载成功")
            } else {
                print("❌ 无法加载perfect图片")
                return
            }
        }
        
        guard let texture = perfectTexture else { return }
        
        // 创建Perfect节点
        let perfectNode = SKSpriteNode(texture: texture)
        perfectNode.size = CGSize(width: 200, height: 100) // 可调整大小
        // 显示在右下角CompactGameStatus上方
        let margin: CGFloat = 120 // 距离边缘的距离
        perfectNode.position = CGPoint(x: size.width - margin, y: margin + 80) // 右下角，CompactGameStatus上方
        perfectNode.zPosition = 1000 // 最顶层
        perfectNode.name = "perfectEffect"
        perfectNode.setScale(ratioA) // 初始缩放
        perfectNode.alpha = 0.0 // 初始透明
        
        addChild(perfectNode)
        
        // 创建非线性放大动画
        let scaleAction = SKAction.scale(to: ratioB, duration: 0.8)
        scaleAction.timingMode = .easeOut // 非线性缓动
        
        // 淡入动画
        let fadeInAction = SKAction.fadeIn(withDuration: 0.3)
        
        // 等待一段时间
        let waitAction = SKAction.wait(forDuration: 0.5)
        
        // 淡出动画
        let fadeOutAction = SKAction.fadeOut(withDuration: 0.5)
        
        // 移除节点
        let removeAction = SKAction.removeFromParent()
        
        // 组合动画
        let appearGroup = SKAction.group([scaleAction, fadeInAction])
        let fullSequence = SKAction.sequence([appearGroup, waitAction, fadeOutAction, removeAction])
        
        perfectNode.run(fullSequence)
        
        print("🎉 Perfect效果已显示，缩放从 \(ratioA) 到 \(ratioB)")
    }
    
    /**
     * 清理所有粒子
     */
    func clearAllParticles() {
        enumerateChildNodes(withName: "*") { node, _ in
            if node is SKShapeNode || node.name == "flyingStar" {
                node.removeFromParent()
            }
        }
    }
}

// MARK: - SwiftUI包装器

/**
 * SpriteKit粒子系统的SwiftUI包装器
 */
#if os(iOS)
struct SpriteKitParticleView: UIViewRepresentable {
    @EnvironmentObject var coordinator: ParticleCoordinator
    
    func makeUIView(context: Context) -> SKView {
        print("🎬 创建iOS SKView")
        let skView = SKView()
        skView.backgroundColor = .clear
        skView.allowsTransparency = true
        skView.isOpaque = false
        
        // 启用调试信息
        skView.showsFPS = true
        skView.showsNodeCount = true
        skView.showsPhysics = true // 显示物理边界，便于调试
        
        let scene = NoteExplodeScene()
        scene.scaleMode = .resizeFill
        scene.backgroundColor = .clear
        
        coordinator.scene = scene
        skView.presentScene(scene)
        
        print("✅ iOS SKView创建完成")
        return skView
    }
    
    func updateUIView(_ uiView: SKView, context: Context) {
        // 更新场景大小
        if let scene = coordinator.scene {
            let newSize = uiView.bounds.size
            scene.updateSceneSize(newSize)
            print("📐 iOS SKView 大小更新: \(newSize)")
        }
    }
}
#else
struct SpriteKitParticleView: NSViewRepresentable {
    @EnvironmentObject var coordinator: ParticleCoordinator
    
    func makeNSView(context: Context) -> SKView {
        print("🎬 创建macOS SKView")
        let skView = SKView()
        skView.allowsTransparency = true
        
        // 启用调试信息
        skView.showsFPS = true
        skView.showsNodeCount = true
        skView.showsPhysics = true // 显示物理边界，便于调试
        
        let scene = NoteExplodeScene()
        scene.scaleMode = .resizeFill
        scene.backgroundColor = .clear
        
        coordinator.scene = scene
        skView.presentScene(scene)
        
        print("✅ macOS SKView创建完成")
        return skView
    }
    
    func updateNSView(_ nsView: SKView, context: Context) {
        print("🔧 updateNSView被调用，视图边界: \(nsView.bounds)")
        
        // 🔧 强制设置场景大小
        if let scene = coordinator.scene {
            let newSize = nsView.bounds.size
            print("📐 强制设置场景大小: \(newSize)")
            
            // 立即设置场景大小
            scene.size = newSize
            
            // 如果大小有效，立即绘制调试元素
            if newSize.width > 0 && newSize.height > 0 {
                print("✅ 大小有效，调用updateSceneSize")
                scene.updateSceneSize(newSize)
            } else {
                print("❌ 大小仍然无效: \(newSize)")
            }
        }
    }
}
#endif

// MARK: - 协调器

/**
 * 粒子系统协调器
 * 负责管理SpriteKit场景和SwiftUI的交互
 */
class ParticleCoordinator: ObservableObject {
    static let shared = ParticleCoordinator()
    var scene: NoteExplodeScene?
    
    func explodeNote(_ noteName: String, at position: CGPoint, color: Color) {
        print("🎆 ParticleCoordinator.explodeNoteexplodeNote: \(noteName) at \(position)")
        
        if scene == nil {
            print("❌ Scene is nil!")
            return
        }
        
        DispatchQueue.main.async {
            print("🎯 调用scene.explodeNote")
            self.scene?.explodeNote(noteName, at: position, color: color)
        }
    }
    
    func setMusicNoteIconPosition(_ position: CGPoint) {
        print("🎵 设置音符图标目标位置: \(position)")
        DispatchQueue.main.async {
            self.scene?.setMusicNoteIconPosition(position)
        }
    }
    
    func clearParticles() {
        print("🧹 ParticleCoordinator.clearParticles")
        DispatchQueue.main.async {
            self.scene?.clearAllParticles()
        }
    }
    
    func showPerfectEffect(fromScale ratioA: CGFloat = 1.0, toScale ratioB: CGFloat = 1.5) {
        print("🎉 ParticleCoordinator.showPerfectEffect")
        DispatchQueue.main.async {
            self.scene?.showPerfectEffect(fromScale: ratioA, toScale: ratioB)
        }
    }
}

// MARK: - 视图修饰符

extension View {
    /**
     * 添加SpriteKit粒子效果支持
     */
    func withSpriteKitParticles() -> some View {
        self // 简化实现，直接返回原视图
    }
} 
