//
//  ChordPlayer.swift
//  单个和弦的播放.支持多个播放方法.如:同时按下block ;  琶音 arpeggio
//
//  Created by <PERSON> on 2025/5/26.
/**
 * 和弦播放器 - 处理和弦的MIDI播放
 * 专注于单个和弦的MIDI播放，支持两种播放方式：
 * 1. 同时播放所有音符 (block_chord)
 * 2. 琶音播放 (arpeggio_chord)
 */

import Foundation
import AVFoundation


class ChordPlayer: ObservableObject {
    
    // 🎹 人性化琶音配置参数
    struct HumanizedArpeggioConfig {
        /// 每分钟节拍数
        var bpm: Int = 70
        /// 基础音符间隔系数（1.0表示一个完整拍，0.5表示半拍）
        var baseNoteSpacingFactor: Double = 0.25
        /// 最后一个音符的持续时间（秒）
        var lastNoteDuration: Double = 2.0

        // 🎨 人性化参数
        /// 时间变化范围（±百分比，0.3表示±30%）
        var timingVariation: Double = 0.3
        /// 力度变化范围（±值，15表示±15）
        var velocityVariation: Int = 15
        /// 基础力度
        var baseVelocity: Int = 80
        /// 是否使用渐强效果（从弱到强）
        var useCrescendo: Bool = true
        /// 是否随机化音符顺序（而非总是从低到高）
        var randomizeOrder: Bool = false
    }
    private var arpeggioConfig = HumanizedArpeggioConfig()
    
    
    private let midiManager: MIDIManager
    @Published var isPlaying: Bool = false
    
    // 播放控制
    private var playbackTimer: Timer?
    private var currentNoteIndex: Int = 0
    private var playedNotes: [UInt8] = []
    private var sustainDelay:Double = 2.0   //和弦播放的时间
    
    private var debug : Bool = false
    
    init(midiManager: MIDIManager) {
        self.midiManager = midiManager
    }
    
    /**
     * 播放和弦 - 主要接口
     * - Parameter notes: 要播放的MIDI音符数组
     * - Parameter velocity: 音符力度 (0-127)
     * - Parameter playbackType: 播放方式
     * - Parameter bpm: 每分钟节拍数（用于琶音模式）
     * - Parameter noteSpacingFactor: 音符间隔系数（用于琶音模式）
     */
    func playChord(notes: [Int], velocity: Int = 80, playbackType: ChordPlaybackType = .block_chord, bpm: Int = 120, noteSpacingFactor: Double = 0.25) {
        
        if debug{
            print("🎵 ChordPlayer: 开始播放音符组: \(notes.sorted())")
            print("🔧 播放方式: \(playbackType.displayName)")
        }
        
        // 停止当前播放
        stopPlaying()
        
        // 转换为UInt8数组并验证范围
        let validNotes = notes.compactMap { note -> UInt8? in
            guard note >= 0 && note <= 127 else {
                print("⚠️ 无效的MIDI音符: \(note)")
                return nil
            }
            return UInt8(note)
        }
        
        guard !validNotes.isEmpty else {
            print("❌ 没有有效的音符可播放")
            return
        }
        
        playedNotes = validNotes
        isPlaying = true
        
        // 根据播放类型选择播放方式
        switch playbackType {
        case .block_chord:
            playBlock(notes: validNotes, velocity: UInt8(velocity))
        case .arpeggio_chord:
            playArpeggio(notes: validNotes, velocity: UInt8(velocity), bpm: bpm, noteSpacingFactor: noteSpacingFactor)
        }
    }
    
    /**
     * 播放和弦配置 - 兼容旧接口
     * - Parameter voicing: 要播放的和弦配置
     * - Parameter baseNote: 基础音符（默认使用配置中的base_note）
     * - Parameter playbackType: 播放方式
     */
    func playChord(_ voicing: ChordVoicing, baseNote: UInt8? = nil, playbackType: ChordPlaybackType = .block_chord) {
        print("🎵 ChordPlayer: 播放和弦配置: \(voicing.name)")
        
        // 解析基础音符
        let base = baseNote ?? parseBaseNote(voicing.base_note)
        
        // 计算实际音符
        let notes = voicing.intervals.map { Int(base) + $0 }
        
        // 调用主要接口
        playChord(notes: notes, velocity: 80, playbackType: playbackType)
    }
    
    /**
     * 停止播放
     */
    func stopPlaying() {
        guard isPlaying else { return }

        print("🛑 ChordPlayer: 停止播放")

        // 停止定时器
        playbackTimer?.invalidate()
        playbackTimer = nil

        // 停止所有正在播放的音符
        for note in playedNotes {
            midiManager.stopListenNote(Int(note))
        }
        playedNotes.removeAll()

        // 重置状态
        isPlaying = false
        currentNoteIndex = 0
    }

    /**
     * 🎨 配置人性化琶音参数
     * 允许外部调整琶音的人性化程度
     */
    func configureHumanizedArpeggio(
        timingVariation: Double = 0.3,
        velocityVariation: Int = 15,
        useCrescendo: Bool = true,
        randomizeOrder: Bool = false
    ) {
        arpeggioConfig.timingVariation = max(0.0, min(0.5, timingVariation))
        arpeggioConfig.velocityVariation = max(5, min(30, velocityVariation))
        arpeggioConfig.useCrescendo = useCrescendo
        arpeggioConfig.randomizeOrder = randomizeOrder

        if debug {
            print("🎨 人性化琶音配置更新:")
            print("   时间变化: ±\(Int(arpeggioConfig.timingVariation * 100))%")
            print("   力度变化: ±\(arpeggioConfig.velocityVariation)")
            print("   渐强效果: \(arpeggioConfig.useCrescendo ? "启用" : "禁用")")
            print("   随机顺序: \(arpeggioConfig.randomizeOrder ? "启用" : "禁用")")
        }
    }
    
    /**
     * 立即停止播放 - 外部调用接口
     */
    func stopImmediately() {
        stopPlaying()
    }
    
    // MARK: - Private Methods
    
    /**
     * 同时播放所有音符
     */
    private func playBlock(notes: [UInt8], velocity: UInt8 = 80) {
        
        if debug{
            print("🎼 ChordPlayer: Block模式播放音符: \(notes)")
        }
        
        // 同时发送所有音符
        for note in notes {
            midiManager.playListenNote(Int(note), velocity: velocity)
        }
        
        // 🔧 缩短默认播放时间到1秒，便于及时停止
        playbackTimer = Timer.scheduledTimer(withTimeInterval: sustainDelay, repeats: false) { _ in
            self.stopPlaying()
        }
    }
    
    /**
     * 🎹 人性化琶音播放（逐个音符）
     * - Parameter bpm: 每分钟节拍数
     * - Parameter noteSpacingFactor: 音符间隔系数
     */
    private func playArpeggio(notes: [UInt8], velocity: UInt8 = 80, bpm: Int = 120, noteSpacingFactor: Double = 0.25) {

        if debug{
            print("🎼 ChordPlayer: 人性化Arpeggio模式播放音符: \(notes), BPM: \(bpm), 间隔系数: \(noteSpacingFactor)")
        }

        // 🎨 更新配置
        arpeggioConfig.bpm = bpm
        arpeggioConfig.baseNoteSpacingFactor = noteSpacingFactor
        arpeggioConfig.baseVelocity = Int(velocity)

        // 🎹 准备音符序列（支持随机化或传统排序）
        let orderedNotes: [UInt8]
        if arpeggioConfig.randomizeOrder && notes.count > 2 {
            // 随机化顺序，但保持最低音在前，最高音在后
            let sortedNotes = notes.sorted()
            let firstNote = sortedNotes.first!
            let lastNote = sortedNotes.last!
            let middleNotes = Array(sortedNotes.dropFirst().dropLast()).shuffled()
            orderedNotes = [firstNote] + middleNotes + [lastNote]
        } else {
            // 传统从低到高排序
            orderedNotes = notes.sorted()
        }

        // 🎨 生成人性化的时间间隔和力度序列
        let humanizedSequence = generateHumanizedSequence(for: orderedNotes)

        currentNoteIndex = 0
        playNextHumanizedNote(sequence: humanizedSequence)
    }
    
    // MARK: - 🎹 人性化琶音系统

    /// 人性化音符数据结构
    struct HumanizedNote {
        let note: UInt8
        let velocity: UInt8
        let delay: Double  // 到下一个音符的延迟时间
    }

    /**
     * 🎨 生成人性化的琶音序列
     * 包含变化的时间间隔和力度，模拟真人演奏
     */
    private func generateHumanizedSequence(for notes: [UInt8]) -> [HumanizedNote] {
        var sequence: [HumanizedNote] = []
        let noteCount = notes.count

        // 🔧 基础时间间隔计算
        let secondsPerBeat = 60.0 / Double(arpeggioConfig.bpm)
        let baseInterval = secondsPerBeat * arpeggioConfig.baseNoteSpacingFactor

        for (index, note) in notes.enumerated() {
            // 🎨 计算人性化力度
            let humanizedVelocity = calculateHumanizedVelocity(
                noteIndex: index,
                totalNotes: noteCount,
                baseVelocity: arpeggioConfig.baseVelocity
            )

            // 🎨 计算人性化时间间隔
            let humanizedDelay = calculateHumanizedDelay(
                noteIndex: index,
                totalNotes: noteCount,
                baseInterval: baseInterval
            )

            let humanizedNote = HumanizedNote(
                note: note,
                velocity: UInt8(humanizedVelocity),
                delay: humanizedDelay
            )

            sequence.append(humanizedNote)

            if debug {
                print("🎨 音符 \(index + 1)/\(noteCount): \(note), 力度: \(humanizedVelocity), 延迟: \(String(format: "%.3f", humanizedDelay))s")
            }
        }

        return sequence
    }

    /**
     * 🎨 计算人性化力度
     * 支持根音强调、渐强效果和随机变化
     */
    private func calculateHumanizedVelocity(noteIndex: Int, totalNotes: Int, baseVelocity: Int) -> Int {
        var velocity = baseVelocity

        // 🎹 根音强调：第一个音符（根音）力度最强
        if noteIndex == 0 {
            velocity = baseVelocity + arpeggioConfig.velocityVariation / 2
            print("🎹 根音强调: velocity = \(velocity)")
        } else {
            // 🎵 其他音符稍弱，营造层次感
            velocity = baseVelocity - arpeggioConfig.velocityVariation / 4

            // 🎵 渐强效果：从第二个音符开始逐渐增强（但不超过根音）
            if arpeggioConfig.useCrescendo && totalNotes > 2 {
                let crescendoFactor = Double(noteIndex - 1) / Double(totalNotes - 2)
                let crescendoBoost = Int(Double(arpeggioConfig.velocityVariation / 3) * crescendoFactor)
                velocity += crescendoBoost
            }
        }

        // 🎲 添加微妙的随机变化（减少变化幅度，保持音乐性）
        let randomVariation = Int.random(in: -arpeggioConfig.velocityVariation/4...arpeggioConfig.velocityVariation/4)
        velocity += randomVariation

        // 🔧 确保在有效范围内
        return max(40, min(127, velocity))
    }

    /**
     * 🎨 计算人性化时间间隔
     * 添加自然的节奏变化和后面音符加速效果
     */
    private func calculateHumanizedDelay(noteIndex: Int, totalNotes: Int, baseInterval: Double) -> Double {
        // 🎵 最后一个音符不需要延迟
        guard noteIndex < totalNotes - 1 else { return arpeggioConfig.lastNoteDuration }

        var interval = baseInterval

        // 🎹 后面音符逐渐加速：模拟手指在键盘上的自然加速趋势
        if totalNotes > 2 {
            let accelerationFactor = Double(noteIndex) / Double(totalNotes - 1)
            let speedBoost = 1.0 - (accelerationFactor * 0.2) // 最多快20%
            interval *= speedBoost
            print("🎹 音符 \(noteIndex + 1): 加速系数 = \(String(format: "%.2f", speedBoost))")
        }

        // 🎨 添加微妙的随机变化（减少变化幅度，保持音乐性）
        let variationRange = baseInterval * (arpeggioConfig.timingVariation * 0.6) // 减少随机性
        let randomVariation = Double.random(in: -variationRange...variationRange)
        interval += randomVariation

        // 🎵 第一个音符后稍微稳定，建立节奏基础
        if noteIndex == 0 && totalNotes > 2 {
            interval *= 0.95 // 轻微加快，但不如之前激进
        }

        // 🎵 倒数第二个音符稍微慢一点，营造"收尾"感
        if noteIndex == totalNotes - 2 {
            interval *= 1.1 // 减少减速幅度
        }

        // 🔧 确保最小间隔
        return max(0.05, interval)
    }

    /**
     * 🎹 播放人性化琶音序列
     */
    private func playNextHumanizedNote(sequence: [HumanizedNote]) {
        guard currentNoteIndex < sequence.count else {
            // 🔧 所有音符播放完毕
            playbackTimer = Timer.scheduledTimer(withTimeInterval: arpeggioConfig.lastNoteDuration, repeats: false) { _ in
                self.stopPlaying()
            }
            return
        }

        let humanizedNote = sequence[currentNoteIndex]

        // 停止上一个音符（如果有）
        if let lastNote = playedNotes.last {
            midiManager.stopListenNote(Int(lastNote))
            playedNotes.removeLast()
        }

        // 🎹 播放当前音符（使用人性化力度）
        midiManager.playListenNote(Int(humanizedNote.note), velocity: humanizedNote.velocity)
        playedNotes.append(humanizedNote.note)

        currentNoteIndex += 1

        // 🎨 使用人性化延迟时间
        playbackTimer = Timer.scheduledTimer(withTimeInterval: humanizedNote.delay, repeats: false) { _ in
            self.playNextHumanizedNote(sequence: sequence)
        }
    }
    
    /**
     * 解析base_note字符串为MIDI音符值
     */
    private func parseBaseNote(_ baseNoteString: String) -> UInt8 {
        let noteMap: [String: Int] = [
            "C": 0, "C#": 1, "Db": 1,
            "D": 2, "D#": 3, "Eb": 3,
            "E": 4,
            "F": 5, "F#": 6, "Gb": 6,
            "G": 7, "G#": 8, "Ab": 8,
            "A": 9, "A#": 10, "Bb": 10,
            "B": 11
        ]
        
        let cleanString = baseNoteString.trimmingCharacters(in: .whitespaces)
        
        var noteName = ""
        var octave = 3 // 默认八度
        
        for (index, char) in cleanString.enumerated() {
            if char.isNumber {
                noteName = String(cleanString.prefix(index))
                if let octaveValue = Int(String(cleanString.suffix(from: cleanString.index(cleanString.startIndex, offsetBy: index)))) {
                    octave = octaveValue
                }
                break
            }
        }
        
        if noteName.isEmpty {
            noteName = cleanString
        }
        
        let noteValue = noteMap[noteName] ?? 0
        let midiNote = (octave + 1) * 12 + noteValue
        
        return UInt8(max(0, min(127, midiNote)))
    }
}

// MARK: - 播放类型枚举
enum ChordPlaybackType: String, CaseIterable, Identifiable {
    case block_chord = "block chord"
    case arpeggio_chord = "arpeggio"
    
    var id: String { self.rawValue }
    
    var displayName: String {
        switch self {
        case .block_chord:
            return "同时按下"
        case .arpeggio_chord:
            return "从低到高"
        }
    }
}
