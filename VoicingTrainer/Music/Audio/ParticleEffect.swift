//
//  ParticleEffect.swift
//  FullUI
//
//  Created by <PERSON> on 2025/5/26.
//  粒子效果系统 - 用于音符击中时的视觉反馈
//

import SwiftUI

// MARK: - 粒子数据模型

/**
 * 单个粒子的数据模型
 */
struct Particle: Identifiable {
    let id = UUID()
    var position: CGPoint
    var velocity: CGVector
    var scale: CGFloat
    var opacity: Double
    var color: Color
    var life: Double // 0.0 到 1.0，1.0表示刚生成
    
    init(position: CGPoint, color: Color) {
        self.position = position
        self.color = color
        self.scale = Double.random(in: 0.5...1.5)
        self.opacity = 1.0
        self.life = 1.0
        
        // 随机速度方向
        let angle = Double.random(in: 0...(2 * .pi))
        let speed = Double.random(in: 50...150)
        self.velocity = CGVector(
            dx: cos(angle) * speed,
            dy: sin(angle) * speed
        )
    }
}

// MARK: - 粒子效果视图

/**
 * 粒子效果视图组件
 * 轻量级设计，优化性能
 */
struct ParticleEffectView: View {
    @State private var particles: [Particle] = []
    @State private var animationTimer: Timer?
    
    let maxParticles = 20 // 限制粒子数量以保证性能
    let particleLifetime: Double = 1.5 // 粒子生命周期（秒）
    
    var body: some View {
        Canvas { context, size in
            // 绘制所有粒子
            for particle in particles {
                let rect = CGRect(
                    x: particle.position.x - 2,
                    y: particle.position.y - 2,
                    width: 4 * particle.scale,
                    height: 4 * particle.scale
                )
                
                context.fill(
                    Path(ellipseIn: rect),
                    with: .color(particle.color.opacity(particle.opacity))
                )
            }
        }
        .allowsHitTesting(false) // 不拦截触摸事件
        .onDisappear {
            stopAnimation()
        }
    }
    
    /**
     * 在指定位置触发粒子爆炸效果
     * - Parameters:
     *   - position: 爆炸位置
     *   - color: 粒子颜色
     */
    func triggerExplosion(at position: CGPoint, color: Color = .blue) {
        // 生成新粒子
        let newParticles = (0..<maxParticles).map { _ in
            Particle(position: position, color: color)
        }
        
        particles.append(contentsOf: newParticles)
        
        // 启动动画定时器
        startAnimation()
        
        // 自动清理粒子
        DispatchQueue.main.asyncAfter(deadline: .now() + particleLifetime) {
            particles.removeAll { particle in
                particle.life <= 0
            }
        }
    }
    
    private func startAnimation() {
        guard animationTimer == nil else { return }
        
        animationTimer = Timer.scheduledTimer(withTimeInterval: 1/60.0, repeats: true) { _ in
            updateParticles()
        }
    }
    
    private func stopAnimation() {
        animationTimer?.invalidate()
        animationTimer = nil
    }
    
    private func updateParticles() {
        let deltaTime = 1/60.0
        
        for i in particles.indices.reversed() {
            // 更新粒子属性
            particles[i].position.x += particles[i].velocity.dx * deltaTime
            particles[i].position.y += particles[i].velocity.dy * deltaTime
            
            // 应用重力
            particles[i].velocity.dy += 200 * deltaTime
            
            // 减少生命值
            particles[i].life -= deltaTime / particleLifetime
            
            // 更新透明度和缩放
            particles[i].opacity = max(0, particles[i].life)
            particles[i].scale *= 0.99
            
            // 移除死亡粒子
            if particles[i].life <= 0 {
                particles.remove(at: i)
            }
        }
        
        // 如果没有粒子了，停止动画
        if particles.isEmpty {
            stopAnimation()
        }
    }
}

// MARK: - 粒子效果修饰符

/**
 * 粒子效果修饰符
 * 方便在任何视图上添加粒子效果
 */
struct ParticleEffectModifier: ViewModifier {
    @State private var particleEffect = ParticleEffectView()
    
    func body(content: Content) -> some View {
        content
            .overlay(
                particleEffect
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
            )
    }
    
    func triggerExplosion(at position: CGPoint, color: Color = .blue) {
        particleEffect.triggerExplosion(at: position, color: color)
    }
}

extension View {
    /**
     * 添加粒子效果支持
     */
    func withParticleEffect() -> some View {
        modifier(ParticleEffectModifier())
    }
} 