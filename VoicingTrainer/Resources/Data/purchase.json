{
  "version": "1.0",
  "last_updated": "2025-05-26T14:30:00Z",
  "environment": "Production", // Sandbox/Production
  "user_id": "USER123456", // 可替换为实际用户ID
  "purchases": [
    {
      "product_id": "com.shzx.voicingtrainer.ChordPack1",
      "purchase_date": "2025-05-20T09:15:22Z",
      "expires_date": null, // 非订阅项目设为null
      "is_trial": false,
      "is_refunded": false,
      "platform": "iOS",
      "receipt": "BASE64_ENCODED_RECEIPT_DATA", // 实际存储完整的收据
      "entitlements": ["advanced_chords", "jazz_progressions"]
    },
    {
      "product_id": "com.shzx.voicingtrainer.ProgressionPack1",
      "purchase_date": "2025-05-25T16:45:10Z",
      "expires_date": "2025-06-25T16:45:10Z", // 订阅项目需包含过期时间
      "is_trial": true,
      "is_refunded": false,
      "platform": "macOS",
      "receipt": "BASE64_ENCODED_RECEIPT_DATA",
      "entitlements": ["circle_progressions", "modes_analysis"]
    },
    {
      "product_id": "com.shzx.voicingtrainer.SongPack1",
      "purchase_date": null, // 未购买状态
      "expires_date": null,
      "is_trial": false,
      "is_refunded": false,
      "platform": null,
      "receipt": null,
      "entitlements": []
    }
  ],
  "subscription_status": {
    "is_active": true,
    "renewal_enabled": true,
    "grace_period_until": null
  },
  "device_sync": [
    {
      "device_id": "iPhone15,3",
      "last_sync": "2025-05-26T12:10:00Z"
    },
    {
      "device_id": "MacBookPro18,1",
      "last_sync": "2025-05-26T13:22:00Z"
    }
  ]
}
