# Practice Pattern Engine 重构总结

## 重构背景

`ChordGameManager` 和 `ProgressionGameManager` 中存在大量重复的练习模式相关代码，包括：
- 练习模式数据结构和管理器
- 数学计算函数（最大公约数等）
- 多循环覆盖算法
- 游戏参数计算逻辑
- 状态管理代码

## 重构目标

1. **消除代码重复**：将共同的练习模式逻辑抽取到统一的工具类
2. **提高可维护性**：减少重复代码，便于后续修改和扩展
3. **统一接口**：为练习模式提供一致的API接口
4. **保持功能不变**：确保重构后功能完全一致

## 创建的新工具类

### PracticePatternEngine.swift

新创建的练习模式引擎包含以下组件：

#### 1. 数据结构
- `PracticePattern`: 练习模式配置结构
- `PracticePatternManager`: 练习模式管理器
- `PracticeEngineState`: 练习引擎状态结构

#### 2. 数学计算
```swift
static func greatestCommonDivisor(_ a: Int, _ b: Int) -> Int
static func calculateGameParameters(for offset: Int) -> (totalRounds: Int, cyclesNeeded: Int, roundsPerCycle: Int)
```

#### 3. 状态管理
```swift
static func initializeState(for pattern: PracticePattern?) -> PracticeEngineState
```

#### 4. 根音计算（和弦游戏）
```swift
static func calculateNextRootNote(
    currentRootNote: Int,
    initialRootNote: Int,
    practiceOffset: Int,
    state: inout PracticeEngineState
) -> (nextRootNote: Int, isCompleted: Bool)
```

#### 5. 移调计算（进行游戏）
```swift
static func calculateNextTransposition(
    practiceOffset: Int,
    state: inout PracticeEngineState
) -> (nextTransposition: Int, isCompleted: Bool)
```

#### 6. 辅助工具
```swift
static func getNoteNameFromMIDI(_ midiNote: Int) -> String
static func getTranspositionName(_ semitones: Int) -> String
static func transposeMIDINotes(_ notes: [Int], by semitones: Int) -> [Int]
```

## 重构的文件

### ChordGameManager.swift 变更

#### 移除的代码
- `PracticePattern` 和 `PracticePatternManager` 类定义（~ 80 行）
- `greatestCommonDivisor()` 函数
- `calculateGameParameters()` 函数
- `getNoteNameFromMIDI()` 函数
- 复杂的多循环计算逻辑

#### 新增的使用方式
```swift
// 使用统一引擎初始化状态
practiceEngineState = PracticePatternEngine.initializeState(for: selectedPracticePattern)

// 使用统一引擎计算下一个根音
let result = PracticePatternEngine.calculateNextRootNote(
    currentRootNote: currentRootNote,
    initialRootNote: initialRootNote,
    practiceOffset: currentPracticeOffset,
    state: &practiceEngineState
)
```

#### 属性变更
```swift
// 旧的分散属性
private var cyclesNeeded: Int = 1
private var roundsPerCycle: Int = 12
private var currentTransposition: Int = 0

// 新的统一状态
private var practiceEngineState: PracticeEngineState = PracticeEngineState()
```

### ProgressionGameManager.swift 变更

#### 移除的代码
- `greatestCommonDivisor()` 函数（重复代码）
- `calculateGameParameters()` 函数（重复代码）
- `getTranspositionName()` 函数
- 复杂的移调计算逻辑

#### 新增的使用方式
```swift
// 使用统一引擎计算移调
let result = PracticePatternEngine.calculateNextTransposition(
    practiceOffset: pattern.offset,
    state: &practiceEngineState
)

// 使用统一工具进行移调
notes: PracticePatternEngine.transposeMIDINotes(chord.notes, by: semitones)
```

## 重构效果

### 1. 代码减少
- **ChordGameManager.swift**: 减少约 120 行重复代码
- **ProgressionGameManager.swift**: 减少约 80 行重复代码
- **总计**: 减少约 200 行重复代码

### 2. 维护性提升
- 练习模式逻辑集中在单一位置
- 修改算法只需要更新一个文件
- 新增练习模式更加简单

### 3. 一致性保证
- 两个游戏管理器使用相同的算法
- 统一的状态管理和计算逻辑
- 减少了不一致导致的bug风险

### 4. 功能完整性
- ✅ 编译通过
- ✅ 所有原有功能保持不变
- ✅ 练习模式正常工作
- ✅ 多循环覆盖算法正确

## 技术细节

### 多循环覆盖算法

引擎使用最大公约数算法确保所有12个调都能被练习到：

```swift
let gcd = greatestCommonDivisor(12, abs(offset))
let cyclesNeeded = gcd
let roundsPerCycle = 12 / gcd
```

例如：
- 五度圈（offset=7）：GCD(12,7)=1，需要1个循环，每循环12轮
- 小三度（offset=3）：GCD(12,3)=3，需要3个循环，每循环4轮

### 状态同步

引擎状态与UI显示状态保持同步：

```swift
// 同步UI显示状态
currentCycle = practiceEngineState.currentCycle
currentRoundInCycle = practiceEngineState.currentRoundInCycle
```

## 后续改进建议

1. **单元测试**：为 `PracticePatternEngine` 添加完整的单元测试
2. **性能优化**：如有需要，可以对频繁调用的计算函数进行优化
3. **扩展功能**：可以轻松添加新的练习模式和算法
4. **文档完善**：为工具类添加更详细的API文档

## 验证结果

- ✅ 项目编译成功
- ✅ 无语法错误
- ✅ 重构前后功能一致
- ✅ 代码结构更清晰
- ✅ 可维护性显著提升

## 总结

此次重构成功地将两个游戏管理器中约200行重复的练习模式代码抽取到了统一的 `PracticePatternEngine` 工具类中。重构后的代码更加简洁、可维护，同时保持了所有原有功能的完整性。这为后续的功能扩展和维护奠定了良好的基础。 