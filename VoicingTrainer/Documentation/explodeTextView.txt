note on:48 velocity:58
[]
48 
🎹 playedNotes 变化: [] -> [48]
🎯 expectedNotes: [48, 52, 55]
💥 hitNotes: []
🆕 新击中的音符: [48]
🎯 ===== 音符击中调试 =====
🎵 被击中的音符: 48
📍 音符存储的全局位置: (396.1666666666667, 334.0)
🎵 音名: C
💥 准备触发爆炸:
   音名: C
   位置: (396.1666666666667, 334.0)
   颜色: red
🎆 ParticleCoordinator.explodeNote: C at (396.1666666666667, 334.0)
🎯 ===== 音符击中调试结束 =====
🎯 调用scene.explodeNote
🎆 ===== 粒子爆炸坐标调试 =====
🎵 音名: C
📱 接收到的SwiftUI全局坐标: (396.1666666666667, 334.0)
📐 当前SpriteKit场景大小: (825.0, 556.0)
🔄 转换后的SpriteKit坐标: (396.1666666666667, 222.0)
🎨 使用颜色: Catalog color: #$customDynamic 39FF1E8D-8340-4728-A1E5-1558473D2516
🔵 第一个粒子的SpriteKit坐标: (416.1666666666667, 222.0)
✅ 创建了 20 个粒子
🎆 ===== 调试信息结束 =====
[]

🎹 playedNotes 变化: [48] -> []
🎯 expectedNotes: [48, 52, 55]
💥 hitNotes: [48]
🆕 新击中的音符: []
Round timeout
Round 2: Playing F chord
Base note: C3 (MIDI: 48)
Root note: 53
Expected notes: [53, 57, 60]
🎯 音符 57 坐标调试:
   📍 本地坐标: (40.0, 30.0)
   🌍 全局坐标: (664.5, 334.0)
   📏 本地frame: (0.0, 0.0, 80.0, 60.0)
   🌏 全局frame: (624.5, 304.0, 80.0, 60.0)
🎯 音符 53 坐标调试:
   📍 本地坐标: (40.0, 30.0)
   🌍 全局坐标: (396.1666666666667, 334.0)
   📏 本地frame: (0.0, 0.0, 80.0, 60.0)
   🌏 全局frame: (356.1666666666667, 304.0, 80.0, 60.0)
🎯 音符 60 坐标调试:
   📍 本地坐标: (40.0, 30.0)
   🌍 全局坐标: (932.8333333333335, 334.0)
   📏 本地frame: (0.0, 0.0, 80.0, 60.0)
   🌏 全局frame: (892.8333333333335, 304.0, 80.0, 60.0)
Playing listen note: 53 (using dedicated listenSampler)
Playing listen note: 57 (using dedicated listenSampler)
Playing listen note: 60 (using dedicated listenSampler)
chords interval:12.0
Stopping listen note: 53 (using dedicated listenSampler)