# Progression文件风格分类指南

## 概述

VoicingTrainer现在支持按音乐风格对和弦进行进行分类。这个功能允许用户根据不同的音乐风格（如Jazz、R&B、Classical等）来浏览和选择和弦进行。

## 方案选择

经过分析，我们选择了**在文件内添加数据项**的方案，理由如下：

1. **数据完整性**: 风格属于和弦进行的内在属性，应该存储在数据中
2. **向后兼容**: 对于没有style字段的旧文件，系统会自动从文件名推断风格
3. **扩展性强**: 未来可以添加更多元数据（难度、时长、作者等）
4. **查询效率**: 支持多维度分类和过滤
5. **国际化友好**: 可以支持多语言风格名称

## 文件格式变更

### 新增字段

在progression对象中添加可选的`style`字段：

```json
{
    "progression": {
        "name": "Example-Jazz-Modern",
        "style": "Jazz",          // 新增：风格字段
        "rootNote": "C",
        "scale": "Major",
        "recordingOctave": 2,
        "chords": [...]
    }
}
```

### 支持的风格类型

目前系统识别以下风格类型：

- `Jazz` - 爵士乐
- `R&B` - 节奏布鲁斯
- `Classical` - 古典音乐
- `Funk` - 放克音乐
- `Blues` - 布鲁斯
- `Pop` - 流行音乐
- `Latin` - 拉丁音乐
- `Gospel` - 福音音乐
- `Bossa Nova` - 波萨诺瓦
- `Swing` - 摇摆乐
- `Other` - 其他

## 如何修改现有文件

### 1. 手动添加风格字段

对于现有的progression文件，在`progression`对象中添加`style`字段：

**修改前（Jazzy.progression）：**
```json
{
    "progression": {
        "name": "Superb - Jazzy",
        "rootNote": "C#",
        "scale": "Major",
        "recordingOctave": 2,
        "chords": [...]
    }
}
```

**修改后：**
```json
{
    "progression": {
        "name": "Superb - Jazzy",
        "style": "Jazz",              // 新增此行
        "rootNote": "C#",
        "scale": "Major",
        "recordingOctave": 2,
        "chords": [...]
    }
}
```

### 2. 批量修改脚本

可以使用以下Python脚本批量修改文件：

```python
import json
import os
import glob

# 风格映射表
STYLE_MAPPING = {
    'jazz': 'Jazz',
    'rnb': 'R&B',
    'r&b': 'R&B',
    'classical': 'Classical',
    'mozzart': 'Classical',
    'funk': 'Funk',
    'blues': 'Blues',
    'pop': 'Pop',
    'latin': 'Latin',
    'gospel': 'Gospel',
    'bossa': 'Bossa Nova',
    'swing': 'Swing'
}

def infer_style_from_name(name):
    """从文件名推断风格"""
    name_lower = name.lower()
    for keyword, style in STYLE_MAPPING.items():
        if keyword in name_lower:
            return style
    return 'Other'

def add_style_to_progression_file(file_path):
    """为progression文件添加风格字段"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # 检查是否已有style字段
        if 'style' not in data['progression']:
            # 从文件名推断风格
            file_name = os.path.basename(file_path).replace('.progression', '')
            inferred_style = infer_style_from_name(data['progression']['name'])
            
            # 添加style字段
            data['progression']['style'] = inferred_style
            
            # 写回文件
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=4, ensure_ascii=False)
            
            print(f"✅ {file_name}: 添加风格 '{inferred_style}'")
        else:
            print(f"⚠️  {os.path.basename(file_path)}: 已有风格字段")
            
    except Exception as e:
        print(f"❌ 处理文件失败 {file_path}: {e}")

# 批量处理
progression_files = glob.glob("VoicingTrainer/Resources/Data/progressions/*.progression")
for file_path in progression_files:
    add_style_to_progression_file(file_path)
```

## 向后兼容性

系统具有完整的向后兼容性：

1. **旧文件支持**: 没有`style`字段的文件仍然可以正常加载
2. **自动推断**: 系统会从文件名自动推断风格
3. **渐进式迁移**: 可以逐步为文件添加风格字段

## UI功能

新的风格分类系统提供以下UI功能：

1. **风格过滤器**: 在和弦进行选择界面顶部显示风格按钮
2. **分组显示**: 在"全部"模式下按风格分组显示
3. **风格标识**: 每个和弦进行显示其风格标签
4. **统计信息**: 显示每个风格下的和弦进行数量

## 建议的修改优先级

建议按以下优先级修改现有文件：

1. **高优先级**: 明确属于某种风格的文件
   - Jazzy.progression → Jazz
   - Superb - RnB.progression → R&B
   - Superb - Classical.progression → Classical

2. **中优先级**: 可以从名称推断风格的文件
   - 包含风格关键词的文件

3. **低优先级**: 通用或难以分类的文件
   - 设置为 "Other" 或根据音乐内容手动分类

## 示例文件

参考 `Example-Jazz-Modern.progression` 文件查看完整的新格式示例。

## 注意事项

1. `style`字段是可选的，确保向后兼容
2. 风格名称建议使用英文，便于系统识别
3. 保持JSON格式的正确性
4. 建议在修改前备份原文件 