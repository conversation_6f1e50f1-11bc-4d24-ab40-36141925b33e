# VoicingTrainer 测试系统

## 🚀 快速开始

### 每日必用命令
```bash
./daily_test.sh --quick           # 快速检查 (30秒)
./daily_test.sh --full            # 完整测试 (5分钟)  
./run_chord_tests.sh              # 诊断测试 (1分钟)
```

### Xcode快捷键
- `⌘+U` - 运行单元测试
- `⌘+6` - 打开Test Navigator
- `⌘+9` - 查看代码覆盖率

## 📊 测试覆盖范围

### 核心功能测试
- ✅ **MIDI处理** - 音符事件、和弦识别
- ✅ **音符解析** - 各种音符格式转换
- ✅ **和弦分析** - 三和弦、七和弦、转位
- ✅ **性能测试** - 关键算法性能验证

### 测试文件
- `MIDIManagerTests.swift` - MIDI相关功能测试
- `FullUITests.swift` - 完整UI集成测试
- `DataProcessingUtils.swift` - 数据处理工具测试

## 🧪 测试驱动开发

### 自动提醒系统
- 📊 代码变更自动追踪
- ⏰ 15分钟或5次变更后提醒
- 🎨 Debug模式可视化提醒
- 🚀 一键运行测试

### 使用方法
```swift
// 已集成到关键代码中
recordCodeChange("MIDIManager.updateChordInfo")
recordTestRun() // 测试完成后调用
```

## 📅 每日测试流程

### 🌅 每日开始 (5分钟)
```bash
git status                        # 检查状态
./daily_test.sh --quick          # 快速检查
```

### 🔧 开发过程中 (每小时)
- 关注测试提醒系统
- 累积变更后运行测试
- 使用 `⌘+U` 快速验证

### 🌙 每日结束 (10分钟)
```bash
./daily_test.sh --full --coverage  # 完整测试
# 在Xcode中查看覆盖率 (⌘+9)
git commit -m "feat: 功能描述 - 测试通过"
```

## 🎯 测试覆盖率目标

| 模块 | 当前覆盖率 | 目标 |
|-----|----------|------|
| 音符解析 | ~95% | 95%+ |
| 和弦分析 | ~85% | 90%+ |
| MIDI处理 | ~90% | 95%+ |
| UI交互 | ~70% | 85%+ |

## 🛠 故障排除

### 编译问题
```bash
xcodebuild clean -scheme VoicingTrainer
xcodebuild build -scheme VoicingTrainer -destination 'platform=macOS'
```

### 测试失败
```bash
# 查看详细日志
cat daily_test_$(date +%Y%m%d).log

# 运行特定测试
xcodebuild test -scheme VoicingTrainer -only-testing:VoicingTrainerTests/MIDIManagerTests
```

## 📖 详细文档

- 📋 [完整测试指南](./TestingGuide.md)
- 📅 [每日测试实践](./DailyTestingPractice.md)
- 🧪 [测试用例详情](../VoicingTrainerTests/README.md)

## 🏆 最佳实践

### ✅ 推荐
- ⭐ 每天运行测试
- 🔄 遵循测试提醒
- 📝 保持测试更新
- 🎯 关注覆盖率

### ❌ 避免
- 跳过测试提交
- 忽略失败测试
- 测试间相互依赖
- 只测正常流程

---

**记住：每天跑单元测试是专业开发的标志！** 🚀 