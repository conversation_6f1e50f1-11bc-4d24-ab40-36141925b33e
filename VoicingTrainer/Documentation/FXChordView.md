# FXChordView - 和弦动态显示控件

一个用于SwiftUI项目的和弦动态显示控件，支持macOS和iOS平台。

## 功能特性

### 🎨 视觉效果
- 支持10种自定义颜色（十六进制格式，如 `#1D4B69`）
- 动态背景切换
- 音符状态动画效果
- 全高亮时的酷炫彩色正方形背景动画

### 🎵 音符处理
- 基于MIDI值的音符系统
- 自动转换MIDI值为音名（使用 `NoteNameGenerator`）
- 支持单字符和双字符音名（如 `C`, `Bb`）
- 音符坐标自动计算和布局

### 🎯 状态管理
- **Hide**: 隐藏状态
- **Show**: 显示状态  
- **Highlight**: 高亮状态
- **SceneHighlight**: 场景高亮（所有音符高亮时触发）

### 🏗️ 架构设计
- 使用MVVM架构模式
- 低CPU占用，优化性能
- 可替换的显示算法
- 统一的外部接口

## 使用方法

### 基本初始化

```swift
import SwiftUI

// 使用默认颜色
let chordView = FXChordView()

// 使用自定义颜色
let customChordView = FXChordView(colors: [
    "#1D4B69", "#2E5A7B", "#3F6B8C", "#507C9D",
    "#618DAE", "#729EBF", "#83AFD0", "#94C0E1", 
    "#A5D1F2", "#B6E2FF"
])
```

### 设置和弦

```swift
// 设置C大调和弦 (C, E, G)
chordView.setChord(chordName: "C Major", notes: [60, 64, 67])

// 设置F大调和弦 (F, A, C)
chordView.setChord(chordName: "F Major", notes: [65, 69, 72])
```

### 控制音符状态

```swift
// 设置单个音符状态
chordView.setSingleNoteState(index: 0, state: .show)      // 显示第一个音符
chordView.setSingleNoteState(index: 1, state: .highlight) // 高亮第二个音符

// 设置所有音符状态
chordView.setAllNotesState(state: .show)      // 显示所有音符
chordView.setAllNotesState(state: .highlight) // 高亮所有音符
chordView.setAllNotesState(state: .hide)      // 隐藏所有音符
```

### 在SwiftUI中使用

```swift
struct MyView: View {
    @State private var chordView = FXChordView()
    
    var body: some View {
        VStack {
            // 显示和弦控件
            chordView
                .frame(height: 400)
            
            // 控制按钮
            HStack {
                Button("显示和弦") {
                    chordView.setChord(chordName: "C Major", notes: [60, 64, 67])
                    chordView.setAllNotesState(state: .show)
                }
                
                Button("高亮和弦") {
                    chordView.setAllNotesState(state: .highlight)
                }
            }
        }
    }
}
```

## 演示项目

项目包含一个完整的演示应用 `FXChordViewDemo`，展示了以下功能：

- 多种和弦切换（C Major, F Major, G Major, A Minor, D Minor）
- 不同状态控制（显示、高亮、隐藏）
- 逐个音符显示
- 自动演示模式

## 技术实现

### 核心组件

1. **NoteState**: 音符状态枚举
2. **SceneState**: 场景状态枚举  
3. **NoteData**: 音符数据模型
4. **FXChordViewModel**: 视图模型（ObservableObject）
5. **FXChordView**: SwiftUI视图

### 动画系统

- 使用 `@Published` 属性触发UI更新
- Spring动画用于音符状态切换
- 缓动动画用于场景转换
- 随机延迟的正方形动画效果

### 颜色系统

- 支持十六进制颜色字符串解析
- `Color` 扩展支持 3、6、8 位十六进制格式
- 自动颜色渐变和随机选择

## 性能优化

- 使用 `GeometryReader` 进行响应式布局
- 条件渲染减少不必要的视图更新
- 优化的动画计算，避免过度绘制
- 内存高效的数据结构

## 适用场景

- 钢琴练习应用
- 音乐教学软件
- 和弦识别工具
- 音乐游戏
- 任何需要音符可视化的应用

## 系统要求

- iOS 14.0+ / macOS 11.0+
- SwiftUI
- Xcode 12.0+

## 文件结构

```
FXChordViewDemo/
├── FXChordView.swift          # 主控件实现
├── FXChordViewDemo.swift      # 演示应用
├── NoteNameGenerator.swift    # 音符名称转换工具
├── ContentView.swift          # 主视图
└── README.md                  # 说明文档
```

## 未来扩展

由于采用了可替换的设计架构，您可以轻松：

- 替换不同的视觉效果算法
- 添加新的音符状态
- 自定义动画效果
- 扩展颜色主题系统
- 集成音频反馈功能

---

该控件设计时考虑了工程最佳实践，具有良好的可维护性和可扩展性，适合在多个项目中复用。 
