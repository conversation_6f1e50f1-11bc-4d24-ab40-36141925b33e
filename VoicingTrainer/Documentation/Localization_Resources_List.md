# VoicingTrainer 本地化资源清单

## 文档说明

本文档记录了 VoicingTrainer 项目中所有需要本地化的UI文本资源，按功能模块分类整理。

- **创建日期**: 2025年1月5日
- **支持语言**: 中文（简体）、英文
- **本地化文件位置**:
  - 中文：`VoicingTrainer/zh-Hans.lproj/Localizable.strings`
  - 英文：`VoicingTrainer/en.lproj/Localizable.strings`

---

## 本地化字符串分类清单

### 1. 游戏按钮 (Game Buttons)
| Key | 中文 | 英文 | 使用场景 |
|-----|------|------|----------|
| `start_practice` | 开始练习 | Start Practice | 游戏主界面 |
| `start_level` | 开始关卡 | Start Level | 关卡选择 |
| `select_level` | 选择关卡 | Select Level | 关卡选择按钮 |
| `next_level` | 下一关 | Next Level | 关卡完成后 |
| `replay` | 重播 | Replay | 重播按钮 |
| `menu` | 菜单 | Menu | 返回菜单 |

### 2. 游戏状态 (Game States)
| Key | 中文 | 英文 | 使用场景 |
|-----|------|------|----------|
| `press_start` | 按开始键！ | Press Start! | 游戏待机状态 |
| `select_level_first` | 选择关卡 | Select Level | 未选择关卡提示 |
| `playing` | 非常好... | Playing... | 游戏进行中 |
| `get_ready` | 准备好！ | Get Ready! | 倒计时准备 |
| `complete` | 完成！ | Complete! | 游戏完成 |
| `listen_and_play` | 听音并弹奏： | Listen and Play: | 听音练习提示 |

### 3. 游戏界面 (Game Interface)
| Key | 中文 | 英文 | 使用场景 |
|-----|------|------|----------|
| `level_complete` | 🎉 关卡完成！ | 🎉 Level Complete! | 关卡完成提示 |
| `correct` | 正确 | Correct | 答题正确反馈 |
| `wrong` | 错误 | Wrong | 答题错误反馈 |
| `round` | 轮次 | Round | 回合显示 |
| `time_remaining` | 剩余时间 | Time Remaining | 倒计时显示 |

### 4. 音符相关 (Note Related)
| Key | 中文 | 英文 | 使用场景 |
|-----|------|------|----------|
| `middle_c` | 中央C | Middle C | 中央C按钮 |
| `note_name` | 音名 | Note Name | 音符名称显示 |

### 5. 通用按钮 (Common Buttons)
| Key | 中文 | 英文 | 使用场景 |
|-----|------|------|----------|
| `done` | 完成 | Done | 完成操作 |
| `cancel` | 取消 | Cancel | 取消操作 |
| `save` | 保存 | Save | 保存操作 |
| `clear` | 清除 | Clear | 清除操作 |
| `ok` | 确定 | OK | 确认操作 |
| `retry` | 重试 | Retry | 重试操作 |
| `close` | 关闭 | Close | 关闭操作 |
| `select` | 选择 | Select | 选择操作 |
| `start` | 开始 | Start | 开始操作 |
| `stop` | 停止 | Stop | 停止操作 |
| `reset` | 重置 | Reset | 重置操作 |

### 6. 和弦相关 (Chord Related)
| Key | 中文 | 英文 | 使用场景 |
|-----|------|------|----------|
| `select_chord` | 选择和弦 | Select Chord | 和弦选择界面 |
| `loading_chords` | 加载和弦中... | Loading chords... | 加载状态 |
| `chord_browser` | 和弦浏览器 | Chord Browser | 和弦浏览界面 |
| `voicing` | 配音 | Voicing | 和弦配音 |
| `create_voicing` | 创建配音 | Create Voicing | 创建配音功能 |
| `intervals` | 音程 | Intervals | 音程显示 |
| `difficulty` | 难度 | Difficulty | 难度选择 |
| `family` | 族 | Family | 和弦族分类 |
| `structure` | 结构 | Structure | 和弦结构 |
| `hands` | 手 | Hands | 左右手指法 |
| `chord_count` | 和弦数量 | Chord Count | 和弦数量统计 |

### 7. 和弦进行相关 (Progression Related)
| Key | 中文 | 英文 | 使用场景 |
|-----|------|------|----------|
| `select_progression` | 选择和弦进行 | Select a Progression to Practice | 和弦进行选择 |
| `progressions` | 和弦进行 | Progressions | 和弦进行标签 |
| `styles` | 风格 | Styles | 音乐风格分类 |
| `mode` | 模式 | Mode | 练习模式 |
| `playback` | 播放 | Playback | 播放控制 |
| `workout_types` | 训练类型 | Workout Types | 训练类型选择 |
| `practice_pattern` | 练习模式 | Practice Pattern | 练习模式设置 |
| `bpm` | 拍数 | BPM | 节拍设置 |
| `infinite_loop` | 无限循环 | Infinite Loop | 无限循环模式 |
| `ready_to_play` | 准备播放 | Ready to Play | 播放准备状态 |
| `current_chord` | 当前和弦 | Current Chord | 当前和弦显示 |
| `recorded_chords` | 已录制的和弦 | Recorded Chords | 录制和弦列表 |
| `save_progression` | 保存和弦进行 | Save Progression | 保存和弦进行 |
| `create_new_progressions` | 创建新和弦进行 | Create New Progressions | 创建新进行 |

### 8. 录制功能 (Recording Features)
| Key | 中文 | 英文 | 使用场景 |
|-----|------|------|----------|
| `pedal_pressed_record` | 踏板按下录制 | Pedal Pressed Record | 录制状态显示 |
| `waiting_chord_play` | 等待弹奏和弦... | Waiting for chord play... | 等待录制状态 |
| `chord_components` | 组成音 | Components | 和弦组成音显示 |
| `no_chords_recorded` | 还未录制任何和弦 | No chords recorded yet | 空状态提示 |
| `play_chord_then_pedal` | 弹奏和弦，然后踩下踏板录制 | Play chord, then press pedal to record | 录制指导 |
| `clear_recording` | 清除录制 | Clear Recording | 清除录制内容 |
| `recorded_count` | 已录制 %d 个和弦 | Recorded %d chords | 录制数量显示 |

### 9. 使用说明 (Usage Instructions)
| Key | 中文 | 英文 | 使用场景 |
|-----|------|------|----------|
| `usage_instructions` | 使用说明 | Usage Instructions | 说明标题 |
| `step1_play_chord` | 弹奏和弦 | Play Chord | 步骤1说明 |
| `step2_press_pedal` | 踩下踏板录制 | Press Pedal to Record | 步骤2说明 |

### 10. 游戏状态消息 (Game Status Messages)
| Key | 中文 | 英文 | 使用场景 |
|-----|------|------|----------|
| `prepare_practice` | 准备练习 | Prepare Practice | 练习准备 |
| `playing_status` | 播放中... | Playing... | 播放状态 |
| `waiting_input` | 等待输入: %@ | Waiting Input: %@ | 等待用户输入 |
| `response_time` | 反应时间... | Response Time... | 反应时间计时 |
| `practice_complete` | 练习完成! | Practice Complete! | 练习完成 |
| `progress_text` | 进度: %@ | Progress: %@ | 进度显示 |
| `infinite_loop_playing` | 无限循环播放... | Infinite Loop Playing... | 无限循环状态 |
| `infinite_loop_current_chord` | 无限循环模式 - 当前第 %d 个和弦 | Infinite Loop Mode - Current %d chord | 循环模式进度 |

### 11. 导航标题 (Navigation Titles)
| Key | 中文 | 英文 | 使用场景 |
|-----|------|------|----------|
| `navigation_help` | 使用帮助 | Help | 帮助页面标题 |
| `navigation_audio_settings` | 音频设置 | Audio Settings | 音频设置页面 |
| `navigation_save_progression` | 保存和弦进行 | Save Progression | 保存页面标题 |
| `navigation_select_chord` | 选择和弦 | Select Chord | 和弦选择页面 |
| `navigation_power_test_results` | 功耗测试结果 | Power Test Results | 测试结果页面 |
| `navigation_test_details` | 测试详情 | Test Details | 测试详情页面 |
| `navigation_songs` | 歌曲 | Songs | 歌曲页面标题 |

### 12. 设置界面 (Settings Interface)
| Key | 中文 | 英文 | 使用场景 |
|-----|------|------|----------|
| `settings` | 设置 | Settings | 设置标签 |
| `playback_volume` | 播放音量 | Playback Volume | 音量设置 |
| `volume_label` | 音量: %d | Volume: %d | 音量数值显示 |
| `clear_all_achievement` | 清除所有成就 | Clear All Achievement | 清除成就按钮 |
| `audio_settings` | 音频设置 | Audio Settings | 音频设置项 |
| `version` | 版本 2.1.0 | Version 2.1.0 | 版本信息 |
| `sustain_pedal` | 延音踏板:  | Sustain Pedal:  | 踏板状态显示 |

### 13. 错误和提示 (Errors and Tips)
| Key | 中文 | 英文 | 使用场景 |
|-----|------|------|----------|
| `error` | 错误 | Error | 错误提示标题 |
| `tip` | 提示 | Tip | 提示信息标题 |
| `loading` | 加载中... | Loading... | 加载状态 |
| `no_level_selected` | 请选择练习组 | Please select practice group | 未选择提示 |
| `select_chord_group_tip` | 点击左上角按钮选择要练习的和弦组合 | Click top-left button to select chord group | 操作提示 |
| `select_voicing_start_game` | 选择配音并开始游戏 | Select a voicing and start the game | 游戏开始提示 |
| `permanent_wait_mode` | 永久等待模式 | Permanent Wait Mode | 等待模式说明 |
| `select_progression_start` | 选择和弦进行开始练习 | Select progression to start practice | 练习开始提示 |

### 14. 统计信息 (Statistics)
| Key | 中文 | 英文 | 使用场景 |
|-----|------|------|----------|
| `current_voicing` | 当前: %d | Current: %d | 当前配音显示 |
| `right_notes` | 正确音符 | Right Notes | 正确音符统计 |
| `error_notes` | 错误音符 | Error Notes | 错误音符统计 |
| `right_chords` | 正确和弦 | Right Chords | 正确和弦统计 |
| `error_chords` | 错误和弦 | Error Chords | 错误和弦统计 |
| `current_round` | 当前轮次 | Current Round | 当前回合显示 |
| `accuracy` | 准确率 | Accuracy | 准确率显示 |

### 15. 帮助内容 (Help Content)
| Key | 中文 | 英文 | 使用场景 |
|-----|------|------|----------|
| `help_loading` | 加载帮助内容... | Loading help content... | 帮助加载状态 |
| `help_load_failed` | 加载帮助内容失败 | Failed to load help content | 帮助加载失败 |
| `welcome_title` | 欢迎使用 VoicingWorkout | Welcome to VoicingWorkout | 欢迎页标题 |
| `getting_started` | 开始使用 | Getting Started | 入门指南 |
| `midi_setup` | MIDI设置 | MIDI Setup | MIDI设置说明 |
| `practice_modes` | 练习模式 | Practice Modes | 练习模式说明 |

### 16. 测试和调试 (Testing and Debugging)
| Key | 中文 | 英文 | 使用场景 |
|-----|------|------|----------|
| `test_reminder` | 测试提醒 | Test Reminder | 测试提醒标题 |
| `run_test` | 运行测试 | Run Test | 运行测试按钮 |
| `remind_later` | 稍后提醒 | Remind Later | 稍后提醒按钮 |
| `force_optimization` | 强制优化 | Force Optimization | 强制优化按钮 |
| `reset_monitoring` | 重置监控 | Reset Monitoring | 重置监控按钮 |
| `clear_results` | 清空结果 | Clear Results | 清空结果按钮 |
| `no_test_data` | 暂无测试数据 | No test data | 无数据状态 |
| `start_test_tip` | 点击开始测试按钮进行功耗测试 | Click start test button to perform power test | 测试提示 |
| `test_particle` | 🎆 测试粒子 | 🎆 Test Particle | 粒子测试按钮 |
| `enable_line_particles` | 🎆 开启线条粒子 | 🎆 Enable Line Particles | 开启粒子效果 |
| `disable_line_particles` | 🎆 关闭线条粒子 | 🎆 Disable Line Particles | 关闭粒子效果 |
| `set_c_major` | 设置C大三和弦 | Set C Major Chord | 测试和弦设置 |
| `trigger_perfect_match` | 触发完全匹配 | Trigger Perfect Match | 触发匹配测试 |

### 17. 电池和系统状态 (Battery and System Status)
| Key | 中文 | 英文 | 使用场景 |
|-----|------|------|----------|
| `charging` | 充电中 | Charging | 充电状态 |
| `full` | 已充满 | Full | 电池充满 |
| `unplugged` | 未充电 | Unplugged | 未连接充电器 |
| `unknown` | 未知 | Unknown | 未知状态 |
| `thermal_normal` | 正常 | Normal | 温度正常 |
| `thermal_fair` | 良好 | Fair | 温度良好 |
| `thermal_serious` | 严重 | Serious | 温度严重 |
| `thermal_critical` | 危险 | Critical | 温度危险 |

### 18. 关于界面 (About Interface)
| Key | 中文 | 英文 | 使用场景 |
|-----|------|------|----------|
| `app_name` | VoicingWorkout | VoicingWorkout | 应用名称 |

### 19. 恭喜消息 (Congratulation Messages)
| Key | 中文 | 英文 | 使用场景 |
|-----|------|------|----------|
| `perfect_performance` | 🎉 Perfect! Outstanding Performance! | 🎉 Perfect! Outstanding Performance! | 完美表现 |
| `excellent_job` | ⭐️ Excellent! Great Job! | ⭐️ Excellent! Great Job! | 优秀表现 |
| `good_work` | 👍 Good Work! Keep Practicing! | 👍 Good Work! Keep Practicing! | 良好表现 |
| `not_bad` | 💪 Not Bad! You Can Do Better! | 💪 Not Bad! You Can Do Better! | 一般表现 |
| `keep_trying` | 🎯 Keep Trying! Practice Makes Perfect! | 🎯 Keep Trying! Practice Makes Perfect! | 鼓励继续 |

### 20. 和弦进行命名 (Progression Naming)
| Key | 中文 | 英文 | 使用场景 |
|-----|------|------|----------|
| `name_your_progression` | 为你的和弦进行命名 | Name your chord progression | 命名提示 |
| `name_limit` | 名称限制：64个字符以内 | Name limit: within 64 characters | 命名限制说明 |
| `note_count` | 音符数: %d | Note count: %d | 音符数量显示 |

### 21. 复杂度和分类 (Complexity and Categories)
| Key | 中文 | 英文 | 使用场景 |
|-----|------|------|----------|
| `all_structures` | 全部结构 | All Structures | 全部结构选项 |
| `complexity_count` | %d 个 | %d items | 复杂度数量 |
| `category_chord_count` | %d | %d | 分类和弦数 |

### 22. 调试说明 (Debug Instructions)
| Key | 中文 | 英文 | 使用场景 |
|-----|------|------|----------|
| `debug_instructions_title` | 使用说明: | Usage Instructions: | 调试说明标题 |
| `debug_instruction_1` | 1. 点击'设置C大三和弦'显示预期音符(蓝色) | 1. Click 'Set C Major Chord' to show expected notes (blue) | 调试步骤1 |
| `debug_instruction_2` | 2. 点击'触发完全匹配'会触发绿色粒子爆炸 | 2. Click 'Trigger Perfect Match' will trigger green particle explosion | 调试步骤2 |
| `debug_instruction_3` | 3. 🎆 线条粒子：从连线上徐徐飘散的彩色粒子 | 3. 🎆 Line particles: Colorful particles gently drifting from lines | 调试步骤3 |
| `debug_instruction_4` | 4. 粒子特点：无重力、速度逐渐减慢、轻柔飘散 | 4. Particle features: No gravity, gradually slowing speed, gentle drift | 调试步骤4 |

### 23. 开发者工具 (Developer Tools)
| Key | 中文 | 英文 | 使用场景 |
|-----|------|------|----------|
| `developer_internal_tool` | 开发者内部工具 | Developer Internal Tool | 开发工具标题 |
| `press_keys_capture` | 按下琴键并踩踏板捕获音符 | Press keys and pedal to capture notes | 捕获操作说明 |
| `captured_notes` | 已捕获的音符: | Captured notes: | 捕获结果显示 |
| `lowest_note` | 最低音 (root_note): %@ | Lowest note (root_note): %@ | 最低音显示 |
| `intervals_label` | 音程 (intervals): %@ | Intervals: %@ | 音程信息显示 |
| `generated_voicing_data` | 生成的 Voicing 数据: | Generated Voicing data: | 生成数据显示 |
| `save_result` | 保存结果 | Save Result | 保存结果按钮 |

### 24. 倒计时 (Countdown)
| Key | 中文 | 英文 | 使用场景 |
|-----|------|------|----------|
| `countdown_number` | %d | %d | 倒计时数字显示 |

### 25. 其他 (Others)
| Key | 中文 | 英文 | 使用场景 |
|-----|------|------|----------|
| `level_number` | Level %d | Level %d | 关卡号显示 |
| `playing_chord_index` | Playing %d/%d | Playing %d/%d | 播放进度显示 |
| `current_status` | 当前: %@ | Current: %@ | 当前状态显示 |
| `round_info` | (%d/%d) | (%d/%d) | 回合信息显示 |
| `details` | 详情 | Details | 详情按钮 |

---

## 本地化实施状态

### ✅ 已完成
- [x] 扩展 `zh-Hans.lproj/Localizable.strings` 文件，新增约200个本地化字符串
- [x] 扩展 `en.lproj/Localizable.strings` 文件，新增对应的英文翻译
- [x] 按功能模块分类组织所有本地化字符串
- [x] 使用 `MARK` 注释进行清晰的分段
- [x] 支持格式化字符串（%d, %@等）

### 📋 待实施（需程序逻辑修改）
- [ ] 将代码中的硬编码字符串替换为 `NSLocalizedString` 调用
- [ ] 验证所有本地化字符串在实际使用中的正确性
- [ ] 测试不同语言环境下的UI显示效果

---

## 使用说明

### 在代码中使用本地化字符串

将硬编码字符串替换为本地化调用：

```swift
// 替换前
Text("开始练习")

// 替换后
Text(NSLocalizedString("start_practice", value: "Start Practice", comment: ""))
```

### 格式化字符串使用示例

对于包含参数的字符串：

```swift
// 显示音符数量
String(format: NSLocalizedString("note_count", comment: ""), noteCount)

// 显示当前状态
String(format: NSLocalizedString("waiting_input", comment: ""), chordName)
```

---

## 维护说明

1. **新增本地化字符串**：在两个 `Localizable.strings` 文件中同时添加
2. **修改已有字符串**：确保中英文版本保持同步
3. **删除不用字符串**：从两个文件中同时删除，并更新此文档
4. **分类管理**：新增字符串时请归类到合适的 MARK 分段中

---

## 符合iOS/macOS开发规范

### 文件结构
- ✅ 使用标准的 `.lproj` 目录结构
- ✅ 使用标准的 `Localizable.strings` 文件名
- ✅ 支持 iOS 和 macOS 平台

### 命名规范
- ✅ 使用语义化的 key 名称
- ✅ 使用下划线分隔单词
- ✅ 按功能模块分组

### 编码规范
- ✅ 使用 UTF-8 编码
- ✅ 支持格式化字符串
- ✅ 包含适当的注释说明

### 可维护性
- ✅ 清晰的分类结构
- ✅ 完整的文档记录
- ✅ 统一的命名约定 