# FullUI 项目结构文档

## 项目概述
FullUI 是一个基于 SwiftUI 的音乐学习应用，支持音符识别和和弦练习功能。

## 文件夹结构

### 📁 Core/ - 应用程序核心
应用程序的核心架构和基础设施

#### 📁 Core/App/ - 应用程序入口
- `FullUIApp.swift` - 应用程序主入口点，定义 App 生命周期
- `ContentView.swift` - 应用程序根视图
- `MusicAppView.swift` - 主要的音乐应用界面，包含标签页导航

#### 📁 Core/Configuration/ - 配置管理
- `GameConfig.swift` - 游戏配置数据模型和管理器
- `GameConfig.json` - 游戏默认配置文件
- `FullUI.entitlements` - 应用程序权限配置
- `PlatformDependant.swift` - 平台相关的代码适配

#### 📁 Core/Managers/ - 核心管理器
- `MIDIManager.swift` - MIDI 输入输出管理，处理键盘连接和音符事件
- `NoteGameManager.swift` - 音符游戏逻辑管理器，处理游戏状态和评分
- `ChordGameManager.swift` - 和弦游戏逻辑管理器，处理和弦识别和练习

### 📁 Game/ - 游戏模块
包含所有游戏相关的视图和逻辑

#### 📁 Game/Chord/ - 和弦游戏
- `ChordsView.swift` - 和弦练习主界面
- `ChordBrowserView.swift` - 三级和弦浏览器，支持分类、类型、配置选择
- `CompactVoicingPicker.swift` - 紧凑型和弦配置选择器

#### 📁 Game/Note/ - 音符游戏
- `NotesView.swift` - 音符识别练习主界面
- `NoteGenerator.swift` - 音符生成器，用于练习模式

#### 📁 Game/Shared/ - 游戏共享组件
- `EnhancedTargetNotesDisplay.swift` - 增强型目标音符显示组件
- `CompactGameStatus.swift` - 紧凑型游戏状态显示
- `LevelClearView.swift` - 关卡完成界面
- `PianoKeyboardView.swift` - 虚拟钢琴键盘组件

### 📁 UI/ - 用户界面
应用程序的用户界面组件

#### 📁 UI/Views/ - 主要视图
- `SettingsView.swift` - 设置界面
- `FullScreenSettingsView.swift` - 全屏设置界面
- `ProgressionsView.swift` - 和弦进行练习界面
- `SongsView.swift` - 歌曲练习界面

#### 📁 UI/Shared/ - 共享UI组件
- `ChordBadge.swift` - 和弦标识组件
- `HapticFeedback.swift` - 触觉反馈工具类

### 📁 Music/ - 音乐相关
音乐理论、数据模型和音频处理

#### 📁 Music/Models/ - 音乐数据模型
- `ChordVoicing.swift` - 和弦配置数据模型
- `ChordsParser.swift` - 和弦数据解析器，处理三级和弦结构
- `NoteNameGenerator.swift` - 音符名称生成工具，统一处理 MIDI 到音符名的转换

#### 📁 Music/Audio/ - 音频处理
- `ChordPlayer.swift` - 和弦播放器，支持预览和播放功能
- `SpriteKitParticleSystem.swift` - 基于 SpriteKit 的粒子系统
- `ParticleEffect.swift` - 粒子效果管理器

#### 📁 Music/Analysis/ - 音乐分析
- `ChordAnalyzer.swift` - 和弦分析器，识别和分析演奏的和弦

### 📁 Resources/ - 资源文件
应用程序使用的各种资源

#### 📁 Resources/Assets/ - 图片资源
- `Assets.xcassets/` - Xcode 资源目录
- `perfect.png` - Perfect 动画图片
- `star.png` - 星星图标

#### 📁 Resources/Data/ - 数据文件
- `trial.json` - 试用版配置数据
- `chords.json` - 和弦数据库，包含三级结构的和弦信息
- `voicings.json` - 和弦配置数据

#### 📁 Resources/Sounds/ - 音频文件
- `UprightPiano.sf2` - 立式钢琴音色库 (9MB)
- `Piano.sf2` - 钢琴音色库 (55MB)
- `GM.sf2` - 通用 MIDI 音色库 (307MB)

### 📁 Documentation/ - 文档
项目文档和开发日志

#### 📁 Documentation/DevLogs/ - 开发日志
- `PERFECT_ANIMATION_ENHANCEMENT.md` - Perfect 动画增强记录
- `OFF_BY_ONE_FIX.md` - 偏移错误修复记录
- `COORDINATE_FIX_FINAL.md` - 坐标修复最终版本
- `COORDINATE_DEBUG_SUMMARY.md` - 坐标调试总结
- `COORDINATE_CONVERSION_FIXES.md` - 坐标转换修复
- `PARTICLE_EXPLOSION_FIXES.md` - 粒子爆炸效果修复
- `DEBUG_PARTICLE_FIXES.md` - 粒子系统调试修复
- `PARTICLE_SYSTEM_IMPLEMENTATION.md` - 粒子系统实现记录
- `CROSS_PLATFORM_FIXES.md` - 跨平台兼容性修复
- `REFACTOR_SUMMARY.md` - 重构总结

#### 📁 Documentation/ - 其他文档
- `explodeTextView.txt` - 文本爆炸效果相关文档
- `ProjectStructure.md` - 本项目结构文档

## 架构设计原则

### 1. 分层架构
- **Core**: 应用程序核心，包含启动、配置和核心管理器
- **Game**: 游戏逻辑层，按功能模块分离
- **UI**: 用户界面层，分离视图和共享组件
- **Music**: 音乐业务逻辑层，处理音乐相关的数据和算法
- **Resources**: 资源层，统一管理各类资源文件

### 2. 模块化设计
- 按功能领域划分模块（和弦、音符、UI等）
- 共享组件独立管理，避免重复代码
- 工具类统一放置，便于复用

### 3. 数据驱动
- 配置文件外部化，支持动态调整
- 音乐数据结构化存储，支持扩展
- 资源文件分类管理，便于维护

### 4. 平台兼容
- 平台相关代码集中管理
- 使用条件编译处理平台差异
- 统一的接口设计，隐藏平台细节

## 开发规范

### 文件命名
- 使用 PascalCase 命名 Swift 文件
- 文件夹使用简洁的英文名称
- 资源文件使用小写字母和下划线

### 代码组织
- 每个文件专注单一职责
- 使用 MARK 注释分组代码
- 保持文件大小适中（建议不超过 500 行）

### 依赖管理
- 避免循环依赖
- 优先使用协议和依赖注入
- 核心模块不依赖 UI 模块

---

*文档创建时间: 2025年1月*
*最后更新: 项目重构完成后* 