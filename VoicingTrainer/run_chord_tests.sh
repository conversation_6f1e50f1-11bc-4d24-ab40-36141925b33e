#!/bin/bash

# VoicingTrainer 新格式测试脚本
# 测试新的和弦进行格式功能

echo "🎵 ==================== VoicingTrainer 新格式测试 ===================="
echo "📅 测试时间: $(date)"
echo "🗂️  当前目录: $(pwd)"
echo ""

# 设置颜色
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 测试计数器
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# 测试函数
run_test() {
    local test_name="$1"
    local test_command="$2"
    
    echo -e "${BLUE}🧪 测试: $test_name${NC}"
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    
    if eval "$test_command" 2>/dev/null; then
        echo -e "${GREEN}✅ 通过${NC}"
        PASSED_TESTS=$((PASSED_TESTS + 1))
    else
        echo -e "${RED}❌ 失败${NC}"
        FAILED_TESTS=$((FAILED_TESTS + 1))
    fi
    echo ""
}

# 设置文件路径
NEW_FORMAT_FILE="Resources/Data/progressions_v2/major 251 P3.progression"
OLD_FORMAT_FILE="Resources/Data/progressions/Jazz II-V-I.progression"

# 1. 检查新格式文件是否存在
echo -e "${YELLOW}📁 检查新格式文件...${NC}"
run_test "检查新格式示例文件" "test -f '$NEW_FORMAT_FILE'"

# 2. 检查JSON格式是否有效
echo -e "${YELLOW}📋 检查JSON格式...${NC}"
run_test "验证JSON格式" "python3 -c 'import json; json.load(open(\"$NEW_FORMAT_FILE\"))'"

# 3. 检查新格式字段
echo -e "${YELLOW}🔍 检查新格式字段...${NC}"
run_test "检查suffix字段" "python3 -c 'import json; data=json.load(open(\"$NEW_FORMAT_FILE\")); assert \"suffix\" in data[\"progression\"][\"chords\"][0]'"
run_test "检查rootNote字段" "python3 -c 'import json; data=json.load(open(\"$NEW_FORMAT_FILE\")); assert \"rootNote\" in data[\"progression\"][\"chords\"][0]'"
run_test "检查scale字段" "python3 -c 'import json; data=json.load(open(\"$NEW_FORMAT_FILE\")); assert \"scale\" in data[\"progression\"]'"

# 4. 验证数据结构
echo -e "${YELLOW}📊 验证数据结构...${NC}"
run_test "验证和弦数量" "python3 -c 'import json; data=json.load(open(\"$NEW_FORMAT_FILE\")); assert len(data[\"progression\"][\"chords\"]) == 3'"
run_test "验证notes数组" "python3 -c 'import json; data=json.load(open(\"$NEW_FORMAT_FILE\")); assert isinstance(data[\"progression\"][\"chords\"][0][\"notes\"], list)'"

# 5. 验证音程数组内容
echo -e "${YELLOW}🎹 验证音程数组内容...${NC}"
run_test "验证第一个和弦音程" "python3 -c 'import json; data=json.load(open(\"$NEW_FORMAT_FILE\")); assert data[\"progression\"][\"chords\"][0][\"notes\"] == [0, 10, 15, 19, 26]'"
run_test "验证根音格式" "python3 -c 'import json; data=json.load(open(\"$NEW_FORMAT_FILE\")); assert \"3\" in data[\"progression\"][\"chords\"][0][\"rootNote\"]'"

# 6. 特定格式测试
echo -e "${YELLOW}🎹 特定格式测试...${NC}"

# 创建简单的Swift测试脚本
cat > test_new_format.swift << 'EOF'
import Foundation

// 测试新格式解析
let testData = """
{
  "progression": {
    "name": "Test Progression",
    "scale": "CM",
    "chords": [
      {
        "suffix": "m7",
        "rootNote": "D3",
        "notes": [0, 3, 7, 10]
      }
    ]
  }
}
"""

struct TestChordInfo: Codable {
    let suffix: String
    let rootNote: String
    let notes: [Int]
}

struct TestProgression: Codable {
    let name: String
    let scale: String
    let chords: [TestChordInfo]
}

struct TestProgressionFile: Codable {
    let progression: TestProgression
}

do {
    let data = testData.data(using: .utf8)!
    let progressionFile = try JSONDecoder().decode(TestProgressionFile.self, from: data)
    
    print("✅ 新格式解析成功")
    print("和弦进行名称: \(progressionFile.progression.name)")
    print("调性: \(progressionFile.progression.scale)")
    print("和弦数量: \(progressionFile.progression.chords.count)")
    
    let firstChord = progressionFile.progression.chords[0]
    print("第一个和弦:")
    print("  - 根音: \(firstChord.rootNote)")
    print("  - 后缀: \(firstChord.suffix)")
    print("  - 音程: \(firstChord.notes)")
    
    exit(0)
} catch {
    print("❌ 新格式解析失败: \(error)")
    exit(1)
}
EOF

run_test "新格式解析测试" "swift test_new_format.swift"

# 清理测试文件
rm -f test_new_format.swift

# 7. 向后兼容性测试
echo -e "${YELLOW}🔄 向后兼容性测试...${NC}"
run_test "检查旧格式文件" "test -f '$OLD_FORMAT_FILE'"

# 8. 检查新格式文件的具体内容
echo -e "${YELLOW}📋 检查新格式文件内容...${NC}"
if [ -f "$NEW_FORMAT_FILE" ]; then
    echo -e "${GREEN}✅ 新格式文件内容预览:${NC}"
    echo "----------------------------------------"
    head -20 "$NEW_FORMAT_FILE"
    echo "----------------------------------------"
fi

# 9. 统计新格式文件数量
echo -e "${YELLOW}📊 统计新格式文件...${NC}"
NEW_FORMAT_COUNT=$(find Resources/Data/progressions_v2 -name "*.progression" | wc -l)
OLD_FORMAT_COUNT=$(find Resources/Data/progressions -name "*.progression" | wc -l)

echo "新格式文件数量: $NEW_FORMAT_COUNT"
echo "旧格式文件数量: $OLD_FORMAT_COUNT"

# 测试总结
echo -e "${YELLOW}📊 ==================== 测试总结 ====================${NC}"
echo "总测试数: $TOTAL_TESTS"
echo -e "通过: ${GREEN}$PASSED_TESTS${NC}"
echo -e "失败: ${RED}$FAILED_TESTS${NC}"

if [ $FAILED_TESTS -eq 0 ]; then
    echo -e "${GREEN}🎉 所有测试通过！新格式实现成功！${NC}"
    exit 0
else
    echo -e "${RED}❌ 有 $FAILED_TESTS 个测试失败，请检查实现${NC}"
    exit 1
fi 