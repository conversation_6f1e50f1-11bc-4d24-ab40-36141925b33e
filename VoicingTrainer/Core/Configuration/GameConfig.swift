//
//  GameConfig.swift
//  FullUI
//
//  Created by <PERSON> on 2025/5/26.
//

import Foundation

// MARK: - SoundFont配置
struct SoundFontOption: Codable, Identifiable, Equatable, Hashable {
    let name: String
    let filename: String
    
    var id: String { filename }
}

struct SoundFontSettings: Codable {
    let soundFontCount: Int
    let soundFonts: [SoundFontOption]
}

// MARK: - 键盘配置
struct KeyboardLayout: Codable, Identifiable, CaseIterable, Equatable, Hashable {
    let id: String
    let name: String
    let keyCount: Int
    let firstKey: Int
    let lastKey: Int
    
    static let allCases: [KeyboardLayout] = [
        KeyboardLayout(id: "88keys", name: "88键", keyCount: 88, firstKey: 21, lastKey: 108),
        KeyboardLayout(id: "61keys", name: "61键", keyCount: 61, firstKey: 36, lastKey: 96),
        KeyboardLayout(id: "49keys", name: "49键", keyCount: 49, firstKey: 36, lastKey: 84),
        KeyboardLayout(id: "37keys", name: "37键", keyCount: 37, firstKey: 48, lastKey: 84),
        KeyboardLayout(id: "25keys", name: "25键", keyCount: 25, firstKey: 48, lastKey: 72)
    ]
    
    static let defaultLayout = KeyboardLayout.allCases[0] // 88键为默认
}

struct GameSettings: Codable {
    let roundTotalCount: Int    //总轮数
    let loKey: Int              //最低音
    let hiKey: Int              //最高音
    let whiteKeyOnly: Bool      //只有白键

    // 🎵 全局BPM系统 - 所有功能统一使用
    let globalBpm: Int              // 全局BPM设置（所有功能使用）
    let beatsPerNote: Double        // Note模式：每个音符占用的拍数
    let beatsPerChord: Double       // Chord模式：每个和弦占用的拍数
    let beatsPerProgression: Double // Progression模式：每个和弦占用的拍数
    let responseBeats: Double       // 反应时间占用的拍数（所有模式通用）

    // 🎵 BPM范围限制
    let minBpm: Int                 // 最小BPM
    let maxBpm: Int                 // 最大BPM
    let minWaitTime: Double         // 最小等待时间（秒）
    let maxWaitTime: Double         // 最大等待时间（秒）
    let minResponseTime: Double     // 最小反应时间（秒）
    let maxResponseTime: Double     // 最大反应时间（秒）

    // 🎵 向后兼容（已弃用，保留用于配置迁移）
    let defaultBpm: Int?            // 已弃用，迁移到globalBpm
    let waitTime: Double?           // 已弃用
    let responseTime: Double?       // 已弃用
    let noteInterval: Double?       // 已弃用
    let chordsWaitInterval: Double? // 已弃用
}

struct AudioSettings: Codable {
    let mode: String // "soundFont" or "audioKit"
    let soundFontName: String
    let playbackVolume: Double  // 新增：游戏音频回放音量 (0-127)
}

struct MIDISettings: Codable {
    let selectedInputDevices: [String]  // 多选输入设备
    let selectedOutputDevice: String    // 单选输出设备
}

struct KeyboardSettings: Codable {
    let selectedLayoutId: String
    
    var selectedLayout: KeyboardLayout {
        return KeyboardLayout.allCases.first { $0.id == selectedLayoutId } ?? KeyboardLayout.defaultLayout
    }
}

struct GameConfig: Codable {
    let gameSettings: GameSettings
    let audioSettings: AudioSettings
    let keyboardSettings: KeyboardSettings
    let soundFontSettings: SoundFontSettings
    let midiSettings: MIDISettings
}

class GameConfigManager: ObservableObject {
    static let shared = GameConfigManager()
    
    @Published var config: GameConfig
    
    private init() {
        // 默认配置
        self.config = GameConfig(
            gameSettings: GameSettings(
                roundTotalCount: 12,
                loKey: 60,
                hiKey: 72,
                whiteKeyOnly: true,

                // 🎵 全局BPM系统 - 所有功能统一使用
                globalBpm: 60,             // 全局60 BPM（适中速度，所有功能使用）
                beatsPerNote: 10.0,        // Note模式：每个音符10拍 (60BPM × 10拍 = 10秒，保持原体验)
                beatsPerChord: 12.0,       // Chord模式：每个和弦12拍 (60BPM × 12拍 = 12秒，保持原体验)
                beatsPerProgression: 4.0,  // Progression模式：每个和弦4拍 (60BPM × 4拍 = 4秒，合理的练习速度)
                responseBeats: 5.0,        // 反应时间5拍 (60BPM × 5拍 = 5秒，保持原体验)

                // 🎵 BPM范围和安全时间
                minBpm: 30,                // 最小30 BPM
                maxBpm: 200,               // 最大200 BPM
                minWaitTime: 1.5,          // 最小1.5秒等待
                maxWaitTime: 20.0,         // 最大20秒等待
                minResponseTime: 0.5,      // 最小0.5秒反应
                maxResponseTime: 8.0,      // 最大8秒反应

                // 🎵 向后兼容（已弃用）
                defaultBpm: nil,
                waitTime: nil,
                responseTime: nil,
                noteInterval: nil,
                chordsWaitInterval: nil
            ),
            audioSettings: AudioSettings(
                mode: "soundFont",
                soundFontName: "piano",
                playbackVolume: 80.0
            ),
            keyboardSettings: KeyboardSettings(
                selectedLayoutId: KeyboardLayout.defaultLayout.id
            ),
            soundFontSettings: SoundFontSettings(
                soundFontCount: 2,
                soundFonts: [
                    SoundFontOption(name: "Piano", filename: "piano.sf2"),
                    SoundFontOption(name: "E-piano", filename: "ep.sf2")
                ]
            ),
            midiSettings: MIDISettings(
                selectedInputDevices: [],
                selectedOutputDevice: "Built-in Sound"
            )
        )

        loadConfig()

        // 🔧 检查并修复异常的BPM值
        resetBpmIfNeeded()
    }
    
    private func loadConfig() {
        // 优先从UserDefaults加载已保存的配置
        if let data = UserDefaults.standard.data(forKey: "GameConfig") {
            do {
                let savedConfig = try JSONDecoder().decode(GameConfig.self, from: data)
                print("✅ Loaded config from UserDefaults")
                print("🔍 UserDefaults soundFonts count: \(savedConfig.soundFontSettings.soundFonts.count)")
                // 如果UserDefaults中的soundFonts为空，清除它并从JSON重新加载
                if savedConfig.soundFontSettings.soundFonts.isEmpty {
                    print("⚠️ UserDefaults config has empty soundFonts, clearing and reloading from JSON")
                    UserDefaults.standard.removeObject(forKey: "GameConfig")
                    // 继续加载JSON配置
                } else {
                    self.config = savedConfig
                    return
                }
            } catch {
                print("⚠️ Failed to decode saved config (possibly old version): \(error)")
                print("🧹 Clearing old config and using defaults")
                // 清除不兼容的旧配置
                UserDefaults.standard.removeObject(forKey: "GameConfig")
                // 继续使用默认配置
            }
        }
        
        // 如果没有保存的配置，尝试从bundle加载默认配置
        guard let url = Bundle.main.url(forResource: "GameConfig", withExtension: "json") else {
            print("❌ GameConfig.json not found in bundle")
            print("Failed to load config, using defaults")
            return
        }
        
        print("✅ Found GameConfig.json at: \(url)")
        
        guard let data = try? Data(contentsOf: url) else {
            print("❌ Failed to read GameConfig.json")
            print("Failed to load config, using defaults")
            return
        }
        
        print("✅ Successfully read GameConfig.json (\(data.count) bytes)")
        
        do {
            let config = try JSONDecoder().decode(GameConfig.self, from: data)
            print("✅ Successfully decoded GameConfig from JSON")
            print("🔍 JSON soundFonts count: \(config.soundFontSettings.soundFonts.count)")
            for soundFont in config.soundFontSettings.soundFonts {
                print("   - \(soundFont.name): \(soundFont.filename)")
            }
            self.config = config
        } catch {
            print("❌ Failed to decode GameConfig: \(error)")
            if let jsonString = String(data: data, encoding: .utf8) {
                print("JSON content: \(jsonString)")
            }
            print("Failed to load config, using defaults")
        }
    }
    
    func updateMIDIInputDevices(_ devices: [String]) {
        config = GameConfig(
            gameSettings: config.gameSettings,
            audioSettings: config.audioSettings,
            keyboardSettings: config.keyboardSettings,
            soundFontSettings: config.soundFontSettings,
            midiSettings: MIDISettings(
                selectedInputDevices: devices,
                selectedOutputDevice: config.midiSettings.selectedOutputDevice
            )
        )
        saveConfig()
    }
    
    func updateMIDIOutputDevice(_ device: String) {
        config = GameConfig(
            gameSettings: config.gameSettings,
            audioSettings: config.audioSettings,
            keyboardSettings: config.keyboardSettings,
            soundFontSettings: config.soundFontSettings,
            midiSettings: MIDISettings(
                selectedInputDevices: config.midiSettings.selectedInputDevices,
                selectedOutputDevice: device
            )
        )
        saveConfig()
    }
    
    func updateKeyboardLayout(_ layout: KeyboardLayout) {
        config = GameConfig(
            gameSettings: config.gameSettings,
            audioSettings: config.audioSettings,
            keyboardSettings: KeyboardSettings(
                selectedLayoutId: layout.id
            ),
            soundFontSettings: config.soundFontSettings,
            midiSettings: config.midiSettings
        )
        saveConfig()
    }
    
    func updateSoundFont(_ soundFontFilename: String) {
        config = GameConfig(
            gameSettings: config.gameSettings,
            audioSettings: AudioSettings(
                mode: config.audioSettings.mode,
                soundFontName: soundFontFilename,
                playbackVolume: config.audioSettings.playbackVolume
            ),
            keyboardSettings: config.keyboardSettings,
            soundFontSettings: config.soundFontSettings,
            midiSettings: config.midiSettings
        )
        saveConfig()
    }
    
    func updatePlaybackVolume(_ volume: Double) {
        config = GameConfig(
            gameSettings: config.gameSettings,
            audioSettings: AudioSettings(
                mode: config.audioSettings.mode,
                soundFontName: config.audioSettings.soundFontName,
                playbackVolume: volume
            ),
            keyboardSettings: config.keyboardSettings,
            soundFontSettings: config.soundFontSettings,
            midiSettings: config.midiSettings
        )
        saveConfig()
    }

    /// 🎵 更新全局BPM设置
    func updateGlobalBpm(_ bpm: Int) {
        let clampedBpm = max(config.gameSettings.minBpm, min(config.gameSettings.maxBpm, bpm))

        config = GameConfig(
            gameSettings: GameSettings(
                roundTotalCount: config.gameSettings.roundTotalCount,
                loKey: config.gameSettings.loKey,
                hiKey: config.gameSettings.hiKey,
                whiteKeyOnly: config.gameSettings.whiteKeyOnly,
                globalBpm: clampedBpm,
                beatsPerNote: config.gameSettings.beatsPerNote,
                beatsPerChord: config.gameSettings.beatsPerChord,
                beatsPerProgression: config.gameSettings.beatsPerProgression,
                responseBeats: config.gameSettings.responseBeats,
                minBpm: config.gameSettings.minBpm,
                maxBpm: config.gameSettings.maxBpm,
                minWaitTime: config.gameSettings.minWaitTime,
                maxWaitTime: config.gameSettings.maxWaitTime,
                minResponseTime: config.gameSettings.minResponseTime,
                maxResponseTime: config.gameSettings.maxResponseTime,
                defaultBpm: config.gameSettings.defaultBpm,
                waitTime: config.gameSettings.waitTime,
                responseTime: config.gameSettings.responseTime,
                noteInterval: config.gameSettings.noteInterval,
                chordsWaitInterval: config.gameSettings.chordsWaitInterval
            ),
            audioSettings: config.audioSettings,
            keyboardSettings: config.keyboardSettings,
            soundFontSettings: config.soundFontSettings,
            midiSettings: config.midiSettings
        )
        saveConfig()
        print("🎵 全局BPM已更新为: \(clampedBpm)")
    }
    
    private func saveConfig() {
        // 保存配置到UserDefaults
        if let data = try? JSONEncoder().encode(config) {
            UserDefaults.standard.set(data, forKey: "GameConfig")
        }
    }
    
    func saveConfigIfNeeded() {
        saveConfig()
    }

    /// 🔧 重置BPM配置（用于修复旧数据）
    func resetBpmIfNeeded() {
        // 如果BPM超出合理范围，重置为默认值
        if config.gameSettings.globalBpm < config.gameSettings.minBpm ||
           config.gameSettings.globalBpm > config.gameSettings.maxBpm {
            print("🔧 检测到异常BPM值: \(config.gameSettings.globalBpm)，重置为默认值")
            updateGlobalBpm(60) // 重置为默认的60 BPM
        }
    }
    
    // 测试函数：验证配置加载状态
    func testConfigLoading() {
        print("=== GameConfig 测试开始 ===")
        print("当前配置来源：", config.audioSettings.soundFontName == "ep" ? "从JSON加载" : "使用默认值")
        print("SoundFont名称：", config.audioSettings.soundFontName)
        print("键盘布局：", config.keyboardSettings.selectedLayoutId)
        print("默认BPM：", config.gameSettings.defaultBpm)
        print("=== GameConfig 测试结束 ===")
    }

    // MARK: - 🎵 统一BPM时间计算系统

    /// 计算基于BPM的等待时间
    /// - Parameters:
    ///   - bpm: 每分钟节拍数
    ///   - beats: 占用的拍数
    ///   - minTime: 最小时间限制
    ///   - maxTime: 最大时间限制
    /// - Returns: 计算后的时间（秒）
    func calculateBpmTime(bpm: Int, beats: Double, minTime: Double, maxTime: Double) -> Double {
        let secondsPerBeat = 60.0 / Double(bpm)
        let calculatedTime = secondsPerBeat * beats
        let clampedTime = max(minTime, min(maxTime, calculatedTime))

        print("🎵 BPM时间计算: BPM=\(bpm), 拍数=\(beats), 每拍=\(String(format: "%.2f", secondsPerBeat))s, 计算=\(String(format: "%.2f", calculatedTime))s, 最终=\(String(format: "%.2f", clampedTime))s")

        return clampedTime
    }

    /// Note模式等待时间（使用全局BPM）
    func getNoteWaitTime(bpm: Int? = nil) -> Double {
        let actualBpm = bpm ?? config.gameSettings.globalBpm
        return calculateBpmTime(
            bpm: actualBpm,
            beats: config.gameSettings.beatsPerNote,
            minTime: config.gameSettings.minWaitTime,
            maxTime: config.gameSettings.maxWaitTime
        )
    }

    /// Chord模式等待时间（使用全局BPM）
    func getChordWaitTime(bpm: Int? = nil) -> Double {
        let actualBpm = bpm ?? config.gameSettings.globalBpm
        return calculateBpmTime(
            bpm: actualBpm,
            beats: config.gameSettings.beatsPerChord,
            minTime: config.gameSettings.minWaitTime,
            maxTime: config.gameSettings.maxWaitTime
        )
    }

    /// Progression模式等待时间（使用全局BPM）
    func getProgressionWaitTime(bpm: Int? = nil) -> Double {
        let actualBpm = bpm ?? config.gameSettings.globalBpm
        return calculateBpmTime(
            bpm: actualBpm,
            beats: config.gameSettings.beatsPerProgression,
            minTime: config.gameSettings.minWaitTime,
            maxTime: config.gameSettings.maxWaitTime
        )
    }

    /// 反应时间（所有模式通用，使用全局BPM）
    func getResponseTime(bpm: Int? = nil) -> Double {
        let actualBpm = bpm ?? config.gameSettings.globalBpm
        return calculateBpmTime(
            bpm: actualBpm,
            beats: config.gameSettings.responseBeats,
            minTime: config.gameSettings.minResponseTime,
            maxTime: config.gameSettings.maxResponseTime
        )
    }
}
