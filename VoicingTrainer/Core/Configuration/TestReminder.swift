import Foundation
import SwiftUI

/// 测试提醒系统 - 实现测试驱动开发
/// 在Debug构建中提醒开发者定期运行单元测试
class TestReminder: ObservableObject {
    
    static let shared = TestReminder()
    
    @Published var shouldShowTestReminder = false
    @Published var lastTestRun: Date?
    @Published var codeChangesCount = 0
    
    private let maxChangesBeforeReminder = 5
    private let testReminderInterval: TimeInterval = 15 * 60 // 15分钟
    
    private init() {
        loadTestHistory()
        startReminderTimer()
    }
    
    // MARK: - 公共接口
    
    /// 记录代码变更（在关键代码修改时调用）
    func recordCodeChange(component: String) {
        #if DEBUG
        codeChangesCount += 1
        print("🔧 代码变更记录: \(component) (总变更: \(codeChangesCount))")
        
        if codeChangesCount >= maxChangesBeforeReminder {
            showTestReminder()
        }
        
        saveTestHistory()
        #endif
    }
    
    /// 记录测试运行
    func recordTestRun() {
        #if DEBUG
        lastTestRun = Date()
        codeChangesCount = 0
        shouldShowTestReminder = false
        
        print("✅ 测试运行记录已更新")
        saveTestHistory()
        #endif
    }
    
    /// 显示测试提醒
    func showTestReminder() {
        #if DEBUG
        shouldShowTestReminder = true
        print("🧪 测试提醒: 建议运行单元测试！")
        print("   已累积 \(codeChangesCount) 次代码变更")
        if let lastTest = lastTestRun {
            let timeSinceLastTest = Date().timeIntervalSince(lastTest)
            print("   上次测试: \(Int(timeSinceLastTest/60)) 分钟前")
        } else {
            print("   尚未记录测试运行")
        }
        #endif
    }
    
    /// 获取测试提醒消息
    var testReminderMessage: String {
        var message = "建议运行单元测试！\n"
        message += "已累积 \(codeChangesCount) 次代码变更\n"
        
        if let lastTest = lastTestRun {
            let timeSinceLastTest = Date().timeIntervalSince(lastTest)
            let minutesAgo = Int(timeSinceLastTest / 60)
            message += "上次测试: \(minutesAgo) 分钟前"
        } else {
            message += "尚未记录测试运行"
        }
        
        return message
    }
    
    // MARK: - 私有方法
    
    private func startReminderTimer() {
        #if DEBUG
        Timer.scheduledTimer(withTimeInterval: testReminderInterval, repeats: true) { _ in
            if self.codeChangesCount > 0 {
                if let lastTest = self.lastTestRun {
                    let timeSinceLastTest = Date().timeIntervalSince(lastTest)
                    if timeSinceLastTest > self.testReminderInterval {
                        self.showTestReminder()
                    }
                } else {
                    self.showTestReminder()
                }
            }
        }
        #endif
    }
    
    private func loadTestHistory() {
        #if DEBUG
        let defaults = UserDefaults.standard
        if let lastTestData = defaults.object(forKey: "lastTestRun") as? Date {
            lastTestRun = lastTestData
        }
        codeChangesCount = defaults.integer(forKey: "codeChangesCount")
        #endif
    }
    
    private func saveTestHistory() {
        #if DEBUG
        let defaults = UserDefaults.standard
        defaults.set(lastTestRun, forKey: "lastTestRun")
        defaults.set(codeChangesCount, forKey: "codeChangesCount")
        #endif
    }
}

// MARK: - SwiftUI 视图组件

struct TestReminderView: View {
    @ObservedObject var testReminder = TestReminder.shared
    
    var body: some View {
        #if DEBUG
        if testReminder.shouldShowTestReminder {
            VStack(spacing: 12) {
                HStack {
                    Image(systemName: "exclamationmark.triangle.fill")
                        .foregroundColor(.orange)
                    Text("测试提醒")
                        .font(.headline)
                        .fontWeight(.bold)
                }
                
                Text(testReminder.testReminderMessage)
                    .font(.body)
                    .multilineTextAlignment(.center)
                
                HStack(spacing: 16) {
                    Button("运行测试") {
                        runTests()
                    }
                    .buttonStyle(.borderedProminent)
                    
                    Button("稍后提醒") {
                        testReminder.shouldShowTestReminder = false
                    }
                    .buttonStyle(.bordered)
                }
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(.ultraThinMaterial)
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(.orange.opacity(0.5), lineWidth: 1)
                    )
            )
            .padding(.horizontal)
        }
        #endif
    }
    
    private func runTests() {
        #if DEBUG
        testReminder.recordTestRun()
        
        // 在实际项目中，这里应该触发测试运行
        print("🧪 启动单元测试...")
        
        #if os(macOS)
        // 运行shell命令来执行测试 (仅在macOS上支持)
        let task = Process()
        task.launchPath = "/bin/bash"
        task.arguments = ["-c", "cd \(FileManager.default.currentDirectoryPath) && ./run_chord_tests.sh"]
        
        do {
            try task.run()
            print("✅ 测试脚本已启动")
        } catch {
            print("❌ 启动测试脚本失败: \(error.localizedDescription)")
        }
        #else
        // iOS上只记录测试运行，不执行shell命令
        print("✅ 测试运行已记录 (iOS平台)")
        #endif
        #endif
    }
}

// MARK: - 便利宏

/// 在关键代码修改时调用，记录代码变更
/// - Parameter component: 修改的组件名称
func recordCodeChange(_ component: String) {
    #if DEBUG
    TestReminder.shared.recordCodeChange(component: component)
    #endif
}

/// 记录测试运行
func recordTestRun() {
    #if DEBUG
    TestReminder.shared.recordTestRun()
    #endif
} 