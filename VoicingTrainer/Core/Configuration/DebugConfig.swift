import Foundation

/// 调试配置管理器 - 控制应用的调试输出行为
/// 可以根据需要动态调整调试级别，优化性能
struct DebugConfig {
    
    // MARK: - Global Debug Settings
    
    /// 主调试开关 - 控制所有调试输出
    #if DEBUG
    static let isGlobalDebugEnabled = true
    #else
    static let isGlobalDebugEnabled = false
    #endif
    
    // MARK: - Category-Specific Settings
    
    /// MIDI相关调试
    static let isMIDIDebugEnabled = false
    
    /// 音频相关调试
    static let isAudioDebugEnabled = false
    
    /// 游戏逻辑调试
    static let isGameDebugEnabled = false
    
    /// UI相关调试
    static let isUIDebugEnabled = false
    
    /// 功耗监控调试 - 保持开启以获取测试结果
    static let isPowerDebugEnabled = false
    
    /// 性能监控调试
    static let isPerformanceDebugEnabled = true
    
    /// 网络相关调试
    static let isNetworkDebugEnabled = false
    
    /// 存储相关调试
    static let isStorageDebugEnabled = false
    
    // MARK: - Level-Specific Settings
    
    /// 详细日志开关
    static let isVerboseEnabled = false
    
    /// 信息日志开关
    static let isInfoEnabled = true
    
    /// 警告日志开关
    static let isWarningEnabled = true
    
    /// 错误日志开关
    static let isErrorEnabled = true
    
    /// 严重错误日志开关 - 总是开启
    static let isCriticalEnabled = true
    
    // MARK: - Performance Settings
    
    /// 是否启用性能测量
    static let isPerformanceMeasurementEnabled = true
    
    /// 性能测量阈值（毫秒）- 超过此值才记录
    static let performanceThresholdMs: Double = 10.0
    
    /// 是否启用内存使用监控
    static let isMemoryMonitoringEnabled = true
    
    /// 内存监控间隔（秒）
    static let memoryMonitoringInterval: TimeInterval = 30.0
    
    // MARK: - Power Test Settings
    
    /// 功耗测试采样间隔（秒）
    static let powerTestSamplingInterval: TimeInterval = 5.0
    
    /// 功耗测试持续时间（秒）
    static let powerTestDuration: TimeInterval = 60.0
    
    /// 是否保存功耗测试结果到文件
    static let shouldSavePowerTestResults = true
    
    /// 是否在控制台输出功耗测试详情
    static let shouldLogPowerTestDetails = true
    
    // MARK: - Audio Debug Settings
    static let debugAudio = true
    static let debugSoundEffects = true
    static let enableAudioWorkarounds = true  // 🔧 iPadOS 16.x 音频兼容模式
    static let disableSoundEffectsOnOldIOS = false  // 🔧 紧急情况下禁用音效
    
    // MARK: - Audio Session Configuration
    static let useUnifiedAudioSession = true  // 使用统一音频会话管理
    static let enableAudioSessionRecovery = true  // 启用音频会话恢复机制
    
    // MARK: - Circle12NotesView Configuration
    static let circle12ParticleEnabled = false
    static let circle12ParticleLifetime: Double = 3.0      // 粒子生命周期（秒）
    static let circle12ParticleCount: Int = 30             // 粒子数量
    static let circle12ParticleSize: Double = 2.0          // 粒子大小
    static let circle12ParticleMinForce: Double = 100.0    // 最小爆炸力
    static let circle12ParticleMaxForce: Double = 200.0    // 最大爆炸力
    static let circle12ParticleDebugEnabled = false        // 粒子效果调试模式
    
    // MARK: - ChordNameParser Configuration
    /// 是否启用和弦名称解析器调试
    static let chordNameParserDebug = true
    
    // MARK: - Helper Methods
    
    /// 检查特定类别的调试是否启用
    static func isDebugEnabled(for category: DebugLogger.Category) -> Bool {
        guard isGlobalDebugEnabled else { return false }
        
        switch category {
        case .midi: return isMIDIDebugEnabled
        case .audio: return isAudioDebugEnabled
        case .game: return isGameDebugEnabled
        case .ui: return isUIDebugEnabled
        case .power: return isPowerDebugEnabled
        case .performance: return isPerformanceDebugEnabled
        case .network: return isNetworkDebugEnabled
        case .storage: return isStorageDebugEnabled
        case .general: return true
        }
    }
    
    /// 检查特定级别的日志是否启用
    static func isLogLevelEnabled(_ level: DebugLogger.Level) -> Bool {
        switch level {
        case .verbose: return isVerboseEnabled
        case .info: return isInfoEnabled
        case .warning: return isWarningEnabled
        case .error: return isErrorEnabled
        case .critical: return isCriticalEnabled
        }
    }
    
    /// 获取当前调试配置摘要
    static func getConfigSummary() -> String {
        var summary = "🔧 调试配置摘要:\n"
        summary += "全局调试: \(isGlobalDebugEnabled ? "✅" : "❌")\n"
        summary += "MIDI调试: \(isMIDIDebugEnabled ? "✅" : "❌")\n"
        summary += "音频调试: \(isAudioDebugEnabled ? "✅" : "❌")\n"
        summary += "游戏调试: \(isGameDebugEnabled ? "✅" : "❌")\n"
        summary += "UI调试: \(isUIDebugEnabled ? "✅" : "❌")\n"
        summary += "功耗调试: \(isPowerDebugEnabled ? "✅" : "❌")\n"
        summary += "性能调试: \(isPerformanceDebugEnabled ? "✅" : "❌")\n"
        summary += "详细日志: \(isVerboseEnabled ? "✅" : "❌")\n"
        summary += "性能测量: \(isPerformanceMeasurementEnabled ? "✅" : "❌")\n"
        return summary
    }
    
    /// 运行时调整配置（用于测试）
    static func setDebugLevel(category: DebugLogger.Category, enabled: Bool) {
        // 注意：这里只是示例，实际实现需要使用可变的配置存储
        print("⚙️ 调试配置变更: \(category.rawValue) = \(enabled)")
    }
    
    /// 应用Circle12粒子效果配置
    func applyCircle12ParticleConfig() {
        // 只有在启用时才应用配置
        if DebugConfig.circle12ParticleEnabled {
            ChromaCircleEffects.particleLifetime = DebugConfig.circle12ParticleLifetime
            ChromaCircleEffects.particleCount = DebugConfig.circle12ParticleCount
            ChromaCircleEffects.particleSize = CGFloat(DebugConfig.circle12ParticleSize)
            ChromaCircleEffects.explosionForceRange = CGFloat(DebugConfig.circle12ParticleMinForce)...CGFloat(DebugConfig.circle12ParticleMaxForce)
            
            // 启用调试模式
            ChromaCircleViewModel.debugEnabled = DebugConfig.circle12ParticleDebugEnabled
            
            if DebugConfig.circle12ParticleDebugEnabled {
                print("🎆 Circle12粒子效果配置已应用:")
                print("   - 生命周期: \(DebugConfig.circle12ParticleLifetime)秒")
                print("   - 粒子数量: \(DebugConfig.circle12ParticleCount)")
                print("   - 粒子大小: \(DebugConfig.circle12ParticleSize)")
                print("   - 爆炸力范围: \(DebugConfig.circle12ParticleMinForce)~\(DebugConfig.circle12ParticleMaxForce)")
            }
        }
    }
}

// MARK: - Runtime Configuration

/// 运行时调试配置管理器
/// 允许在应用运行时动态调整调试设置
class RuntimeDebugConfig: ObservableObject {
    
    @Published var isPowerTestEnabled = DebugConfig.isPowerDebugEnabled
    @Published var isPerformanceTestEnabled = DebugConfig.isPerformanceDebugEnabled
    @Published var isVerboseLoggingEnabled = DebugConfig.isVerboseEnabled
    
    static let shared = RuntimeDebugConfig()
    
    private init() {}
    
    /// 切换功耗测试调试
    func togglePowerTestDebug() {
        isPowerTestEnabled.toggle()
        DebugLogger.info("功耗测试调试: \(isPowerTestEnabled ? "启用" : "禁用")", category: .power)
    }
    
    /// 切换性能测试调试
    func togglePerformanceTestDebug() {
        isPerformanceTestEnabled.toggle()
        DebugLogger.info("性能测试调试: \(isPerformanceTestEnabled ? "启用" : "禁用")", category: .performance)
    }
    
    /// 切换详细日志
    func toggleVerboseLogging() {
        isVerboseLoggingEnabled.toggle()
        DebugLogger.info("详细日志: \(isVerboseLoggingEnabled ? "启用" : "禁用")", category: .general)
    }
    
    /// 获取当前配置状态
    func getCurrentStatus() -> String {
        return """
        🔧 运行时调试状态:
        功耗测试: \(isPowerTestEnabled ? "✅" : "❌")
        性能测试: \(isPerformanceTestEnabled ? "✅" : "❌")
        详细日志: \(isVerboseLoggingEnabled ? "✅" : "❌")
        """
    }
} 
