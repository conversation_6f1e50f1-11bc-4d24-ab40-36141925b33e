//
//  PlatformDependant.swift
//  FullUI
//
//  Created by <PERSON> Li on 2025/5/26.
//

// 在独立文件中定义
import SwiftUI

extension View {
    func settingsNavigationLink<Destination: View>(_ title: String, destination: Destination) -> some View {
        #if os(macOS)
        return AnyView(<PERSON><PERSON>(title) {
            // macOS实现
        })
        #else
        return AnyView(NavigationLink(title, destination: destination))
        #endif
    }
}

extension Color {
    /// 跨平台系统背景色
    static var systemBackground: Color {
        #if os(iOS)
        return Color(UIColor.systemBackground)
        #elseif os(macOS)
        return Color(NSColor.windowBackgroundColor)
        #else
        return Color.white // 其他平台回退
        #endif
    }
    
    /// 跨系统次级背景色
    static var secondarySystemBackground: Color {
        #if os(iOS)
        return Color(UIColor.secondarySystemBackground)
        #elseif os(macOS)
        return Color(NSColor.controlBackgroundColor)
        #else
        return Color.gray.opacity(0.1)
        #endif
    }
}


