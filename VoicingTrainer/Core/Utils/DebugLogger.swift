import Foundation
import os.log

/// 调试日志管理器 - 统一管理应用的调试输出
/// 可以通过配置控制是否输出调试信息，优化Release版本性能
class DebugLogger {
    
    // MARK: - Configuration
    /// 是否启用调试输出 - Release版本应设为false
    #if DEBUG
    static let isDebugEnabled = true
    #else
    static let isDebugEnabled = false
    #endif
    
    /// 是否启用详细日志 - 可以进一步细化控制
    static let isVerboseEnabled = false
    
    /// 是否启用性能日志
    static let isPerformanceEnabled = true
    
    /// 是否启用功耗测试日志
    static let isPowerTestEnabled = true
    
    // MARK: - Log Categories
    enum Category: String {
        case general = "General"
        case midi = "MIDI"
        case audio = "Audio"
        case game = "Game"
        case ui = "UI"
        case power = "Power"
        case performance = "Performance"
        case network = "Network"
        case storage = "Storage"
        
        var emoji: String {
            switch self {
            case .general: return "📝"
            case .midi: return "🎹"
            case .audio: return "🎵"
            case .game: return "🎮"
            case .ui: return "🖼️"
            case .power: return "🔋"
            case .performance: return "⚡"
            case .network: return "🌐"
            case .storage: return "💾"
            }
        }
    }
    
    enum Level {
        case verbose    // 详细信息
        case info      // 一般信息
        case warning   // 警告
        case error     // 错误
        case critical  // 严重错误
        
        var prefix: String {
            switch self {
            case .verbose: return "🔍"
            case .info: return "ℹ️"
            case .warning: return "⚠️"
            case .error: return "❌"
            case .critical: return "🚨"
            }
        }
    }
    
    // MARK: - Logging Methods
    
    /// 通用日志方法
    static func log(_ message: String, category: Category = .general, level: Level = .info, file: String = #file, function: String = #function, line: Int = #line) {
        
        // 根据配置决定是否输出
        guard shouldLog(category: category, level: level) else { return }
        
        let fileName = (file as NSString).lastPathComponent
        let timestamp = DateFormatter.logFormatter.string(from: Date())
        
        let logMessage = "\(level.prefix) \(category.emoji) [\(fileName):\(function):\(line)] \(message)"
        
        // 在Debug模式下使用print，Release模式下使用os_log
        #if DEBUG
        print("\(timestamp) \(logMessage)")
        #else
        if isPowerTestEnabled && category == .power {
            print("\(timestamp) \(logMessage)")
        } else {
            os_log("%@", log: OSLog(subsystem: "com.shzx.voicingtrainer", category: category.rawValue), type: level.osLogType, logMessage)
        }
        #endif
    }
    
    /// 便捷方法 - 详细日志
    static func verbose(_ message: String, category: Category = .general, file: String = #file, function: String = #function, line: Int = #line) {
        log(message, category: category, level: .verbose, file: file, function: function, line: line)
    }
    
    /// 便捷方法 - 信息日志
    static func info(_ message: String, category: Category = .general, file: String = #file, function: String = #function, line: Int = #line) {
        log(message, category: category, level: .info, file: file, function: function, line: line)
    }
    
    /// 便捷方法 - 警告日志
    static func warning(_ message: String, category: Category = .general, file: String = #file, function: String = #function, line: Int = #line) {
        log(message, category: category, level: .warning, file: file, function: function, line: line)
    }
    
    /// 便捷方法 - 错误日志
    static func error(_ message: String, category: Category = .general, file: String = #file, function: String = #function, line: Int = #line) {
        log(message, category: category, level: .error, file: file, function: function, line: line)
    }
    
    /// 便捷方法 - 严重错误日志
    static func critical(_ message: String, category: Category = .general, file: String = #file, function: String = #function, line: Int = #line) {
        log(message, category: category, level: .critical, file: file, function: function, line: line)
    }
    
    // MARK: - Performance Logging
    
    /// 性能测量
    static func measureTime<T>(_ operation: String, category: Category = .performance, block: () throws -> T) rethrows -> T {
        let startTime = CFAbsoluteTimeGetCurrent()
        let result = try block()
        let timeElapsed = CFAbsoluteTimeGetCurrent() - startTime
        
        if isPerformanceEnabled {
            log("⏱️ \(operation) 耗时: \(String(format: "%.3f", timeElapsed * 1000))ms", category: category, level: .info)
        }
        
        return result
    }
    
    // MARK: - Private Methods
    
    private static func shouldLog(category: Category, level: Level) -> Bool {
        // 严重错误总是记录
        if level == .critical {
            return true
        }
        
        // 检查类别是否启用
        guard DebugConfig.isDebugEnabled(for: category) else {
            // 即使类别禁用，也要记录警告和错误
            return level == .warning || level == .error
        }
        
        // 检查日志级别是否启用
        return DebugConfig.isLogLevelEnabled(level)
    }
}

// MARK: - Extensions

extension DebugLogger.Level {
    var osLogType: OSLogType {
        switch self {
        case .verbose: return .debug
        case .info: return .info
        case .warning: return .default
        case .error: return .error
        case .critical: return .fault
        }
    }
}

extension DateFormatter {
    static let logFormatter: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateFormat = "HH:mm:ss.SSS"
        return formatter
    }()
}

// MARK: - Convenience Global Functions

/// 全局便捷日志函数
func DLog(_ message: String, category: DebugLogger.Category = .general, level: DebugLogger.Level = .info, file: String = #file, function: String = #function, line: Int = #line) {
    DebugLogger.log(message, category: category, level: level, file: file, function: function, line: line)
}

/// MIDI相关日志
func MIDILog(_ message: String, level: DebugLogger.Level = .info, file: String = #file, function: String = #function, line: Int = #line) {
    DebugLogger.log(message, category: .midi, level: level, file: file, function: function, line: line)
}

/// 游戏相关日志
func GameLog(_ message: String, level: DebugLogger.Level = .info, file: String = #file, function: String = #function, line: Int = #line) {
    DebugLogger.log(message, category: .game, level: level, file: file, function: function, line: line)
}

/// 功耗相关日志
func PowerLog(_ message: String, level: DebugLogger.Level = .info, file: String = #file, function: String = #function, line: Int = #line) {
    DebugLogger.log(message, category: .power, level: level, file: file, function: function, line: line)
}

/// 性能相关日志
func PerfLog(_ message: String, level: DebugLogger.Level = .info, file: String = #file, function: String = #function, line: Int = #line) {
    DebugLogger.log(message, category: .performance, level: level, file: file, function: function, line: line)
} 