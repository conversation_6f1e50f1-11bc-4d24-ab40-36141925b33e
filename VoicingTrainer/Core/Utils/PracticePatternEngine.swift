import Foundation
import SwiftUI
import Combine

// MARK: - Practice Pattern Data Structures

/// 练习模式配置
struct PracticePattern: Codable, Identifiable {
    let id: String
    let name: String
    let description: String
    let offset: Int
    let useCircleOfFifths: Bool
    
    init(id: String, name: String, description: String, offset: Int, useCircleOfFifths: Bool = false) {
        self.id = id
        self.name = name
        self.description = description
        self.offset = offset
        self.useCircleOfFifths = useCircleOfFifths
    }
}

// MARK: - Practice Pattern Manager

/// 练习模式管理器
class PracticePatternManager: ObservableObject {
    @Published var availablePatterns: [PracticePattern] = []
    @Published var selectedPattern: PracticePattern?
    
    
    init() {
        loadPracticePatterns()
    }
    
    private func loadPracticePatterns() {
        guard let url = Bundle.main.url(forResource: "chord_voicing", withExtension: "json"),
              let data = try? Data(contentsOf: url),
              let json = try? JSONSerialization.jsonObject(with: data) as? [String: Any],
              let practicePatterns = json["practice_patterns"] as? [String: [String: Any]] else {
            print("Failed to load practice patterns from voicing data")
            // 使用默认模式
            setDefaultPattern()
            return
        }
        
        availablePatterns = practicePatterns.compactMap { (key, value) in
            guard let name = value["name"] as? String,
                  let description = value["description"] as? String,
                  let offset = value["offset"] as? Int else {
                return nil
            }
            let useCircleOfFifths = value["useCircleOfFifths"] as? Bool ?? false
            return PracticePattern(id: key, name: name, description: description, offset: offset, useCircleOfFifths: useCircleOfFifths)
        }.sorted { $0.name < $1.name }
        
        // 默认选择五度圈反向模式
        selectedPattern = availablePatterns.first { $0.id == "circle_of_fifths_reverse" } ?? availablePatterns.first
        
        print("Loaded \(availablePatterns.count) practice patterns")
        if let selected = selectedPattern {
            print("Selected pattern: \(selected.name) (offset: \(selected.offset))")
        }
    }
    
    private func setDefaultPattern() {
        let defaultPattern = PracticePattern(
            id: "circle_of_fifths_reverse",
            name: "Circle of Fifths Reverse",
            description: "Practice in reverse circle of fifths order",
            offset: 5,
            useCircleOfFifths: true
        )
        availablePatterns = [defaultPattern]
        selectedPattern = defaultPattern
    }
}

// MARK: - Practice Engine State

/// 练习引擎状态
struct PracticeEngineState {
    var currentCycle: Int = 0           // 当前循环数（从0开始）
    var currentRoundInCycle: Int = 0    // 当前循环内的轮数（从0开始）
    var cyclesNeeded: Int = 1           // 需要的总循环数
    var roundsPerCycle: Int = 12        // 每个循环的轮数
    var totalRounds: Int = 12           // 总轮数
    var currentTransposition: Int = 0   // 当前移调偏移量（半音数）
    /// 重置状态
    mutating func reset() {
        currentCycle = 0
        currentRoundInCycle = 0
        currentTransposition = 0
    }
    
    /// 是否已完成所有循环
    var isCompleted: Bool {
        return currentCycle >= cyclesNeeded
    }
    
    /// 获取进度描述
    func getProgressDescription() -> String {
        return "循环 \(currentCycle + 1)/\(cyclesNeeded) - 轮次 \(currentRoundInCycle + 1)/\(roundsPerCycle)"
    }
}

// MARK: - Practice Pattern Engine

/// 练习模式引擎 - 统一管理练习模式的数学计算和状态管理
class PracticePatternEngine {
    
    // MARK: - Mathematical Calculations
    
    /// 计算最大公约数
    static func greatestCommonDivisor(_ a: Int, _ b: Int) -> Int {
        let absA = abs(a)
        let absB = abs(b)
        if absB == 0 { return absA }
        return greatestCommonDivisor(absB, absA % absB)
    }
    
    /// 根据音程偏移量计算游戏参数，确保覆盖所有12个调
    static func calculateGameParameters(for offset: Int) -> (totalRounds: Int, cyclesNeeded: Int, roundsPerCycle: Int) {
        let absOffset = abs(offset)
        if absOffset == 0 {
            // 特殊情况：偏移量为0，只练习一个调
            return (1, 1, 1)
        }
        
        let gcd = greatestCommonDivisor(12, absOffset)
        let cyclesNeeded = gcd
        let roundsPerCycle = 12 / gcd
        let totalRounds = cyclesNeeded * roundsPerCycle
        
        print("🎵 Practice pattern analysis:")
        print("   Offset: \(offset), GCD(12,\(absOffset)) = \(gcd)")
        print("   Cycles needed: \(cyclesNeeded), Rounds per cycle: \(roundsPerCycle)")
        print("   Total rounds: \(totalRounds)")
        
        return (totalRounds, cyclesNeeded, roundsPerCycle)
    }
    
    // MARK: - State Management
    
    /// 初始化练习引擎状态
    static func initializeState(for pattern: PracticePattern?) -> PracticeEngineState {
        guard let pattern = pattern else {
            // 非练习模式，使用默认参数
            return PracticeEngineState(
                currentCycle: 0,
                currentRoundInCycle: 0,
                cyclesNeeded: 1,
                roundsPerCycle: 1,
                totalRounds: 1,
                currentTransposition: 0
            )
        }
        
        let params = calculateGameParameters(for: pattern.offset)
        return PracticeEngineState(
            currentCycle: 0,
            currentRoundInCycle: 0,
            cyclesNeeded: params.cyclesNeeded,
            roundsPerCycle: params.roundsPerCycle,
            totalRounds: params.totalRounds,
            currentTransposition: 0
        )
    }
    
    // MARK: - Root Note Calculation (for Chord Games)
    
    /// 调整和弦音符到中心音域范围，返回调整后的音符数组和偏移量
    /// - Parameter notes: 原始音符数组
    /// - Returns: 包含调整后音符数组和偏移量的元组
    ///
    static func adjustChordNotesToCenterRange(_ notes: [Int]) -> (adjustedNotes: [Int], adjustmentInterval: Int) {
        
        
        
    // 计算当前和弦中心
    let min = notes.min() ?? 60
    let max = notes.max() ?? 60
    let center = (min + max) / 2
    
        print("调整当前和弦中心 adjustChordNotesToCenterRange \(center)")
        
    // 目标范围：C4(60) - B4(71)
    let targetMin = 53  //  F3
    let targetMax = 66  //  
    
    // 如果中心不在目标范围内，计算需要移动的八度数
    if center < targetMin || center > targetMax {
        
        print("和弦中心不在C4B4,移动中心\(center)")
        
        let octaveSize = 12
        var adjustment = 0
        
        if center < targetMin {
            adjustment = ((targetMin - center + octaveSize - 1) / octaveSize) * octaveSize
        } else {
            adjustment = -((center - targetMax + octaveSize - 1) / octaveSize) * octaveSize
        }
        
        // 应用调整
        let adjustNotes = notes.map { $0 + adjustment }
        print("和弦中心不在C4B4,调整和弦音符位置 \(adjustNotes)")
        
        return (adjustNotes,adjustment)
    }
    
    return (notes,0)
}
   
    
    /// 计算下一个根音（用于和弦游戏）
    static func calculateNextRootNote(
        currentRootNote: Int,
        initialRootNote: Int,
        practiceOffset: Int,
        intervals: [Int],
        state: inout PracticeEngineState
    ) -> (nextRootNote: Int, isCompleted: Bool) {
        
        // 计算和弦宽度（八度数）
        let chordSpan = intervals.max() ?? 0  // 和弦跨度（半音数）
        let lowestInterval = intervals.min() ?? 0
        let octaveSpan = Double(chordSpan - lowestInterval) / 12.0  // 和弦跨越的八度数
        
        // 根据和弦宽度确定根音范围
        let (minRootNote, maxRootNote) = determineRootNoteRange(octaveSpan: octaveSpan)
        
        print("🎵 calculateNextRootNote - 和弦分析:")
        print("   音程数组: \(intervals)")
        print("   和弦跨度: \(chordSpan) 半音")
        print("   八度跨越: \(String(format: "%.1f", octaveSpan)) 八度")
        print("   根音范围: \(getNoteNameFromMIDI(minRootNote)) - \(getNoteNameFromMIDI(maxRootNote)) (MIDI: \(minRootNote)-\(maxRootNote))")
        
        state.currentRoundInCycle += 1
        
        // 检查是否完成当前循环
        if state.currentRoundInCycle >= state.roundsPerCycle {
            // 完成一个循环，检查是否需要开始下一个循环
            state.currentCycle += 1
            state.currentRoundInCycle = 0
            
            if state.currentCycle >= state.cyclesNeeded {
                // 所有循环完成
                return (currentRootNote, true)
            } else {
                // 开始新循环，起始音为初始根音 + 当前循环数
                var nextRootNote = initialRootNote + state.currentCycle
                // 根据和弦宽度确保根音在合适范围内
                nextRootNote = adjustRootNoteToRange(nextRootNote, minRange: minRootNote, maxRange: maxRootNote)
                
                let cycleName = getNoteNameFromMIDI(nextRootNote)
                print("🎵 Starting cycle \(state.currentCycle + 1)/\(state.cyclesNeeded) from \(cycleName) (八度跨越: \(String(format: "%.1f", octaveSpan)))")
                
                return (nextRootNote, false)
            }
        } else {
            // 同一循环内的下一轮，使用音程偏移
            var nextRootNote = currentRootNote + practiceOffset
            
            // 根据和弦宽度确保根音在合适范围内
            nextRootNote = adjustRootNoteToRange(nextRootNote, minRange: minRootNote, maxRange: maxRootNote)
            
            print("🎵 同一循环内的下一轮: \(getNoteNameFromMIDI(currentRootNote)) -> \(getNoteNameFromMIDI(nextRootNote)) (偏移: \(practiceOffset), 八度跨越: \(String(format: "%.1f", octaveSpan)))")
            
            return (nextRootNote, false)
        }
    }
    
    /// 根据和弦宽度确定根音范围
    /// - Parameter octaveSpan: 和弦跨越的八度数
    /// - Returns: (最小根音MIDI值, 最大根音MIDI值)
    private static func determineRootNoteRange(octaveSpan: Double) -> (Int, Int) {
        if octaveSpan < 1.0 {
            // 和弦小于1个八度：根音可以在[C3,C5]
            return (48, 72)  // C3 = 48, C5 = 72
        } else if octaveSpan < 2.5 {
            // 和弦宽度小于2.5个八度：根音调整在[C3,C4]
            return (48, 60)  // C3 = 48, C4 = 60
        } else {
            // 和弦大于2.5个八度：根音在[C2,C3]
            return (36, 48)  // C2 = 36, C3 = 48
        }
    }
    
    /// 调整根音到指定范围内
    /// - Parameters:
    ///   - rootNote: 当前根音MIDI值
    ///   - minRange: 允许的最小根音MIDI值
    ///   - maxRange: 允许的最大根音MIDI值
    /// - Returns: 调整后的根音MIDI值
    private static func adjustRootNoteToRange(_ rootNote: Int, minRange: Int, maxRange: Int) -> Int {
        var adjustedNote = rootNote
        
        // 如果超出上限，下降八度
        while adjustedNote > maxRange {
            adjustedNote -= 12
        }
        
        // 如果超出下限，上升八度
        while adjustedNote < minRange {
            adjustedNote += 12
        }
        
        return adjustedNote
    }
    
    // MARK: - Transposition Calculation (for Progression Games)
    
    /// 计算下一个移调值（用于和弦进行游戏）
    static func calculateNextTransposition(
        practiceOffset: Int,
        state: inout PracticeEngineState
    ) -> (nextTransposition: Int, isCompleted: Bool) {
        
        state.currentRoundInCycle += 1
        
        // 检查是否完成当前循环
        if state.currentRoundInCycle >= state.roundsPerCycle {
            // 完成一个循环，检查是否需要开始下一个循环
            state.currentCycle += 1
            state.currentRoundInCycle = 0
            
            if state.currentCycle >= state.cyclesNeeded {
                // 所有循环完成
                print("🎉 练习模式完成！覆盖了所有12个调")
                return (state.currentTransposition, true)
            } else {
                // 开始新循环，移调为初始调 + 当前循环数
                state.currentTransposition = state.currentCycle
                print("🎵 开始循环 \(state.currentCycle + 1)/\(state.cyclesNeeded)，移调 \(state.currentTransposition) 半音")
                
                return (state.currentTransposition, false)
            }
        } else {
            // 同一循环内的下一轮，使用音程偏移
            state.currentTransposition += practiceOffset
            // 确保移调在合理范围内（-12到+12）
            while state.currentTransposition > 12 { state.currentTransposition -= 12 }
            while state.currentTransposition < -12 { state.currentTransposition += 12 }
            
            return (state.currentTransposition, false)
        }
    }
    
    // MARK: - Utility Functions
    
    /// 从MIDI音符获取音符名称
    static func getNoteNameFromMIDI(_ midiNote: Int) -> String {
        let noteNames = ["C", "C#", "D", "D#", "E", "F", "F#", "G", "G#", "A", "A#", "B"]
        let pitchClass = midiNote % 12
        let octave = (midiNote / 12) - 1
        return "\(noteNames[pitchClass])\(octave)"
    }
    
    /// 获取移调的音符名称描述
    static func getTranspositionName(_ semitones: Int) -> String {
        let noteNames = ["C", "C#", "D", "D#", "E", "F", "F#", "G", "G#", "A", "A#", "B"]
        let adjustedSemitones = ((semitones % 12) + 12) % 12
        return noteNames[adjustedSemitones]
    }
    
    /// 将MIDI音符列表移调指定的半音数
    static func transposeMIDINotes(_ notes: [Int], by semitones: Int) -> [Int] {
        return notes.map { $0 + semitones }
    }
    
    // MARK: - Smart Starting Note Calculation
    
    /// 智能计算最佳起始根音，根据和弦特性和练习模式动态调整
    /// - Parameters:
    ///   - intervals: 和弦音程数组
    ///   - defaultRootNote: JSON中定义的默认根音
    ///   - pattern: 练习模式
    /// - Returns: 优化后的起始根音MIDI值
    static func calculateOptimalStartingNote(
        intervals: [Int],
        defaultRootNote: Int,
        pattern: PracticePattern?
    ) -> Int {
        // 分析和弦特性
        let chordSpan = intervals.max() ?? 0  // 和弦跨度（半音数）
        let lowestInterval = intervals.min() ?? 0
        let octaveSpan = Double(chordSpan - lowestInterval) / 12.0  // 和弦跨越的八度数
        var finalRootNote = defaultRootNote

        print("🎵 智能根音计算:")
        print("   和弦音程: \(intervals)")
        print("   和弦跨度: \(chordSpan) 半音")
        print("   八度跨越: \(String(format: "%.1f", octaveSpan)) 八度")
        print("   默认根音: \(defaultRootNote) (\(getNoteNameFromMIDI(defaultRootNote)))")
        
        guard let pattern = pattern else {
            // 非练习模式，使用默认根音
            print("   结果: 使用默认根音 \(defaultRootNote)")
            return defaultRootNote
        }
        
        // 根据练习模式特性调整起始八度
     //   var octaveAdjustment = 0
        
        switch pattern.offset {
        case 1:  // 半音上行 (StepWise Ascending)
            // 需要从较低八度开始，避免音域过高
            if octaveSpan <= 1.5 {  //和弦范围小,半音上行从C3开始
                finalRootNote = 48  //C3
             //   octaveAdjustment = -12  // 下降一个八度 (C3)
            } else if octaveSpan <= 2.5 {
             //   octaveAdjustment = -12  // 下降八度
                finalRootNote =  48
            } else {
              //  octaveAdjustment = -12  // 大跨度和弦从C2开始
                finalRootNote = 36  //  C2
            }
            
        case -1: // 半音下行 (StepWise Descending)
            // 需要从较高八度开始，避免音域过低
            if octaveSpan <= 1.5 {
               // octaveAdjustment = 0    // 保持C4
                finalRootNote = 60
            } else if octaveSpan <= 2.5 {
                //octaveAdjustment = 12   // 上升一个八度
                finalRootNote = 60
            } else {
                //octaveAdjustment = 12   // 大跨度和弦半音下行,从C3开始
                finalRootNote = 48
            }
            
        case 5, -5:  // 五度圈 (Circle of Fifths)
            // 五度圈相对稳定，根据跨度微调
            if octaveSpan <= 1.0 {
             //   octaveAdjustment = 0    // 小跨度保持C4
                finalRootNote = 48
            } else if octaveSpan <= 2.0 {
           //     octaveAdjustment = -12  // 中跨度用C3
                finalRootNote = 48
            } else {
          //      octaveAdjustment = -12  // 大跨度用C2
                finalRootNote = 36
            }
            
        case 2, -2:  // 全音上行/下行 (Major 2nd)
            if pattern.offset > 0 {
                // 全音上行：从较低八度开始，避免音域过高
                if octaveSpan <= 1.0 {
                    finalRootNote = 48  // C3，小跨度和弦
                } else if octaveSpan <= 2.0 {
                    finalRootNote = 48  // C3，中跨度和弦
                } else {
                    finalRootNote = 36  // C2，大跨度和弦
                }
            } else {
                // 全音下行：从较高八度开始，避免音域过低
                if octaveSpan <= 1.0 {
                    finalRootNote = 60  // C4，小跨度和弦
                } else if octaveSpan <= 2.0 {
                    finalRootNote = 60  // C4，中跨度和弦
                } else {
                    finalRootNote = 48  // C3，大跨度和弦
                }
            }
            
        case 3, -3, 4, -4:  // 小三度、大三度
            // 三度进行相对温和
            if octaveSpan > 2.0 {
           //     octaveAdjustment = pattern.offset > 0 ? -12 : 12
            } else {
          //      octaveAdjustment = 0
            }
            
        default:
            // 其他模式使用保守策略
            print("practice engine default")
         //   octaveAdjustment = octaveSpan > 2.0 ? -12 : 0
        }
        
        //let adjustedRootNote = defaultRootNote + octaveAdjustment
        
        // 安全边界检查：确保根音在合理范围内
        //finalRootNote = adjustedRootNote
        //let minNote = 36  // C1
        //let maxNote = 60  // C5
        
        // 确保最低音符不会过低
        /*
        let lowestNote = finalRootNote + lowestInterval
        if lowestNote < minNote {
            finalRootNote = minNote - lowestInterval
        }
        */
        // 确保最高音符不会过高
        /*
        let highestNote = finalRootNote + chordSpan
        if highestNote > maxNote + 12 {  // 允许到C6
            finalRootNote = (maxNote + 12) - chordSpan
        }
        */
        // 确保finalRootNote本身在合理范围
      //  finalRootNote = max(minNote, min(maxNote, finalRootNote))
        
        let finalNoteName = getNoteNameFromMIDI(finalRootNote)
        let patternName = pattern.name
        
        print("   练习模式: \(patternName) (偏移: \(pattern.offset))")
        print("   最终根音: \(finalNoteName) (MIDI: \(finalRootNote))")
        print("   音域范围: \(getNoteNameFromMIDI(finalRootNote + lowestInterval)) - \(getNoteNameFromMIDI(finalRootNote + chordSpan))")
        
        return finalRootNote
    }
} 
