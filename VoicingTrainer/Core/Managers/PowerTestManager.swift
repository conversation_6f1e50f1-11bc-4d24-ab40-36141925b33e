import Foundation
#if os(iOS)
import UIKit
#endif
import Combine

/// 功耗测试管理器 - 专门用于iOS应用内的功耗测试
class PowerTestManager: ObservableObject {
    
    // MARK: - Singleton
    static let shared = PowerTestManager()
    
    // MARK: - Published Properties
    @Published var isRunning: Bool = false
    @Published var testResults: [PowerTestResult] = []
    @Published var currentResult: PowerTestResult?
    @Published var testProgress: Double = 0.0
    @Published var testStatus: String = "准备就绪"
    
    // MARK: - Test Configuration
    private let testDuration: TimeInterval = 60 // 1分钟测试
    private let samplingInterval: TimeInterval = 5 // 每5秒采样一次
    
    // MARK: - Private Properties
    private var testTimer: Timer?
    private var startTime: Date?
    private var sampleCount: Int = 0
    private var totalSamples: Int = 0
    
    private init() {}
    
    // MARK: - Test Result Structure
    struct PowerTestResult: Identifiable, Codable {
        var id = UUID()
        let timestamp: Date
        let batteryLevel: Float
        let cpuUsage: Double
        let memoryUsage: Double
        let thermalStateRaw: Int // 存储原始值而不是枚举
        let isBackgroundMode: Bool
        let powerEfficiencyScore: Double
        
        // 计算属性来获取ThermalState
        var thermalState: ProcessInfo.ThermalState {
            return ProcessInfo.ThermalState(rawValue: thermalStateRaw) ?? .nominal
        }
        
        var description: String {
            let formatter = DateFormatter()
            formatter.timeStyle = .medium
            
            return """
            时间: \(formatter.string(from: timestamp))
            电池: \(String(format: "%.1f%%", batteryLevel * 100))
            CPU: \(String(format: "%.1f%%", cpuUsage))
            内存: \(String(format: "%.1f%%", memoryUsage))
            热状态: \(thermalStateString)
            后台: \(isBackgroundMode ? "是" : "否")
            效率: \(String(format: "%.1f%%", powerEfficiencyScore))
            """
        }
        
        private var thermalStateString: String {
            switch thermalState {
            case .nominal: return "正常"
            case .fair: return "良好"
            case .serious: return "严重"
            case .critical: return "危险"
            @unknown default: return "未知"
            }
        }
    }
    
    // MARK: - Public Methods
    
    /// 开始功耗测试
    func startTest() {
        guard !isRunning else { return }
        
        print("🔋 开始功耗测试...")
        
        isRunning = true
        testResults.removeAll()
        sampleCount = 0
        totalSamples = Int(testDuration / samplingInterval)
        startTime = Date()
        testProgress = 0.0
        testStatus = "测试进行中..."
        
        // 立即采集第一个样本
        collectSample()
        
        // 启动定时器
        testTimer = Timer.scheduledTimer(withTimeInterval: samplingInterval, repeats: true) { [weak self] _ in
            self?.collectSample()
        }
        
        // 设置测试结束定时器
        DispatchQueue.main.asyncAfter(deadline: .now() + testDuration) { [weak self] in
            self?.stopTest()
        }
    }
    
    /// 停止功耗测试
    func stopTest() {
        guard isRunning else { return }
        
        PowerLog("功耗测试完成!", level: .info)
        
        isRunning = false
        testTimer?.invalidate()
        testTimer = nil
        testProgress = 1.0
        testStatus = "测试完成"
        
        generateTestReport()
        saveTestResults()
    }
    
    /// 清除测试结果
    func clearResults() {
        testResults.removeAll()
        currentResult = nil
        testProgress = 0.0
        testStatus = "准备就绪"
    }
    
    // MARK: - Private Methods
    
    /// 采集功耗数据样本
    private func collectSample() {
        let powerManager = PowerManager.shared
        
        let result = PowerTestResult(
            timestamp: Date(),
            batteryLevel: powerManager.batteryLevel,
            cpuUsage: powerManager.cpuUsage,
            memoryUsage: powerManager.memoryUsage,
            thermalStateRaw: powerManager.thermalState.rawValue,
            isBackgroundMode: powerManager.isBackgroundMode,
            powerEfficiencyScore: powerManager.powerEfficiencyScore
        )
        
        sampleCount += 1
        testResults.append(result)
        currentResult = result
        
        // 更新进度
        testProgress = Double(sampleCount) / Double(totalSamples)
        
        PowerLog("样本 \(sampleCount)/\(totalSamples):", level: .info)
        PowerLog(result.description, level: .info)
        PowerLog("------------------------------", level: .info)
    }
    
    /// 生成测试报告
    private func generateTestReport() {
        guard !testResults.isEmpty else {
            PowerLog("没有测试数据", level: .error)
            return
        }
        
        let avgBattery = testResults.map { $0.batteryLevel }.reduce(0, +) / Float(testResults.count)
        let avgCPU = testResults.map { $0.cpuUsage }.reduce(0, +) / Double(testResults.count)
        let avgMemory = testResults.map { $0.memoryUsage }.reduce(0, +) / Double(testResults.count)
        let avgEfficiency = testResults.map { $0.powerEfficiencyScore }.reduce(0, +) / Double(testResults.count)
        
        let maxCPU = testResults.map { $0.cpuUsage }.max() ?? 0
        let maxMemory = testResults.map { $0.memoryUsage }.max() ?? 0
        let minEfficiency = testResults.map { $0.powerEfficiencyScore }.min() ?? 100
        
        let backgroundSamples = testResults.filter { $0.isBackgroundMode }.count
        let foregroundSamples = testResults.count - backgroundSamples
        
        PowerLog("测试报告:", level: .info)
        PowerLog("总样本数: \(testResults.count)", level: .info)
        PowerLog("前台样本: \(foregroundSamples)", level: .info)
        PowerLog("后台样本: \(backgroundSamples)", level: .info)
        PowerLog("", level: .info)
        PowerLog("平均指标:", level: .info)
        PowerLog("电池消耗: \(String(format: "%.2f%%", avgBattery * 100))", level: .info)
        PowerLog("CPU使用: \(String(format: "%.1f%%", avgCPU))", level: .info)
        PowerLog("内存使用: \(String(format: "%.1f%%", avgMemory))", level: .info)
        PowerLog("功耗效率: \(String(format: "%.1f%%", avgEfficiency))", level: .info)
        PowerLog("", level: .info)
        PowerLog("峰值指标:", level: .info)
        PowerLog("最高CPU: \(String(format: "%.1f%%", maxCPU))", level: .info)
        PowerLog("最高内存: \(String(format: "%.1f%%", maxMemory))", level: .info)
        PowerLog("最低效率: \(String(format: "%.1f%%", minEfficiency))", level: .info)
        
        // 计算功耗等级
        let powerGrade = calculatePowerGrade(avgEfficiency: avgEfficiency, maxCPU: maxCPU, maxMemory: maxMemory)
        PowerLog("", level: .info)
        PowerLog("功耗等级: \(powerGrade)", level: .info)
        
        testStatus = "测试完成 - \(powerGrade)"
    }
    
    /// 计算功耗等级
    private func calculatePowerGrade(avgEfficiency: Double, maxCPU: Double, maxMemory: Double) -> String {
        var score = 100.0
        
        // 效率评分影响
        score -= (100.0 - avgEfficiency) * 0.5
        
        // CPU峰值影响
        if maxCPU > 80 {
            score -= (maxCPU - 80) * 2
        }
        
        // 内存峰值影响
        if maxMemory > 85 {
            score -= (maxMemory - 85) * 1.5
        }
        
        switch score {
        case 90...100:
            return "A+ (优秀) ✅"
        case 80..<90:
            return "A (良好) ✅"
        case 70..<80:
            return "B (一般) ⚠️"
        case 60..<70:
            return "C (较差) ⚠️"
        default:
            return "D (不合格) ❌"
        }
    }
    
    /// 保存测试结果
    private func saveTestResults() {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd_HH-mm-ss"
        let filename = "PowerTest_\(formatter.string(from: Date())).json"
        
        do {
            let jsonData = try JSONEncoder().encode(testResults)
            let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask)[0]
            let fileURL = documentsPath.appendingPathComponent(filename)
            
            try jsonData.write(to: fileURL)
            PowerLog("测试结果已保存: \(filename)", level: .info)
        } catch {
            PowerLog("保存测试结果失败: \(error)", level: .error)
        }
    }
    
    /// 获取功耗建议
    func getPowerRecommendations() -> [String] {
        guard !testResults.isEmpty else { return [] }
        
        var recommendations: [String] = []
        
        let avgCPU = testResults.map { $0.cpuUsage }.reduce(0, +) / Double(testResults.count)
        let avgMemory = testResults.map { $0.memoryUsage }.reduce(0, +) / Double(testResults.count)
        let avgEfficiency = testResults.map { $0.powerEfficiencyScore }.reduce(0, +) / Double(testResults.count)
        
        if avgCPU > 60 {
            recommendations.append("CPU使用率较高，建议优化算法或减少后台任务")
        }
        
        if avgMemory > 70 {
            recommendations.append("内存使用率较高，建议优化内存管理或清理缓存")
        }
        
        if avgEfficiency < 75 {
            recommendations.append("功耗效率较低，建议启用更积极的省电策略")
        }
        
        let thermalIssues = testResults.filter { $0.thermalState == .serious || $0.thermalState == .critical }.count
        if thermalIssues > 0 {
            recommendations.append("检测到热压力问题，建议优化CPU密集型操作")
        }
        
        let backgroundSamples = testResults.filter { $0.isBackgroundMode }.count
        if backgroundSamples > 0 {
            let backgroundEfficiency = testResults.filter { $0.isBackgroundMode }.map { $0.powerEfficiencyScore }.reduce(0, +) / Double(backgroundSamples)
            if backgroundEfficiency < 80 {
                recommendations.append("后台运行效率较低，建议优化后台任务")
            }
        }
        
        return recommendations
    }
}

// MARK: - String Extension
private extension String {
    static func *(lhs: String, rhs: Int) -> String {
        return String(repeating: lhs, count: rhs)
    }
} 