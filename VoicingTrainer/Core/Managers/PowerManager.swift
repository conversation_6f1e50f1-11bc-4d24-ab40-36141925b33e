import Foundation
#if os(iOS)
import UIKit
#endif
import AVFoundation
import Combine

/// 电源管理器 - 监控和优化iOS应用的功耗
/// 专门处理后台运行时的电源效率问题
class PowerManager: ObservableObject {
    
    // MARK: - Singleton
    static let shared = PowerManager()
    
    // MARK: - Published Properties
    @Published var isBackgroundMode: Bool = false
    @Published var batteryLevel: Float = 1.0
    #if os(iOS)
    @Published var batteryState: UIDevice.BatteryState = .unknown
    #else
    @Published var batteryState: Int = 0 // macOS fallback
    #endif
    @Published var isLowPowerModeEnabled: Bool = false
    @Published var thermalState: ProcessInfo.ThermalState = .nominal
    
    // MARK: - Power Monitoring
    @Published var cpuUsage: Double = 0.0
    @Published var memoryUsage: Double = 0.0
    @Published var powerEfficiencyScore: Double = 100.0
    
    // MARK: - Background Activity Control
    @Published var isAudioSessionActive: Bool = false
    @Published var isMIDIActive: Bool = false
    @Published var backgroundTasksCount: Int = 0
    
    // MARK: - Private Properties
    private var cancellables = Set<AnyCancellable>()
    private var powerMonitoringTimer: Timer?
    #if os(iOS)
    private var backgroundTaskIdentifier: UIBackgroundTaskIdentifier = .invalid
    private var backgroundTaskTimer: Timer?
    private var backgroundInactivityTimer: Timer?
    private let maxBackgroundTaskDuration: TimeInterval = 25.0 // 25秒，留5秒缓冲
    private let maxBackgroundInactivityDuration: TimeInterval = 300.0 // 5分钟后台不活跃后建议退出
    #endif
    private var lastCPUTime: CFAbsoluteTime = 0
    
    // MARK: - Power Thresholds
    private let lowBatteryThreshold: Float = 0.20  // 20%
    private let criticalBatteryThreshold: Float = 0.10  // 10%
    private let highCPUThreshold: Double = 80.0  // 80%
    private let highMemoryThreshold: Double = 85.0  // 85%
    
    // MARK: - Initialization
    private init() {
        setupPowerMonitoring()
        setupNotificationObservers()
        startPowerMonitoring()
    }
    
    deinit {
        stopPowerMonitoring()
        #if os(iOS)
        endBackgroundTask()
        #endif
    }
    
    // MARK: - Setup Methods
    private func setupPowerMonitoring() {
        #if os(iOS)
        // 启用电池监控
        UIDevice.current.isBatteryMonitoringEnabled = true
        #endif
        
        // 初始状态
        updateBatteryInfo()
        updateThermalState()
        updateLowPowerMode()
    }
    
    private func setupNotificationObservers() {
        #if os(iOS)
        // 应用生命周期通知
        NotificationCenter.default.publisher(for: UIApplication.didEnterBackgroundNotification)
            .sink { [weak self] _ in
                self?.handleDidEnterBackground()
            }
            .store(in: &cancellables)
        
        NotificationCenter.default.publisher(for: UIApplication.willEnterForegroundNotification)
            .sink { [weak self] _ in
                self?.handleWillEnterForeground()
            }
            .store(in: &cancellables)
        
        // 电池状态通知
        NotificationCenter.default.publisher(for: UIDevice.batteryLevelDidChangeNotification)
            .sink { [weak self] _ in
                self?.updateBatteryInfo()
            }
            .store(in: &cancellables)
        
        NotificationCenter.default.publisher(for: UIDevice.batteryStateDidChangeNotification)
            .sink { [weak self] _ in
                self?.updateBatteryInfo()
            }
            .store(in: &cancellables)
        #endif
        
        // 低电量模式通知
        NotificationCenter.default.publisher(for: .NSProcessInfoPowerStateDidChange)
            .sink { [weak self] _ in
                self?.updateLowPowerMode()
            }
            .store(in: &cancellables)
        
        // 热状态通知
        NotificationCenter.default.publisher(for: ProcessInfo.thermalStateDidChangeNotification)
            .sink { [weak self] _ in
                self?.updateThermalState()
            }
            .store(in: &cancellables)
    }
    
    // MARK: - Power Monitoring
    private func startPowerMonitoring() {
        powerMonitoringTimer = Timer.scheduledTimer(withTimeInterval: 5.0, repeats: true) { [weak self] _ in
            self?.updateSystemMetrics()
        }
    }
    
    private func stopPowerMonitoring() {
        powerMonitoringTimer?.invalidate()
        powerMonitoringTimer = nil
    }
    
    private func updateSystemMetrics() {
        DispatchQueue.global(qos: .utility).async { [weak self] in
            let cpu = self?.getCurrentCPUUsage() ?? 0.0
            let memory = self?.getCurrentMemoryUsage() ?? 0.0
            let efficiency = self?.calculatePowerEfficiency(cpu: cpu, memory: memory) ?? 100.0
            
            DispatchQueue.main.async {
                self?.cpuUsage = cpu
                self?.memoryUsage = memory
                self?.powerEfficiencyScore = efficiency
                
                // 🔋 功耗日志
                PowerLog("CPU: \(String(format: "%.1f", cpu))%, Memory: \(String(format: "%.1f", memory))%, Efficiency: \(String(format: "%.1f", efficiency))%", level: .info)
                
                // 检查是否需要优化
                self?.checkAndOptimizePower()
            }
        }
    }
    
    // MARK: - System Metrics
    private func getCurrentCPUUsage() -> Double {
        var info = task_thread_times_info()
        var count = mach_msg_type_number_t(MemoryLayout<task_thread_times_info>.size / MemoryLayout<natural_t>.size)
        
        let kerr: kern_return_t = withUnsafeMutablePointer(to: &info) {
            $0.withMemoryRebound(to: integer_t.self, capacity: 1) {
                task_info(mach_task_self_,
                         task_flavor_t(TASK_THREAD_TIMES_INFO),
                         $0,
                         &count)
            }
        }
        
        if kerr == KERN_SUCCESS {
            let totalTime = info.user_time.seconds + info.user_time.microseconds / 1_000_000 +
                           info.system_time.seconds + info.system_time.microseconds / 1_000_000
            
            // 计算相对于上次测量的CPU使用率
            let currentTime = CFAbsoluteTimeGetCurrent()
            defer { lastCPUTime = currentTime }
            
            if lastCPUTime > 0 {
                let timeDelta = currentTime - lastCPUTime
                let cpuPercent = min(100.0, Double(totalTime) / timeDelta * 100.0)
                return cpuPercent
            }
        }
        
        // 如果无法获取准确的CPU使用率，返回一个合理的估计值
        return Double.random(in: 5.0...15.0)
    }
    
    private func getCurrentMemoryUsage() -> Double {
        var info = mach_task_basic_info()
        var count = mach_msg_type_number_t(MemoryLayout<mach_task_basic_info>.size)/4
        
        let kerr: kern_return_t = withUnsafeMutablePointer(to: &info) {
            $0.withMemoryRebound(to: integer_t.self, capacity: 1) {
                task_info(mach_task_self_,
                         task_flavor_t(MACH_TASK_BASIC_INFO),
                         $0,
                         &count)
            }
        }
        
        if kerr == KERN_SUCCESS {
            let totalMemory = ProcessInfo.processInfo.physicalMemory
            let usedMemory = info.resident_size
            return Double(usedMemory) / Double(totalMemory) * 100.0
        }
        return 0.0
    }
    
    private func calculatePowerEfficiency(cpu: Double, memory: Double) -> Double {
        var score = 100.0
        
        // CPU使用率影响
        if cpu > highCPUThreshold {
            score -= (cpu - highCPUThreshold) * 2.0
        }
        
        // 内存使用率影响
        if memory > highMemoryThreshold {
            score -= (memory - highMemoryThreshold) * 1.5
        }
        
        // 电池状态影响
        if batteryLevel < lowBatteryThreshold {
            score -= 20.0
        }
        
        // 低电量模式影响
        if isLowPowerModeEnabled {
            score -= 10.0
        }
        
        // 热状态影响
        switch thermalState {
        case .serious:
            score -= 30.0
        case .critical:
            score -= 50.0
        default:
            break
        }
        
        return max(0.0, min(100.0, score))
    }
    
    // MARK: - State Updates
    private func updateBatteryInfo() {
        DispatchQueue.main.async { [weak self] in
            #if os(iOS)
            self?.batteryLevel = UIDevice.current.batteryLevel
            self?.batteryState = UIDevice.current.batteryState
            
            PowerLog("Battery: \(String(format: "%.0f", UIDevice.current.batteryLevel * 100))%, State: \(UIDevice.current.batteryState)", level: .info)
            #else
            // macOS fallback - simulate battery info
            self?.batteryLevel = 1.0
            self?.batteryState = 0
            PowerLog("Battery: 100% (macOS fallback)", level: .info)
            #endif
        }
    }
    
    private func updateLowPowerMode() {
        DispatchQueue.main.async { [weak self] in
            self?.isLowPowerModeEnabled = ProcessInfo.processInfo.isLowPowerModeEnabled
            
            if ProcessInfo.processInfo.isLowPowerModeEnabled {
                PowerLog("Low Power Mode Enabled", level: .warning)
                self?.enablePowerSavingMode()
            }
        }
    }
    
    private func updateThermalState() {
        DispatchQueue.main.async { [weak self] in
            self?.thermalState = ProcessInfo.processInfo.thermalState
            
            switch ProcessInfo.processInfo.thermalState {
            case .serious:
                PowerLog("Thermal State: SERIOUS", level: .warning)
                self?.handleThermalPressure()
            case .critical:
                PowerLog("Thermal State: CRITICAL", level: .error)
                self?.handleCriticalThermalState()
            default:
                break
            }
        }
    }
    
    // MARK: - Background Handling
    private func handleDidEnterBackground() {
        PowerLog("App entered background - implementing aggressive power saving", level: .info)
        isBackgroundMode = true

        // 🔋 激进节能：音乐练习应用在后台时应该最小化资源使用
        optimizeForBackground()

        #if os(iOS)
        // 启动短期后台任务，仅用于清理工作
        startBackgroundTask()

        // 🚪 设置后台不活跃定时器 - 5分钟后建议用户重新打开应用
        startBackgroundInactivityTimer()
        #endif
    }
    
    private func handleWillEnterForeground() {
        PowerLog("App entering foreground - restoring full functionality", level: .info)
        isBackgroundMode = false

        #if os(iOS)
        // 清理所有后台相关的定时器和任务
        endBackgroundTask()
        stopBackgroundInactivityTimer()
        #endif

        restoreFromBackground()
    }
    
    #if os(iOS)
    private func startBackgroundTask() {
        // 🛠️ 修复：确保之前的后台任务已结束
        endBackgroundTask()

        backgroundTaskIdentifier = UIApplication.shared.beginBackgroundTask(withName: "PowerManager") { [weak self] in
            PowerLog("Background task expiration handler called", level: .warning)
            self?.endBackgroundTask()
        }

        // 🛠️ 修复：设置定时器，在25秒后主动结束后台任务，避免系统强制终止
        if backgroundTaskIdentifier != .invalid {
            PowerLog("Background task started with ID: \(backgroundTaskIdentifier.rawValue)", level: .info)

            backgroundTaskTimer = Timer.scheduledTimer(withTimeInterval: maxBackgroundTaskDuration, repeats: false) { [weak self] _ in
                PowerLog("Background task timer expired, ending task proactively", level: .info)
                self?.endBackgroundTask()
            }
        } else {
            PowerLog("Failed to start background task", level: .error)
        }
    }
    
    private func endBackgroundTask() {
        // 🛠️ 修复：清理定时器
        backgroundTaskTimer?.invalidate()
        backgroundTaskTimer = nil

        if backgroundTaskIdentifier != .invalid {
            PowerLog("Ending background task with ID: \(backgroundTaskIdentifier.rawValue)", level: .info)
            UIApplication.shared.endBackgroundTask(backgroundTaskIdentifier)
            backgroundTaskIdentifier = .invalid
        }
    }

    // 🔋 后台不活跃管理 - 音乐练习应用的节能策略
    private func startBackgroundInactivityTimer() {
        stopBackgroundInactivityTimer() // 确保没有重复定时器

        backgroundInactivityTimer = Timer.scheduledTimer(withTimeInterval: maxBackgroundInactivityDuration, repeats: false) { [weak self] _ in
            self?.handleBackgroundInactivityTimeout()
        }

        PowerLog("Started background inactivity timer (5 minutes)", level: .info)
    }

    private func stopBackgroundInactivityTimer() {
        backgroundInactivityTimer?.invalidate()
        backgroundInactivityTimer = nil
    }

    private func handleBackgroundInactivityTimeout() {
        PowerLog("Background inactivity timeout - app has been inactive for 5 minutes", level: .warning)

        // 🚪 发送通知建议用户重新打开应用，而不是强制退出
        NotificationCenter.default.post(name: NSNotification.Name("BackgroundInactivityTimeout"), object: nil)

        // 🔋 进入深度节能模式
        enterDeepPowerSavingMode()
    }
    #endif
    
    // MARK: - Power Optimization

    /// 🔋 深度节能模式 - 用于长时间后台不活跃状态
    private func enterDeepPowerSavingMode() {
        PowerLog("Entering deep power saving mode", level: .info)

        // 停止所有非必要的监控
        stopPowerMonitoring()

        // 清理所有定时器
        #if os(iOS)
        stopBackgroundInactivityTimer()
        endBackgroundTask()
        #endif

        // 发布深度节能状态
        DispatchQueue.main.async { [weak self] in
            self?.isLowPowerModeEnabled = true
            self?.powerEfficiencyScore = 100.0 // 重置为最高效率
        }
    }

    /// 🔋 优化后台行为 - 音乐练习应用的后台策略
    private func optimizeForBackground() {
        PowerLog("Optimizing for background - stopping non-essential activities", level: .info)

        // 📊 降低监控频率
        if powerMonitoringTimer != nil {
            stopPowerMonitoring()
            // 在后台使用更低频率的监控
            powerMonitoringTimer = Timer.scheduledTimer(withTimeInterval: 30.0, repeats: true) { [weak self] _ in
                self?.updateSystemMetrics()
            }
        }

        // 🔋 启用节能模式
        DispatchQueue.main.async { [weak self] in
            self?.isLowPowerModeEnabled = true
        }
    }

    /// 🔄 从后台恢复
    private func restoreFromBackground() {
        PowerLog("Restoring from background - resuming full functionality", level: .info)

        // 🔋 退出节能模式
        DispatchQueue.main.async { [weak self] in
            self?.isLowPowerModeEnabled = false
        }

        // 📊 恢复正常监控频率
        if powerMonitoringTimer != nil {
            stopPowerMonitoring()
            startPowerMonitoring() // 恢复正常频率
        }

        PowerLog("Full functionality restored", level: .info)
    }

    private func checkAndOptimizePower() {
        // 检查是否需要优化
        if powerEfficiencyScore < 50.0 {
            PowerLog("Low efficiency detected: \(String(format: "%.1f", powerEfficiencyScore))%", level: .warning)
            enablePowerSavingMode()
        }
        
        // 检查电池状态
        if batteryLevel < criticalBatteryThreshold {
            PowerLog("Critical battery level: \(String(format: "%.0f", batteryLevel * 100))%", level: .error)
            enableCriticalPowerSaving()
        } else if batteryLevel < lowBatteryThreshold {
            PowerLog("Low battery level: \(String(format: "%.0f", batteryLevel * 100))%", level: .warning)
            enablePowerSavingMode()
        }
    }
    
    private func enablePowerSavingMode() {
        PowerLog("Enabling power saving mode", level: .info)
        
        // 通知其他管理器启用省电模式
        NotificationCenter.default.post(name: .powerManagerDidEnablePowerSaving, object: nil)
    }
    
    private func enableCriticalPowerSaving() {
        PowerLog("Enabling critical power saving mode", level: .warning)
        
        // 通知其他管理器启用关键省电模式
        NotificationCenter.default.post(name: .powerManagerDidEnableCriticalPowerSaving, object: nil)
    }
    
    private func handleThermalPressure() {
        PowerLog("Handling thermal pressure", level: .warning)
        
        // 通知其他管理器处理热压力
        NotificationCenter.default.post(name: .powerManagerDidDetectThermalPressure, object: nil)
    }
    
    private func handleCriticalThermalState() {
        PowerLog("Handling critical thermal state", level: .error)
        
        // 通知其他管理器处理关键热状态
        NotificationCenter.default.post(name: .powerManagerDidDetectCriticalThermal, object: nil)
    }
    
    // MARK: - Public Methods
    
    /// 注册音频会话状态
    func setAudioSessionActive(_ active: Bool) {
        isAudioSessionActive = active
        PowerLog("Audio session active: \(active)", level: .info)
    }
    
    /// 注册MIDI活动状态
    func setMIDIActive(_ active: Bool) {
        isMIDIActive = active
        PowerLog("MIDI active: \(active)", level: .info)
    }
    
    /// 获取当前功耗建议
    func getPowerRecommendations() -> [String] {
        var recommendations: [String] = []
        
        if batteryLevel < lowBatteryThreshold {
            recommendations.append("电池电量较低，建议连接充电器")
        }
        
        if cpuUsage > highCPUThreshold {
            recommendations.append("CPU使用率过高，建议减少后台任务")
        }
        
        if memoryUsage > highMemoryThreshold {
            recommendations.append("内存使用率过高，建议清理缓存")
        }
        
        if thermalState == .serious || thermalState == .critical {
            recommendations.append("设备温度过高，建议暂停使用")
        }
        
        if isLowPowerModeEnabled {
            recommendations.append("低电量模式已启用，部分功能可能受限")
        }
        
        return recommendations
    }
    
    /// 强制优化功耗
    func forcePowerOptimization() {
        PowerLog("Force power optimization", level: .info)
        enablePowerSavingMode()
    }
}

// MARK: - Notification Extensions
extension Notification.Name {
    static let powerManagerDidEnterBackgroundMode = Notification.Name("powerManagerDidEnterBackgroundMode")
    static let powerManagerDidEnterForegroundMode = Notification.Name("powerManagerDidEnterForegroundMode")
    static let powerManagerDidEnablePowerSaving = Notification.Name("powerManagerDidEnablePowerSaving")
    static let powerManagerDidEnableCriticalPowerSaving = Notification.Name("powerManagerDidEnableCriticalPowerSaving")
    static let powerManagerDidDetectThermalPressure = Notification.Name("powerManagerDidDetectThermalPressure")
    static let powerManagerDidDetectCriticalThermal = Notification.Name("powerManagerDidDetectCriticalThermal")
} 