//
//  ChordMatchManager.swift
//  VoicingTrainer
//
//  Created by AI Assistant on 2025/12/19.
//

import Foundation
import Combine

/// 和弦匹配状态
enum ChordMatchState {
    case idle           // 无匹配状态
    case partial        // 部分匹配
    case complete       // 完全匹配
}

/// 和弦匹配事件
enum ChordMatchEvent {
    case matchStateChanged(ChordMatchState)
    case perfectMatch           // 完美匹配（第一次达成）
    case repeatMatch            // 重复匹配（防止重复反馈）
    case partialMatch          // 部分匹配
    case noMatch               // 无匹配
}

/// 和弦匹配管理器 - 统一处理所有和弦匹配逻辑
class ChordMatchManager: ObservableObject {
    
    // MARK: - Published Properties
    
    @Published var currentMatchState: ChordMatchState = .idle
    @Published var expectedNotes: Set<Int> = []
    @Published var pressedNotes: Set<Int> = []
    @Published var matchedNotes: Set<Int> = []
    @Published var extraNotes: Set<Int> = []
    @Published var missingNotes: Set<Int> = []
    
    // MARK: - Private Properties
    
    private var expectedMIDINotes: Set<Int> = []  // 完整MIDI音符（精确匹配）
    private var pressedMIDINotes: Set<Int> = []   // 完整MIDI音符（精确匹配）
    
    private var hasMatchedOnce: Bool = false      // 防止重复反馈
    private var lastMatchedMIDINotes: Set<Int> = [] // 上次匹配的MIDI音符
    
    // MARK: - Callbacks
    
    /// 匹配事件回调
    var onMatchEvent: ((ChordMatchEvent) -> Void)?
    
    /// 完美匹配回调（仅第一次触发）
    var onPerfectMatch: (() -> Void)?
    
    /// 粒子爆炸回调
    var onParticleExplosion: (() -> Void)?
    
    /// 音效回调
    var onSoundEffect: ((ChordMatchEvent) -> Void)?
    
    /// 视觉反馈回调
    var onVisualFeedback: ((ChordMatchEvent) -> Void)?
    
    // MARK: - Debug Settings
    
    private let debugEnabled: Bool = true
    
    // MARK: - Initialization
    
    init() {
        if debugEnabled {
            print("🎯 ChordMatchManager 初始化完成")
        }
    }
    
    // MARK: - Public Methods
    
    /// 设置期望的和弦音符
    /// - Parameter midiNotes: 完整的MIDI音符数组
    func setExpectedChord(_ midiNotes: [Int]) {
        expectedMIDINotes = Set(midiNotes)
        expectedNotes = Set(midiNotes.map { $0 % 12 })
        
        // 重置状态
        resetMatchState()
        
        if debugEnabled {
            print("🎯 设置期望和弦: \(midiNotes.sorted()) (半音: \(Array(expectedNotes).sorted()))")
        }
    }
    
    /// 更新当前按下的音符
    /// - Parameter midiNotes: 完整的MIDI音符数组
    func updatePressedNotes(_ midiNotes: [Int]) {
        let newPressedMIDINotes = Set(midiNotes)
        let newPressedNotes = Set(midiNotes.map { $0 % 12 })
        
        // 检查是否有变化
        let notesChanged = newPressedMIDINotes != pressedMIDINotes
        
        // 更新状态
        pressedMIDINotes = newPressedMIDINotes
        pressedNotes = newPressedNotes
        
        if debugEnabled {
            print("🎯 更新按下音符: \(midiNotes.sorted()) (半音: \(Array(pressedNotes).sorted()))")
        }
        
        // 计算匹配状态
        calculateMatchState()
        
        // 处理匹配事件
        handleMatchEvent(notesChanged: notesChanged)
    }
    
    /// 清除所有状态
    func clearAll() {
        expectedMIDINotes.removeAll()
        expectedNotes.removeAll()
        pressedMIDINotes.removeAll()
        pressedNotes.removeAll()
        
        resetMatchState()
        
        if debugEnabled {
            print("🎯 清除所有状态")
        }
    }
    
    /// 重置匹配状态（保留期望音符）
    func resetMatchState() {
        hasMatchedOnce = false
        lastMatchedMIDINotes.removeAll()
        currentMatchState = .idle
        
        calculateMatchState()
        
        if debugEnabled {
            print("🎯 重置匹配状态")
        }
    }
    
    /// 检查是否完全匹配（基于完整MIDI音符）
    var isPerfectMatch: Bool {
        return !expectedMIDINotes.isEmpty && expectedMIDINotes == pressedMIDINotes
    }
    
    /// 检查是否部分匹配（基于半音）
    var isPartialMatch: Bool {
        return !expectedNotes.isEmpty && !pressedNotes.isEmpty && 
               !expectedNotes.intersection(pressedNotes).isEmpty
    }
    
    // MARK: - Private Methods
    
    /// 计算匹配状态
    private func calculateMatchState() {
        let oldState = currentMatchState
        
        // 计算匹配的音符、多余音符、缺少音符
        matchedNotes = expectedNotes.intersection(pressedNotes)
        extraNotes = pressedNotes.subtracting(expectedNotes)
        missingNotes = expectedNotes.subtracting(pressedNotes)
        
        // 确定新状态
        if isPerfectMatch {
            currentMatchState = .complete
        } else if isPartialMatch {
            currentMatchState = .partial
        } else {
            currentMatchState = .idle
        }
        
        // 状态变化通知
        if oldState != currentMatchState {
            onMatchEvent?(.matchStateChanged(currentMatchState))
            
            if debugEnabled {
                print("🎯 匹配状态变化: \(oldState) -> \(currentMatchState)")
                print("🎯 匹配音符: \(Array(matchedNotes).sorted())")
                print("🎯 多余音符: \(Array(extraNotes).sorted())")
                print("🎯 缺少音符: \(Array(missingNotes).sorted())")
            }
        }
    }
    
    /// 处理匹配事件
    private func handleMatchEvent(notesChanged: Bool) {
        switch currentMatchState {
        case .idle:
            if !pressedNotes.isEmpty {
                onMatchEvent?(.noMatch)
            }
            
        case .partial:
            onMatchEvent?(.partialMatch)
            
        case .complete:
            handlePerfectMatch(notesChanged: notesChanged)
        }
    }
    
    /// 处理完美匹配
    private func handlePerfectMatch(notesChanged: Bool) {
        let isFirstTimeMatch = !hasMatchedOnce
        let isNewCombination = lastMatchedMIDINotes != pressedMIDINotes
        
        if isFirstTimeMatch || (notesChanged && isNewCombination) {
            // 第一次匹配或音符组合发生变化
            hasMatchedOnce = true
            lastMatchedMIDINotes = pressedMIDINotes
            
            // 触发各种回调
            onMatchEvent?(.perfectMatch)
            onPerfectMatch?()
            onParticleExplosion?()
            onSoundEffect?(.perfectMatch)
            onVisualFeedback?(.perfectMatch)
            
            if debugEnabled {
                print("🎯 ✅ 完美匹配！(第一次或新组合)")
                print("🎯 匹配的完整MIDI音符: \(Array(pressedMIDINotes).sorted())")
            }
        } else {
            // 重复匹配
            onMatchEvent?(.repeatMatch)
            
            if debugEnabled {
                print("🎯 🔄 重复匹配，跳过反馈")
            }
        }
    }
    
    /// 获取匹配详情（用于调试）
    func getMatchDetails() -> String {
        var details = "匹配状态: \(currentMatchState)\n"
        details += "期望音符: \(Array(expectedNotes).sorted())\n"
        details += "按下音符: \(Array(pressedNotes).sorted())\n"
        details += "匹配音符: \(Array(matchedNotes).sorted())\n"
        details += "多余音符: \(Array(extraNotes).sorted())\n"
        details += "缺少音符: \(Array(missingNotes).sorted())\n"
        details += "完美匹配: \(isPerfectMatch)\n"
        details += "已匹配过: \(hasMatchedOnce)"
        return details
    }
}

// MARK: - 便利方法扩展

extension ChordMatchManager {
    
    /// 设置所有回调的便利方法
    func setCallbacks(
        onMatchEvent: ((ChordMatchEvent) -> Void)? = nil,
        onPerfectMatch: (() -> Void)? = nil,
        onParticleExplosion: (() -> Void)? = nil,
        onSoundEffect: ((ChordMatchEvent) -> Void)? = nil,
        onVisualFeedback: ((ChordMatchEvent) -> Void)? = nil
    ) {
        self.onMatchEvent = onMatchEvent
        self.onPerfectMatch = onPerfectMatch
        self.onParticleExplosion = onParticleExplosion
        self.onSoundEffect = onSoundEffect
        self.onVisualFeedback = onVisualFeedback
    }
    
    /// 检查是否需要触发反馈
    func shouldTriggerFeedback() -> Bool {
        return isPerfectMatch && !hasMatchedOnce
    }
    
    /// 强制触发匹配事件（用于测试）
    func forceTriggerMatch() {
        if isPerfectMatch {
            hasMatchedOnce = false
            handlePerfectMatch(notesChanged: true)
        }
    }
} 
