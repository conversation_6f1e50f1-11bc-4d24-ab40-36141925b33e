//
//  AchievementManager.swift
//  VoicingTrainer
//
//  Created by <PERSON> on 2025/1/5.
//

import Foundation

/**
 * 成就管理器
 * 负责管理用户的游戏成就数据，包括读取、保存和更新
 */
class AchievementManager: ObservableObject {
    static let shared = AchievementManager()
    
    @Published var achievementData: AchievementData
    
    private let userDefaults = UserDefaults.standard
    private let achievementKey = "user_achievement_data"
    
    private init() {
        self.achievementData = Self.loadAchievementData()
    }
    
    // MARK: - 数据加载与保存
    
    /**
     * 从UserDefaults加载成就数据
     */
    private static func loadAchievementData() -> AchievementData {
        guard let data = UserDefaults.standard.data(forKey: "user_achievement_data") else {
            print("No achievement data found, creating default data")
            return createDefaultAchievementData()
        }
        
        do {
            let decoder = JSONDecoder()
            decoder.dateDecodingStrategy = .iso8601
            let achievementData = try decoder.decode(AchievementData.self, from: data)
            print("Successfully loaded achievement data, version: \(achievementData.version)")
            return achievementData
        } catch {
            print("Failed to decode achievement data: \(error)")
            print("Creating default data as fallback")
            return createDefaultAchievementData()
        }
    }
    
    /**
     * 创建默认的成就数据（第一关解锁）
     */
    private static func createDefaultAchievementData() -> AchievementData {
        var achievements: [String: LevelAchievement] = [:]
        // 第一关默认解锁
        achievements["first_c"] = LevelAchievement(isUnlocked: true)
        
        return AchievementData(
            version: "1.0",
            lastPlayedLevel: "first_c",
            achievements: achievements
        )
    }
    
    /**
     * 保存成就数据到UserDefaults
     */
    private func saveAchievementData() {
        do {
            let encoder = JSONEncoder()
            encoder.dateEncodingStrategy = .iso8601
            encoder.outputFormatting = .prettyPrinted
            let data = try encoder.encode(achievementData)
            userDefaults.set(data, forKey: achievementKey)
            print("Achievement data saved successfully")
            
            // 调试：输出保存的数据
            if let jsonString = String(data: data, encoding: .utf8) {
                print("Saved achievement data:\n\(jsonString)")
            }
        } catch {
            print("Failed to save achievement data: \(error)")
        }
    }
    
    // MARK: - 公共接口
    
    /**
     * 获取指定关卡的成就记录
     */
    func getAchievement(for levelId: String) -> LevelAchievement {
        return achievementData.getAchievement(for: levelId)
    }
    
    /**
     * 检查关卡是否解锁
     */
    func isLevelUnlocked(_ levelId: String, in levelManager: NoteLevelManager) -> Bool {
        return achievementData.isLevelUnlocked(levelId, in: levelManager)
    }
    
    /**
     * 更新关卡成绩
     */
    func updateScore(_ score: GameScore, for levelId: String, in levelManager: NoteLevelManager) {
        // 更新成就数据
        achievementData = achievementData.updatedWithScore(score, for: levelId)
        
        // 如果满分通关，解锁下一关
        if score.isPerfect {
            unlockNextLevel(after: levelId, in: levelManager)
        }
        
        // 保存数据
        saveAchievementData()
        
        print("Updated score for level \(levelId): \(score)")
    }
    
    /**
     * 解锁下一关
     */
    private func unlockNextLevel(after levelId: String, in levelManager: NoteLevelManager) {
        if let nextLevelId = levelManager.getNextLevelId(for: levelId) {
            let currentAchievement = getAchievement(for: nextLevelId)
            if !currentAchievement.isUnlocked {
                achievementData = achievementData.withUnlockedLevel(nextLevelId)
                print("Unlocked next level: \(nextLevelId)")
            }
        }
    }
    
    /**
     * 获取最近解锁的关卡ID
     */
    func getLastUnlockedLevelId(in levelManager: NoteLevelManager) -> String? {
        // 如果有最后游玩的关卡且已解锁，返回它
        if let lastPlayed = achievementData.lastPlayedLevel,
           isLevelUnlocked(lastPlayed, in: levelManager) {
            return lastPlayed
        }
        
        // 否则找到最后一个解锁的关卡
        for (index, level) in levelManager.levels.enumerated() {
            if !isLevelUnlocked(level.id, in: levelManager) {
                // 找到第一个未解锁的关卡，返回前一个
                if index > 0 {
                    return levelManager.levels[index - 1].id
                } else {
                    return level.id // 如果连第一关都未解锁，返回第一关
                }
            }
        }
        
        // 如果所有关卡都解锁了，返回最后一关
        return levelManager.levels.last?.id
    }
    
    /**
     * 重置所有成就数据（调试用）
     */
    func resetAllAchievements() {
        achievementData = Self.createDefaultAchievementData()
        saveAchievementData()
        print("All achievements reset to default")
    }
    
    /**
     * 获取已解锁关卡的总数
     */
    func getUnlockedLevelCount(in levelManager: NoteLevelManager) -> Int {
        return levelManager.levels.filter { level in
            isLevelUnlocked(level.id, in: levelManager)
        }.count
    }
    
    /**
     * 获取满分通关的关卡总数
     */
    func getPerfectCompletedLevelCount() -> Int {
        return achievementData.achievements.values.filter { $0.isPerfectCompleted }.count
    }
} 