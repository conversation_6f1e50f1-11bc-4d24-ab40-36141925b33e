import Foundation
import SwiftUI
import Combine

enum ChordGameState {
    case idle
    case playingChord
    case waitingForResponse
    case completed
}

class ChordGameManager: ObservableObject {
    @Published var gameState: ChordGameState = .idle {
        didSet {
            // 当游戏状态变化时发送通知
            NotificationCenter.default.post(name: NSNotification.Name("ChordGameStateChanged"), object: gameState)
        }
    }
    @Published var currentRound: Int = 0
    @Published var rightNoteCount: Int = 0  //  正确音符个数
    @Published var errorNoteCount: Int = 0  //  错误音符个数

    @Published var rightChordCount: Int = 0 //  正确和弦个数
    @Published var errorChordCount: Int = 0 //  错误和弦个数
    @Published var totalTime: Double = 0
    @Published var totalRound: Int = 12
    @Published var showLevelClearView: Bool = false
    @Published var currentRootIndex: Int = 0  // 五度圈索引（用于显示）
    @Published var currentRootNote: Int = 60   // 🎵 当前根音MIDI值（C4=60开始）
    @Published var currentChordNotes: [Int] = []  // 当前和弦音符
    @Published var expectedNotes: Set<Int> = []  // 期望的音符
    @Published var playedNotes: Set<Int> = []    // 玩家已弹奏的音符
    
    // 🎵 练习模式支持 - 使用统一的引擎
    private var practicePatternManager: PracticePatternManager
    private var practiceEngineState: PracticeEngineState = PracticeEngineState()
    private var currentPracticeOffset: Int = 5  // 默认五度圈反向
    private var initialRootNote: Int = 60          // 初始根音（用于新循环的起始点）
    @Published var selectedPracticePattern: PracticePattern?  // 🎵 暴露给UI的当前选择模式
    
    // 🎵 暴露练习引擎状态给UI
    @Published var currentCycle: Int = 0           // 当前循环数（从0开始）
    @Published var currentRoundInCycle: Int = 0    // 当前循环内的轮数（从0开始）
    
    // 🎵 新增：音符播放状态通知
    @Published var currentPlayingNoteIndex: Int = -1  // 当前正在播放的音符索引 (-1表示无音符播放)
    @Published var currentPlayingNote: Int = -1       // 当前正在播放的音符MIDI值
    
    // 🎵 和弦播放完成状态
    @Published var isChordPlaybackComplete: Bool = false
    
    // 🔧 防止同一轮重复得分
    private var currentRoundScored: Bool = false
    
    // 🔄 永远等待模式支持
    @Published var isInfiniteWaitMode: Bool = false
    
    private var gameConfig: GameConfig
    private var midiManager: MIDIManager
    @Published var selectedVoicing: ChordVoicing? // 暴露给UI使用
    
    // 新增：练习组支持
    private var selectedPracticeGroup: PracticeGroup?
    private var practiceGroupVoicings: [ChordVoicing] = []
    private var currentVoicingIndex: Int = 0
    @Published var practiceGroupName: String = "" // 用于UI显示的练习组名称
    @Published var currentVoicingInGroup: String = "" // 当前练习的voicing名称
    
    private var gameTimer: Timer?
    private var roundTimer: Timer?
    private var nextRoundTask: DispatchWorkItem?  // 🔧 新增：用于管理延迟任务
    // 🔧 新增：用于管理和弦播放的所有异步任务，确保可以取消
    private var chordPlayTasks: [DispatchWorkItem] = []
    private var startTime: Date?
    private var debug:Bool = false
    
    init(config: GameConfig, midiManager: MIDIManager) {
        self.gameConfig = config
        self.midiManager = midiManager
        self.practicePatternManager = PracticePatternManager()
        // 🎵 初始化UI可观察的练习模式属性
        self.selectedPracticePattern = practicePatternManager.selectedPattern
        setupMIDIListener()
        setupPracticePatternListener()
    }
    
    private func setupMIDIListener() {
        // 监听MIDI输入变化
        midiManager.$pressedNotes
            .sink { [weak self] pressedNotes in
                self?.handleMIDIInput(pressedNotes)
            }
            .store(in: &cancellables)
    }
    
    private func setupPracticePatternListener() {
        // 监听练习模式变化
        practicePatternManager.$selectedPattern
            .sink { [weak self] pattern in
                if let pattern = pattern {
                    self?.currentPracticeOffset = pattern.offset
                    self?.selectedPracticePattern = pattern  // 🎵 同步到UI可观察的属性
                    print("🎵 Practice pattern changed to: \(pattern.name) (offset: \(pattern.offset))")
                }
            }
            .store(in: &cancellables)
    }
    
    // MARK: - Practice Pattern Access
    var patternManager: PracticePatternManager {
        return practicePatternManager
    }
    
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - 🎵 练习模式管理 - 使用统一引擎
    
    /// 设置练习游戏参数
    private func setupGameParameters() {
        // 使用统一的练习引擎初始化状态
        practiceEngineState = PracticePatternEngine.initializeState(for: selectedPracticePattern)
        
        // 🎯 练习组模式：总轮数 = 根音数量 × voicing数量
        if selectedPracticeGroup != nil {
            let baseRounds = practiceEngineState.totalRounds
            let voicingCount = practiceGroupVoicings.count
            totalRound = baseRounds * voicingCount
            print("🎯 练习组总轮数: \(baseRounds) 个根音 × \(voicingCount) 个voicing = \(totalRound) 轮")
        } else {
            totalRound = practiceEngineState.totalRounds
        }
        
        // 同步UI显示状态
        currentCycle = practiceEngineState.currentCycle
        currentRoundInCycle = practiceEngineState.currentRoundInCycle
    }
    
    // MARK: - Game Control
    
    // 练习组游戏启动（唯一的游戏启动方式）
    func startGame(with practiceGroup: PracticeGroup, voicings: [ChordVoicing]) {
        guard gameState == .idle else { return }
        
        print("🎯 开始练习组游戏: \(practiceGroup.name)")
        print("🎯 包含 \(voicings.count) 个voicing: \(voicings.map { $0.name })")
        
        selectedPracticeGroup = practiceGroup
        practiceGroupVoicings = voicings
        currentVoicingIndex = 0
        practiceGroupName = practiceGroup.name
        
        // 使用第一个voicing开始游戏
        if let firstVoicing = voicings.first {
            selectedVoicing = firstVoicing
            currentVoicingInGroup = firstVoicing.name
            print("🎯 开始第一个voicing: \(firstVoicing.name) (1/\(voicings.count))")
            startSingleVoicingGame(with: firstVoicing)
        }
    }
    
    func stopGame() {
        print("🛑 停止和弦游戏 - 当前状态: \(gameState)")
        
        if selectedPracticeGroup != nil {
            print("🛑 停止练习组: \(practiceGroupName)")
        }
        
        // 🔧 立即设置为idle状态，防止其他延迟任务执行
        gameState = .idle
        
        // 🔧 停止所有计时器和延迟任务
        stopAllTimers()
        
        // 🔧 明确清除练习组状态
        selectedPracticeGroup = nil
        practiceGroupVoicings.removeAll()
        currentVoicingIndex = 0
        practiceGroupName = ""
        currentVoicingInGroup = ""
        
        // 🔧 重置游戏状态
        resetGame()
        
        print("🎯 和弦游戏已完全停止")
    }
    
    private func startRound() {
        // 🔧 检查游戏状态，如果已停止则不执行
        guard gameState != .idle else {
            print("🔧 startRound: 游戏已停止，取消执行")
            return
        }
        
        // 🔧 详细调试信息
        print("🎯 ===== startRound 开始 =====")
        print("🎯 gameState: \(gameState)")
        print("🎯 selectedPracticeGroup: \(selectedPracticeGroup?.name ?? "nil")")
        print("🎯 practiceGroupVoicings count: \(practiceGroupVoicings.count)")
        print("🎯 currentVoicingIndex: \(currentVoicingIndex)")
        print("🎯 currentVoicingInGroup: \(currentVoicingInGroup)")
        
        guard let voicing = selectedVoicing else { 
            print("❌ selectedVoicing is nil!")
            return 
        }
        
        // 🔧 重置当前轮得分状态
        currentRoundScored = false
        
        // 🎵 直接使用当前根音构建和弦
        let rootNote = currentRootNote
        
        // 🔧 先更新currentRootIndex，再发布currentChordNotes，确保监听者获得正确的状态
        let rootPitchClass = rootNote % 12
        currentRootIndex = rootPitchClass
        
        // 🔧 添加防抖动机制，避免同一frame内多次更新currentChordNotes
        let newChordNotes = voicing.intervals.map { rootNote + $0 }
        if newChordNotes != currentChordNotes {
            // 然后才设置和发布currentChordNotes
            currentChordNotes = newChordNotes
            expectedNotes = Set(currentChordNotes)
            playedNotes.removeAll()
            
            print("🔍 DEBUG: ChordGameManager - currentChordNotes updated to: \(currentChordNotes)")
        } else {
            print("🔍 DEBUG: ChordGameManager - currentChordNotes unchanged, skipping update")
        }
        
        // 🎵 获取根音名称用于显示
        let rootNoteName = PracticePatternEngine.getNoteNameFromMIDI(rootNote)
        
        if debug{
            print("🎵 Round \(currentRound): Playing \(rootNoteName) chord")
            print("🎵 Root MIDI note: \(rootNote) (pitch class: \(rootPitchClass))")
            print("🎵 Expected notes: \(currentChordNotes)")
            print("🎵 Practice offset: \(currentPracticeOffset)")
            print("🎵 Current root index: \(currentRootIndex)")
        }
        
        // 新增：练习组调试信息
        print("selectedPracticeGroup:========= \(selectedPracticeGroup)")
        if let practiceGroup = selectedPracticeGroup {
            print("🎯 练习组: \(practiceGroup.name)")
            print("🎯 当前voicing: \(currentVoicingInGroup) (\(currentVoicingIndex + 1)/\(practiceGroupVoicings.count))")
            print("🎯 当前根音: \(rootNoteName) (\(currentRound)/\(totalRound))")
        } else {
            print("🎯 单个和弦模式: \(selectedVoicing?.name ?? "未知")")
        }
        
        // 播放和弦
        playChord()
        
        // 🔧 设置等待响应状态 - 使用DispatchWorkItem管理
        nextRoundTask = DispatchWorkItem { [weak self] in
            self?.gameState = .waitingForResponse
            self?.startRoundTimer()
        }
        
        if let task = nextRoundTask {
            DispatchQueue.main.asyncAfter(deadline: .now() + 1.0, execute: task)
        }
    }
    
    private func playChord() {
        // 🔧 先清理之前的播放任务
        cancelChordPlayTasks()
        
        // 🎵 重置播放状态
        currentPlayingNoteIndex = -1
        currentPlayingNote = -1
        isChordPlaybackComplete = false
        
        print("🎵 开始依次播放和弦音符: \(currentChordNotes)")
        
        // 依次播放和弦音符
        for (index, note) in currentChordNotes.enumerated() {
            let delay = Double(index) * 0.5 + 0.2
            
            // 🔧 创建音符播放任务，使用DispatchWorkItem管理
            let playTask = DispatchWorkItem { [weak self] in
                // 🔧 检查游戏状态，如果已停止则不执行
                guard let self = self, self.gameState != .idle else {
                    print("🔧 playChord音符播放: 游戏已停止，取消播放音符 \(note)")
                    return
                }
                
                // 🎵 更新当前播放音符状态
                self.currentPlayingNoteIndex = index
                self.currentPlayingNote = note
                
                // 播放音符
                self.midiManager.playListenNote(note, velocity: 100)
                
                //print("🎵 播放音符 \(index + 1)/\(self.currentChordNotes.count): \(note)")
                
                // 发送音符开始播放通知
                NotificationCenter.default.post(
                    name: NSNotification.Name("ChordNoteStartedPlaying"), 
                    object: ["noteIndex": index, "note": note]
                )
                
                // 🔧 创建音符停止任务
                let stopTask = DispatchWorkItem { [weak self] in
                    // 🔧 再次检查游戏状态
                    guard let self = self, self.gameState != .idle else {
                        print("🔧 playChord音符停止: 游戏已停止，取消停止音符 \(note)")
                        return
                    }
                    
                    self.midiManager.stopListenNote(note)
                    
                    // 发送音符停止播放通知
                    NotificationCenter.default.post(
                        name: NSNotification.Name("ChordNoteStopped"), 
                        object: ["noteIndex": index, "note": note]
                    )
                    
                    // 🎵 检查是否是最后一个音符
                    if index == self.currentChordNotes.count - 1 {
                        // 🔧 最后一个音符播放完成任务
                        let completeTask = DispatchWorkItem { [weak self] in
                            guard let self = self, self.gameState != .idle else {
                                print("🔧 playChord完成: 游戏已停止，取消完成处理")
                                return
                            }
                            
                            self.currentPlayingNoteIndex = -1
                            self.currentPlayingNote = -1
                            self.isChordPlaybackComplete = true
                            
                            print("🎵 和弦播放完成")
                            
                            // 发送和弦播放完成通知
                            NotificationCenter.default.post(
                                name: NSNotification.Name("ChordPlaybackCompleted"), 
                                object: nil
                            )
                        }
                        
                        // 🔧 将完成任务添加到管理数组
                        self.chordPlayTasks.append(completeTask)
                        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1, execute: completeTask)
                    }
                }
                
                // 🔧 将停止任务添加到管理数组
                self.chordPlayTasks.append(stopTask)
                DispatchQueue.main.asyncAfter(deadline: .now() + 1.0, execute: stopTask)
            }
            
            // 🔧 将播放任务添加到管理数组
            chordPlayTasks.append(playTask)
            DispatchQueue.main.asyncAfter(deadline: .now() + delay, execute: playTask)
        }
    }
    
    private func startRoundTimer() {
        
       // print("chords interval:\(gameConfig.gameSettings.chordsWaitInterval)")
        
        roundTimer?.invalidate()
        
        // 🔄 无尽模式下不启动超时计时器
        if isInfiniteWaitMode {
            print("🔄 无尽模式：跳过超时计时器，等待用户完成")
            return
        }
        
        let waitTime = GameConfigManager.shared.getChordWaitTime()
        roundTimer = Timer.scheduledTimer(withTimeInterval: waitTime, repeats: false) { _ in
            self.handleRoundTimeout()
        }
        
        
    }
    
    private func handleRoundTimeout() {
        print("Round timeout")
        errorChordCount += 1
        nextRound()
    }
    
    private func handleMIDIInput(_ pressedNotes: Set<Int>) {
        guard gameState == .waitingForResponse else { return }
        
        playedNotes = pressedNotes
        
        // 🔧 检查是否完全匹配，并防止重复得分
        if playedNotes == expectedNotes && !playedNotes.isEmpty && !currentRoundScored {
            print("Correct chord! Played: \(Array(playedNotes).sorted())")
            rightChordCount += 1
            currentRoundScored = true  // 🔧 标记当前轮已得分
            roundTimer?.invalidate()
            
            // 播放和弦正确音效
            SoundEffectManager.shared.playChordCorrect()
            
            // 🔧 立即改变状态，防止重复触发
            gameState = .playingChord
            
            // 🎨 使用responseTime等待，给FXChordView的动画留出时间
            let responseTime = GameConfigManager.shared.getResponseTime()
            print("🎨 Chord correct! Waiting \(String(format: "%.2f", responseTime)) seconds for animation before next round")
            
            // 🔧 使用DispatchWorkItem管理延迟任务，确保可以被取消
            nextRoundTask = DispatchWorkItem { [weak self] in
                self?.nextRound()
            }
            
            if let task = nextRoundTask {
                DispatchQueue.main.asyncAfter(deadline: .now() + responseTime, execute: task)
            }
        }
    }
    
    private func nextRound() {
        // 🔧 检查游戏状态，如果已停止则不执行
        guard gameState != .idle else {
            print("🔧 nextRound: 游戏已停止，取消执行")
            return
        }
        
        // 🔧 详细调试信息
        print("🎯 ===== nextRound 开始 =====")
        print("🎯 selectedPracticeGroup: \(selectedPracticeGroup?.name ?? "nil")")
        print("🎯 practiceGroupVoicings count: \(practiceGroupVoicings.count)")
        print("🎯 currentVoicingIndex: \(currentVoicingIndex)")
        
        if debug{
            
            print("nextRound :selectedPracticeGroup:\(selectedPracticeGroup)")
            
        }
        
        // 🎯 练习组模式：先检查是否需要切换voicing
        if selectedPracticeGroup != nil && currentVoicingIndex < practiceGroupVoicings.count - 1 {
            // 切换到当前根音的下一个voicing
            currentVoicingIndex += 1
            let nextVoicing = practiceGroupVoicings[currentVoicingIndex]
            selectedVoicing = nextVoicing
            currentVoicingInGroup = nextVoicing.name
            
            print("🎯 当前根音切换voicing: \(nextVoicing.name) (\(currentVoicingIndex + 1)/\(practiceGroupVoicings.count))")
            
            // 保持当前根音不变，只更新voicing
            gameState = .playingChord
            currentRound += 1
            
            // 🔧 使用DispatchWorkItem管理延迟任务，确保可以被取消
            nextRoundTask = DispatchWorkItem { [weak self] in
                self?.startRound()
            }
            
            if let task = nextRoundTask {
                DispatchQueue.main.asyncAfter(deadline: .now() + 1.0, execute: task)
            }
            return
        }
        
        // 🎯 练习组模式：当前根音的所有voicing都完成了，重置voicing索引
        if selectedPracticeGroup != nil {
            currentVoicingIndex = 0
            if let firstVoicing = practiceGroupVoicings.first {
                selectedVoicing = firstVoicing
                currentVoicingInGroup = firstVoicing.name
                print("🎯 准备下一个根音，重置到第一个voicing: \(firstVoicing.name)")
            }
        }
        
        // 使用统一的练习引擎计算下一个根音
        let intervals = selectedVoicing?.intervals ?? []
        let result = PracticePatternEngine.calculateNextRootNote(
            currentRootNote: currentRootNote,
            initialRootNote: initialRootNote,
            practiceOffset: currentPracticeOffset,
            intervals: intervals,
            state: &practiceEngineState
        )
        
        print("next round:\(result)")
        
        if result.isCompleted {
            // 所有循环完成，游戏结束
            completeGame()
            return
        }
        
        // 更新根音和UI状态
        currentRootNote = result.nextRootNote
        currentRound += 1
        gameState = .playingChord
        
        // 同步UI显示状态
        currentCycle = practiceEngineState.currentCycle
        currentRoundInCycle = practiceEngineState.currentRoundInCycle
        
        let nextNoteName = PracticePatternEngine.getNoteNameFromMIDI(currentRootNote)
        print("🎵 Round \(currentRound)/\(totalRound): \(nextNoteName) (\(practiceEngineState.getProgressDescription()))")
        
        // 🔧 使用DispatchWorkItem管理延迟任务，确保可以被取消
        nextRoundTask = DispatchWorkItem { [weak self] in
            self?.startRound()
        }
        
        if let task = nextRoundTask {
            DispatchQueue.main.asyncAfter(deadline: .now() + 1.0, execute: task)
        }
    }
    
    private func completeGame() {
        stopAllTimers()
        
        // 计算总时间
        if let startTime = startTime {
            totalTime = Date().timeIntervalSince(startTime)
        }
        
        // 播放练习完成音效
        SoundEffectManager.shared.playPracticeComplete()
        
        if selectedPracticeGroup != nil {
            print("🎯 练习组完成: \(practiceGroupName)")
            print("🎯 总共完成了 \(practiceGroupVoicings.count) 个voicing的所有根音练习")
        } else {
            print("🎯 单个和弦练习完成: \(selectedVoicing?.name ?? "未知")")
        }
        
        gameState = .completed
        showLevelClearView = true
        
        // 延迟一段时间后自动重置为idle状态，或者等待用户关闭弹窗
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            self.gameState = .idle
        }
    }
    
    private func resetGame() {
        rightChordCount = 0
        errorChordCount = 0
        currentRound = 0
        totalTime = 0
        currentRootIndex = 0
        currentRootNote = initialRootNote  // 使用保存的初始根音
        currentChordNotes.removeAll()
        expectedNotes.removeAll()
        playedNotes.removeAll()
        showLevelClearView = false
        currentRoundScored = false  // 🔧 重置得分状态
        
        // 重置练习组状态（只在明确停止游戏时清理，不在游戏启动时清理）
        if gameState == .idle && selectedPracticeGroup == nil {
            // 只有在游戏处于idle状态且没有选择练习组时才清理
            practiceGroupVoicings.removeAll()
            currentVoicingIndex = 0
            practiceGroupName = ""
            currentVoicingInGroup = ""
        }
        
        // 🎵 重置练习引擎状态
        practiceEngineState.reset()
        currentCycle = practiceEngineState.currentCycle
        currentRoundInCycle = practiceEngineState.currentRoundInCycle
        
        // 🔧 清理和弦播放任务
        cancelChordPlayTasks()
        
        stopAllTimers()
    }
    
    private func stopAllTimers() {
        print("🔧 停止所有计时器和延迟任务")
        
        // 停止Timer
        gameTimer?.invalidate()
        roundTimer?.invalidate()
        gameTimer = nil
        roundTimer = nil
        
        // 🔧 取消延迟任务
        nextRoundTask?.cancel()
        nextRoundTask = nil
        
        // 🔧 取消所有和弦播放任务
        cancelChordPlayTasks()
        
        // 🔧 立即停止所有正在播放的音符
        for note in currentChordNotes {
            midiManager.stopListenNote(note)
        }
        
        // 🔧 清理音符播放状态
        currentPlayingNoteIndex = -1
        currentPlayingNote = -1
        isChordPlaybackComplete = false
        
        print("🔧 所有计时器和延迟任务已停止")
    }
    
    // MARK: - Configuration Update
    
    func updateConfig(_ newConfig: GameConfig) {
        gameConfig = newConfig
    }
    
    // MARK: - Endless Mode Control
    
    func setEndlessMode(_ enabled: Bool) {
        isInfiniteWaitMode = enabled
        print("🔄 无尽模式设置为: \(enabled ? "开启" : "关闭")")
    }
    
    // MARK: - Replay Chord
    
    func replayCurrentChord() {
        guard gameState == .waitingForResponse else {
            print("replayCurrentChord: not in waitingForResponse state, current: \(gameState)")
            return
        }
        
        print("Replaying current chord")
        playChord()
    }
    
    private func cancelChordPlayTasks() {
        for task in chordPlayTasks {
            task.cancel()
        }
        chordPlayTasks.removeAll()
    }
    
    // 私有方法：启动单个voicing的游戏逻辑（只被练习组模式使用）
    private func startSingleVoicingGame(with voicing: ChordVoicing) {
        selectedVoicing = voicing
        
        // 🎵 智能计算最佳起始根音
        let optimalStartingNote = PracticePatternEngine.calculateOptimalStartingNote(
            intervals: voicing.intervals,
            defaultRootNote: voicing.baseMidiNote,
            pattern: selectedPracticePattern
        )
        initialRootNote = optimalStartingNote  // 使用智能计算的根音
        
        // 🎵 根据当前练习模式设置游戏参数
        setupGameParameters()
        
        resetGame()
        startTime = Date()
        gameState = .playingChord
        currentRound = 1
        currentRootIndex = 0
        currentRootNote = initialRootNote  // 从优化的根音开始
        
        startRound()
    }
}
