//
//  PracticeStatisticsHelper.swift
//  VoicingTrainer
//
//  Created by AI Assistant on 2025/7/28.
//

import Foundation

/// 简单的练习统计工具类
/// 只负责统一保存逻辑，不管理状态，不改变现有调用方式
class PracticeStatisticsHelper {

    /// 保存练习统计数据到数据库
    /// 这是三个View中重复代码的提取，保持原有异步机制不变
    static func savePracticeData(
        to statisticsViewModel: StatisticsViewModel,
        startTime: Date,
        workoutType: WorkoutType,
        workoutName: String,
        totalNoteCount: Int,
        totalCount: Int,
        rightCount: Int
    ) {
        let endTime = Date()
        let duration = Int(endTime.timeIntervalSince(startTime))

        print("📊 统计工具：保存 \(workoutType.displayName) 数据")
        print("  - 名称: \(workoutName)")
        print("  - 时长: \(duration)秒")
        print("  - 音符: \(totalNoteCount), 总数: \(totalCount), 正确: \(rightCount)")

        // 保持原有的异步调用机制，不做任何改变
        Task {
            await statisticsViewModel.addWorkoutItem(
                startTime: startTime,
                duration: duration,
                workoutType: workoutType,
                workoutName: workoutName,
                totalNoteCount: totalNoteCount,
                totalCount: totalCount,
                rightCount: rightCount
            )

            await MainActor.run {
                print("📊 统计工具：\(workoutName) 数据保存完成")
            }
        }
    }
}
