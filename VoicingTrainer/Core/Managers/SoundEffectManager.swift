import AVFoundation
import Foundation
#if os(iOS)
import UIKit
#endif

/// 音频会话协调器 - 统一管理音频会话，避免冲突
class AudioSessionCoordinator {
    static let shared = AudioSessionCoordinator()
    
    private var isConfigured = false
    private let sessionQueue = DispatchQueue(label: "com.voicingtrainer.audiosession", qos: .userInteractive)
    
    private init() {}
    
    /// 配置音频会话（只配置一次）
    func configureAudioSession() {
        #if os(iOS)
        sessionQueue.sync {
            guard !isConfigured else {
                print("✅ AudioSessionCoordinator: 音频会话已配置，跳过")
                return
            }
            
            do {
                let session = AVAudioSession.sharedInstance()
                
                // 使用兼容的配置，支持MIDI和音效
                try session.setCategory(.playAndRecord, 
                                      mode: .default, 
                                      options: [.defaultToSpeaker, .allowBluetooth, .mixWithOthers])
                try session.setActive(true)
                
                isConfigured = true
                print("✅ AudioSessionCoordinator: 音频会话配置成功")
                
                // 监听中断
                NotificationCenter.default.addObserver(
                    self,
                    selector: #selector(handleAudioSessionInterruption),
                    name: AVAudioSession.interruptionNotification,
                    object: nil
                )
                
            } catch {
                print("❌ AudioSessionCoordinator: 音频会话配置失败: \(error)")
                
                // 降级处理：尝试较简单的配置
                do {
                    let session = AVAudioSession.sharedInstance()
                    try session.setCategory(.ambient, mode: .default, options: [.mixWithOthers])
                    try session.setActive(true)
                    isConfigured = true
                    print("🔄 AudioSessionCoordinator: 降级配置成功")
                } catch {
                    print("❌ AudioSessionCoordinator: 降级配置也失败: \(error)")
                }
            }
        }
        #else
        print("✅ AudioSessionCoordinator: macOS不需要配置音频会话")
        isConfigured = true
        #endif
    }
    
    /// 重新激活音频会话
    func reactivateSession() {
        #if os(iOS)
        sessionQueue.async {
            do {
                let session = AVAudioSession.sharedInstance()
                // 🔧 修复: 移除 isActive 检查，直接尝试激活会话（幂等操作）
                try session.setActive(true)
                print("🔄 AudioSessionCoordinator: 重新激活音频会话")
            } catch {
                print("❌ AudioSessionCoordinator: 重新激活失败: \(error)")
            }
        }
        #endif
    }
    
    @objc private func handleAudioSessionInterruption(_ notification: Notification) {
        #if os(iOS)
        guard let info = notification.userInfo,
              let typeValue = info[AVAudioSessionInterruptionTypeKey] as? UInt,
              let type = AVAudioSession.InterruptionType(rawValue: typeValue) else {
            return
        }
        
        switch type {
        case .began:
            print("🔇 AudioSessionCoordinator: 音频会话被中断")
        case .ended:
            print("🔊 AudioSessionCoordinator: 音频会话中断结束")
            reactivateSession()
        @unknown default:
            break
        }
        #endif
    }
}

/// 音效管理器 - 负责播放游戏音效
class SoundEffectManager {
    
    // MARK: - Singleton
    static let shared = SoundEffectManager()
    
    // MARK: - Properties
    private var players: [String: AVAudioPlayer] = [:]
    
    // MARK: - Initialization
    private init() {
        setupAudioSession()
        preloadSounds()
    }
    
    // MARK: - Audio Session Setup
    private func setupAudioSession() {
        // 🔧 修复: 使用统一的音频会话协调器，避免与MIDIManager冲突
        AudioSessionCoordinator.shared.configureAudioSession()
        print("✅ SoundEffectManager: 使用统一音频会话协调器")
    }
    
    // MARK: - Sound Preloading
    private func preloadSounds() {
        // 预加载音效文件
        let soundFiles = [
            "ding": "ding.mp3",
            "cheer": "cheer.mp3"
        ]
        
        for (key, filename) in soundFiles {
            if let player = createPlayer(for: filename) {
                players[key] = player
                print("✅ 预加载音效: \(filename)")
            } else {
                print("❌ 音效文件加载失败: \(filename)")
            }
        }
    }
    
    // MARK: - Player Creation
    private func createPlayer(for filename: String) -> AVAudioPlayer? {
        let resourceName = filename.replacingOccurrences(of: ".mp3", with: "")
        
        // 尝试多种路径查找音频文件
        var path: String?
        
        // 方法1: 直接从Bundle查找
        path = Bundle.main.path(forResource: resourceName, ofType: "mp3")
        
        // 方法2: 从soundEffect子目录查找
        if path == nil {
            path = Bundle.main.path(forResource: resourceName, ofType: "mp3", inDirectory: "soundEffect")
        }
        
        // 方法3: 从Resources/soundEffect目录查找
        if path == nil {
            path = Bundle.main.path(forResource: resourceName, ofType: "mp3", inDirectory: "Resources/soundEffect")
        }
        
        guard let validPath = path else {
            print("❌ 找不到音效文件: \(filename)")
            print("📁 请确保音频文件已添加到Xcode项目中")
            // 调试信息：列出Bundle中所有mp3文件
            if let bundlePath = Bundle.main.resourcePath {
                let fileManager = FileManager.default
                do {
                    let files = try fileManager.contentsOfDirectory(atPath: bundlePath)
                    let mp3Files = files.filter { $0.hasSuffix(".mp3") }
                    print("📁 Bundle中的mp3文件: \(mp3Files)")
                } catch {
                    print("📁 无法列出Bundle内容: \(error)")
                }
            }
            return nil
        }
        
        let url = URL(fileURLWithPath: validPath)
        
        do {
            let player = try AVAudioPlayer(contentsOf: url)
            player.prepareToPlay()
            return player
        } catch {
            print("❌ 创建音效播放器失败: \(error)")
            return nil
        }
    }
    
    // MARK: - Public Methods
    
    /// 播放和弦正确音效 (ding.mp3)
    func playChordCorrect() {
        #if os(iOS)
        // 🔧 iPadOS 16.x 特殊处理：检测系统版本
        if #available(iOS 16.0, *), UIDevice.current.userInterfaceIdiom == .pad {
            let systemVersion = UIDevice.current.systemVersion
            if systemVersion.hasPrefix("16.") {
                print("🔧 检测到 iPadOS 16.x，使用特殊音效处理")
                playSound(key: "ding", useCompatibilityMode: true)
                return
            }
        }
        #endif
        
        playSound(key: "ding")
    }
    
    /// 播放练习完成音效 (cheer.mp3)  
    func playPracticeComplete() {
        #if os(iOS)
        // 🔧 iPadOS 16.x 特殊处理
        if #available(iOS 16.0, *), UIDevice.current.userInterfaceIdiom == .pad {
            let systemVersion = UIDevice.current.systemVersion
            if systemVersion.hasPrefix("16.") {
                print("🔧 检测到 iPadOS 16.x，使用特殊音效处理")
                playSound(key: "cheer", useCompatibilityMode: true)
                return
            }
        }
        #endif
        
        playSound(key: "cheer")
    }
    
    // MARK: - Private Methods
    
    /// 播放指定的音效
    /// - Parameters:
    ///   - key: 音效键名
    ///   - useCompatibilityMode: 是否使用兼容模式（用于旧版本iOS）
    private func playSound(key: String, useCompatibilityMode: Bool = false) {
        // 🔧 修复: 使用音频会话协调器确保会话状态
        AudioSessionCoordinator.shared.reactivateSession()
        
        // 🔧 iPadOS 16.x 兼容模式：延迟播放，避免与MIDI冲突
        if useCompatibilityMode {
            print("🔧 使用 iPadOS 16.x 兼容模式播放音效: \(key)")
            
            // 延迟500ms播放，让MIDI音频先稳定
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                self.playNormalSound(key: key, isRetry: false)
            }
            return
        }
        
        // 正常模式播放
        playNormalSound(key: key, isRetry: false)
    }
    
    /// 正常音效播放逻辑
    private func playNormalSound(key: String, isRetry: Bool) {
        // 在后台队列中播放，不阻塞UI
        DispatchQueue.global(qos: .userInteractive).async { [weak self] in
            guard let player = self?.players[key] else {
                print("❌ 找不到音效播放器: \(key)")
                return
            }
            
            // 🔧 修复: 增加播放前的状态检查
            if !player.prepareToPlay() {
                print("⚠️ 音效播放器准备失败: \(key)")
                // 尝试重新创建播放器
                if let newPlayer = self?.createPlayer(for: "\(key).mp3") {
                    self?.players[key] = newPlayer
                    if newPlayer.prepareToPlay() && newPlayer.play() {
                        print("🔄 重新创建播放器成功: \(key)")
                        return
                    }
                }
                print("❌ 音效播放最终失败: \(key)")
                return
            }
            
            // 如果正在播放，先停止
            if player.isPlaying {
                player.stop()
                player.currentTime = 0
            }
            
            // 播放音效
            let success = player.play()
            if success {
                print("🔊 播放音效: \(key)")
            } else {
                print("❌ 音效播放失败: \(key)")
                
                // 只在第一次失败时尝试重置音频会话
                if !isRetry {
                    #if os(iOS)
                    DispatchQueue.main.async {
                        do {
                            let session = AVAudioSession.sharedInstance()
                            try session.setActive(false)
                            try session.setActive(true)
                            
                            // 再次尝试播放
                            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                                self?.playNormalSound(key: key, isRetry: true)
                            }
                        } catch {
                            print("❌ 重置音频会话失败: \(error)")
                        }
                    }
                    #endif
                } else {
                    print("❌ 重试后仍然播放失败: \(key)")
                }
            }
        }
    }
} 
