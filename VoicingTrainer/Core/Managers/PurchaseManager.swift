import Foundation

// MARK: - Price Type Enum
enum PriceType: String, Codable {
    case free = "free"
    case paid = "paid"
    
    static func from(_ string: String?) -> PriceType {
        guard let string = string else { return .paid }
        return PriceType(rawValue: string) ?? .paid
    }
}

// MARK: - Purchase Manager
class PurchaseManager: ObservableObject {
    static let shared = PurchaseManager()
    
    @Published private(set) var isProUnlocked = false
    
    // 存储Key
    private let proUnlockKey = "com.voicingtrainer.prounlock"
    
    private init() {
        // 从UserDefaults加载购买状态
        isProUnlocked = UserDefaults.standard.bool(forKey: proUnlockKey)
        print("🛒 PurchaseManager initialized - Pro unlocked: \(isProUnlocked)")
    }
    
    func unlockPro() {
        DispatchQueue.main.async {
            self.isProUnlocked = true
            UserDefaults.standard.set(true, forKey: self.proUnlockKey)
            print("🛒 Pro version unlocked!")
        }
    }
    
    func lockPro() {
        DispatchQueue.main.async {
            self.isProUnlocked = false
            UserDefaults.standard.set(false, forKey: self.proUnlockKey)
            print("🛒 Pro version locked (for testing)")
        }
    }
    
    // 检查某个项目是否可用
    func isItemAvailable(priceType: PriceType) -> Bool {
        switch priceType {
        case .free:
            return true
        case .paid:
            return isProUnlocked
        }
    }
    
    // 检查某个项目是否可用（字符串版本）
    func isItemAvailable(priceTypeString: String?) -> Bool {
        let priceType = PriceType.from(priceTypeString)
        return isItemAvailable(priceType: priceType)
    }
    
    // 获取锁定状态描述
    func getLockDescription(priceType: PriceType) -> String? {
        switch priceType {
        case .free:
            return nil
        case .paid:
            return isProUnlocked ? nil : "解锁Pro版可用"
        }
    }
    
    // 获取锁定状态描述（字符串版本）
    func getLockDescription(priceTypeString: String?) -> String? {
        let priceType = PriceType.from(priceTypeString)
        return getLockDescription(priceType: priceType)
    }
} 