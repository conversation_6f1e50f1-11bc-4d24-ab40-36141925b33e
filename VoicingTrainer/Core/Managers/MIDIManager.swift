//
//  MIDIEventType.swift
//  FullUI
//
//  Created by <PERSON> Li on 2025/5/26.
//


import Foundation
import AudioKit
import AudioKitEX
import SwiftUI
import CoreMIDI
#if os(iOS)
import AVFoundation
#elseif os(macOS)
import AppKit
#endif

enum MIDIEventType {
    case none
    case noteOn
    case noteOff
    case continuousControl
    case programChange
}
struct MIDIMonitorData {
    var noteOn = 0
    var velocity = 0
    var noteOff = 0
    var channel = 0
    var afterTouch = 0
    var afterTouchNoteNumber = 0
    var programChange = 0
    var pitchWheelValue = 0
    var controllerNumber = 0
    var controllerValue = 0
}

enum SoundFont{
    case defaultSound
    case PianoSound
    case EPSound
}



class MIDIManager: ObservableObject, MIDIListener {
    @Published private(set) var pressedNotes: Set<Int> = []
    @Published var chordName: String = ""
    @Published var noteNames: String = ""
    @Published var chordNames: [String] = [] {
        didSet {
            if debugMIDI {
                print("🎵 chordNames 变化: \(oldValue) -> \(chordNames)")
            }
        }
    }
    @Published var sustainOn: Bool = false
    @Published var midiEventType: MIDIEventType = .none
    @Published var isShowingMIDIReceived: Bool = false
    @Published var isToggleOn: Bool = false
    @Published var oldControllerValue: Int = 0
    @Published var data = MIDIMonitorData()
    
    // 新增：延音音符集合（按键已松开但因踏板而延音的音符）
    private var sustainedNotes: Set<Int> = []
    
    // 新增：存储每个音符的velocity信息
    @Published private(set) var noteVelocities: [Int: Int] = [:]
    
    // MIDI 设备选择相关
    @Published var availableInputDevices: [String] = []
    @Published var availableOutputDevices: [String] = []
    @Published var selectedInputDevices: Set<String> = []
    @Published var selectedOutputDevice: String = "Built-in Sound" // 改为单选

    // 🔧 现代化MIDI设备管理 - 使用设备ID而不是名称
    private var availableOutputDeviceInfo: [(name: String, endpoint: MIDIEndpointRef)] = []
    private var selectedOutputDeviceEndpoint: MIDIEndpointRef = 0

    // MIDI 输出相关
    private var midiOutputEndpoint: MIDIEndpointRef = 0
    
    // 🛡️ 防止重复初始化的保护机制
    private var isInitializingDevice = false
    private var lastInitializedDevice: String = ""
    private var initializationTimer: Timer?
    
    private var engine = AudioEngine()
    private var playerSampler = MIDISampler(name: "PlayerPiano")  // 玩家按键专用
    private var listenSampler = MIDISampler(name: "ListenPiano")  // 听音符专用
    private let midi = MIDI()
    private let queue = DispatchQueue(label: "com.pianodojo.midi")
    private var voice:SoundFont = SoundFont.PianoSound
    private let pianoSF = "piano"
    private let epSF = "ep"
    private let debugMIDI = false    //是否开启调试本模块
    
    // 🛡️ 测试音符控制（可通过调试关闭以防止循环）
    private var enableTestNote = true
    
    // 🛠️ 扫描频率控制
    private var lastScanTime: Date = Date.distantPast
    private let minimumScanInterval: TimeInterval = 2.0  // 最小扫描间隔2秒

    // 🎛️ MIDI设备变化监听
    private var deviceChangeTimer: Timer?
    private var isMonitoringDeviceChanges = false
    private var previousInputDevices: [String] = []
    private var previousOutputDevices: [String] = []
    
    // Use shared configuration manager to access enableMIDIOutput setting
    private let configManager = GameConfigManager.shared
    
    // 键盘视图模型的弱引用，用于同步MIDI输入状态
    weak var keyboardViewModel: PianoKeyboardViewModel?
    

    
    init() {
        setupAudioSession()
        setupAudioKit()
        setupMIDI()
        setupAppLifecycleObservers()
        setupPowerManagement()

        // 初始化时同步选中的设备状态
        DispatchQueue.main.async {
            self.syncSelectedDevices()
        }

        // 🎛️ 启动MIDI设备变化监听
        startDeviceChangeMonitoring()
    }
    
    // 同步选中的设备状态
    private func syncSelectedDevices() {
        // 从配置加载保存的设备设置
        let savedInputDevices = Set(configManager.config.midiSettings.selectedInputDevices)
        let savedOutputDevice = configManager.config.midiSettings.selectedOutputDevice
        
        // 如果配置中有保存的输入设备，使用保存的设置
        if !savedInputDevices.isEmpty {
            selectedInputDevices = savedInputDevices
            print("🎹 从配置加载输入设备: \(savedInputDevices)")
        } else if !availableInputDevices.isEmpty {
            // 否则默认选中所有可用设备
            selectedInputDevices = Set(availableInputDevices)
            print("🎹 默认选中所有输入设备: \(selectedInputDevices)")
        }
        
        // 设置输出设备
        selectedOutputDevice = savedOutputDevice
        print("🎹 从配置加载输出设备: \(savedOutputDevice)")
        
        // 设置默认输出设备
        setupMIDIOutput()
        
        print("🎹 同步设备状态 - 输入设备: \(selectedInputDevices.count), 输出设备: \(selectedOutputDevice)")
    }
    
    #if os(iOS)
    private func setupAudioSession() {
        // 🔧 修复: 使用统一的音频会话协调器，避免与SoundEffectManager冲突
        // 注意：需要确保AudioSessionCoordinator可用，否则使用原始方法
        do {
            let session = AVAudioSession.sharedInstance()
            try session.setCategory(.playAndRecord, mode: .default, options: [.defaultToSpeaker, .allowBluetooth, .mixWithOthers])
            try session.setActive(true)
            print("✅ MIDIManager: 音频会话设置成功")
        } catch {
            print("❌ MIDIManager: 音频会话设置失败: \(error)")
        }
    }
    #else
    private func setupAudioSession() {}
    #endif
    
    private func setupAudioKit() {
        // 创建混音器来混合两个Sampler的输出
        let mixer = Mixer(playerSampler, listenSampler)
        engine.output = mixer
        
        do {
            // 为两个Sampler加载相同的音色
            
            let sfname = configManager.config.audioSettings.soundFontName
            
            if let _ = Bundle.main.url(forResource:sfname , withExtension: "sf2") {
              //UprightPiano
                
                print("load sopundfont:\(sfname)")
                
                try playerSampler.loadSoundFont(sfname, preset: 0, bank: 0)
                try listenSampler.loadSoundFont(sfname, preset: 0, bank: 0)
                if self.debugMIDI{
                    print("✅ SoundFont loaded successfully")
                }
            } else {
                if self.debugMIDI{
                    print("⚠️ UprightPiano.sf2 not found in bundle")
                }
            }
            try engine.start()
            if self.debugMIDI{
                print("✅ AudioEngine started successfully")
            }
        } catch {
            print("❌ AudioKit error: \(error)")
        }
    }
    
    private func setupMIDI() {
        print("🎹 开始设置MIDI输入输出...")

        
        // 扫描并更新可用设备列表
        scanAvailableDevices()
        
        // 设置输入设备
        if selectedInputDevices.isEmpty {
            // 默认打开所有输入设备
            midi.openInput(name: "Bluetooth")
            midi.openInput()
            selectedInputDevices = Set(availableInputDevices)
        } else {
            // 打开选中的输入设备
            for deviceName in selectedInputDevices {
                midi.openInput(name: deviceName)
                print("🎹 打开MIDI输入设备: \(deviceName)")
            }
        }
        
        midi.addListener(self)
        
        // 设置输出设备 - 重要：必须打开输出端口才能发送MIDI消息
        setupMIDIOutputPort()
        
        print("🎹 MIDI输入输出设置完成")
    }
    
    // 🔧 混合方式：保持AudioKit连接 + 现代化端点引用
    private func setupMIDIOutputPort() {
        if selectedOutputDevice != "Built-in Sound" {
            // 🔧 步骤1：使用AudioKit建立高级连接（保持兼容性）
            // 注意：虽然有弃用警告，但AudioKit仍需要这个调用来建立连接
            midi.openOutput(name: selectedOutputDevice)
            print("🎹 AudioKit连接MIDI输出设备: \(selectedOutputDevice)")

            // 🔧 步骤2：同时获取端点引用用于直接MIDI消息发送
            setupMIDIEndpoint()
        } else {
            print("🎹 使用内建声音，不打开外部MIDI输出")
            selectedOutputDeviceEndpoint = 0
            midiOutputEndpoint = 0
        }
    }

    // 🔧 恢复端点设置方法，用于直接MIDI消息发送
    private func setupMIDIEndpoint() {
        // 优先使用缓存的设备信息
        if let deviceInfo = availableOutputDeviceInfo.first(where: { $0.name == selectedOutputDevice }) {
            selectedOutputDeviceEndpoint = deviceInfo.endpoint
            midiOutputEndpoint = deviceInfo.endpoint
            print("🎹 ✅ 使用缓存建立MIDI端点连接: \(selectedOutputDevice) (Endpoint: \(deviceInfo.endpoint))")

            // 设备连接后进行初始化
            initializeMIDIDevice()
        } else {
            // 回退到实时查找（兼容性保证）
            let destCount = MIDIGetNumberOfDestinations()
            midiOutputEndpoint = 0

            for i in 0..<destCount {
                let dest = MIDIGetDestination(i)
                var name: Unmanaged<CFString>?
                let result = MIDIObjectGetStringProperty(dest, kMIDIPropertyName, &name)

                if result == noErr, let deviceName = name?.takeRetainedValue() as String?, deviceName == selectedOutputDevice {
                    selectedOutputDeviceEndpoint = dest
                    midiOutputEndpoint = dest
                    print("🎹 ✅ 实时查找建立MIDI端点连接: \(deviceName) (Endpoint: \(dest))")

                    // 设备连接后进行初始化
                    initializeMIDIDevice()
                    break
                }
            }

            if midiOutputEndpoint == 0 {
                print("❌ 未找到MIDI输出端点: \(selectedOutputDevice)")
            }
        }
    }
    

    
    // 初始化 MIDI 设备
    private func initializeMIDIDevice() {
        // 🛡️ 防止重复初始化同一设备
        guard !isInitializingDevice else {
            print("⚠️ 设备正在初始化中，跳过重复请求")
            return
        }
        
        guard lastInitializedDevice != selectedOutputDevice else {
            print("⚠️ 设备 \(selectedOutputDevice) 已初始化，跳过重复初始化")
            return
        }
        
        // 取消之前的初始化定时器
        initializationTimer?.invalidate()
        initializationTimer = nil
        
        // 设置初始化状态
        isInitializingDevice = true
        lastInitializedDevice = selectedOutputDevice
        
        print("🎹 开始初始化MIDI设备: \(selectedOutputDevice)")
        
        // 等待一点时间让设备准备就绪
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            // 发送钢琴音色程序变更 (Program 0 = Acoustic Grand Piano)
            self.sendProgramChange(0, channel: 0)
            
            // 发送测试音符 C4 (MIDI 60) - 只在调试模式下发送
            if self.debugMIDI {
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
                    self.sendTestNote()
                    
                    // 设置3秒后重置初始化状态，允许再次初始化
                    self.initializationTimer = Timer.scheduledTimer(withTimeInterval: 3.0, repeats: false) { _ in
                        self.isInitializingDevice = false
                        print("🎹 设备初始化状态已重置")
                    }
                }
            } else {
                // 生产模式下立即重置状态
                self.isInitializingDevice = false
            }
        }
    }
    
    // 发送测试音符
    private func sendTestNote() {
        // 🛡️ 检查测试音符是否已启用
        guard enableTestNote else {
            print("🛡️ 测试音符已禁用，跳过发送")
            return
        }
        
        let testNote: UInt8 = 60 // C4
        let testVelocity: UInt8 = 80
        
        print("🎹 发送测试音符到 ww \(selectedOutputDevice)")
        
        // 发送 Note On
        sendMIDINoteOn(testNote, velocity: testVelocity)
        
        // 0.5秒后发送 Note Off
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            self.sendMIDINoteOff(testNote)
        }
    }
    
    // 🛡️ 紧急停止测试音符功能（用于调试时快速解决循环问题）
    func disableTestNote() {
        enableTestNote = false
        resetInitializationState()
        print("🛡️ 测试音符功能已禁用")
    }
    
    // 🛡️ 重新启用测试音符功能
    func enableTestNoteFunction() {
        enableTestNote = true
        print("🛡️ 测试音符功能已启用")
    }
    
    // 扫描可用的MIDI设备
    func scanAvailableDevices(force: Bool = false) {
        // 🛠️ 修复：控制扫描频率，避免过于频繁的扫描
        let now = Date()
        if !force && now.timeIntervalSince(lastScanTime) < minimumScanInterval {
            print("🎹 扫描过于频繁，跳过本次扫描 (距上次扫描 \(String(format: "%.1f", now.timeIntervalSince(lastScanTime)))秒)")
            return
        }
        
        lastScanTime = now
        print("🎹 开始扫描MIDI设备...")
        
        DispatchQueue.global(qos: .userInitiated).async {
            let inputDevices = self.scanMIDIInputDevices()
            let outputDevices = self.scanMIDIOutputDevices()
            
            DispatchQueue.main.async {
                // 🛠️ 修复：检查设备列表是否真正发生变化，避免不必要的更新
                let inputDevicesChanged = self.availableInputDevices != inputDevices
                let outputDevicesChanged = self.availableOutputDevices != outputDevices
                
                if inputDevicesChanged || outputDevicesChanged {
                    self.availableInputDevices = inputDevices
                    self.availableOutputDevices = outputDevices
                    
                    print("🎹 扫描到 \(inputDevices.count) 个MIDI输入设备")
                    for (index, device) in inputDevices.enumerated() {
                        print("🎹   输入[\(index)]: \(device)")
                    }
                    
                    print("🎹 扫描到 \(outputDevices.count) 个MIDI输出设备")
                    for (index, device) in outputDevices.enumerated() {
                        print("🎹   输出[\(index)]: \(device)")
                    }
                } else {
                    print("🎹 设备列表无变化，跳过更新")
                }
            }
        }
    }
    
    // 使用 CoreMIDI 扫描输入设备
    private func scanMIDIInputDevices() -> [String] {
        var devices: [String] = []
        let sourceCount = MIDIGetNumberOfSources()
        
        for i in 0..<sourceCount {
            let source = MIDIGetSource(i)
            var name: Unmanaged<CFString>?
            let result = MIDIObjectGetStringProperty(source, kMIDIPropertyName, &name)
            
            if result == noErr, let deviceName = name?.takeRetainedValue() as String? {
                devices.append(deviceName)
            }
        }
        
        return devices
    }
    
    // 🔧 现代化：扫描输出设备并收集端点信息
    private func scanMIDIOutputDevices() -> [String] {
        var devices: [String] = ["Built-in Sound"] // 添加内建声音选项
        var deviceInfo: [(name: String, endpoint: MIDIEndpointRef)] = []

        let destCount = MIDIGetNumberOfDestinations()

        for i in 0..<destCount {
            let dest = MIDIGetDestination(i)
            var name: Unmanaged<CFString>?
            let result = MIDIObjectGetStringProperty(dest, kMIDIPropertyName, &name)

            if result == noErr, let deviceName = name?.takeRetainedValue() as String? {
                devices.append(deviceName)
                deviceInfo.append((name: deviceName, endpoint: dest))
            }
        }

        // 🔧 更新设备信息缓存
        availableOutputDeviceInfo = deviceInfo

        return devices
    }
    
    // 更新选中的输入设备
    func updateSelectedInputDevices(_ devices: Set<String>) {
        print("🎹 更新选中的MIDI输入设备: \(devices)")
        selectedInputDevices = devices
        // 同时保存到配置
        configManager.updateMIDIInputDevices(Array(devices))
        reinitializeMIDI()
    }
    
    // 更新选中的输出设备（单选）
    func updateSelectedOutputDevice(_ device: String) {
        print("🎹 更新选中的MIDI输出设备: \(device)")
        
        // 🛡️ 如果设备实际改变了，重置初始化状态
        if selectedOutputDevice != device {
            lastInitializedDevice = "" // 重置以允许新设备初始化
            isInitializingDevice = false
            initializationTimer?.invalidate()
            initializationTimer = nil
        }
        
        selectedOutputDevice = device
        // 同时保存到配置
        configManager.updateMIDIOutputDevice(device)
        setupMIDIOutput()
    }
    
    // 设置 MIDI 输出（从UI调用）
    private func setupMIDIOutput() {
        setupMIDIOutputPort()
    }

    // 🎛️ 手动触发设备重连检查（供UI调用）
    func triggerDeviceReconnection() {
        print("🎛️ 手动触发设备重连检查...")

        // 强制扫描设备
        scanAvailableDevices(force: true)

        // 延迟一点时间让扫描完成，然后尝试重连
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            self.attemptAutoReconnection()
        }
    }
    
    // 发送 MIDI Note On 到外部设备
    private func sendMIDINoteOn(_ note: UInt8, velocity: UInt8, channel: UInt8 = 0) {
        guard midiOutputEndpoint != 0 else { 
            print("❌ 没有有效的MIDI输出端点")
            return 
        }
        
        let noteOnData: [UInt8] = [0x90 | channel, note, velocity] // Note On message
        let result = sendMIDIMessage(noteOnData)
        
        if result == noErr {
            if debugMIDI {
                print("✅ 发送 MIDI Note On: \(note) velocity: \(velocity) 到设备 \(selectedOutputDevice)")
            }
        } else {
            if debugMIDI {
                print("❌ 发送 MIDI Note On 失败: \(result)")
            }
        }
    }
    
    // 发送 MIDI Note Off 到外部设备
    private func sendMIDINoteOff(_ note: UInt8, channel: UInt8 = 0) {
        guard midiOutputEndpoint != 0 else { 
            print("❌ 没有有效的MIDI输出端点")
            return 
        }
        
        let noteOffData: [UInt8] = [0x80 | channel, note, 0] // Note Off message
        let result = sendMIDIMessage(noteOffData)
        
        if result == noErr {
            if debugMIDI {
                print("✅ 发送 MIDI Note Off: \(note) 到设备 \(selectedOutputDevice)")
            }
        } else {
            if debugMIDI {
                print("❌ 发送 MIDI Note Off 失败: \(result)")
            }
        }
    }
    
    // 通用 MIDI 消息发送方法
    private func sendMIDIMessage(_ data: [UInt8]) -> OSStatus {
        let dataCount = data.count
        let packetSize = MemoryLayout<MIDIPacketList>.size + dataCount
        let packet = UnsafeMutablePointer<MIDIPacketList>.allocate(capacity: 1)
        defer { packet.deallocate() }
        
        let packetPtr = MIDIPacketListInit(packet)
        let addResult = MIDIPacketListAdd(packet, packetSize, packetPtr, 0, dataCount, data)
        
        if addResult == nil {
            print("❌ 无法添加MIDI数据到包")
            return -1
        }
        
        let sendResult = MIDISend(midi.outputPort, midiOutputEndpoint, packet)
        
        if debugMIDI {
            print("🎹 MIDI消息详情: \(data.map { String(format: "%02X", $0) }.joined(separator: " "))")
            print("🎹 输出端口: \(midi.outputPort), 端点: \(midiOutputEndpoint)")
        }
        
        return sendResult
    }
    
    // 发送程序变更消息（选择乐器）
    private func sendProgramChange(_ program: UInt8, channel: UInt8 = 0) {
        guard midiOutputEndpoint != 0 else { return }
        
        let programChangeData: [UInt8] = [0xC0 | channel, program] // Program Change message
        let result = sendMIDIMessage(programChangeData)
        
        if result == noErr {
            print("✅ 发送程序变更: \(program) 到设备 \(selectedOutputDevice)")
        } else {
            print("❌ 发送程序变更失败: \(result)")
        }
    }
    
    // 重新初始化MIDI
    func reinitializeMIDI() {
        print("🎹 重新初始化MIDI设备")
        
        // 🛡️ 重置初始化保护状态
        resetInitializationState()
        
        // 关闭所有当前的MIDI连接
        midi.closeAllInputs()
        
        // 重新设置MIDI
        setupMIDI()
    }
    
    // 🛡️ 重置初始化状态（用于调试和故障恢复）
    private func resetInitializationState() {
        initializationTimer?.invalidate()
        initializationTimer = nil
        isInitializingDevice = false
        lastInitializedDevice = ""
        print("🛡️ 初始化保护状态已重置")
    }

    // MARK: - 🎛️ MIDI设备变化监听

    /// 启动MIDI设备变化监听
    private func startDeviceChangeMonitoring() {
        guard !isMonitoringDeviceChanges else { return }

        isMonitoringDeviceChanges = true
        print("🎛️ 启动MIDI设备变化监听")

        // 记录初始设备状态
        previousInputDevices = availableInputDevices
        previousOutputDevices = availableOutputDevices

        // 每3秒检查一次设备变化
        deviceChangeTimer = Timer.scheduledTimer(withTimeInterval: 3.0, repeats: true) { [weak self] _ in
            self?.checkForDeviceChanges()
        }
    }

    /// 停止MIDI设备变化监听
    private func stopDeviceChangeMonitoring() {
        guard isMonitoringDeviceChanges else { return }

        isMonitoringDeviceChanges = false
        deviceChangeTimer?.invalidate()
        deviceChangeTimer = nil
        print("🎛️ 停止MIDI设备变化监听")
    }

    /// 检查设备变化并自动重连
    private func checkForDeviceChanges() {
        // 扫描当前可用设备
        let currentInputDevices = scanMIDIInputDevices()
        let currentOutputDevices = scanMIDIOutputDevices()

        // 检查是否有设备变化
        let inputDevicesChanged = currentInputDevices != previousInputDevices
        let outputDevicesChanged = currentOutputDevices != previousOutputDevices

        if inputDevicesChanged || outputDevicesChanged {
            print("🎛️ 检测到MIDI设备变化")
            print("🎛️   输入设备: \(previousInputDevices.count) -> \(currentInputDevices.count)")
            print("🎛️   输出设备: \(previousOutputDevices.count) -> \(currentOutputDevices.count)")

            // 更新设备列表
            DispatchQueue.main.async {
                self.availableInputDevices = currentInputDevices
                self.availableOutputDevices = currentOutputDevices

                // 尝试自动重连设置中的设备
                self.attemptAutoReconnection()
            }

            // 更新记录的设备状态
            previousInputDevices = currentInputDevices
            previousOutputDevices = currentOutputDevices
        }
    }

    /// 尝试自动重连用户设置的MIDI设备
    private func attemptAutoReconnection() {
        print("🎛️ 尝试自动重连MIDI设备...")

        // 获取用户设置的设备
        let savedInputDevices = Set(configManager.config.midiSettings.selectedInputDevices)
        let savedOutputDevice = configManager.config.midiSettings.selectedOutputDevice

        var reconnectedInputs: [String] = []
        var reconnectedOutput: String? = nil

        // 检查输入设备
        for savedDevice in savedInputDevices {
            if availableInputDevices.contains(savedDevice) {
                reconnectedInputs.append(savedDevice)
                print("🎛️ ✅ 发现设置的输入设备: \(savedDevice)")
            } else {
                print("🎛️ ❌ 设置的输入设备未找到: \(savedDevice)")
            }
        }

        // 检查输出设备
        if availableOutputDevices.contains(savedOutputDevice) {
            reconnectedOutput = savedOutputDevice
            print("🎛️ ✅ 发现设置的输出设备: \(savedOutputDevice)")
        } else {
            print("🎛️ ❌ 设置的输出设备未找到: \(savedOutputDevice)")
        }

        // 如果有可重连的设备，执行重连
        if !reconnectedInputs.isEmpty || reconnectedOutput != nil {
            print("🎛️ 执行自动重连...")

            // 更新选中的设备
            if !reconnectedInputs.isEmpty {
                selectedInputDevices = Set(reconnectedInputs)
            }

            if let outputDevice = reconnectedOutput {
                selectedOutputDevice = outputDevice
            }

            // 重新初始化MIDI连接
            reinitializeMIDI()

            print("🎛️ ✅ 自动重连完成")
            print("🎛️   重连输入设备: \(reconnectedInputs)")
            if let output = reconnectedOutput {
                print("🎛️   重连输出设备: \(output)")
            }
        } else {
            print("🎛️ 没有可重连的设备")
        }
    }
    
    private func setupAppLifecycleObservers() {
        #if os(iOS)
        // 监听应用失去焦点
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(appWillResignActive),
            name: UIApplication.willResignActiveNotification,
            object: nil
        )
        
        // 监听应用进入后台
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(appDidEnterBackground),
            name: UIApplication.didEnterBackgroundNotification,
            object: nil
        )
        #elseif os(macOS)
        // 监听macOS应用失去焦点
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(appWillResignActive),
            name: NSApplication.willResignActiveNotification,
            object: nil
        )
        #endif
    }
    
    @objc private func appWillResignActive() {
        if self.debugMIDI{
            print("App will resign active - stopping all notes")
        }
        resetAllNotes()
    }
    
    #if os(iOS)
    @objc private func appDidEnterBackground() {
        print("App did enter background - stopping all notes")
        resetAllNotes()
    }
    #endif
    
    deinit {
        stopDeviceChangeMonitoring()
        NotificationCenter.default.removeObserver(self)
    }
    
    // MARK: - Power Management
    private func setupPowerManagement() {
        // 监听功耗管理通知
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleBackgroundMode),
            name: .powerManagerDidEnterBackgroundMode,
            object: nil
        )
        
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleForegroundMode),
            name: .powerManagerDidEnterForegroundMode,
            object: nil
        )
        
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handlePowerSaving),
            name: .powerManagerDidEnablePowerSaving,
            object: nil
        )
        
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleCriticalPowerSaving),
            name: .powerManagerDidEnableCriticalPowerSaving,
            object: nil
        )
        
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleThermalPressure),
            name: .powerManagerDidDetectThermalPressure,
            object: nil
        )
        
        // 通知PowerManager MIDI活动状态
        PowerManager.shared.setMIDIActive(true)
    }
    
    @objc private func handleBackgroundMode() {
        if self.debugMIDI{
            print("🎹 MIDIManager - Entering background mode")
        }
        // 降低MIDI处理优先级
        queue.suspend()

        // 停止所有音符避免后台音频
        resetAllNotes()

        // 🎛️ 暂停设备监听以节省电量
        stopDeviceChangeMonitoring()

        // 通知PowerManager MIDI不活跃
        PowerManager.shared.setMIDIActive(false)
    }
    
    @objc private func handleForegroundMode() {
        if self.debugMIDI{
            print("🎹 MIDIManager - Entering foreground mode")
        }
        // 恢复MIDI处理
        queue.resume()

        // 🎛️ 恢复设备监听
        startDeviceChangeMonitoring()

        // 通知PowerManager MIDI活跃
        PowerManager.shared.setMIDIActive(true)
    }
    
    @objc private func handlePowerSaving() {
        MIDILog("Enabling power saving mode", level: .info)
        // 降低音频质量以节省功耗
        // 可以考虑降低采样率或减少polyphony
    }
    
    @objc private func handleCriticalPowerSaving() {
        if self.debugMIDI{
            print("🎹 MIDIManager - Enabling critical power saving mode")
        }
        // 关键省电模式：暂停非必要的MIDI处理
        resetAllNotes()
        
        // 可以考虑暂时关闭音频引擎
        if engine.avEngine.isRunning {
            engine.stop()
            print("🎹 AudioEngine stopped for critical power saving")
        }
    }
    
    @objc private func handleThermalPressure() {
        if self.debugMIDI{
            print("🎹 MIDIManager - Handling thermal pressure")
        }
        // 热压力处理：减少CPU密集型操作
        resetAllNotes()
        
        // 降低音频处理负载
        if engine.avEngine.isRunning {
            engine.stop()
            if self.debugMIDI{
                print("🎹 AudioEngine stopped due to thermal pressure")
            }
        }
    }
    
    func stop() {
        if self.debugMIDI{
            print("🎹 关闭所有MIDI输入")
        }
        midi.closeAllInputs()
    }
    
    // MARK: - MIDIListener Protocol
    func receivedMIDINoteOn(noteNumber: MIDINoteNumber,
                           velocity: MIDIVelocity,
                           channel: MIDIChannel,
                           portID: MIDIUniqueID?,
                           timeStamp: MIDITimeStamp?) {
        if self.debugMIDI{
            print("🎹 收到MIDI Note On: \(noteNumber) velocity: \(velocity) channel: \(channel)")
        }
        noteOn(Int(noteNumber), velocity: Int(velocity))
    }
    
    func receivedMIDINoteOff(noteNumber: MIDINoteNumber,
                            velocity: MIDIVelocity,
                            channel: MIDIChannel,
                            portID: MIDIUniqueID?,
                            timeStamp: MIDITimeStamp?) {
        if self.debugMIDI{
            print("🎹 收到MIDI Note Off: \(noteNumber) velocity: \(velocity) channel: \(channel)")
        }
        noteOff(Int(noteNumber))
    }
    
    func receivedMIDIController(_ controller: MIDIByte,
                                   value: MIDIByte,
                                   channel: MIDIChannel,
                                   portID _: MIDIUniqueID?,
                                   timeStamp _: MIDITimeStamp?)
       {
           if self.debugMIDI{
               print("🎛️ 收到MIDI Controller: \(controller) value: \(value) channel: \(channel)")
           }
           
           if controller == 64 && value == 127{
               print("🦶 ✅ 收到踏板按下事件 (CC64=127)")
               DispatchQueue.main.async {
                   self.sustainOn = true
                   print("🦶 ✅ sustainOn 设置为 true")
               }
               
               // 只有选择内建声音时才发送踏板事件到 sampler
               if selectedOutputDevice == "Built-in Sound" {
                   playerSampler.midiCC(64, value: 127, channel: 0)
                   print("🦶 发送踏板按下到 playerSampler")
               } else {
                   print("🦶 使用外部设备，不处理应用内踏板延音")
               }
           }
           if controller == 64 && value == 0 {
               print("🦶 ✅ 收到踏板松开事件 (CC64=0)")
               DispatchQueue.main.async {
                   self.sustainOn = false
                   print("🦶 ✅ sustainOn 设置为 false")
                   
                   // 🔧 新增：停止所有延音音符
                   self.stopAllSustainedNotes()
               }
               
               // 只有选择内建声音时才发送踏板事件到 sampler
               if selectedOutputDevice == "Built-in Sound" {
                   playerSampler.midiCC(64, value: 0, channel: 0)
                   print("🦶 发送踏板松开到 playerSampler")
               } else {
                   print("🦶 使用外部设备，不处理应用内踏板延音")
               }
           }

           
           DispatchQueue.main.async {
               
               self.midiEventType = .continuousControl
               self.isShowingMIDIReceived = true
               self.data.controllerNumber = Int(controller)
               self.data.controllerValue = Int(value)
               self.oldControllerValue = Int(value)
               self.data.channel = Int(channel)
               if self.oldControllerValue == Int(value) {
                   // Fade out the MIDI received indicator.
                   DispatchQueue.main.async {
                       withAnimation(.easeOut(duration: 0.4)) {
                           self.isShowingMIDIReceived = false
                       }
                   }
               }
               // Show the solid color indicator when the CC value is toggled from 0 to 127
               // Otherwise toggle it off when the CC value is toggled from 127 to 0
               // Useful for stomp box and on/off UI toggled states
               if value == 127 {
                   DispatchQueue.main.async {
                       self.isToggleOn = true
                       
                   }
               } else {
                   // Fade out the Toggle On indicator.
                   DispatchQueue.main.async {
                       self.isToggleOn = false
                   }
               }
           }
       }

    

    
    // Required protocol stubs
    func receivedMIDIAftertouch(noteNumber: MIDINoteNumber,
                               pressure: MIDIByte,
                               channel: MIDIChannel,
                               portID: MIDIUniqueID?,
                               timeStamp: MIDITimeStamp?) {}
    
    func receivedMIDIAftertouch(_ pressure: MIDIByte,
                               channel: MIDIChannel,
                               portID: MIDIUniqueID?,
                               timeStamp: MIDITimeStamp?) {}
    
    func receivedMIDIPitchWheel(_ pitchWheelValue: MIDIWord,
                               channel: MIDIChannel,
                               portID: MIDIUniqueID?,
                               timeStamp: MIDITimeStamp?) {}
    
    func receivedMIDIProgramChange(_ program: MIDIByte,
                                  channel: MIDIChannel,
                                  portID: MIDIUniqueID?,
                                  timeStamp: MIDITimeStamp?) {}
    
    func receivedMIDISystemCommand(_ data: [MIDIByte],
                                  portID: MIDIUniqueID?,
                                  timeStamp: MIDITimeStamp?) {}
    
    func receivedMIDISetupChange() {
        //print("receivedMIDISetupChange")
        
    }
    
    func receivedMIDIPropertyChange(propertyChangeInfo: MIDIObjectPropertyChangeNotification) {}
    
    func receivedMIDINotification(notification: MIDINotification) {
        // 🔧 BugFix: 添加调试信息，看看是否频繁调用导致chordNames被清空
        if self.debugMIDI {
            print("🎹 receivedMIDINotification - 将重置所有音符和和弦名称")
        }
        resetAllNotes()
    }
    
    // MARK: - Note Handling
    
    func noteOn(_ midi: Int, velocity: Int = 100) {
        if self.debugMIDI{
            print("note on:\(midi) velocity:\(velocity)")
        }
        queue.async {
            // 使用sync进行检查，确保原子性
            var shouldUpdate = false
            DispatchQueue.main.sync {
                if !self.pressedNotes.contains(midi) {
                    shouldUpdate = true
                }
            }
            
            guard shouldUpdate else {
                if self.debugMIDI {
                    print("note \(midi) already pressed, skipping")
                }
                return
            }
            
            // 统一在主线程更新状态
            DispatchQueue.main.async {
                // 🔧 BugFix: 原子性地同时更新pressedNotes和noteVelocities
                self.noteVelocities[midi] = velocity
                self.pressedNotes.insert(midi)
                
                // 🔧 MIDI-Keyboard Sync: 同步更新键盘视图模型的用户按键状态
                self.keyboardViewModel?.userPressKey(midi)
                
                // 🔧 Circle12-MIDI Sync: 发送MIDINoteOn通知给Circle12NotesView
                NotificationCenter.default.post(
                    name: NSNotification.Name("MIDINoteOn"),
                    object: nil,
                    userInfo: ["note": midi, "velocity": velocity]
                )
                
                if self.debugMIDI{
                    print("note\(midi) on velocity:\(velocity) - stored before pressedNotes update")
                }
                self.updateChordInfo()
            }
            
            // 根据选择的输出设备播放音符
            if self.selectedOutputDevice == "Built-in Sound" {
                // 只有选择内建声音时才使用 PlayerSampler 播放用户输入
                self.playerSampler.play(noteNumber: MIDINoteNumber(midi), velocity: MIDIVelocity(velocity), channel: 0)
                if self.debugMIDI{
                    print("🎹 使用内建声音播放用户输入音符: \(midi)")
                }
            } else {
                // 选择外部设备时，用户输入不播放内建声音，只发送到外部设备
                //  输bug0629:输入来自外设,不应该发送给外部设备
                /*
                self.sendMIDINoteOn(UInt8(midi), velocity: UInt8(velocity))
                if self.debugMIDI{
                    print("🎹 发送用户输入音符到外部设备: \(midi)")
                }
                 */
                
            }
        }//queue.async
    }
    
    // MARK: - Virtual Keyboard Interaction (虚拟键盘点击，播放声音但不影响pressedNotes状态)
    
    /// 虚拟键盘按键按下（播放声音，但不更新pressedNotes状态）
    /// - Parameters:
    ///   - midi: MIDI音符号
    ///   - velocity: 力度值
    func playVirtualKeyDown(_ midi: Int, velocity: Int = 100) {
        queue.async {
            // 🔧 使用配置中的playbackVolume值
            let playbackVelocity = Int(self.configManager.config.audioSettings.playbackVolume)
            
            if self.debugMIDI{
                print("🎹 虚拟键盘按下: \(midi) velocity: \(velocity) -> playbackVolume: \(playbackVelocity)")
            }
            
            // 根据选择的输出设备播放音符
            if self.selectedOutputDevice == "Built-in Sound" {
                // 使用内建声音播放
                if self.debugMIDI{
                    print("🎹 虚拟键盘使用内建声音播放音符: \(midi) volume: \(playbackVelocity)")
                }
                self.playerSampler.play(noteNumber: MIDINoteNumber(midi), velocity: MIDIVelocity(playbackVelocity), channel: 0)
            } else {
                // 发送 MIDI 到外部设备
                if self.debugMIDI{
                    print("🎹 虚拟键盘发送 MIDI 到外部设备: \(self.selectedOutputDevice) volume: \(playbackVelocity)")
                }
                self.sendMIDINoteOn(UInt8(midi), velocity: UInt8(playbackVelocity))
            }
        }
    }
    
    /// 虚拟键盘按键释放（停止声音，但不更新pressedNotes状态）
    /// - Parameter midi: MIDI音符号
    func playVirtualKeyUp(_ midi: Int) {
        queue.async {
            if self.debugMIDI{
                print("🎹 虚拟键盘释放: \(midi)")
            }
            
            // 根据选择的输出设备停止音符
            if self.selectedOutputDevice == "Built-in Sound" {
                // 停止内建声音
                if self.debugMIDI{
                    print("🎹 停止虚拟键盘内建声音: \(midi)")
                }
                self.playerSampler.stop(noteNumber: MIDINoteNumber(midi), channel: 0)
            } else {
                // 发送 MIDI Note Off 到外部设备
                if self.debugMIDI{
                    print("🎹 发送虚拟键盘 Note Off 到外部设备: \(self.selectedOutputDevice)")
                }
                self.sendMIDINoteOff(UInt8(midi))
            }
        }
    }
    
    // MARK: - Game Audio Playback (不影响pressedNotes状态)
    
    func playListenNote(_ midi: Int, velocity: UInt8 = 100) {
        queue.async {
            // 🔧 使用配置中的playbackVolume值，而不是传入的velocity参数
            let playbackVelocity = Int(self.configManager.config.audioSettings.playbackVolume)
            
            if self.debugMIDI{
                print("Playing listen note: \(midi) velocity: \(velocity) -> playbackVolume: \(playbackVelocity)")
            }
            
            // 根据选择的输出设备播放游戏音频
            if self.selectedOutputDevice == "Built-in Sound" {
                // 使用内建声音播放
                if self.debugMIDI{
                    print("使用内建声音播放音符: \(midi) volume: \(playbackVelocity)")
                }
                self.listenSampler.play(noteNumber: MIDINoteNumber(midi), velocity: MIDIVelocity(playbackVelocity), channel: 0)
            } else {
                // 发送 MIDI 到外部设备
                if self.debugMIDI{
                    print("发送 MIDI 到外部设备: \(self.selectedOutputDevice) volume: \(playbackVelocity)")
                }
                self.sendMIDINoteOn(UInt8(midi), velocity: UInt8(playbackVelocity))
            }
        }
    }
    
    func stopListenNote(_ midi: Int) {
        queue.async {
            if self.debugMIDI{
                print("Stopping listen note: \(midi)")
            }
            
            // 根据选择的输出设备停止游戏音频
            if self.selectedOutputDevice == "Built-in Sound" {
                // 停止内建声音
                if self.debugMIDI{
                    print("停止内建声音: \(midi)")
                }
                self.listenSampler.stop(noteNumber: MIDINoteNumber(midi), channel: 0)
            } else {
                // 发送 MIDI Note Off 到外部设备
                if self.debugMIDI{
                    print("发送 MIDI Note Off 到外部设备: \(self.selectedOutputDevice)")
                }
                self.sendMIDINoteOff(UInt8(midi))
            }
        }
    }
    
    func noteOff(_ midi: Int) {
        
        if self.debugMIDI{
            print("noteoff enter")
        }
        
        queue.async {
            // 使用sync进行检查，确保原子性，与noteOn保持一致
            var shouldUpdate = false
            DispatchQueue.main.sync {
                if self.pressedNotes.contains(midi) {
                    shouldUpdate = true
                }
            }
            
            guard shouldUpdate else {
                if self.debugMIDI{
                    print("note off but not found:\(midi)!!!!!!!!!!!!")
                }
                return
            }
            
            if self.debugMIDI{
                print("async noteoff press cp1 \(self.pressedNotes)")
            }

            // 统一在主线程更新状态
            DispatchQueue.main.async {
                if self.debugMIDI{
                    print("🎹 noteoff press cp3 \(self.pressedNotes) remove:\(midi)")
                    print("🎹 踏板状态 sustainOn: \(self.sustainOn)")
                }
                
                // 根据选择的输出设备处理音符停止
                if self.selectedOutputDevice == "Built-in Sound" {
                    // 只有选择内建声音时才处理 PlayerSampler 的踏板延音逻辑
                    if !self.sustainOn {
                        // 踏板没按下，立即停止
                        if self.debugMIDI {
                            print("🎹 立即停止内建声音音符: \(midi)")
                        }
                        self.playerSampler.stop(noteNumber: MIDINoteNumber(midi), channel: 0)
                    } else {
                        // 踏板按下，加入延音集合
                        if self.debugMIDI {
                            print("🎹 音符加入延音集合: \(midi)")
                        }
                        self.sustainedNotes.insert(midi)
                    }
                } else {
                    // 选择外部设备时，发送 Note Off 到外部设备
                    // 外部设备自己处理踏板延音逻辑
                    //  输bug0629:输入来自外设,不应该发送给外部设备
                    //  输入来自外设,不应该发送给外部设备:
                    /*
                    self.sendMIDINoteOff(UInt8(midi))
                    if self.debugMIDI {
                        print("🎹 发送用户输入 Note Off 到外部设备: \(midi)")
                    }
                    */
                }

                self.pressedNotes.remove(midi)
                // 清除音符的velocity信息
                self.noteVelocities.removeValue(forKey: midi)
                
                // 🔧 MIDI-Keyboard Sync: 同步更新键盘视图模型的用户按键状态
                self.keyboardViewModel?.userReleaseKey(midi)
                
                // 🔧 Circle12-MIDI Sync: 发送MIDINoteOff通知给Circle12NotesView
                NotificationCenter.default.post(
                    name: NSNotification.Name("MIDINoteOff"),
                    object: nil,
                    userInfo: ["note": midi]
                )
                
                if self.debugMIDI{
                    print("🎹 noteoff press cp4 \(self.pressedNotes)")
                    print("🎹 延音集合: \(self.sustainedNotes)")
                }
                self.updateChordInfo()
            }
        }
    }
    
    // MARK: - Sustain Pedal Handling
    
    /// 停止所有延音音符
    private func stopAllSustainedNotes() {
        if debugMIDI {
            print("🦶 停止所有延音音符: \(sustainedNotes)")
        }
        
        for note in sustainedNotes {
            // 根据选择的输出设备停止延音音符
            if selectedOutputDevice == "Built-in Sound" {
                // 只有选择内建声音时才停止 PlayerSampler
                playerSampler.stop(noteNumber: MIDINoteNumber(note), channel: 0)
                if debugMIDI {
                    print("🦶 停止内建声音延音音符: \(note)")
                }
            } else {
                // 选择外部设备时，发送 Note Off 到外部设备
                // 注意：踏板延音由外部设备自己处理，这里仅发送 Note Off
                sendMIDINoteOff(UInt8(note))
                if debugMIDI {
                    print("🦶 发送延音音符 Note Off 到外部设备: \(note)")
                }
            }
        }
        
        sustainedNotes.removeAll()
        
        if debugMIDI {
            print("🦶 延音音符集合已清空")
        }
    }
    
    func resetAllNotes() {
        if debugMIDI {
            print("🎹 resetAllNotes 被调用")
          //  print("🎹 调用堆栈: \(Thread.callStackSymbols.prefix(5))")
        }
        
        queue.async {
            // 根据选择的输出设备停止所有声音
            if self.selectedOutputDevice == "Built-in Sound" {
                // 只有选择内建声音时才停止 PlayerSampler
                // 停止所有玩家按键音符
                for note in self.pressedNotes {
                    self.playerSampler.stop(noteNumber: MIDINoteNumber(note), channel: 0)
                }
                
                // 停止所有延音音符
                for note in self.sustainedNotes {
                    self.playerSampler.stop(noteNumber: MIDINoteNumber(note), channel: 0)
                }
            } else {
                
                // 选择外部设备时，发送所有 Note Off 到外部设备
                /*
                for note in self.pressedNotes {
                    self.sendMIDINoteOff(UInt8(note))
                }
                
                for note in self.sustainedNotes {
                    self.sendMIDINoteOff(UInt8(note))
                }
                
                if self.debugMIDI {
                    print("🎹 发送所有 Note Off 到外部设备")
                }
                 */
                
            }
            
            // 停止所有听音符（游戏音频）- 总是停止 ListenSampler 以清理可能的遗留声音
            for note in 0...127 {
                self.listenSampler.stop(noteNumber: MIDINoteNumber(note), channel: 0)
            }
            
            DispatchQueue.main.async {
                // 🔧 BugFix: 添加调试信息以跟踪chordNames被清空的时机
                if self.debugMIDI {
                    print("🎹 resetAllNotes: 清空所有状态，包括chordNames")
                    print("🎹 resetAllNotes: 当前chordNames: \(self.chordNames) -> 即将清空")
                }
                
                self.pressedNotes.removeAll()
                self.sustainedNotes.removeAll()  // 新增：清空延音集合
                // 清除所有velocity信息
                self.noteVelocities.removeAll()
                
                // 🔧 BugFix: 清空updateChordInfo的缓存
                self.lastAnalyzedNotes.removeAll()
                self.lastChordNames.removeAll()
                
                // 🔧 MIDI-Keyboard Sync: 重置键盘视图模型的用户按键状态
                self.keyboardViewModel?.userReleaseAllKeys()
                
                self.chordName = ""
                self.noteNames = ""
                self.chordNames = [] // 🔧 BugFix: 清空和弦名称数组
                
                // 🔧 强制触发UI更新
                self.objectWillChange.send()
                
                if self.debugMIDI {
                    print("🎹 resetAllNotes: 所有状态已清空，包括分析缓存")
                }
            }
        }
    }
    
    // 防重复调用的缓存
    private var lastAnalyzedNotes: [Int] = []
    private var lastChordNames: [String] = []
    
    private func updateChordInfo() {
        // 🧪 记录代码修改以提醒测试
        recordCodeChange("MIDIManager.updateChordInfo")
        
        if debugMIDI {
            print("🎵 updateChordInfo 被调用")
         //   print("🎵 调用堆栈: \(Thread.callStackSymbols.prefix(5))")
        }
        
        let sorted = Array(pressedNotes).sorted()
        
        // 🔧 BugFix: 移除缓存检查，确保每次都能正确更新UI
        // 注释掉缓存机制，以确保UI能够正确响应每次状态变化
        // if sorted == lastAnalyzedNotes {
        //     if debugMIDI {
        //         print("🎵 updateChordInfo: 音符未变化，跳过重复分析 \(sorted)")
        //     }
        //     return
        // }
        
        var str=""
        for note in sorted {
            str += String(note)+" "
        }
        
        if debugMIDI{
            print("🎵 updateChordInfo 开始分析: \(str.trimmingCharacters(in: .whitespaces))")
            print("🎵 pressedNotes count: \(sorted.count), notes: \(sorted)")
        }
        
        let noteNamesStr = sorted.map { NoteNameGenerator.getNoteName(note: UInt8($0)) }.joined(separator: " ")
        
        // 🔧 BugFix: 详细调试ChordNameParser的调用
        if debugMIDI {
            print("🎵 调用 ChordNameParser.analyze(midiNotes: \(sorted))")
        }
        
        let names = ChordNameParser.analyze(midiNotes: sorted)
        
        // 🔧 更新缓存
        lastAnalyzedNotes = sorted
        lastChordNames = names
        
        // 🔧 BugFix: 只在状态真正改变时才更新@Published变量
        let namesChanged = self.chordNames != names
        let noteNamesChanged = self.noteNames != noteNamesStr
        
        if noteNamesChanged {
            self.noteNames = noteNamesStr
        }
        
        if namesChanged {
            if debugMIDI {
                print("🎵 chordNames 即将更新: \(self.chordNames) -> \(names)")
            }
            self.chordNames = names
        } else if debugMIDI {
            print("🎵 chordNames 未变化，跳过更新: \(names)")
        }
        
        print("🎵 miniManager: pressedNotes=\(sorted), chordNames=\(names)")
        
        // 🔧 Debug: 添加调试信息以追踪和弦名称更新
        if self.debugMIDI {
            print("🎵 ChordNameParser.analyze 返回: \(names)")
            print("🎵 updateChordInfo: pressedNotes=\(sorted), chordNames=\(names)")
            print("🎵 UI更新: chordNames已更新，当前线程: \(Thread.isMainThread ? "主线程" : "后台线程")")
            
            // 🔧 特别调试：检查为什么三个音符返回Unknown
            if sorted.count == 3 && names == ["Unknown"] {
                print("🚨 三音符和弦分析失败!")
                print("🚨 MIDI音符: \(sorted)")
                print("🚨 音符名称: \(noteNamesStr)")
                print("🚨 建议检查ChordNameParser的调试输出")
            }
        }
        
        // 🔧 只在状态真正改变时才强制触发UI更新
        if namesChanged || noteNamesChanged {
            // 🔧 强制触发UI更新 - 使用多种方式确保UI响应
            self.objectWillChange.send()
            
            // 🔧 BugFix: 立即强制UI更新，解决时序问题
            DispatchQueue.main.async {
                self.objectWillChange.send()
                if self.debugMIDI {
                    print("🎵 立即UI更新触发: chordNames=\(self.chordNames)")
                }
            }
            
            // 🔧 BugFix: 额外的延迟UI更新触发，确保SwiftUI响应
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.001) {
                self.objectWillChange.send()
                if self.debugMIDI {
                    print("🎵 延迟UI更新触发: chordNames=\(self.chordNames)")
                }
            }
        } else if debugMIDI {
            print("🎵 状态未变化，跳过UI更新触发")
        }
        
        if !names.isEmpty && names != ["Unknown"]
        {
            /*
            names.forEach{
                chordName in
                print("play Chord:\(chordName)")
            }
            
            
            for chordName in names {
                print("play Chord:\(chordName)")
            }
            */

        } else if debugMIDI {
            print("⚠️ ChordNameParser returned empty or Unknown result for notes: \(sorted)")
        }
        
    }
    
    // MARK: - Velocity Access
    
    /// 获取指定音符的velocity值
    /// - Parameter midiNote: MIDI音符号
    /// - Returns: velocity值，如果音符不存在则返回默认值100
    func getVelocity(for midiNote: Int) -> Int {
        let velocity = noteVelocities[midiNote] ?? 100
        if debugMIDI && velocity == 100 && pressedNotes.contains(midiNote) {
            print("⚠️ Warning: Using default velocity 100 for active note \(midiNote)")
            print("   noteVelocities: \(noteVelocities)")
            print("   pressedNotes: \(pressedNotes)")
        }
        return velocity
    }
}

