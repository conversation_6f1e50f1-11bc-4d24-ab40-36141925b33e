//
//  BuiltInProgressionManager.swift
//  VoicingTrainer
//
//  Created by AI Assistant on 2025/1/28.
//

import Foundation

/// 专门管理内置progression文件的加载器
class BuiltInProgressionManager: ObservableObject {
    
    // MARK: - Published Properties
    @Published var availableProgressions: [String] = []
    @Published var isLoading = false
    @Published var errorMessage: String?
    
    // MARK: - Private Properties
    private let fileManager = FileManager.default
    
    /// 内置progressions目录路径
    private var progressionsDirectoryURL: URL? {
        return Bundle.main.url(forResource: "progressions", withExtension: nil)
    }
    
    // MARK: - Initialization
    init() {
        scanProgressions()
    }
    
    // MARK: - Public Methods
    
    /// 扫描可用的progression文件
    func scanProgressions() {
        isLoading = true
        availableProgressions.removeAll()
        errorMessage = nil
        
        guard let directoryURL = progressionsDirectoryURL else {
            errorMessage = "未找到内置progressions目录"
            print("❌ 未找到内置progressions目录")
            isLoading = false
            return
        }
        
        print("🔍 扫描内置progressions目录: \(directoryURL.path)")
        
        do {
            let fileURLs = try fileManager.contentsOfDirectory(at: directoryURL, includingPropertiesForKeys: nil)
            let progressionFiles = fileURLs.filter { $0.pathExtension == "progression" }
            
            let fileNames = progressionFiles.map { $0.deletingPathExtension().lastPathComponent }
            
            DispatchQueue.main.async {
                self.availableProgressions = fileNames.sorted()
                self.isLoading = false
                print("✅ 发现 \(fileNames.count) 个内置progression文件: \(fileNames)")
            }
            
        } catch {
            DispatchQueue.main.async {
                self.errorMessage = "扫描失败: \(error.localizedDescription)"
                self.isLoading = false
                print("❌ 扫描内置progressions失败: \(error)")
            }
        }
    }
    
    /// 加载指定的progression文件
    /// - Parameter fileName: 文件名（不包含.progression扩展名）
    /// - Returns: 加载的Progression对象，失败返回nil
    func loadProgression(_ fileName: String) -> Progression? {
        guard let directoryURL = progressionsDirectoryURL else {
            print("❌ 未找到内置progressions目录")
            return nil
        }
        
        let fileURL = directoryURL.appendingPathComponent("\(fileName).progression")
        
        guard fileManager.fileExists(atPath: fileURL.path) else {
            print("❌ 文件不存在: \(fileURL.path)")
            return nil
        }
        
        do {
            let data = try Data(contentsOf: fileURL)
            let progressionFile = try JSONDecoder().decode(ProgressionFile.self, from: data)
            
            print("✅ 成功加载内置progression: \(progressionFile.progression.name)")
            return progressionFile.progression
            
        } catch {
            print("❌ 解析内置progression失败 \(fileName): \(error)")
            return nil
        }
    }
    
    /// 检查指定文件是否存在
    /// - Parameter fileName: 文件名（不包含.progression扩展名）
    /// - Returns: 文件是否存在
    func progressionExists(_ fileName: String) -> Bool {
        guard let directoryURL = progressionsDirectoryURL else { return false }
        let fileURL = directoryURL.appendingPathComponent("\(fileName).progression")
        return fileManager.fileExists(atPath: fileURL.path)
    }
    
    // MARK: - Debug Methods
    
    /// 调试方法：打印Bundle内容
    func debugBundleContents() {
        print("🔍 Bundle 主路径: \(Bundle.main.bundlePath)")
        
        if let progressionsURL = progressionsDirectoryURL {
            print("✅ 找到 progressions 目录: \(progressionsURL.path)")
            
            do {
                let contents = try fileManager.contentsOfDirectory(at: progressionsURL, includingPropertiesForKeys: nil)
                print("📁 progressions 目录内容 (\(contents.count) 个文件):")
                for file in contents {
                    print("  - \(file.lastPathComponent)")
                }
            } catch {
                print("❌ 无法读取 progressions 目录: \(error)")
            }
        } else {
            print("❌ 未找到 progressions 目录")
            
            // 列出 Bundle 根目录内容
            let bundleURL = URL(fileURLWithPath: Bundle.main.bundlePath)
            do {
                let contents = try fileManager.contentsOfDirectory(at: bundleURL, includingPropertiesForKeys: nil)
                print("📦 Bundle 根目录内容 (前10个):")
                for file in contents.prefix(10) {
                    print("  - \(file.lastPathComponent)")
                }
            } catch {
                print("❌ 无法读取 Bundle 根目录: \(error)")
            }
        }
    }
}