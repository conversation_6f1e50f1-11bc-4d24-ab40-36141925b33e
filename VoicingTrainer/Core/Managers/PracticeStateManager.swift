//
//  PracticeStateManager.swift
//  VoicingTrainer
//
//  Created by AI Assistant on 2025/7/28.
//

import Foundation

// MARK: - 练习状态数据结构

/// Note模式练习状态
struct NotePracticeState: Codable {
    let selectedLevelIndex: Int
    let currentRound: Int
    let totalRounds: Int
    let lastPracticeDate: Date
    
    init(levelIndex: Int, currentRound: Int = 0, totalRounds: Int = 12) {
        self.selectedLevelIndex = levelIndex
        self.currentRound = currentRound
        self.totalRounds = totalRounds
        self.lastPracticeDate = Date()
    }
}

/// Chord模式练习状态
struct ChordPracticeState: Codable {
    let selectedVoicingName: String
    let selectedChordIndex: Int
    let currentRound: Int
    let totalRounds: Int
    let lastPracticeDate: Date
    
    init(voicingName: String, chordIndex: Int = 0, currentRound: Int = 0, totalRounds: Int = 12) {
        self.selectedVoicingName = voicingName
        self.selectedChordIndex = chordIndex
        self.currentRound = currentRound
        self.totalRounds = totalRounds
        self.lastPracticeDate = Date()
    }
}

/// Progression模式练习状态
struct ProgressionPracticeState: Codable {
    let selectedProgressionName: String
    let workoutType: String // "just this key" 或 "All 12 keys"
    let currentTransposition: Int // 当前移调（12个调模式）
    let currentChordIndex: Int
    let isWorkoutMode: Bool
    let bpm: Int
    let playbackType: String // ChordPlaybackType的rawValue
    let lastPracticeDate: Date
    
    init(progressionName: String, workoutType: String = "just this key", 
         transposition: Int = 0, chordIndex: Int = 0, 
         isWorkoutMode: Bool = false, bpm: Int = 60, 
         playbackType: String = "block_chord") {
        self.selectedProgressionName = progressionName
        self.workoutType = workoutType
        self.currentTransposition = transposition
        self.currentChordIndex = chordIndex
        self.isWorkoutMode = isWorkoutMode
        self.bpm = bpm
        self.playbackType = playbackType
        self.lastPracticeDate = Date()
    }
}

/// 统一的练习状态
struct PracticeStates: Codable {
    var noteState: NotePracticeState?
    var chordState: ChordPracticeState?
    var progressionState: ProgressionPracticeState?
    
    init() {
        self.noteState = nil
        self.chordState = nil
        self.progressionState = nil
    }
}

// MARK: - 练习状态管理器

class PracticeStateManager: ObservableObject {
    static let shared = PracticeStateManager()
    
    @Published private(set) var practiceStates = PracticeStates()
    
    private let userDefaults = UserDefaults.standard
    private let stateKey = "PracticeStates"
    
    private init() {
        loadStates()
    }
    
    // MARK: - 保存和加载
    
    private func saveStates() {
        do {
            let data = try JSONEncoder().encode(practiceStates)
            userDefaults.set(data, forKey: stateKey)
            print("💾 练习状态已保存")
        } catch {
            print("❌ 保存练习状态失败: \(error)")
        }
    }
    
    private func loadStates() {
        guard let data = userDefaults.data(forKey: stateKey) else {
            print("📁 没有找到保存的练习状态，使用默认状态")
            return
        }
        
        do {
            practiceStates = try JSONDecoder().decode(PracticeStates.self, from: data)
            print("📁 练习状态加载成功")
        } catch {
            print("❌ 加载练习状态失败: \(error)")
            practiceStates = PracticeStates()
        }
    }
    
    // MARK: - Note模式状态管理
    
    func saveNoteState(levelIndex: Int, currentRound: Int = 0, totalRounds: Int = 12) {
        practiceStates.noteState = NotePracticeState(
            levelIndex: levelIndex,
            currentRound: currentRound,
            totalRounds: totalRounds
        )
        saveStates()
        print("🎵 Note模式状态已保存: Level \(levelIndex), Round \(currentRound)/\(totalRounds)")
    }
    
    func getNoteState() -> NotePracticeState? {
        return practiceStates.noteState
    }
    
    func hasNoteContinueState() -> Bool {
        guard let state = practiceStates.noteState else { return false }
        // 如果不是刚开始，就可以继续
        return state.currentRound > 0
    }
    
    // MARK: - Chord模式状态管理
    
    func saveChordState(voicingName: String, chordIndex: Int = 0, currentRound: Int = 0, totalRounds: Int = 12) {
        practiceStates.chordState = ChordPracticeState(
            voicingName: voicingName,
            chordIndex: chordIndex,
            currentRound: currentRound,
            totalRounds: totalRounds
        )
        saveStates()
        print("🎹 Chord模式状态已保存: \(voicingName), Chord \(chordIndex), Round \(currentRound)/\(totalRounds)")
    }
    
    func getChordState() -> ChordPracticeState? {
        return practiceStates.chordState
    }
    
    func hasChordContinueState() -> Bool {
        guard let state = practiceStates.chordState else { return false }
        // 如果不是刚开始，就可以继续
        return state.currentRound > 0 || state.selectedChordIndex > 0
    }
    
    // MARK: - Progression模式状态管理
    
    func saveProgressionState(progressionName: String, workoutType: String, 
                            transposition: Int = 0, chordIndex: Int = 0,
                            isWorkoutMode: Bool, bpm: Int, playbackType: String) {
        practiceStates.progressionState = ProgressionPracticeState(
            progressionName: progressionName,
            workoutType: workoutType,
            transposition: transposition,
            chordIndex: chordIndex,
            isWorkoutMode: isWorkoutMode,
            bpm: bpm,
            playbackType: playbackType
        )
        saveStates()
        print("🎼 Progression模式状态已保存: \(progressionName), \(workoutType)")
    }
    
    func getProgressionState() -> ProgressionPracticeState? {
        return practiceStates.progressionState
    }
    
    func hasProgressionContinueState() -> Bool {
        guard let state = practiceStates.progressionState else { return false }
        // 如果有选择的和弦进行，就可以继续
        return !state.selectedProgressionName.isEmpty
    }
    
    // MARK: - 清除状态
    
    func clearNoteState() {
        practiceStates.noteState = nil
        saveStates()
        print("🗑️ Note模式状态已清除")
    }
    
    func clearChordState() {
        practiceStates.chordState = nil
        saveStates()
        print("🗑️ Chord模式状态已清除")
    }
    
    func clearProgressionState() {
        practiceStates.progressionState = nil
        saveStates()
        print("🗑️ Progression模式状态已清除")
    }
    
    func clearAllStates() {
        practiceStates = PracticeStates()
        saveStates()
        print("🗑️ 所有练习状态已清除")
    }
    
    // MARK: - 便利方法
    
    /// 获取最近练习的模式
    func getLastPracticeMode() -> String? {
        var lastDate: Date?
        var lastMode: String?
        
        if let noteState = practiceStates.noteState {
            if lastDate == nil || noteState.lastPracticeDate > lastDate! {
                lastDate = noteState.lastPracticeDate
                lastMode = "note"
            }
        }
        
        if let chordState = practiceStates.chordState {
            if lastDate == nil || chordState.lastPracticeDate > lastDate! {
                lastDate = chordState.lastPracticeDate
                lastMode = "chord"
            }
        }
        
        if let progressionState = practiceStates.progressionState {
            if lastDate == nil || progressionState.lastPracticeDate > lastDate! {
                lastDate = progressionState.lastPracticeDate
                lastMode = "progression"
            }
        }
        
        return lastMode
    }
    
    /// 检查是否有任何可继续的练习
    func hasAnyContinueState() -> Bool {
        return hasNoteContinueState() || hasChordContinueState() || hasProgressionContinueState()
    }
}
