//
//  NoteGameManager.swift
//  FullUI
//
//  Created by <PERSON> Li on 2025/5/26.
//

import Foundation
import AudioKit
import Combine

enum GameState {
    case idle
    case playing
    case waitingForResponse
    case completed
    case countdown  // 新增倒计时状态
}

enum AudioMode {
    case soundFont
    case audioKit // 为将来的声音识别预留
}

enum NoteFeedback: Equatable {
    case neutral        // 中性状态
    case correct        // 正确
    case sharp(Int)     // 偏高，参数为差值
    case flat(Int)      // 偏低，参数为差值
}

class NoteGameManager: ObservableObject {
    @Published var gameState: GameState = .idle {
        didSet {
            // 当游戏状态变化时发送通知
            NotificationCenter.default.post(name: NSNotification.Name("NoteGameStateChanged"), object: gameState)
        }
    }
    @Published var rightNoteCount: Int = 0  //  正确个数
    @Published var errorNoteCount: Int = 0  //  错误个数
    @Published var ErrorInCurrentRound:Int = 0  //  本轮的错误个数
    @Published var currentRound: Int = 0        //  当前轮数
    @Published var totalTime: TimeInterval = 0  //  总时长
    @Published var listenNote: Int = 0          //  当前听写音符
    @Published var showLevelClearView: Bool = false //  显示通关UI
    @Published var noteFeedback: NoteFeedback = .neutral //  音符反馈状态
    
    // 新增关卡相关属性
    @Published var currentLevel: NoteExercise?
    @Published var showLevelStartView: Bool = false //  显示开始玩某关
    @Published var currentLevelRoundCount: Int = 0 // 当前关卡的轮数
    
    // 倒计时相关属性
    @Published var waitTimeRemaining: Double = 0.0 // 剩余等待时间
    @Published var waitTimeProgress: Double = 1.0 // 等待时间进度 (0.0 - 1.0)
    
    private var gameStartTime: Date?
    private var waitTimer: Timer?
    private var responseTimer: Timer?
    private var progressTimer: Timer? // 用于更新进度条的定时器
    private var responseStartTime: Date? // 响应阶段开始时间
    private var gameConfig: GameConfig
    private var midiManager: MIDIManager
    private var cancellables = Set<AnyCancellable>()
    private var lastProcessedNotes: Set<Int> = []
    
    private let noteGenerator: NoteGenerator
    private let achievementManager = AchievementManager.shared
    private var currentLevelManager: NoteLevelManager?
    
    init(config: GameConfig, midiManager: MIDIManager) {
        self.gameConfig = config
        self.midiManager = midiManager
        self.noteGenerator = NoteGenerator(gameConfig: config)
        setupMIDIListener()
    }
    
    private func setupMIDIListener() {
        // 监听MIDI按键事件
        midiManager.$pressedNotes
            .sink { [weak self] pressedNotes in
                DispatchQueue.main.async {
                    self?.handleMIDIInput(pressedNotes: pressedNotes)
                }
            }
            .store(in: &cancellables)
    }
    
    // MARK: - Game Control
    
    func startGame() {
        resetGame()
        gameState = .playing
        gameStartTime = Date()
        playNextNote()
    }
    
    // 新增：开始关卡游戏
    func startLevelGame(with level: NoteExercise, levelManager: NoteLevelManager) {
        currentLevel = level
        currentLevelManager = levelManager
        
        // 根据关卡配置设置音符生成器
        let levelNotes = levelManager.generateNotesForCurrentLevel()
        noteGenerator.setLevelNotes(levelNotes)
        
        //这个函数会重置RoundCount
        resetGame()
        // 设置关卡轮数：优先使用关卡配置，否则使用默认配置
        currentLevelRoundCount = level.roundCount ?? gameConfig.gameSettings.roundTotalCount
        print("Starting level \(level.id) with \(currentLevelRoundCount) rounds")

        print("check CP2\(level.id) with \(currentLevelRoundCount) rounds")
        gameState = .playing
        gameStartTime = Date()
        playNextNote()
    }
    
    func stopGame() {
        gameState = .idle
        stopAllTimers()
        resetGame()
    }
    
    private func resetGame() {
        rightNoteCount = 0
        errorNoteCount = 0
        currentRound = 0
        totalTime = 0
        listenNote = 0
        showLevelClearView = false
        showLevelStartView = false
        noteFeedback = .neutral
        lastProcessedNotes.removeAll()
        stopAllTimers()
        
        // 重置轮数为默认值（在startLevelGame中会被重新设置）
        currentLevelRoundCount = gameConfig.gameSettings.roundTotalCount
    }
    
    private func stopAllTimers() {
        print("Stopping all timers")
        waitTimer?.invalidate()
        waitTimer = nil
        responseTimer?.invalidate()
        responseTimer = nil
        progressTimer?.invalidate()
        progressTimer = nil
        print("All timers stopped")
    }
    
    // MARK: - Game Logic
    
    private func playNextNote() {
        
        guard gameState == .playing else {
            print("playNextNote: gameState is not playing, current state: \(gameState)")
            return 
        }
        
        currentRound += 1
        print("Starting round \(currentRound)/\(currentLevelRoundCount)")
        
        if currentRound > currentLevelRoundCount {
            currentRound = currentLevelRoundCount
            completeGame()
            
            gameState = .idle  //miles add
            return
        }
        
        ErrorInCurrentRound = 0
        
        // 停止之前的计时器
        stopAllTimers()
        
        // 重置反馈状态
        noteFeedback = .neutral
        
        // 生成随机音符
        var newNote: Int
        
        // 检查是否有足够的音符避免重复
        let availableNotes = noteGenerator.getAvailableNotes()
        print("Available notes for level: \(availableNotes.count) notes - \(availableNotes)")
        
        if availableNotes.count <= 1 {
            // 如果只有一个音符或没有音符，直接使用
            newNote = noteGenerator.generateRandomNote()
            print("Single note level: using note \(newNote)")
        } else {
            // 如果有多个音符，避免重复上一个音符
            repeat {
                newNote = noteGenerator.generateRandomNote()
            } while listenNote == newNote
            print("Multi-note level: selected \(newNote), previous was \(listenNote)")
        }
        
        listenNote = newNote
        // 播放音符
        playNote(listenNote)
        
        // 设置等待时间
        gameState = .waitingForResponse
        
        // 设置响应超时 - 确保在主线程中创建
        let timeoutInterval = GameConfigManager.shared.getNoteWaitTime()
        print("Setting timeout timer for \(String(format: "%.2f", timeoutInterval)) seconds")

        // 初始化倒计时相关属性
        waitTimeRemaining = timeoutInterval
        waitTimeProgress = 1.0
        responseStartTime = Date()
        
        DispatchQueue.main.async { [weak self] in
            // 设置超时计时器
            self?.responseTimer = Timer.scheduledTimer(withTimeInterval: timeoutInterval, repeats: false) { [weak self] timer in
                print("Timer fired! Calling handleTimeout")
                self?.handleTimeout()
            }
                        
            // 设置进度更新计时器（每0.1秒更新一次）
            self?.progressTimer = Timer.scheduledTimer(withTimeInterval: 0.1, repeats: true) { [weak self] timer in
                self?.updateWaitProgress()
            }
            
            print("Timer created and scheduled: \(self?.responseTimer != nil)")
        }
    }
    
    private func playNote(_ noteNumber: Int) {
        // 使用MIDIManager的专用方法播放听音音符（不影响pressedNotes状态）
        print("Playing listen note: \(noteNumber)")
        midiManager.playListenNote(noteNumber, velocity: 100)
        
        // 短暂延迟后停止音符
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            print("Stopping listen note: \(noteNumber)")
            self.midiManager.stopListenNote(noteNumber)
        }
    }
    
    private func handleMIDIInput(pressedNotes: Set<Int>) {
        guard gameState == .waitingForResponse else { 
            //print("handleMIDIInput: not in waitingForResponse state, current: \(gameState)")
            return
        }
        
        // 找出新按下的音符（避免重复处理）
        let newNotes = pressedNotes.subtracting(lastProcessedNotes)
        lastProcessedNotes = pressedNotes
        
        print("MIDI input - pressed: \(pressedNotes), new: \(newNotes), target: \(listenNote)")
        
        // 只处理新按下的音符
        for note in newNotes {
            let difference = note - listenNote
            
            if note == listenNote {
                print("Correct answer! Note: \(note)")
                noteFeedback = .correct
                handleCorrectAnswer()
                return
            } else {
                print("Wrong answer! Note: \(note), expected: \(listenNote), difference: \(difference)")
                
                // 更新视觉反馈
                if difference > 0 {
                    noteFeedback = .sharp(difference)
                } else {
                    noteFeedback = .flat(abs(difference))
                }
                
                handleWrongAnswer()
                // 注意：不要return，继续等待正确答案或超时
            }
        }
        
        // 如果没有按键了，重置lastProcessedNotes
        if pressedNotes.isEmpty {
            lastProcessedNotes.removeAll()
        }
    }
    
    private func handleCorrectAnswer() {
        print("handleCorrectAnswer called")
        rightNoteCount += 1
        
        // 取消超时计时器和进度计时器
        print("Cancelling timeout timer")
        responseTimer?.invalidate()
        responseTimer = nil
        progressTimer?.invalidate()
        progressTimer = nil
        
        // 重置MIDI处理状态
        lastProcessedNotes.removeAll()
        gameState = .playing
        
        // 等待responseTime后进入下一轮
        let responseTime = GameConfigManager.shared.getResponseTime()
        print("Setting response timer for \(String(format: "%.2f", responseTime)) seconds")
        waitTimer = Timer.scheduledTimer(withTimeInterval: responseTime, repeats: false) { [weak self] _ in
            print("Response timer fired, playing next note")
            self?.playNextNote()
        }
    }
    
    private func handleWrongAnswer() {
        print("handleWrongAnswer called")
        ErrorInCurrentRound += 1
        errorNoteCount += 1
        // 继续等待正确答案或超时 - 不取消计时器
        print("Wrong answer processed, continuing to wait for correct answer or timeout")
    }
    
    private func handleTimeout() {
        print("handleTimeout!!! Current state: \(gameState)")
        
        // 确保我们仍在等待响应状态
        guard gameState == .waitingForResponse else {
            print("Timeout occurred but not in waitingForResponse state, ignoring")
            return
        }
        
        errorNoteCount += 1
        
        // 停止进度计时器
        progressTimer?.invalidate()
        progressTimer = nil
        
        // 重置MIDI处理状态
        lastProcessedNotes.removeAll()
        gameState = .playing
        
        print("Timeout processed, moving to next note")
        playNextNote()
    }
    
    private func completeGame() {
        gameState = .completed
        
        if let startTime = gameStartTime {
            totalTime = Date().timeIntervalSince(startTime)
        }
        
        // 保存游戏成绩到成就系统
        if let currentLevel = currentLevel,
           let levelManager = currentLevelManager {
            let score = GameScore(
                correctCount: rightNoteCount,
                totalCount: currentLevelRoundCount,
                accuracy: currentLevelRoundCount > 0 ? Double(rightNoteCount) / Double(currentLevelRoundCount) * 100.0 : 0.0,
                totalTime: totalTime
            )
            
            achievementManager.updateScore(score, for: currentLevel.id, in: levelManager)
            print("Saved achievement for level \(currentLevel.id) with score: \(score)")
        }
        
        showLevelClearView = true
        stopAllTimers()
    }
    
    // MARK: - Wait Progress Update
    
    private func updateWaitProgress() {
        guard let startTime = responseStartTime else { return }

        let totalWaitTime = GameConfigManager.shared.getNoteWaitTime()
        let elapsed = Date().timeIntervalSince(startTime)
        let remaining = max(0, totalWaitTime - elapsed)
        let progress = remaining / totalWaitTime

        waitTimeRemaining = remaining
        waitTimeProgress = progress
        
        // 如果时间用完，停止进度计时器
        if remaining <= 0 {
            progressTimer?.invalidate()
            progressTimer = nil
        }
    }
    
    // MARK: - Level Management
    
    func showLevelStart(for level: NoteExercise) {
        currentLevel = level
        showLevelStartView = true
    }
    
    func startGameFromLevelStart(levelManager: NoteLevelManager) {
        guard let level = currentLevel else { return }
        showLevelStartView = false
        startLevelGame(with: level, levelManager: levelManager)
    }
    
    func replayCurrentLevel(levelManager: NoteLevelManager) {
        guard let level = currentLevel else { return }
        showLevelClearView = false
        startLevelGame(with: level, levelManager: levelManager)
    }
    
    func nextLevel(levelManager: NoteLevelManager) {
        showLevelClearView = false
        levelManager.nextLevel()
        
        if let nextLevel = levelManager.currentLevel {
            showLevelStart(for: nextLevel)
        }
    }
    
    // MARK: - Configuration Update
    
    func updateConfig(_ newConfig: GameConfig) {
        gameConfig = newConfig
    }
    
    // MARK: - Replay Note
    
    func replayCurrentNote() {
        guard gameState == .waitingForResponse else {
            print("replayCurrentNote: not in waitingForResponse state, current: \(gameState)")
            return
        }
        
        print("Replaying current note: \(listenNote)")
        playNote(listenNote)
    }
    
    // MARK: - Audio Interface (为将来扩展预留)
    
    private func playNoteWithAudioKit(_ noteNumber: Int) {
        // 预留接口：使用AudioKit进行声音识别模式
        // 将来可以在这里实现原声钢琴声音播放
    }
    
    private func setupAudioKitRecognition() {
        // 预留接口：设置AudioKit声音识别
        // 将来可以在这里实现声音识别功能
    }
} 
