//
//  MIDIRoutingManager.swift
//  VoicingTrainer
//
//  Created by AI Assistant on 2025/6/15.
//

import Foundation
import SwiftUI
import Combine

/// MIDI路由管理器 - 负责将MIDI输入路由到正确的处理器
class MIDIRoutingManager: ObservableObject {
    
    /// 当前活跃的页面类型
    enum ActivePage {
        case none
        case notes
        case chords
        case progressions
        case newProgression
    }
    
    /// MIDI输入处理器协议
    protocol MIDIInputHandler {
        func handleMIDIInput(_ notes: Set<Int>)
        func handleSustainInput(_ sustainOn: Bool)
    }
    
    @Published var activePage: ActivePage = .none
    
    // 各页面的处理器
    private var notesHandler: MIDIInputHandler?
    private var chordsHandler: MIDIInputHandler?
    private var progressionsHandler: MIDIInputHandler?
    private var newProgressionHandler: MIDIInputHandler?
    
    /// 注册MIDI处理器
    func registerHandler(_ handler: MIDIInputHandler, for page: ActivePage) {
        switch page {
        case .notes:
            notesHandler = handler
        case .chords:
            chordsHandler = handler
        case .progressions:
            progressionsHandler = handler
        case .newProgression:
            newProgressionHandler = handler
        case .none:
            break
        }
        print("🎛️ 已注册MIDI处理器: \(page)")
    }
    
    /// 取消注册MIDI处理器
    func unregisterHandler(for page: ActivePage) {
        switch page {
        case .notes:
            notesHandler = nil
        case .chords:
            chordsHandler = nil
        case .progressions:
            progressionsHandler = nil
        case .newProgression:
            newProgressionHandler = nil
        case .none:
            break
        }
        print("🎛️ 已取消注册MIDI处理器: \(page)")
    }
    
    /// 路由MIDI输入到当前活跃页面
    func routeMIDIInput(_ notes: Set<Int>) {
        // Gemini Debugging: Print received keys and the target handler page.
        print(">>> 2. MIDIRoutingManager: Received keys \(notes.sorted()). Routing to active page: \(activePage)")
        
        switch activePage {
        case .none:
            break
        case .notes:
            notesHandler?.handleMIDIInput(notes)
        case .chords:
            chordsHandler?.handleMIDIInput(notes)
        case .progressions:
            progressionsHandler?.handleMIDIInput(notes)
        case .newProgression:
            newProgressionHandler?.handleMIDIInput(notes)
        }
    }
    
    /// 路由踏板输入到当前活跃页面
    func routeSustainInput(_ sustainOn: Bool) {
        switch activePage {
        case .none:
            break
        case .notes:
            notesHandler?.handleSustainInput(sustainOn)
        case .chords:
            chordsHandler?.handleSustainInput(sustainOn)
        case .progressions:
            progressionsHandler?.handleSustainInput(sustainOn)
        case .newProgression:
            newProgressionHandler?.handleSustainInput(sustainOn)
        }
    }
    
    /// 设置当前活跃页面
    func setActivePage(_ page: ActivePage) {
        if activePage != page {
            activePage = page
            print("🎛️ 切换到活跃页面: \(page)")
        }
    }
}

/// 环境键，用于在SwiftUI环境中传递MIDIRoutingManager
struct MIDIRoutingManagerKey: EnvironmentKey {
    static let defaultValue = MIDIRoutingManager()
}

extension EnvironmentValues {
    var midiRoutingManager: MIDIRoutingManager {
        get { self[MIDIRoutingManagerKey.self] }
        set { self[MIDIRoutingManagerKey.self] = newValue }
    }
} 