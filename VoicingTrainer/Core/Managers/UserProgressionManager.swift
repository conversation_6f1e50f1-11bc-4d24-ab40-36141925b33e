//
//  UserProgressionManager.swift
//  VoicingTrainer
//
//  Created by AI Assistant on 2025/5/26.
//

import Foundation

class UserProgressionManager: ObservableObject {
    static let shared = UserProgressionManager()
    
    @Published var userProgressions: [Progression] = []
    @Published var isLoading = false
    @Published var errorMessage: String?
    
    private let fileManager = FileManager.default
    private var userProgressionsURL: URL {
        let documentsURL = fileManager.urls(for: .documentDirectory, in: .userDomainMask).first!
        return documentsURL.appendingPathComponent("UserProgressions")
    }
    
    private init() {
        createUserProgressionsDirectoryIfNeeded()
        loadUserProgressions()
    }
    
    // MARK: - Directory Management
    
    private func createUserProgressionsDirectoryIfNeeded() {
        if !fileManager.fileExists(atPath: userProgressionsURL.path) {
            try? fileManager.createDirectory(at: userProgressionsURL, withIntermediateDirectories: true)
        }
    }
    
    // MARK: - CRUD Operations
    
    /// 保存用户自定义和弦进行
    func saveProgression(_ progression: Progression) -> Bool {
        let fileName = sanitizeFileName(progression.name) + ".progression"
        let fileURL = userProgressionsURL.appendingPathComponent(fileName)
        
        let progressionFile = ProgressionFile(progression: progression)
        
        do {
            let encoder = JSONEncoder()
            encoder.outputFormatting = .prettyPrinted
            let data = try encoder.encode(progressionFile)
            try data.write(to: fileURL)
            
            // 更新内存中的列表
            if let index = userProgressions.firstIndex(where: { $0.name == progression.name }) {
                userProgressions[index] = progression
            } else {
                userProgressions.append(progression)
            }
            
            print("✅ 成功保存用户和弦进行: \(progression.name)")
            return true
        } catch {
            errorMessage = "保存失败: \(error.localizedDescription)"
            print("❌ 保存失败: \(error)")
            return false
        }
    }
    
    /// 加载所有用户自定义和弦进行
    func loadUserProgressions() {
        isLoading = true
        userProgressions.removeAll()
        
        print("🔍 开始加载用户自定义和弦进行...")
        print("📂 用户进行目录: \(userProgressionsURL.path)")
        
        do {
            let fileURLs = try fileManager.contentsOfDirectory(at: userProgressionsURL, includingPropertiesForKeys: nil)
            let progressionFiles = fileURLs.filter { $0.pathExtension == "progression" }
            
            print("📄 找到 \(progressionFiles.count) 个 .progression 文件")
            
            for fileURL in progressionFiles {
                print("🔍 尝试加载文件: \(fileURL.lastPathComponent)")
                if let progression = loadProgression(from: fileURL) {
                    userProgressions.append(progression)
                    print("✅ 成功加载: \(progression.name)")
                } else {
                    print("❌ 加载失败: \(fileURL.lastPathComponent)")
                }
            }
            
            print("✅ 加载了 \(userProgressions.count) 个用户自定义和弦进行")
        } catch {
            errorMessage = "加载失败: \(error.localizedDescription)"
            print("❌ 加载用户和弦进行失败: \(error)")
        }
        
        isLoading = false
    }
    
    /// 从文件加载单个和弦进行
    private func loadProgression(from url: URL) -> Progression? {
        print("🔍 尝试解析文件: \(url.lastPathComponent)")
        do {
            let data = try Data(contentsOf: url)
            print("📄 文件大小: \(data.count) bytes")
            
            let progressionFile = try JSONDecoder().decode(ProgressionFile.self, from: data)
            print("✅ JSON解析成功: \(progressionFile.progression.name)")
            return progressionFile.progression
        } catch {
            print("❌ 加载文件失败: \(url.lastPathComponent) - \(error)")
            // 打印文件内容的前几个字符以帮助调试
            if let data = try? Data(contentsOf: url),
               let preview = String(data: data.prefix(200), encoding: .utf8) {
                print("📄 文件内容预览: \(preview)")
            }
            return nil
        }
    }
    
    /// 删除用户自定义和弦进行
    func deleteProgression(_ progression: Progression) -> Bool {
        let fileName = sanitizeFileName(progression.name) + ".progression"
        let fileURL = userProgressionsURL.appendingPathComponent(fileName)
        
        do {
            try fileManager.removeItem(at: fileURL)
            userProgressions.removeAll { $0.name == progression.name }
            print("✅ 成功删除用户和弦进行: \(progression.name)")
            return true
        } catch {
            errorMessage = "删除失败: \(error.localizedDescription)"
            print("❌ 删除失败: \(error)")
            return false
        }
    }
    
    /// 检查和弦进行名称是否已存在
    func progressionExists(_ name: String) -> Bool {
        return userProgressions.contains { $0.name == name }
    }
    
    // MARK: - Helper Methods
    
    private func sanitizeFileName(_ name: String) -> String {
        // 移除或替换文件名中的非法字符
        let invalidChars = CharacterSet(charactersIn: "\\/:*?\"<>|")
        return name.components(separatedBy: invalidChars).joined(separator: "_")
    }
} 