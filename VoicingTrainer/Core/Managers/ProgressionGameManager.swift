//
//  ProgressionGameManager.swift
//  VoicingTrainer
//
//  Created by AI Assistant on 2025/5/26.
//

import Foundation
import Combine

/// 和弦进行游戏状态
enum ProgressionGameState {
    case idle           // 空闲状态
    case playingChord   // 播放和弦
    case waitingForResponse // 等待玩家响应
    case completed      // 游戏完成
    case waitingResponse // 等待反应时间
}

/// 和弦进行游戏管理器
class ProgressionGameManager: ObservableObject {
    
    // MARK: - Published Properties
    
    @Published var gameState: ProgressionGameState = .idle {
        didSet {
            // 当游戏状态变化时发送通知
            NotificationCenter.default.post(name: NSNotification.Name("ProgressionGameStateChanged"), object: gameState)
        }
    }
    
    @Published var currentChordIndex: Int = 0     // 当前和弦索引
    @Published var correctChordCount: Int = 0     // 正确和弦数量
    @Published var totalChordCount: Int = 0       // 总和弦数量
    @Published var currentProgression: Progression? // 当前和弦进行
    @Published var expectedNotes: Set<Int> = []   // 期望的音符
    @Published var playedNotes: Set<Int> = []     // 玩家已弹奏的音符
    @Published var currentChordName: String = ""  // 当前和弦名称
    @Published var showCompletionView: Bool = false // 显示完成界面
    @Published var targetChordNotes: Set<Int> = [] // 目标和弦音符（用于保持高亮显示）
    @Published var isEndlessMode: Bool = false // 无限循环模式
    @Published var foreverWaitMode: Bool = true // 永远等待用户输入模式
    @Published var transposeAllKeys: Bool = false //是否移调到所有12个调

    // 🎉 练习时间跟踪
    @Published var totalTime: TimeInterval = 0 // 总练习时间
    private var practiceStartTime: Date? // 练习开始时间
    
    // 保存整个和弦进行的八度偏移量 ： 这一个和弦进行都使用这个音程来偏移所有和弦
    private var adjustInterval: Int = 0
    
    // 🎵 练习模式支持 - 使用统一的引擎
    @Published var workoutMode: Bool = false // 练习模式开关 true: 练习 false:listen
    @Published var selectedPracticePattern: PracticePattern? // 当前选择的练习模式
    @Published var currentCycle: Int = 0           // 当前循环数（从0开始）
    @Published var currentRoundInCycle: Int = 0    // 当前循环内的轮数（从0开始）
    @Published var currentTransposition: Int = 0   // 当前移调值（半音数）
    private var practiceEngineState: PracticeEngineState = PracticeEngineState()
    private var originalProgression: Progression?  // 保存原始进行
    private var practicePatternManager: PracticePatternManager // 练习模式管理器
    
    // MARK: - Private Properties
    
    private var midiManager: MIDIManager
    private var gameConfig: GameConfig
    private var progressionReader: ProgressionReader // 重构后的和弦进行阅读器
    private var cancellables = Set<AnyCancellable>()
    private var playbackTimer: Timer?

    private var responseTimer: Timer? // 反应时间计时器
    private var debugOut = false
    private var chordPlayer:ChordPlayer //和弦播放类

    // MARK: - Public ChordPlayer Access

    /// 🎹 配置人性化琶音参数
    /// 提供外部访问ChordPlayer配置的接口
    func configureHumanizedArpeggio(
        timingVariation: Double = 0.3,
        velocityVariation: Int = 15,
        useCrescendo: Bool = true,
        randomizeOrder: Bool = false
    ) {
        chordPlayer.configureHumanizedArpeggio(
            timingVariation: timingVariation,
            velocityVariation: velocityVariation,
            useCrescendo: useCrescendo,
            randomizeOrder: randomizeOrder
        )
    }
    
    
    // 🔧 新增：管理所有异步延迟任务，确保可以取消
    private var listenModeTask: DispatchWorkItem?
    private var practiceWaitTask: DispatchWorkItem?
    private var responseWaitTask: DispatchWorkItem?

    // 键盘视图模型的弱引用，用于设置目标音符高亮
    weak var keyboardViewModel: PianoKeyboardViewModel?
    
    // Circle12NotesView 视图模型的弱引用，用于显示音符匹配状态
    weak var chromaCircleViewModel: ChromaCircleViewModel?
    
    // 🎯 统一的和弦匹配管理器
    @Published var chordMatchManager = ChordMatchManager()
    
    // 当前游戏参数
    private var currentPlaybackType: ChordPlaybackType = .block_chord
    private var currentBpm: Int = 100
    private var currentKeepHighlighted: Bool = true
    
    // MARK: - Initialization
    
    init(midiManager: MIDIManager, gameConfig: GameConfig = GameConfigManager.shared.config) {
        self.midiManager = midiManager
        self.gameConfig = gameConfig
        self.progressionReader = ProgressionReader() // 初始化重构后的阅读器
        self.practicePatternManager = PracticePatternManager()
        
        // 🎵 初始化练习模式
        self.selectedPracticePattern = practicePatternManager.selectedPattern
        
        print("MARK: - Initialization")
        chordPlayer = ChordPlayer(midiManager: midiManager)

        setupMIDIListener()
        setupPracticePatternListener()
        setupChordMatchManager()
    }

    // MARK: - 🎵 BPM动态等待时间计算

    /// 根据BPM计算和弦等待时间
    /// - Parameter bpm: 每分钟节拍数
    /// - Returns: 等待时间（秒）
    private func calculateChordWaitTime(bpm: Int) -> Double {
        // 🎵 使用统一的BPM时间计算系统
        return GameConfigManager.shared.getProgressionWaitTime(bpm: bpm)
    }

    // MARK: - MIDI Listener Setup
    
    private func setupMIDIListener() {
        // 监听MIDI输入变化
        midiManager.$pressedNotes
            .sink { [weak self] pressedNotes in
                DispatchQueue.main.async {
                    self?.handleMIDIInput(pressedNotes)
                }
            }
            .store(in: &cancellables)
    }
    
    private func setupPracticePatternListener() {
        // 监听练习模式变化
        practicePatternManager.$selectedPattern
            .sink { [weak self] pattern in
                self?.selectedPracticePattern = pattern
                if let pattern = pattern {
                    print("🎵 Progression practice pattern changed to: \(pattern.name) (offset: \(pattern.offset))")
                }
            }
            .store(in: &cancellables)
    }
    
    private func setupChordMatchManager() {
        // 设置和弦匹配管理器的回调
        chordMatchManager.setCallbacks(
            onMatchEvent: { [weak self] event in
                self?.handleChordMatchEvent(event)
            },
            onPerfectMatch: { [weak self] in
                self?.handlePerfectMatch()
            },
            onParticleExplosion: { [weak self] in
                self?.triggerParticleExplosion()
            },
            onSoundEffect: { [weak self] event in
                self?.handleSoundEffect(event)
            },
            onVisualFeedback: { [weak self] event in
                self?.handleVisualFeedback(event)
            }
        )
    }
    
    // MARK: - 🎵 练习模式管理 - 使用统一引擎
    
    /// 设置练习游戏参数
    private func setupPracticeGameParameters() {
        // 使用统一的练习引擎初始化状态
        practiceEngineState = PracticePatternEngine.initializeState(for: selectedPracticePattern)
        
        // 同步UI显示状态
        currentCycle = practiceEngineState.currentCycle
        currentRoundInCycle = practiceEngineState.currentRoundInCycle
        currentTransposition = practiceEngineState.currentTransposition
    }
    
    /// 将和弦进行移调指定的半音数
    private func transposeProgression(_ progression: Progression, by semitones: Int) -> Progression {
        // 🎵 重构后的版本：使用ProgressionReader处理移调
        // 这里直接返回原始进行，移调通过ProgressionReader的transposeFromBase方法处理
        return progression
    }
     
    /// 处理和弦进行完成（练习模式专用）
    private func handleProgressionComplete(playbackType: ChordPlaybackType, bpm: Int, keepHighlighted: Bool) {
        guard let originalProgression = originalProgression,
              let pattern = selectedPracticePattern else {
            completeGame()
            return
        }
        
        // 使用统一的练习引擎计算下一个移调值
        let result = PracticePatternEngine.calculateNextTransposition(
            practiceOffset: pattern.offset,
            state: &practiceEngineState
        )
        
        if result.isCompleted {
            // 所有循环完成
            if isEndlessMode {
                // 🔄 无限循环模式：重新开始整个练习循环
                print("🔄 无限循环模式：所有12个调练习完成，重新开始")
                practiceEngineState.reset()
                currentCycle = practiceEngineState.currentCycle
                currentRoundInCycle = practiceEngineState.currentRoundInCycle
                currentTransposition = practiceEngineState.currentTransposition
                
                // 重新开始第一个移调
                currentProgression = transposeProgression(originalProgression, by: practiceEngineState.currentTransposition)
                currentChordIndex = 0
                
                // 继续下一个和弦进行
                playNextChord(playbackType: playbackType, bpm: bpm, keepHighlighted: keepHighlighted)
                return
            } else {
                // 练习完成，游戏结束
                completeGame()
                return
            }
        }
        
        // 应用新的移调
        currentProgression = transposeProgression(originalProgression, by: result.nextTransposition)
        
        // 重置和弦索引，开始新的进行
        currentChordIndex = 0
        
        // 同步UI显示状态
        currentCycle = practiceEngineState.currentCycle
        currentRoundInCycle = practiceEngineState.currentRoundInCycle
        currentTransposition = practiceEngineState.currentTransposition
        
        let transpositionName = PracticePatternEngine.getTranspositionName(result.nextTransposition)
        print("🎵 进行练习 - \(practiceEngineState.getProgressDescription())")
        print("🎵 新移调: \(result.nextTransposition) 半音 (\(transpositionName))")
        
        // 继续下一个和弦进行
        playNextChord(playbackType: playbackType, bpm: bpm, keepHighlighted: keepHighlighted)
    }
    

     
     // MARK: - Game Control
    
    /// 开始游戏
    func startGame(with progression: Progression,
                   workoutMode: Bool,   //  练习/播放
                   playbackType: ChordPlaybackType = .block_chord,
                   bpm: Int = 70,
                   keepHighlighted: Bool = true,
                   endlessMode: Bool = false,
                   transposeToAllKeys: Bool = false,
                   foreverWait: Bool = true) {
        guard gameState == .idle else { return }
        
        // 🎵 保存原始进行和设置练习模式
        originalProgression =   progression
        self.workoutMode =      workoutMode
        transposeAllKeys =      transposeToAllKeys
        
        // 🎵 设置练习游戏参数
        if transposeToAllKeys {
            setupPracticeGameParameters()
            print("🎵 练习模式启用，模式: \(selectedPracticePattern?.name ?? "未选择")")
            print("🎵 需要循环数: \(practiceEngineState.cyclesNeeded)，每循环和弦进行数: \(practiceEngineState.roundsPerCycle)")
        }
        
        // 🎵 应用当前移调（初始为0）
        currentProgression = transposeProgression(progression, by: practiceEngineState.currentTransposition)
        resetGame()
        
        // 保存当前游戏参数
        currentPlaybackType = playbackType
        currentBpm = bpm
        currentKeepHighlighted = keepHighlighted
        isEndlessMode = endlessMode
        foreverWaitMode = foreverWait  // 新增：保存永远等待模式设置
        
        totalChordCount = currentProgression!.chords.count
        
        // 🔧 同步UI显示的移调值
        currentTransposition = transposeAllKeys ? practiceEngineState.currentTransposition : 0
        
        print("🎮 开始和弦进行游戏: \(progression.name)")
        print("🎮 总和弦数: \(totalChordCount)")
        print("🎮 播放模式: \(playbackType.displayName)")
        print("🎮 保持按键高亮: \(keepHighlighted)")
        print("🎮 无限循环模式: \(endlessMode)")
        print("🎮 永远等待模式: \(foreverWait)")
        print("🎮 练习模式: \(workoutMode)")
        print("🎮 移调12个调模式: \(transposeToAllKeys)")
        
        
        if transposeAllKeys {
            print("🎮 当前移调: \(practiceEngineState.currentTransposition) 半音")
        }
        print("🎮 反应时间: \(GameConfigManager.shared.getResponseTime(bpm: currentBpm))秒")

        // 🎉 开始计时（只在练习模式下）
        if workoutMode {
            practiceStartTime = Date()
            totalTime = 0
            print("⏱️ 开始练习计时")
        }

        playNextChord(playbackType: playbackType, bpm: bpm, keepHighlighted: keepHighlighted)
    }
    
    // MARK: - 🎵 新增：统一的和弦进行播放控制方法
    
    /// 播放和弦进行（Listen模式）
    func playProgression(progression: Progression,
                        playbackType: ChordPlaybackType = .block_chord,
                        bpm: Int = 70,
                        keepHighlighted: Bool = true,
                        endlessMode: Bool = false,
                        transposeToAllKeys: Bool = false) {
        print("🎵 ProgressionGameManager: 开始播放和弦进行")
        
        // 使用startGame方法，但设置为非练习模式
        startGame(
            with: progression,
            workoutMode: false,  // Listen模式
            playbackType: playbackType,
            bpm: bpm,
            keepHighlighted: keepHighlighted,
            endlessMode: endlessMode,
            transposeToAllKeys: transposeToAllKeys,
            foreverWait: true  // Listen模式不需要等待用户输入
        )
    }
    
    /// 停止和弦进行播放
    func stopProgression() {
        print("🛑 ProgressionGameManager: 停止和弦进行播放")
        stopGame()
    }
    
    /// 检查是否正在播放
    var isPlaying: Bool {
        return gameState != .idle
    }
    
    /// 获取当前播放索引
    var currentPlayingIndex: Int {
        return currentChordIndex
    }
    
    /// 获取当前和弦名称（用于UI显示）
    var currentDisplayChordName: String {
        return currentChordName
    }
    
    /// 获取当前和弦的MIDI音符（用于UI显示）
    var currentDisplayChordNotes: [Int] {
        guard let progression = currentProgression,
              currentChordIndex < progression.chords.count else { return [] }
        
        let currentChord = progression.chords[currentChordIndex]
        let currentTransposition = transposeAllKeys ? practiceEngineState.currentTransposition : 0
        return currentChord.getMIDINotes(transpositionSemitones: currentTransposition)
    }
    
    /// 获取移调后的和弦名称数组（用于UI显示）
    func getTransposedChordNames() -> [String] {
        guard let progression = currentProgression else { return [] }
        
        let shouldTranspose = transposeAllKeys && gameState != .idle
        
        if shouldTranspose {
            let currentTransposition = practiceEngineState.currentTransposition
            return progression.chords.map { chord in
                chord.getChordName(transpositionSemitones: currentTransposition)
            }
        } else {
            return progression.chords.map { $0.getChordName() }
        }
    }
    
    /// 停止游戏
    func stopGame() {
        print("🛑 停止和弦进行游戏 manager.stopGame")

        // 🎉 结束计时（只在练习模式下）
        if workoutMode, let startTime = practiceStartTime {
            totalTime = Date().timeIntervalSince(startTime)
            print("⏱️ 练习结束，总时间: \(String(format: "%.1f", totalTime))秒")
            practiceStartTime = nil
        }

        stopAllTimers()
        resetGame()
        gameState = .idle

        // 🔧 重置移调状态
        currentTransposition = 0

        // 🔄 重置所有UI状态
        resetAllUIStates()
    }
    
    /// 重置游戏状态
    private func resetGame() {
        currentChordIndex = 0
        correctChordCount = 0
        totalChordCount = 0
        expectedNotes.removeAll()
        playedNotes.removeAll()
        targetChordNotes.removeAll()
        keyboardViewModel?.clearTargetNotes()
        currentChordName = ""
        showCompletionView = false
        
        // 🎯 清除和弦匹配管理器状态
        chordMatchManager.clearAll()
        
        // 🎯 清除Circle12NotesView状态
        chromaCircleViewModel?.clearAll()
        
        // 🎵 重置练习模式状态（只在非练习模式或游戏开始时重置）
        if !transposeAllKeys {
            practiceEngineState.reset()
            currentCycle = practiceEngineState.currentCycle
            currentRoundInCycle = practiceEngineState.currentRoundInCycle
            currentTransposition = practiceEngineState.currentTransposition
        }
        
        stopAllTimers()
    }
    
    /// 停止所有定时器
    private func stopAllTimers() {
        print("🔧 ProgressionGameManager: 停止所有定时器和延迟任务")
        
        // 停止Timer
        playbackTimer?.invalidate()
        playbackTimer = nil
        
        responseTimer?.invalidate()
        responseTimer = nil
        
        // 🔧 取消所有DispatchWorkItem延迟任务
        listenModeTask?.cancel()
        listenModeTask = nil
        
        practiceWaitTask?.cancel()
        practiceWaitTask = nil
        
        responseWaitTask?.cancel()
        responseWaitTask = nil
        
        // 🔧 停止ChordPlayer
        chordPlayer.stopPlaying()
        
        // 🔧 CRITICAL: 立即停止所有正在播放的音符
        stopAllPlayingNotes()
        
        print("🔧 ProgressionGameManager: 所有计时器和延迟任务已停止")
    }
    
    /// 停止所有正在播放的音符
    private func stopAllPlayingNotes() {
        print("🔧 停止所有正在播放的音符")
        
        // 1. 停止当前期望音符集合中的所有音符
        for note in expectedNotes {
            midiManager.stopListenNote(note)
            print("🔧 停止期望音符: \(note)")
        }
        
        // 2. 停止目标和弦音符集合中的所有音符
        for note in targetChordNotes {
            midiManager.stopListenNote(note)
            print("🔧 停止目标音符: \(note)")
        }
        
        // 3. 如果有当前和弦进行，停止当前和弦的所有音符
        if let progression = currentProgression,
           currentChordIndex < progression.chords.count {
            let currentChord = progression.chords[currentChordIndex]
            let currentTransposition = transposeAllKeys ? practiceEngineState.currentTransposition : 0
            let currentNotes = currentChord.getMIDINotes(transpositionSemitones: currentTransposition)
            
            for note in currentNotes {
                midiManager.stopListenNote(note)
                print("🔧 停止当前和弦音符: \(note)")
            }
        }
        
        // 4. 额外保险：停止常用音符范围内的所有可能音符
        // 这确保即使有遗漏也能停止所有声音
        for note in 21...108 { // 钢琴音符范围
            midiManager.stopListenNote(note)
        }
        
        print("🔧 所有正在播放的音符已停止")
    }
    
    // MARK: - Game Logic
    
    /// 播放下一个和弦
    private func playNextChord(playbackType: ChordPlaybackType = .block_chord, bpm: Int = 100, keepHighlighted: Bool = true) {
        
        print("playNextChord:\(currentChordIndex)")
        
        guard let progression = currentProgression else { return }
        
        guard currentChordIndex < progression.chords.count else {
            
            // 🎵 练习模式：检查是否需要进入下一个循环
            if transposeAllKeys {
                return handleProgressionComplete(playbackType: playbackType, bpm: bpm, keepHighlighted: keepHighlighted)
            }
            
            // 检查是否为无限循环模式
            if isEndlessMode {
                print("🔄 无限循环模式：重新开始练习和弦进行")
                currentChordIndex = 0
                // 重置进度但不重置游戏状态
                playNextChord(playbackType: playbackType, bpm: bpm, keepHighlighted: keepHighlighted)
                return
            } else {
                // 所有和弦练习完毕
                completeGame()
                return
            }
        }
        
        let currentChord = progression.chords[currentChordIndex]
        
        // 🎵 获取当前移调值（练习模式）
        let currentTransposition = transposeAllKeys ? practiceEngineState.currentTransposition : 0
        
        //////////////////调整根音.让第一个和弦中心在[C4,B4],其他和弦按 progressionOctaveOffset 偏移
        // 🎵 直接从ChordInfo获取和弦信息，应用移调
        currentChordName = currentChord.getChordName(transpositionSemitones: currentTransposition)
        var currentNotes = currentChord.getMIDINotes(transpositionSemitones: currentTransposition)
        print("调整和弦中心 \(currentChordIndex)=====================================")
        // 🎵 如果是第一个和弦，调整整个和弦进行的中心音高
        if currentChordIndex == 0 {
            // 调整第一个和弦到目标中心范围，同时获取偏移量
            let result = PracticePatternEngine.adjustChordNotesToCenterRange(currentNotes)
            currentNotes = result.adjustedNotes
            adjustInterval = result.adjustmentInterval
        } else {
            // 后续和弦使用相同的偏移量
            currentNotes = currentNotes.map { $0 + adjustInterval }
        }
        //////////////////调整根音
        ///
        expectedNotes = Set(currentNotes)
        playedNotes.removeAll()
        
        print("弹和弦:currentTransposition \(currentTransposition) 和弦名:\(currentChordName) 音符:\(currentNotes)")
        
        // 🎯 使用统一的和弦匹配管理器设置期望音符
        chordMatchManager.setExpectedChord(currentNotes)
        
        // 🎯 设置chromaCircleViewModel的预期音符
        let rootNote = currentChord.getRootNote(transpositionSemitones: currentTransposition)
        chromaCircleViewModel?.setExpectedNotes(currentNotes,rootNote:rootNote)
        
        print("🎵 练习和弦 \(currentChordIndex + 1)/\(totalChordCount): \(currentChordName)")
        print("🎵 期望音符: \(currentNotes.sorted())")
        print("🎵 播放模式: \(playbackType.displayName)")
        
        // 🎵 练习模式：显示当前循环信息
        if transposeAllKeys {
            print("🎵 练习模式 - \(practiceEngineState.getProgressDescription())")
            print("🎵 当前移调: \(practiceEngineState.currentTransposition) 半音")
        }
        
        gameState = .playingChord
        
        // 🎵 使用实时的全局BPM或传入的BPM参数
        let actualBpm = bpm != 100 ? bpm : GameConfigManager.shared.config.gameSettings.globalBpm
        print("🎵 播放和弦使用BPM: \(actualBpm) (传入:\(bpm), 全局:\(GameConfigManager.shared.config.gameSettings.globalBpm))")

        // 使用ChordPlayer播放当前和弦，传入实时BPM参数
        chordPlayer.playChord(notes: currentNotes, velocity: 80, playbackType: playbackType, bpm: actualBpm)
        
        // 如果需要保持高亮，设置目标音符
        if keepHighlighted {
            targetChordNotes = Set(currentNotes)
            keyboardViewModel?.setTargetNotes(currentNotes)
            print("🔵 保持目标音符高亮: \(currentNotes.sorted())")
        }
        
        
        // 播放完成后的处理：只有练习模式才等待用户输入
        let playDuration = playbackType == .arpeggio_chord ?
            2.0 + Double(currentNotes.count) * (60.0 / Double(actualBpm) / 4.0) : 2.0
        
        if workoutMode {
            // Practice模式：播放完成后进入等待用户输入状态
            
            // 🔧 使用DispatchWorkItem管理Practice模式延迟任务，确保可以被取消
            practiceWaitTask = DispatchWorkItem { [weak self] in
                // 🔧 检查游戏状态，如果已停止则不执行
                guard let self = self, self.gameState == .playingChord else {
                    print("🔧 Practice模式延迟任务: 游戏状态不正确，取消执行")
                    return
                }
                
                self.gameState = .waitingForResponse
                print("🎯 等待玩家输入和弦: \(self.currentChordName)")
                print("🎯 状态切换时 - isPerfectMatch: \(self.chordMatchManager.isPerfectMatch)")
                print("🎯 状态切换时 - playedNotes: \(Array(self.playedNotes).sorted())")
                
                // 状态切换后立即检查是否在播放期间已经按对了
                if self.chordMatchManager.isPerfectMatch {
                    print("🎯 状态切换时检测到播放期间已按对，直接处理正确和弦")
                    self.handleCorrectChord()
                } else if !self.playedNotes.isEmpty {
                    print("🎯 状态切换时检查当前已有输入")
                    // 🎯 ChordMatchManager已经处理了匹配逻辑，这里只需要等待
                    print("🎯 当前匹配状态: \(self.chordMatchManager.currentMatchState)")
                } else {
                    print("🎯 状态切换时无匹配和弦，等待用户输入")
                    // 🔧 设置超时计时器（根据永远等待模式决定）
                    self.startWaitingTimer()
                }
            }
            
            if let task = practiceWaitTask {
                DispatchQueue.main.asyncAfter(deadline: .now() + playDuration, execute: task)
            }
        } else {
            // Listen模式：播放完成后直接播放下一个和弦
            print("🎵 Listen模式：播放完成，直接播放下一个和弦 CP1 \(DispatchTime.now())")
            
            // 🔧 使用DispatchWorkItem管理Listen模式延迟任务，确保可以被取消
            listenModeTask = DispatchWorkItem { [weak self] in
                // 🔧 检查游戏状态，如果已停止则不执行
                guard let self = self, self.gameState != .idle else {
                    print("🔧 Listen模式延迟任务: 游戏已停止，取消执行")
                    return
                }
                
                print("🎵 Listen模式：播放完成，直接播放下一个和弦 CP2 \(DispatchTime.now())")
                self.currentChordIndex += 1
                self.playNextChord(playbackType: playbackType, bpm: bpm, keepHighlighted: keepHighlighted)
            }
            
            if let task = listenModeTask {
                DispatchQueue.main.asyncAfter(deadline: .now() + playDuration, execute: task)
            }
        }
    }
    

    

    
    /// 处理MIDI输入
    private func handleMIDIInput(_ pressedNotes: Set<Int>) {
        //print("🎯 handleMIDIInput - 游戏状态: \(gameState)")
        //print("🎯 接收音符: \(Array(pressedNotes).sorted())")
        //print("🎯 期望音符: \(Array(expectedNotes).sorted())")
        
        // 只在游戏进行中的两个状态接收输入
        guard gameState == .playingChord || gameState == .waitingForResponse else {
            if(debugOut){
                print("🎯 ❌ 游戏状态不接受输入: \(gameState)")
            }
            return
        }
        
        // 更新当前按下的音符
        playedNotes = pressedNotes
        
        // 🎯 使用统一的和弦匹配管理器处理MIDI输入
        chordMatchManager.updatePressedNotes(Array(pressedNotes))
        
        // 🎯 更新Circle12NotesView的按下音符
        chromaCircleViewModel?.setPressedNotes(Array(pressedNotes))
    }
    
    // MARK: - 🎯 和弦匹配管理器回调处理
    
    /// 处理和弦匹配事件
    private func handleChordMatchEvent(_ event: ChordMatchEvent) {
        switch event {
        case .matchStateChanged(let state):
            print("🎯 和弦匹配状态变化: \(state)")
            
        case .perfectMatch:
            print("🎯 ✅ 完美匹配事件")
            
        case .repeatMatch:
            print("🎯 🔄 重复匹配，跳过反馈")
            
        case .partialMatch:
            print("🎯 ⚠️ 部分匹配")
            
        case .noMatch:
            print("🎯 ❌ 无匹配")
        }
    }
    
    /// 处理完美匹配
    private func handlePerfectMatch() {
        // 只在等待响应状态时处理完美匹配
        if gameState == .waitingForResponse {
            print("🎯 等待响应期间完美匹配 - 处理正确和弦")
            // 🌟 触发绿色光芒效果（在处理正确和弦之前）
             chordMatchManager.onPerfectMatch?()
            handleCorrectChord()
        } else if gameState == .playingChord {
            print("🎯 播放期间完美匹配 - 给出即时反馈")
            // 播放期间的匹配只给出音效反馈，不改变游戏状态
            chordMatchManager.onPerfectMatch?()
        }
    }
    
    /// 触发粒子爆炸
    private func triggerParticleExplosion() {
        // 粒子爆炸已经在ChromaCircleView中处理
        print("🎯 触发粒子爆炸")
    }
    
    /// 处理音效
    private func handleSoundEffect(_ event: ChordMatchEvent) {
        switch event {
        case .perfectMatch:
            SoundEffectManager.shared.playChordCorrect()
            print("🎵 播放正确音效 ding")
        default:
            break
        }
    }
    
    /// 处理视觉反馈
    private func handleVisualFeedback(_ event: ChordMatchEvent) {
        // 视觉反馈主要在各个View中处理
        switch event {
        case .perfectMatch:
            print("🎯 视觉反馈: 完美匹配")
        default:
            break
        }
    }
    
    /// 开始等待计时器（根据永远等待模式决定是否设置超时）
    private func startWaitingTimer() {
        // 先停止之前的计时器
        responseTimer?.invalidate()
        responseTimer = nil
        
        // 🔧 永远等待模式下不启动超时计时器
        if foreverWaitMode {
            print("🔄 永远等待模式：跳过超时计时器，等待用户完成")
            return
        }
        
        // 定时等待模式：设置超时计时器，使用实时全局BPM
        let actualBpm = GameConfigManager.shared.config.gameSettings.globalBpm
        let waitInterval = calculateChordWaitTime(bpm: actualBpm)
        print("⏱️ 定时等待模式：设置\(String(format: "%.2f", waitInterval))秒超时计时器（基于实时BPM=\(actualBpm)）")
        
        responseTimer = Timer.scheduledTimer(withTimeInterval: waitInterval, repeats: false) { [weak self] _ in
            print("⏱️ 等待超时，自动进入下一个和弦")
            self?.responseTimer = nil

            // 📊 发送统计通知：错误和弦（超时）
            NotificationCenter.default.post(
                name: NSNotification.Name("ProgressionChordIncorrect"),
                object: nil
            )

            // 超时处理：直接进入下一个和弦
            self?.playNextChord(playbackType: self?.currentPlaybackType ?? .block_chord,
                               bpm: self?.currentBpm ?? 100,
                               keepHighlighted: self?.currentKeepHighlighted ?? true)
        }
    }
    
    /// 处理正确的和弦
    private func handleCorrectChord() {
        correctChordCount += 1
        currentChordIndex += 1

        print("🎯 handleCorrectChord:正确! 进度: \(correctChordCount)/\(totalChordCount)")
        print("⏱️ 等待反应时间: \(GameConfigManager.shared.getResponseTime(bpm: currentBpm))秒")
        print("⏱️ 永远等待模式: \(foreverWaitMode)")

        // 📊 发送统计通知：正确和弦
        NotificationCenter.default.post(
            name: NSNotification.Name("ProgressionChordCorrect"),
            object: nil
        )

        // 🎬 每个和弦正确时触发和弦成功动画（通过通知系统）
        print("🎬 单个和弦正确！触发和弦成功动画")
        triggerChordSuccessAnimation()

        // 🎨 检查是否完成了一组和弦进行（触发绿色背景效果）
        if let progression = currentProgression,
           currentChordIndex == progression.chords.count {

            print("🎨 完成一组和弦进行！触发绿色背景效果")
            print("🎨 和弦进行: \(progression.name)，共 \(progression.chords.count) 个和弦")

            // 🎨 恢复背景特效
            triggerBackgroundEffects()
            
            //  完成进行的动画
            triggerProgressionCompleteAnimation()
        }

        // 🔧 取消超时计时器（如果有的话）
        responseTimer?.invalidate()
        responseTimer = nil

        // 🔧 立即停止当前和弦播放
        chordPlayer.stopPlaying()
        
        // 🎯 注意：音效播放现在由ChordMatchManager统一处理，避免重复播放
        
        // 进入反应时间等待状态
        gameState = .waitingResponse
        
        // 🔧 修复永远等待模式逻辑：无论是否为永远等待模式，弹对了都要等待responseTime后进入下一轮
        let actualBpm = GameConfigManager.shared.config.gameSettings.globalBpm
        let responseTime = GameConfigManager.shared.getResponseTime(bpm: actualBpm)
        print("⏱️ 设置\(String(format: "%.2f", responseTime))秒反应时间计时器（基于实时BPM=\(actualBpm)）")
        
        // 🔧 使用DispatchWorkItem管理反应时间延迟任务，确保可以被取消
        responseWaitTask = DispatchWorkItem { [weak self] in
            // 🔧 检查游戏状态，如果已停止则不执行
            guard let self = self, self.gameState == .waitingResponse else {
                print("🔧 反应时间延迟任务: 游戏状态不正确，取消执行")
                return
            }
            
            print("⏱️ 反应时间结束，自动进入下一个和弦")
            // 使用保存的参数继续下一个和弦
            self.playNextChord(playbackType: self.currentPlaybackType, 
                               bpm: self.currentBpm, 
                               keepHighlighted: self.currentKeepHighlighted)
        }
        
        if let task = responseWaitTask {
            DispatchQueue.main.asyncAfter(deadline: .now() + responseTime, execute: task)
        }
    }



    /// 🎨 触发
    private func triggerBackgroundEffects() {
        print("🎨 触发背景和光芒效果")

        // 1. 触发背景颜色变化
        NotificationCenter.default.post(
            name: NSNotification.Name("TriggerSuccessBackgroundAnimation"),
            object: nil
        )




    }

    /// 🎉 触发进行完成动画
    private func triggerProgressionCompleteAnimation() {
        guard let progression = currentProgression else {
            print("❌ 没有当前和弦进行")
            return
        }

        // 🎉 获取整个进行的所有音符（用于动画定位）
        var allProgressionNotes: Set<Int> = []
        for chord in progression.chords {
            let transposition = transposeAllKeys ? practiceEngineState.currentTransposition : 0
            let chordNotes = chord.getMIDINotes(transpositionSemitones: transposition)
            allProgressionNotes.formUnion(chordNotes.map { $0 % 12 })
        }

        let progressionNotes = Array(allProgressionNotes)
        print("🎉 进行完成！触发动画，涉及音符: \(progressionNotes)")

        // 🎉 通过通知系统触发进行完成动画
        NotificationCenter.default.post(
            name: NSNotification.Name("TriggerProgressionCompleteAnimation"),
            object: progressionNotes
        )

        print("✅ 进行完成动画通知发送完成")
    }

    /// 🎬 触发单个和弦成功动画
    private func triggerChordSuccessAnimation() {
        guard let progression = currentProgression else {
            print("❌ 没有当前和弦进行")
            return
        }

        // 🎬 获取当前正确的和弦（注意：currentChordIndex已经+1了，所以要-1）
        let chordIndex = currentChordIndex - 1
        guard chordIndex >= 0 && chordIndex < progression.chords.count else {
            print("❌ 和弦索引超出范围: \(chordIndex)")
            return
        }

        let currentChord = progression.chords[chordIndex]
        let transposition = transposeAllKeys ?
            practiceEngineState.currentTransposition : 0
        let chordName = currentChord.getChordName(transpositionSemitones: transposition)
        print("🎬 开始为当前和弦触发成功动画: \(chordName)")

        // 🎬 获取当前和弦的音符（半音索引）
        let chordNotes = currentChord.getMIDINotes(transpositionSemitones: transposition)
        let currentChordNotes = Array(Set(chordNotes.map { $0 % 12 }))

        guard !currentChordNotes.isEmpty else {
            print("❌ 当前和弦没有音符")
            return
        }

        print("🎬 当前和弦音符（半音索引）: \(currentChordNotes)")

        // 🎬 通过通知系统触发和弦成功动画
        NotificationCenter.default.post(
            name: NSNotification.Name("TriggerChordSuccessAnimation"),
            object: currentChordNotes
        )

        print("✅ 和弦成功动画通知发送完成")
    }

    /// 🎆 通过ChromaCircleView触发白色粒子爆炸（精确位置）- 保留实现但暂时不使用
    private func triggerWhiteParticleExplosionInChromaCircle() {
        guard let progression = currentProgression else {
            print("❌ 没有当前和弦进行")
            return
        }

        // 🎆 获取当前正确的和弦（注意：currentChordIndex已经+1了，所以要-1）
        let chordIndex = currentChordIndex - 1
        guard chordIndex >= 0 && chordIndex < progression.chords.count else {
            print("❌ 和弦索引超出范围: \(chordIndex)")
            return
        }

        let currentChord = progression.chords[chordIndex]
        let transposition = transposeAllKeys ?
            practiceEngineState.currentTransposition : 0
        let chordName = currentChord.getChordName(transpositionSemitones: transposition)
        print("🎆 开始通过ChromaCircle为当前和弦触发白色粒子爆炸: \(chordName)")

        // 🎆 获取当前和弦的音符（半音索引）
        let chordNotes = currentChord.getMIDINotes(transpositionSemitones: transposition)
        let currentChordNotes = Array(Set(chordNotes.map { $0 % 12 }))

        guard !currentChordNotes.isEmpty else {
            print("❌ 当前和弦没有音符")
            return
        }

        print("🎆 当前和弦音符（半音索引）: \(currentChordNotes)")

        // 🎆 通过通知系统调用ChromaCircleView的白色粒子方法
        NotificationCenter.default.post(
            name: NSNotification.Name("TriggerWhiteParticleExplosion"),
            object: currentChordNotes
        )

        print("✅ 白色粒子爆炸通知发送完成")
    }

    /// 🔧 检查是否即将完成整个游戏
    private func checkIfWillCompleteGame() -> Bool {
        guard let pattern = selectedPracticePattern else {
            return false
        }

        // 模拟计算下一个移调值，看是否会完成
        var tempState = practiceEngineState
        let result = PracticePatternEngine.calculateNextTransposition(
            practiceOffset: pattern.offset,
            state: &tempState
        )

        // 如果下一步会完成且不是无限循环模式，则即将完成游戏
        return result.isCompleted && !isEndlessMode
    }
    
    /// 完成游戏
    private func completeGame() {
        print("🎉 和弦进行\(workoutMode ? "练习" : "播放")完成!")
        print("🎉 总得分: \(correctChordCount)/\(totalChordCount)")

        // 🎉 结束计时（只在练习模式下）
        if workoutMode, let startTime = practiceStartTime {
            totalTime = Date().timeIntervalSince(startTime)
            print("⏱️ 练习完成，总时间: \(String(format: "%.1f", totalTime))秒")
            practiceStartTime = nil
        }

        if workoutMode {
            // 播放练习完成音效
            SoundEffectManager.shared.playPracticeComplete()
        }

        // 🎉 游戏完成：设置状态为idle让PlayStopButton恢复
        gameState = .idle  // 让PlayStopButton显示为"播放"状态

        // 🎉 只在练习模式下显示完成界面
        if workoutMode {
            showCompletionView = true  // 显示PracticeSummaryView
            print("🎉 练习模式完成，显示练习总结")
        } else {
            print("🎵 Listen模式完成，不显示练习总结")
            // 🔧 BugFix: Listen模式完成时也需要清理UI状态，特别是键盘按键
            resetAllUIStates()
            print("🔧 Listen模式完成，已清理所有UI状态包括键盘按键")
        }

        // 🎉 LevelClearView 将由用户主动关闭，不再自动关闭
        // 移除了原来的3秒自动关闭逻辑
    }

    /// 手动关闭完成界面并重置到空闲状态
    func dismissCompletionView() {
        print("🎉 用户手动关闭完成界面")
        showCompletionView = false
        // gameState 已经在 completeGame() 中设置为 .idle，这里不需要重复设置

        // 🔄 重置所有UI状态
        resetAllUIStates()
    }
    
    // MARK: - Public Methods
    
    /// 使用ProgressionReader加载和弦进行
    func loadProgression(fileName: String, transposeOffset: Int = 0) {
        progressionReader.loadProgression(fileName)
        
        // 设置移调
        if transposeOffset != 0 {
            progressionReader.transposeFromBase(transposeOffset)
        }
        
        // 🔧 使用DispatchWorkItem管理加载完成的延迟任务
        let loadCompleteTask = DispatchWorkItem { [weak self] in
            guard let self = self else { return }
            
            if let progression = self.progressionReader.currentProgression {
                // 直接使用ProgressionReader中的Progression
                self.currentProgression = progression
                self.totalChordCount = self.progressionReader.chordCount
                print("✅ 加载和弦进行: \(progression.name), 和弦数量: \(self.totalChordCount)")
            }
        }
        
        // 等待加载完成后更新当前进行
        // 注意：这个任务通常不需要取消，因为是加载操作
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1, execute: loadCompleteTask)
    }
    
    /// 重播当前和弦
    func replayCurrentChord() {
        guard gameState == .waitingForResponse,
              let progression = currentProgression,
              currentChordIndex < progression.chords.count else { return }
        
        let currentChord = progression.chords[currentChordIndex]
        
        // 🎵 获取当前移调值（练习模式）
        let currentTransposition = transposeAllKeys ? practiceEngineState.currentTransposition : 0
        
        // 🎵 直接从ChordInfo获取和弦信息，应用移调
        let chordName = currentChord.getChordName(transpositionSemitones: currentTransposition)
        let chordNotes = currentChord.getMIDINotes(transpositionSemitones: currentTransposition)
        print("🔄 重播当前和弦: \(chordName)")
        print("🔄 重播音符: \(chordNotes)")
        
        // 使用ChordPlayer重播当前和弦，传入BPM参数
        chordPlayer.playChord(notes: chordNotes, velocity: 80, playbackType: currentPlaybackType, bpm: currentBpm)
        
        // 如果需要保持高亮，设置目标音符
        if currentKeepHighlighted {
            targetChordNotes = Set(chordNotes)
            keyboardViewModel?.setTargetNotes(chordNotes)
        }
    }
    
    /// 获取当前进度百分比
    var progressPercentage: Double {
        guard totalChordCount > 0 else { return 0.0 }
        return Double(correctChordCount) / Double(totalChordCount)
    }
    
    /// 获取进度文本
    var progressText: String {
        if transposeAllKeys {
            return "\(practiceEngineState.getProgressDescription()) - 和弦 \(correctChordCount)/\(totalChordCount)"
        } else {
            return "\(correctChordCount)/\(totalChordCount)"
        }
    }
    
    // MARK: - Practice Pattern Access
    
    /// 获取练习模式管理器
    var patternManager: PracticePatternManager {
        return practicePatternManager
    }
    
    // MARK: - UI State Reset
    
    /// 重置所有UI状态
    private func resetAllUIStates() {
        print("🔄 重置所有UI状态")
        
        // 1. 重置PianoKeyboardView状态
        keyboardViewModel?.userReleaseAllKeys()
        keyboardViewModel?.appReleaseAllKeys()
        keyboardViewModel?.clearTargetNotes()
        print("🎹 键盘状态已重置")
        
        // 2. 重置ChromaCircleView状态
        chromaCircleViewModel?.clearAll()
        print("🎯 ChromaCircle状态已重置")
        
        // 3. 重置和弦匹配管理器状态
        chordMatchManager.clearAll()
        print("🎯 和弦匹配管理器状态已重置")
        
        // 4. 清理当前高亮音符
        targetChordNotes.removeAll()
        expectedNotes.removeAll()
        playedNotes.removeAll()
        currentChordName = ""
        print("🎵 高亮音符已清除")
        
        // 5. 发送通知让其他UI组件重置状态（可选，如果有订阅者）
        NotificationCenter.default.post(name: NSNotification.Name("ProgressionGameUIReset"), object: nil)
        print("📢 UI重置通知已发送")
    }
} 
