//
//  StoreKitManager.swift
//  VoicingTrainer
//
//  Created by <PERSON> Li on 2025/1/20.
//

import Foundation
import StoreKit
import Combine

// MARK: - Product Identifiers
enum ProductIdentifier: String, CaseIterable {
    case pro = "com.voicingtrainer.pro"
    case jazzProgressions = "com.voicingtrainer.dlc.jazz_progressions"
    case masterVoicings = "com.voicingtrainer.dlc.master_voicings"
    
    var displayName: String {
        switch self {
        case .pro:
            return NSLocalizedString("PRO_VERSION", value: "VoicingTrainer Pro", comment: "Pro version name")
        case .jazzProgressions:
            return NSLocalizedString("JAZZ_PROGRESSIONS_PACK", value: "Jazz Progressions Pack", comment: "Jazz progressions DLC name")
        case .masterVoicings:
            return NSLocalizedString("MASTER_VOICINGS_PACK", value: "Master Voicings Pack", comment: "Master voicings DLC name")
        }
    }
}

// MARK: - Purchase Error
enum PurchaseError: Error, LocalizedError {
    case productNotFound
    case purchaseFailed(String)
    case verificationFailed
    case userCancelled
    case networkError
    case unknown
    
    var errorDescription: String? {
        switch self {
        case .productNotFound:
            return NSLocalizedString("PRODUCT_NOT_FOUND", value: "Product not found", comment: "Product not found error")
        case .purchaseFailed(let reason):
            return NSLocalizedString("PURCHASE_FAILED", value: "Purchase failed: \(reason)", comment: "Purchase failed error")
        case .verificationFailed:
            return NSLocalizedString("VERIFICATION_FAILED", value: "Purchase verification failed", comment: "Verification failed error")
        case .userCancelled:
            return NSLocalizedString("USER_CANCELLED", value: "Purchase cancelled by user", comment: "User cancelled error")
        case .networkError:
            return NSLocalizedString("NETWORK_ERROR", value: "Network error", comment: "Network error")
        case .unknown:
            return NSLocalizedString("UNKNOWN_ERROR", value: "Unknown error occurred", comment: "Unknown error")
        }
    }
}

// MARK: - Purchase State
enum PurchaseState {
    case notPurchased
    case purchasing
    case purchased
    case failed(PurchaseError)
    case restored
}

// MARK: - StoreKit Manager
@MainActor
class StoreKitManager: ObservableObject {
    static let shared = StoreKitManager()
    
    // MARK: - Published Properties
    @Published private(set) var products: [Product] = []
    @Published private(set) var purchasedProductIDs: Set<String> = []
    @Published private(set) var purchaseState: PurchaseState = .notPurchased
    @Published private(set) var isLoading = false
    @Published private(set) var errorMessage: String?
    
    // MARK: - Private Properties
    private var updateListenerTask: Task<Void, Error>?
    private let userDefaults = UserDefaults.standard
    private let purchasedProductsKey = "purchased_products"
    
    private init() {
        // 加载已购买的产品
        loadPurchasedProducts()
        
        // 开始监听交易更新
        updateListenerTask = listenForTransactions()
        
        // 请求产品信息
        Task {
            await requestProducts()
        }
    }
    
    deinit {
        updateListenerTask?.cancel()
    }
    
    // MARK: - Public Methods
    
    /// 请求产品信息
    func requestProducts() async {
        isLoading = true
        errorMessage = nil

        print("🛒 开始请求产品信息...")
        print("🛒 产品ID列表: \(ProductIdentifier.allCases.map { $0.rawValue })")

        do {
            let productIdentifiers = ProductIdentifier.allCases.map { $0.rawValue }
            let storeProducts = try await Product.products(for: productIdentifiers)

            print("🛒 从StoreKit获取到 \(storeProducts.count) 个产品")
            for product in storeProducts {
                print("🛒 产品: \(product.id) - \(product.displayName) - \(product.displayPrice)")
            }

            // 按照我们定义的顺序排序
            let sortedProducts = ProductIdentifier.allCases.compactMap { identifier in
                storeProducts.first { $0.id == identifier.rawValue }
            }

            products = sortedProducts
            print("✅ 成功加载 \(products.count) 个产品")

            if products.isEmpty {
                errorMessage = "未找到任何产品。请检查StoreKit配置或网络连接。"
                print("⚠️ 警告：没有加载到任何产品")
            }

        } catch {
            errorMessage = error.localizedDescription
            print("❌ 加载产品失败: \(error)")
            print("❌ 错误详情: \(error)")

            // 提供更详细的错误信息
            if error.localizedDescription.contains("network") {
                errorMessage = "网络连接问题，请检查网络设置"
            } else if error.localizedDescription.contains("configuration") {
                errorMessage = "StoreKit配置问题，请联系开发者"
            }
        }

        isLoading = false
    }
    
    /// 购买产品
    func purchase(_ product: Product) async throws {
        purchaseState = .purchasing
        errorMessage = nil
        
        do {
            let result = try await product.purchase()
            
            switch result {
            case .success(let verification):
                let transaction = try await checkVerified(verification)
                
                // 更新购买状态
                await updatePurchasedProducts(transaction)
                
                // 完成交易
                await transaction.finish()
                
                purchaseState = .purchased
                print("✅ 购买成功: \(product.id)")
                
            case .pending:
                purchaseState = .notPurchased
                print("⏳ 购买待处理: \(product.id)")
                
            case .userCancelled:
                purchaseState = .failed(.userCancelled)
                print("❌ 用户取消购买: \(product.id)")
                
            @unknown default:
                purchaseState = .failed(.unknown)
                print("❌ 未知购买结果: \(product.id)")
            }
            
        } catch {
            purchaseState = .failed(.purchaseFailed(error.localizedDescription))
            errorMessage = error.localizedDescription
            print("❌ 购买失败: \(error)")
            throw PurchaseError.purchaseFailed(error.localizedDescription)
        }
    }
    
    /// 恢复购买
    func restorePurchases() async {
        isLoading = true
        errorMessage = nil
        
        do {
            try await AppStore.sync()
            
            var restoredAny = false
            for await result in Transaction.currentEntitlements {
                do {
                    let transaction = try await checkVerified(result)
                    await updatePurchasedProducts(transaction)
                    restoredAny = true
                } catch {
                    print("❌ 恢复购买验证失败: \(error)")
                }
            }
            
            if restoredAny {
                purchaseState = .restored
                print("✅ 成功恢复购买")
            } else {
                errorMessage = NSLocalizedString("NO_PURCHASES_TO_RESTORE", value: "No purchases to restore", comment: "No purchases to restore")
                print("ℹ️ 没有可恢复的购买")
            }
            
        } catch {
            errorMessage = error.localizedDescription
            print("❌ 恢复购买失败: \(error)")
        }
        
        isLoading = false
    }
    
    /// 检查产品是否已购买
    func isPurchased(_ productIdentifier: ProductIdentifier) -> Bool {
        return purchasedProductIDs.contains(productIdentifier.rawValue)
    }
    
    /// 检查是否已购买Pro版本
    func isProUnlocked() -> Bool {
        return isPurchased(.pro)
    }
    
    /// 获取产品价格显示文本
    func priceText(for product: Product) -> String {
        return product.displayPrice
    }
    
    // MARK: - Private Methods
    
    /// 监听交易更新
    private func listenForTransactions() -> Task<Void, Error> {
        return Task.detached {
            for await result in Transaction.updates {
                do {
                    let transaction = try await self.checkVerified(result)
                    await self.updatePurchasedProducts(transaction)
                    await transaction.finish()
                } catch {
                    print("❌ 交易更新处理失败: \(error)")
                }
            }
        }
    }
    
    /// 验证交易
    private func checkVerified<T>(_ result: VerificationResult<T>) async throws -> T {
        switch result {
        case .unverified:
            throw PurchaseError.verificationFailed
        case .verified(let safe):
            return safe
        }
    }
    
    /// 更新已购买产品列表
    private func updatePurchasedProducts(_ transaction: Transaction) async {
        if transaction.revocationDate == nil {
            // 产品未被撤销，添加到已购买列表
            purchasedProductIDs.insert(transaction.productID)
        } else {
            // 产品被撤销，从已购买列表移除
            purchasedProductIDs.remove(transaction.productID)
        }
        
        // 保存到 UserDefaults
        savePurchasedProducts()
        
        // 更新 PurchaseManager 状态
        updatePurchaseManagerState()
    }
    
    /// 保存已购买产品到本地
    private func savePurchasedProducts() {
        let productArray = Array(purchasedProductIDs)
        userDefaults.set(productArray, forKey: purchasedProductsKey)
        print("💾 保存购买状态: \(productArray)")
    }
    
    /// 从本地加载已购买产品
    private func loadPurchasedProducts() {
        let productArray = userDefaults.stringArray(forKey: purchasedProductsKey) ?? []
        purchasedProductIDs = Set(productArray)
        print("📁 加载购买状态: \(productArray)")
        
        // 更新 PurchaseManager 状态
        updatePurchaseManagerState()
    }
    
    /// 更新 PurchaseManager 的状态
    private func updatePurchaseManagerState() {
        Task { @MainActor in
            if isProUnlocked() {
                PurchaseManager.shared.unlockPro()
            } else {
                PurchaseManager.shared.lockPro()
            }
        }
    }
    
    /// 获取产品按标识符
    func product(for identifier: ProductIdentifier) -> Product? {
        return products.first { $0.id == identifier.rawValue }
    }
    
    /// 检查是否有网络连接（简单实现）
    private func hasNetworkConnection() -> Bool {
        // 这里可以实现更复杂的网络检测逻辑
        return true
    }
}

// MARK: - Extensions for Testing
extension StoreKitManager {
    /// 仅用于测试：模拟购买
    func simulatePurchase(_ productIdentifier: ProductIdentifier) {
        purchasedProductIDs.insert(productIdentifier.rawValue)
        savePurchasedProducts()
        updatePurchaseManagerState()
        purchaseState = .purchased
    }
    
    /// 仅用于测试：清除所有购买
    func clearAllPurchases() {
        purchasedProductIDs.removeAll()
        savePurchasedProducts()
        updatePurchaseManagerState()
        purchaseState = .notPurchased
    }
} 