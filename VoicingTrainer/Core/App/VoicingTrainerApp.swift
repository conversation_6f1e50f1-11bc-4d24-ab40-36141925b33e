//
//  VoicingTrainerApp.swift
//  VoicingTrainer
//
//  Created on 2024/11/22.
//

import SwiftUI

@main
struct VoicingTrainerApp: App {
    
    init() {
        #if os(iOS)
        // 在App初始化时就设置横屏
        DispatchQueue.main.async {
            forceLandscapeOrientation()
        }
        #endif
        
        // 测试GameConfig加载状态
        print("🚀 应用初始化开始")
        GameConfigManager.shared.testConfigLoading()
        print("🚀 应用初始化完成")
    }
    
    var body: some Scene {
        WindowGroup {
            MusicAppView()
                // 🔒 强制横屏显示 - 在应用启动时就应用
                #if os(iOS)
                .landscapeOnly()
                .onAppear {
                    // 在视图出现时再次确保横屏
                    forceLandscapeOrientation()
                }
                #endif
        }
        #if os(macOS)
        // 🖥️ macOS窗口配置 - 设置合适的最小窗口尺寸，避免标题被挤压
        .windowResizability(.contentSize)
        .defaultSize(width: 1024, height: 768) // 横屏比例的默认尺寸
        .windowToolbarStyle(.unifiedCompact) // 使用紧凑式工具栏样式
        .defaultPosition(.center) // 默认居中显示
        #endif
    }
}

// MARK: - 横屏支持扩展

#if os(iOS)
// 🔒 全局横屏强制函数
func forceLandscapeOrientation() {
    // 使用现代API（iOS 16+）
    if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene {
        Task { @MainActor in
            let geometryPreferences = UIWindowScene.GeometryPreferences.iOS(interfaceOrientations: .landscape)
            windowScene.requestGeometryUpdate(geometryPreferences) { error in
                print("🔄 Landscape orientation update: \(error.localizedDescription)")
            }
        }
    }
}

// 🎯 针对iOS设备的屏幕方向控制
struct LandscapeOnlyModifier: ViewModifier {
    func body(content: Content) -> some View {
        content
            .onAppear {
                // 强制设置为横屏模式
                forceLandscapeOrientation()
            }
            .onReceive(NotificationCenter.default.publisher(for: UIDevice.orientationDidChangeNotification)) { _ in
                // 监听设备旋转，确保始终保持横屏
                ensureLandscapeOrientation()
            }
    }
    
    private func ensureLandscapeOrientation() {
        let orientation = UIDevice.current.orientation
        
        // 📱 如果检测到竖屏，强制回到横屏
        if orientation.isPortrait || orientation.isFlat || orientation == .unknown {
            print("🔄 Detected non-landscape orientation: \(orientation), forcing landscape...")
            forceLandscapeOrientation()
        }
    }
}

extension View {
    // 🎯 便捷的横屏限制修饰符
    func landscapeOnly() -> some View {
        self.modifier(LandscapeOnlyModifier())
    }
}
#endif
