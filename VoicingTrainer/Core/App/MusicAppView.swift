//
//  MusicAppView.swift
//  FullUI
//
//  Created by <PERSON> on 2025/5/26.
//
/*
 几种常用键盘布局的MIDI值范围如下:
 61键(36,96)
 49键(36,84)
 37键(48,84)
 25键(48,72)
 */

import SwiftUI
import Lottie

struct MusicAppView: View {
    @State private var selectedTab: Tab = .progressions
    @State private var showSettings = false // 控制设置面板显示
    @State private var showAbout = false // 控制关于面板显示
    @State private var showHelp = false // 控制帮助视图显示
    @State private var showProUpgradePopover = false // 控制Pro升级弹窗显示
    @State private var currentGameState: AppGameState = .idle // 添加游戏状态管理

    
    @StateObject private var midiManager = MIDIManager() // 添加MIDI管理器
    @ObservedObject private var configManager = GameConfigManager.shared // 添加配置管理器
    @StateObject private var sharedKeyboardViewModel = PianoKeyboardViewModel() // 共享的键盘ViewModel
    @StateObject private var purchaseManager = PurchaseManager.shared // 内购管理器
    @StateObject private var practiceStateManager = PracticeStateManager.shared // 练习状态管理器
    
    // MARK: - MIDI路由系统
    @StateObject private var midiRoutingManager = MIDIRoutingManager()
    
    enum Tab: Int, CaseIterable {
        case notes, chords, progressions, statistics,new_prog   //, songs,new_prog
        
        var title: String {
            switch self {
            case .notes: return "Notes"
            case .chords: return "Chords"
            case .progressions: return "Progressions"
            case .statistics: return "Statistics"
        //    case .songs: return "Songs"
            case .new_prog: return "Create New Progressions"
            }
        }
        
        var icon: String {
            switch self {
            case .notes: return "music.note"
            case .chords: return "music.quarternote.3"
            case .progressions: return "pianokeys.inverse"
            case .statistics: return "chart.bar.xaxis.ascending.badge.clock"
         //   case .songs: return "doc.text"
            case .new_prog: return "bookmark.slash.fill"
            }
        }
    }
    
    // 统一的游戏状态枚举
    enum AppGameState {
        case idle                   //  空闲
        case playing                //  播放
        case waitingForResponse     //  等待用户反应
        case completed              //  完成
    }
    
    var body: some View {
        VStack {
            // 🧪 测试提醒视图（仅在Debug模式显示）
            //TestReminderView()
            
            VStack(spacing: 0) {
                // 顶部导航栏
                topNavigationBar
                
                // 主内容区
                contentView
                
                // 钢琴键盘视图 - 使用配置中的键盘布局
                PianoKeyboardView(
                    viewModel: sharedKeyboardViewModel,
                    firstKey: configManager.config.keyboardSettings.selectedLayout.firstKey,
                    lastKey: configManager.config.keyboardSettings.selectedLayout.lastKey,
                    onKeyDown: { noteNumber, velocity in 
                        // 🎹 触摸键盘完全等效于MIDI键盘：更新游戏状态并播放声音
                        midiManager.noteOn(noteNumber, velocity: velocity)
                    },
                    onKeyUp: { noteNumber in 
                        // 🎹 触摸键盘完全等效于MIDI键盘：更新游戏状态并停止声音
                        midiManager.noteOff(noteNumber)
                    }
                )
                .frame(height: 120) // 设置固定高度
                .background(Color.white)
                .overlay(
                    Rectangle()
                        .stroke(Color.gray.opacity(0.3), lineWidth: 1)
                )
            }
            .edgesIgnoringSafeArea(.bottom)
            // 🔒 强制横屏显示 - 确保iPad获得最大可用空间，禁止竖屏旋转
            #if os(iOS)
            .landscapeOnly() // 应用自定义的横屏限制修饰符
            .preferredColorScheme(.dark) // 可选：在横屏模式下使用深色主题
            #endif
            .onAppear {
                // 🔧 MIDI-Keyboard Sync: 建立MIDIManager和键盘视图模型之间的连接
                midiManager.keyboardViewModel = sharedKeyboardViewModel
                
                // 🎛️ 初始化MIDI路由器
                updateActivePage(for: selectedTab)
            }
        }
        #if os(macOS)
        // 🖥️ macOS专用：设置最小窗口尺寸，防止标题被挤压
        .frame(minWidth: 600, minHeight: 400)
        #endif
        .environmentObject(midiManager)
        .environmentObject(sharedKeyboardViewModel)
        .environment(\.midiRoutingManager, midiRoutingManager)

        // MARK: - 统一MIDI路由处理
        .onChange(of: selectedTab) { newTab in
            // 当切换Tab时，更新活跃页面
            updateActivePage(for: newTab)
            
            // 🎹 重要：切换游戏模式时必须重置键盘状态，清除所有按键显示
            sharedKeyboardViewModel.appReleaseAllKeys()
            sharedKeyboardViewModel.userReleaseAllKeys()
            sharedKeyboardViewModel.clearTargetNotes()
            
            // 📊 统计系统：切换到统计页面时刷新数据
            if newTab == .statistics {
                NotificationCenter.default.post(name: NSNotification.Name("RefreshStatistics"), object: nil)
                print("📊 切换到统计页面，发送刷新通知")
            }
            
            print("🎹 模式切换到 \(newTab.title)，已重置键盘状态")
        }
        
        .onChange(of: midiManager.pressedNotes) {
            notes in
            
            let _ = print("MusicAppView onChange  \(notes)")
            // 统一的MIDI输入路由处理
            midiRoutingManager.routeMIDIInput(notes)
        }
        
        .onChange(of: midiManager.sustainOn) { sustainOn in
            // 统一的踏板输入路由处理
            midiRoutingManager.routeSustainInput(sustainOn)
        }
        .onReceive(NotificationCenter.default.publisher(for: NSNotification.Name("BackgroundInactivityTimeout"))) { _ in
            handleBackgroundInactivityTimeout()
        }
        .sheet(isPresented: $showProUpgradePopover) {
            ZStack {
                Color.black.opacity(0.3)
                    .ignoresSafeArea()
                
                ProUpgradeView(
                    featureName: "创建新和弦进行",
                    onUpgrade: {
                        // 升级完成后的回调
                        showProUpgradePopover = false
                    },
                    onDismiss: {
                        // 🔧 关闭ProUpgradeView
                        showProUpgradePopover = false
                    }
                )
            }
        }
        .sheet(isPresented: $showHelp) {
            HelpView()
        }
    }
    
    // MARK: - 顶部导航栏
    var topNavigationBar: some View {
        HStack(spacing: 16) {
            // 左侧：Dropdown ComboBox
            Menu {
                ForEach(Tab.allCases, id: \.self) { tab in
                    Button(action: {
                        if currentGameState == .idle {
                            // 添加触觉反馈
                            #if os(iOS)
                            let impactFeedback = UIImpactFeedbackGenerator(style: .light)
                            impactFeedback.impactOccurred()
                            #endif
                            
                            // 使用更生动的动画
                            withAnimation(.spring(response: 0.5, dampingFraction: 0.7, blendDuration: 0.1)) {
                                selectedTab = tab
                            }
                        }
                    }) {
                        HStack {
                            Image(systemName: tab.icon)
                            Text(tab.title)
                            Spacer()
                            if selectedTab == tab {
                                Image(systemName: "checkmark")
                                    .foregroundColor(.blue)
                            }
                        }
                    }
                    .disabled(currentGameState != .idle)
                }
            } label: {
                HStack {
                    Image(systemName: selectedTab.icon)
                    Text(selectedTab.title)
                        .font(.title2)
                        .fontWeight(.semibold)
                    Image(systemName: "chevron.down")
                        .font(.caption)
                }
                .foregroundColor(currentGameState == .idle ? .primary : .secondary)
                .padding(.horizontal, 16)
                .padding(.vertical, 8)
                .background(
                    RoundedRectangle(cornerRadius: 8)
                        .fill(.ultraThinMaterial)
                        .opacity(currentGameState == .idle ? 1.0 : 0.6)
                )
            }
            .disabled(currentGameState != .idle)
            
            // 游戏状态指示器（当游戏进行中时显示）
            if currentGameState != .idle {
                HStack(spacing: 4) {
                    Circle()
                        .fill(statusColor)
                        .frame(width: 8, height: 8)
                    Text(statusText)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                .padding(.horizontal, 8)
                .padding(.vertical, 4)
                .background(
                    RoundedRectangle(cornerRadius: 4)
                        .fill(.ultraThinMaterial)
                )
            }
            
            Spacer()
            
            // 右侧：耳机图标和设置按钮
            HStack(spacing: 12) {
                
                Text("延音踏板: ")
                Circle()
                    .fill(midiManager.sustainOn ? Color.green : Color.gray)
                    .frame(width: 16, height: 16)
                
                //设置按钮
                Button(action: {
                    if currentGameState == .idle {
                        showSettings.toggle()
                    }
                }) {
                    Image(systemName: "gearshape")
                        .foregroundColor(currentGameState == .idle ? .gray : .secondary)
                }
                .disabled(currentGameState != .idle)
                .popover(isPresented: $showSettings, arrowEdge: .top) {
                    let _ = print("SettingsPopoverView CP1")
                    SettingsPopoverView(isPresented: $showSettings, midiManager: midiManager)
                }
                
                // 关于按钮
                Button(action: {
                    if currentGameState == .idle {
                        showAbout.toggle()
                    }
                }) {
                    Image(systemName: "info.circle")
                        .foregroundColor(currentGameState == .idle ? .gray : .secondary)
                }
                .disabled(currentGameState != .idle)
                .popover(isPresented: $showAbout, arrowEdge: .top) {
                    AboutPopoverView(isPresented: $showAbout)
                }
                
                // 帮助按钮
                Button(action: {
                    if currentGameState == .idle {
                        showHelp = true
                    }
                }) {
                    Image(systemName: "questionmark.circle")
                        .foregroundColor(currentGameState == .idle ? .gray : .secondary)
                }
                .disabled(currentGameState != .idle)
                
            }
        }
        .padding(.horizontal)
        .padding(.vertical, 8)
        .background(.ultraThinMaterial)
    }
    
    var contentView: some View {
        // 带动画的标签页切换容器
        GeometryReader { geometry in
            ZStack {
                // 为每个标签页创建独立的视图
                ForEach(Tab.allCases, id: \.self) { tab in
                    viewForTab(tab)
                        .frame(width: geometry.size.width, height: geometry.size.height)
                        .opacity(selectedTab == tab ? 1 : 0)
                        .scaleEffect(selectedTab == tab ? 1.0 : 0.9) // 增强缩放效果
                        .offset(x: offsetForTab(tab, screenWidth: geometry.size.width))
                        .rotation3DEffect(
                            .degrees(selectedTab == tab ? 0 : (tab.rawValue > selectedTab.rawValue ? 25 : -25)),
                            axis: (x: 0, y: 1, z: 0),
                            perspective: 0.8
                        ) // 增强3D旋转效果
                        .blur(radius: selectedTab == tab ? 0 : 2) // 添加模糊效果
                        .brightness(selectedTab == tab ? 0 : -0.1) // 添加亮度变化
                        .animation(
                            .spring(response: 0.8, dampingFraction: 0.7, blendDuration: 0.2)
                            .delay(selectedTab == tab ? 0.05 : 0), // 当前标签页稍作延迟，增加层次感
                            value: selectedTab
                        )
                        .zIndex(selectedTab == tab ? 1 : 0)
                }
            }
            .clipped()
        }
    }
    
    // 计算每个标签页的偏移位置
    private func offsetForTab(_ tab: Tab, screenWidth: CGFloat) -> CGFloat {
        if selectedTab == tab {
            return 0 // 当前选中的标签页在中心
        } else if tab.rawValue > selectedTab.rawValue {
            return screenWidth * 1.2 // 右侧标签页在屏幕右边更远处
        } else {
            return -screenWidth * 1.2 // 左侧标签页在屏幕左边更远处
        }
    }
    
    @ViewBuilder
    private func viewForTab(_ tab: Tab) -> some View {
        switch tab {
        case .statistics:
            StatisticsView()
        case .progressions:
            ProgressionsView(midiManager: midiManager, keyboardViewModel: sharedKeyboardViewModel)
                .onReceive(NotificationCenter.default.publisher(for: NSNotification.Name("ProgressionGameStateChanged"))) { notification in
                    if let state = notification.object as? ProgressionGameState {
                        updateGameState(from: state)
                    }
                }
                .onReceive(NotificationCenter.default.publisher(for: NSNotification.Name("ProgressionPlayingStateChanged"))) { notification in
                    if let isPlaying = notification.object as? Bool {
                        // 处理本地播放状态变化
                        currentGameState = isPlaying ? .playing : .idle
                    }
                }
        case .notes:
            NotesView(midiManager: midiManager)
                .onReceive(NotificationCenter.default.publisher(for: NSNotification.Name("NoteGameStateChanged"))) { notification in
                    if let state = notification.object as? GameState {
                        updateGameState(from: state)
                    }
                }
        case .chords:
            ChordsView(midiManager: midiManager, keyboardViewModel: sharedKeyboardViewModel)
                .onReceive(NotificationCenter.default.publisher(for: NSNotification.Name("ChordGameStateChanged"))) { notification in
                    if let state = notification.object as? ChordGameState {
                        updateGameState(from: state)
                    }
                }

            
        case .new_prog:
            if PurchaseManager.shared.isProUnlocked {
                NewProgressionView(midiManager: midiManager)
                    //.environmentObject(midiManager)
                    .environment(\.midiRoutingManager,midiRoutingManager)
            } else {
                // 显示Pro升级提示
                ZStack {
                    Color.black.opacity(0.1)
                        .ignoresSafeArea()
                    
                    VStack(spacing: 24) {
                        Image(systemName: "lock.fill")
                            .font(.system(size: 64))
                            .foregroundColor(.orange)
                        
                        Text("创建新和弦进行")
                            .font(.title.bold())
                        
                        Text("此功能需要Pro版本")
                            .font(.title3)
                            .foregroundColor(.secondary)
                        
                        Button("升级到Pro版") {
                            showProUpgradePopover = true
                        }
                        .buttonStyle(.borderedProminent)
                        .controlSize(.large)
                    }
                }
            }
            /*
        case .songs:
            Text(tab.title)
                .font(.title)
                .frame(maxHeight: .infinity)
             */
        }
    }
    
    // MARK: - 游戏状态管理
    
    private var statusColor: Color {
        switch currentGameState {
        case .idle:
            return .gray
        case .playing:
            return .green
        case .waitingForResponse:
            return .orange
        case .completed:
            return .blue
        }
    }
    
    private var statusText: String {
        switch currentGameState {
        case .idle:
            return "Ready"
        case .playing:
            return "Playing"
        case .waitingForResponse:
            return "Waiting"
        case .completed:
            return "Complete"
        }
    }
    
    private func updateGameState(from noteState: GameState) {
        switch noteState {
        case .idle:
            currentGameState = .idle
        case .playing:
            currentGameState = .playing
        case .waitingForResponse:
            currentGameState = .waitingForResponse
        case .completed:
            currentGameState = .completed
        case .countdown:
            currentGameState = .playing // 倒计时期间显示为playing状态
        }
    }
    
    private func updateGameState(from chordState: ChordGameState) {
        switch chordState {
        case .idle:
            currentGameState = .idle
        case .playingChord:
            currentGameState = .playing
        case .waitingForResponse:
            currentGameState = .waitingForResponse
        case .completed:
            currentGameState = .completed
        }
    }
    
    private func updateGameState(from progressionState: ProgressionGameState) {
        switch progressionState {
        case .idle:
            currentGameState = .idle
        case .playingChord:
            currentGameState = .playing
        case .waitingForResponse:
            currentGameState = .waitingForResponse
        case .waitingResponse:
            currentGameState = .waitingForResponse
        case .completed:
            currentGameState = .completed
        }
    }
    


    
    
    // MARK: - 统一MIDI路由处理
    
    /// 更新当前活跃页面
    private func updateActivePage(for newTab: Tab) {
        let activePage: MIDIRoutingManager.ActivePage
        switch newTab {
        case .notes:
            activePage = .notes
        case .chords:
            activePage = .chords
        case .progressions:
            activePage = .progressions
        case .statistics:
            activePage = .none
        case .new_prog:
            activePage = .newProgression
            /*
        case .songs:
            activePage = .none
       */
       }
        
        midiRoutingManager.setActivePage(activePage)
    }

    // MARK: - 🔋 后台节能管理

    /// 处理后台不活跃超时
    private func handleBackgroundInactivityTimeout() {
        print("🔋 应用后台不活跃超时 - 音乐练习应用应该在前台使用")

        // 🎵 停止所有音频播放
        // 这里可以添加停止音频的逻辑

        // 🎹 清理MIDI状态
        sharedKeyboardViewModel.userReleaseAllKeys()
        sharedKeyboardViewModel.clearTargetNotes()

        // 📱 在iOS上，可以考虑显示本地通知提醒用户
        #if os(iOS)
        scheduleBackgroundInactivityNotification()
        #endif

        print("🔋 已清理应用状态，建议用户重新打开应用进行练习")
    }

    #if os(iOS)
    /// 📱 安排后台不活跃通知
    private func scheduleBackgroundInactivityNotification() {
        // 这里可以添加本地通知逻辑，提醒用户重新打开应用
        // 例如："VoicingTrainer已暂停以节省电量，点击重新开始练习"
        print("📱 可以在这里添加本地通知逻辑")
    }
    #endif
}


