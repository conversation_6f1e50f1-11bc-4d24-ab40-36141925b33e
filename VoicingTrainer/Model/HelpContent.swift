import Foundation

// MARK: - 帮助内容数据模型

struct HelpContent: Codable {
    let pages: [HelpPage]
}

struct HelpPage: Codable, Identifiable {
    let id: Int
    let title: String
    let content: String
    let image: String?
    let hasImage: Bool
    
    enum CodingKeys: String, CodingKey {
        case id, title, content, image, hasImage
    }
}

// MARK: - 帮助内容管理器

class HelpContentManager: ObservableObject {
    @Published var helpContent: HelpContent?
    @Published var isLoading: Bool = false
    @Published var errorMessage: String?
    
    static let shared = HelpContentManager()
    
    private init() {
        loadHelpContent()
    }
    
    func loadHelpContent() {
        isLoading = true
        errorMessage = nil
        
        DispatchQueue.global(qos: .userInitiated).async { [weak self] in
            guard let self = self else { return }
            
            do {
                let content = try self.loadContentFromBundle()
                DispatchQueue.main.async {
                    self.helpContent = content
                    self.isLoading = false
                }
            } catch {
                DispatchQueue.main.async {
                    self.errorMessage = error.localizedDescription
                    self.isLoading = false
                    // 如果加载失败，使用默认内容
                    self.helpContent = self.getDefaultHelpContent()
                }
            }
        }
    }
    
    private func loadContentFromBundle() throws -> HelpContent {
        // 根据系统语言选择对应的JSON文件
        let preferredLanguage = Locale.current.language.languageCode?.identifier ?? "en"
        let fileName = preferredLanguage == "zh" ? "help_content_zh" : "help_content"
        
        guard let url = Bundle.main.url(forResource: fileName, withExtension: "json") else {
            // 如果找不到本地化文件，尝试加载默认的英文文件
            guard let defaultUrl = Bundle.main.url(forResource: "help_content", withExtension: "json") else {
                throw HelpContentError.fileNotFound
            }
            let data = try Data(contentsOf: defaultUrl)
            return try JSONDecoder().decode(HelpContent.self, from: data)
        }
        
        let data = try Data(contentsOf: url)
        return try JSONDecoder().decode(HelpContent.self, from: data)
    }
    
    // 默认帮助内容（作为备用）
    private func getDefaultHelpContent() -> HelpContent {
        let pages = [
            HelpPage(
                id: 1,
                title: "欢迎使用 VoicingWorkout",
                content: """
                VoicingWorkout 是一个专业的钢琴和弦训练应用，帮助您：
                
                • 学习和练习各种和弦配置
                • 提高和弦识别能力
                • 掌握和弦进行
                • 连接MIDI键盘进行互动练习
                
                让我们开始您的音乐学习之旅！
                """,
                image: nil,
                hasImage: false
            ),
            HelpPage(
                id: 2,
                title: "开始使用",
                content: """
                使用 VoicingWorkout 非常简单：
                
                1. 连接您的MIDI键盘（可选）
                2. 选择练习模式：音符、和弦或和弦进行
                3. 根据提示演奏正确的音符或和弦
                4. 获得即时反馈并提高您的技能
                
                如果没有MIDI键盘，您也可以使用屏幕上的虚拟钢琴键盘。
                """,
                image: nil,
                hasImage: false
            ),
            HelpPage(
                id: 3,
                title: "MIDI设置",
                content: """
                为了获得最佳体验，建议连接MIDI键盘：
                
                • 在设置中选择您的MIDI设备
                • 确保设备正确连接
                • 测试音符输入是否正常工作
                • 调整延迟设置以获得最佳响应
                
                支持大多数标准MIDI键盘和控制器。
                """,
                image: nil,
                hasImage: false
            ),
            HelpPage(
                id: 4,
                title: "练习模式",
                content: """
                VoicingWorkout 提供三种主要练习模式：
                
                音符练习：识别和演奏单个音符
                和弦练习：学习各种和弦配置
                和弦进行：练习和弦序列和转换
                
                每种模式都有不同的难度级别，适合从初学者到高级演奏者。
                """,
                image: nil,
                hasImage: false
            ),
            HelpPage(
                id: 5,
                title: "获取帮助",
                content: """
                需要更多帮助？
                
                • 查看应用内的设置和选项
                • 发送邮件给我们：<EMAIL>
                • 在App Store给我们评价和反馈
                
                我们重视您的意见，会持续改进应用体验。
                """,
                image: nil,
                hasImage: false
            )
        ]
        
        return HelpContent(pages: pages)
    }
}

// MARK: - 错误类型

enum HelpContentError: LocalizedError {
    case fileNotFound
    case invalidData
    
    var errorDescription: String? {
        switch self {
        case .fileNotFound:
            return "帮助文件未找到"
        case .invalidData:
            return "帮助内容格式错误"
        }
    }
} 