/**
 ProgressionReader : 读取和弦进行文件
 简化版本，专注核心功能：
 1. 从文稿/UserProgressions文件夹加载.progression文件
 2. 移调功能 - transposeFromBase(offset)
 3. 获取和弦名称和MIDI音符
 **/
import Foundation
import Combine

// MARK: - 数据模型

/// 和弦进行数据模型
struct Progression: Codable, Equatable {
    let name: String
    let style: String?
    let key: String?  // 调性，如"CM", "Am"等
    let chords: [ChordInfo]
    let priceType: String? // 价格类型：free 或 paid
    
    // 计算属性：获取价格类型枚举
    var priceTypeEnum: PriceType {
        return PriceType.from(priceType)
    }
    
    // 检查是否可用
    func isAvailable() -> Bool {
        return PurchaseManager.shared.isItemAvailable(priceType: priceTypeEnum)
    }
    
    // 获取锁定描述
    func getLockDescription() -> String? {
        return PurchaseManager.shared.getLockDescription(priceType: priceTypeEnum)
    }
    
    private enum CodingKeys: String, CodingKey {
        case name, style, key, chords
        case priceType = "price_type"
    }
    
    init(name: String, style: String? = nil, key: String? = nil, chords: [ChordInfo], priceType: String? = nil) {
        self.name = name
        self.style = style
        self.key = key
        self.chords = chords
        self.priceType = priceType
    }
}

/// 和弦信息模型
struct ChordInfo: Codable, Identifiable, Equatable {
    let id = UUID()
    let suffix: String      // 和弦后缀，如 "m9", "7b913", "M9"
    let root: String        // 根音，如 "D3", "G3", "C3"
    let notes: [Int]        // 音程偏移数组，相对于根音
    
    private enum CodingKeys: String, CodingKey {
        case suffix, root, notes
    }
    
    /// 获取和弦名称（可移调）
    func getChordName(transpositionSemitones: Int = 0) -> String {
        let rootName = String(root.dropLast())  // 去掉八度
        if transpositionSemitones == 0 {
            return "\(rootName)\(suffix)"
        } else {
            let transposedRoot = transposeNoteName(rootName, by: transpositionSemitones)
            return "\(transposedRoot)\(suffix)"
        }
    }
    
    /// 获取MIDI音符（可移调）
    func getMIDINotes(transpositionSemitones: Int = 0) -> [Int] {
        let rootMIDI = parseMIDINote(root)
        return notes.map { rootMIDI + $0 + transpositionSemitones }
    }
    
    /// 获取根音的MIDI值
    /// - Parameter transpositionSemitones: 移调半音数
    /// - Returns: 根音的MIDI值
    func getRootNote(transpositionSemitones: Int = 0) -> Int {
        // 解析根音字符串（如"D3" -> MIDI 50）
        let noteNames = ["C", "C#", "D", "D#", "E", "F", "F#", "G", "G#", "A", "A#", "B"]
        
        // 提取音名和八度
        let rootString = root
        let noteNamePart = String(rootString.dropLast())
        let octavePart = String(rootString.suffix(1))
        
        guard let noteIndex = noteNames.firstIndex(of: noteNamePart),
              let octave = Int(octavePart) else {
            return 60 // 默认C4
        }
        
        let baseMIDI = (octave + 1) * 12 + noteIndex
        return baseMIDI + transpositionSemitones
    }
    // MARK: - 辅助方法
    
    /// 移调音符名称
    private func transposeNoteName(_ noteName: String, by semitones: Int) -> String {
        let noteNames = ["C", "C#", "D", "D#", "E", "F", "F#", "G", "G#", "A", "A#", "B"]
        guard let currentIndex = noteNames.firstIndex(of: noteName) else { return noteName }
        let newIndex = (currentIndex + semitones + 12) % 12
        return noteNames[newIndex]
    }
    
    /// 解析MIDI音符值从字符串（如"D3" -> 50）
    private func parseMIDINote(_ noteString: String) -> Int {
        let noteNames = ["C", "C#", "D", "D#", "E", "F", "F#", "G", "G#", "A", "A#", "B"]
        
        let noteNamePart = String(noteString.dropLast())
        let octavePart = String(noteString.suffix(1))
        
        guard let noteIndex = noteNames.firstIndex(of: noteNamePart),
              let octave = Int(octavePart) else {
            return 60  // 默认C4
        }
        
        return (octave + 1) * 12 + noteIndex
    }
    
    static func == (lhs: ChordInfo, rhs: ChordInfo) -> Bool {
        return lhs.suffix == rhs.suffix &&
               lhs.root == rhs.root &&
               lhs.notes == rhs.notes
    }
}

/// 和弦进行文件的根结构
struct ProgressionFile: Codable {
    let progression: Progression
}

// MARK: - ProgressionReader类

/// 简化的和弦进行解析器
class ProgressionReader: ObservableObject {
    @Published var currentProgression: Progression?
    @Published var errorMessage: String?
    @Published var availableProgressions: [String] = []

    private let fileManager = FileManager.default
    private let builtInManager = BuiltInProgressionManager()

    private var userProgressionsURL: URL {
        let documentsURL = fileManager.urls(for: .documentDirectory, in: .userDomainMask).first!
        return documentsURL.appendingPathComponent("UserProgressions")
    }

    init() {
        createUserProgressionsDirectoryIfNeeded()
        scanAvailableProgressions()
    }
    
    // MARK: - 目录管理
    
    private func createUserProgressionsDirectoryIfNeeded() {
        if !fileManager.fileExists(atPath: userProgressionsURL.path) {
            try? fileManager.createDirectory(at: userProgressionsURL, withIntermediateDirectories: true)
        }
    }
    
    func scanAvailableProgressions() {
        var allProgressions: [String] = []

        // 1. 扫描内置progression文件
        let builtInProgressions = builtInManager.availableProgressions
        allProgressions.append(contentsOf: builtInProgressions)
        print("📋 发现 \(builtInProgressions.count) 个内置progression文件")

        // 2. 扫描用户自定义progression文件
        do {
            let fileURLs = try fileManager.contentsOfDirectory(at: userProgressionsURL, includingPropertiesForKeys: nil)
            let progressionFiles = fileURLs.filter { $0.pathExtension == "progression" }
            let userProgressions = progressionFiles.map { $0.deletingPathExtension().lastPathComponent }
            allProgressions.append(contentsOf: userProgressions)
            print("📋 发现 \(userProgressions.count) 个用户自定义progression文件")
        } catch {
            print("❌ 扫描用户progression文件失败: \(error)")
        }

        // 3. 去重并排序
        availableProgressions = Array(Set(allProgressions)).sorted()
        print("📋 总共发现 \(availableProgressions.count) 个progression文件")
    }
    
    // MARK: - 加载功能
    
    /// 加载和弦进行文件
    /// - Parameter fileName: 文件名（不包含扩展名）
    func loadProgression(_ fileName: String) {
        // 1. 首先尝试从内置文件加载
        if let builtInProgression = builtInManager.loadProgression(fileName) {
            DispatchQueue.main.async {
                self.currentProgression = builtInProgression
                self.errorMessage = nil
                print("✅ 成功加载内置和弦进行: \(builtInProgression.name)")
                print("   调性: \(builtInProgression.key ?? "未指定")")
                print("   和弦数量: \(builtInProgression.chords.count)")
            }
            return
        }

        // 2. 如果内置文件中没有，尝试从用户文件加载
        let fileURL = userProgressionsURL.appendingPathComponent("\(fileName).progression")

        guard fileManager.fileExists(atPath: fileURL.path) else {
            DispatchQueue.main.async {
                self.errorMessage = "文件不存在: \(fileName).progression"
                print("❌ 文件不存在: \(fileURL.path)")
            }
            return
        }

        do {
            let data = try Data(contentsOf: fileURL)
            let progressionFile = try JSONDecoder().decode(ProgressionFile.self, from: data)

            DispatchQueue.main.async {
                self.currentProgression = progressionFile.progression
                self.errorMessage = nil
                print("✅ 成功加载用户和弦进行: \(progressionFile.progression.name)")
                print("   调性: \(progressionFile.progression.key ?? "未指定")")
                print("   和弦数量: \(progressionFile.progression.chords.count)")
            }
        } catch {
            DispatchQueue.main.async {
                self.errorMessage = "解析文件失败: \(error.localizedDescription)"
                print("❌ 解析文件失败: \(error)")
            }
        }
    }
    
    // MARK: - 移调功能
    
    /// 基于原始JSON数据移调
    /// - Parameter offset: 移调偏移 [-11, 11]
    func transposeFromBase(_ offset: Int) {
        guard abs(offset) <= 11 else {
            print("❌ 移调偏移超出范围: \(offset)，应在 [-11, 11] 范围内")
            return
        }
        
        guard let progression = currentProgression else {
            print("❌ 没有加载的和弦进行")
            return
        }
        
        print("🎵 移调和弦进行: \(progression.name)，偏移: \(offset) 半音")
        
        // 移调在获取音符时进行，这里只记录偏移量
        currentTransposition = offset
        
        print("   原调性: \(progression.key ?? "未指定")")
        print("   移调偏移: \(offset) 半音")
    }
    
    private var currentTransposition: Int = 0
    
    // MARK: - 获取和弦信息
    
    /// 获取所有和弦名称
    var chordNames: [String] {
        guard let progression = currentProgression else { return [] }
        return progression.chords.map { $0.getChordName(transpositionSemitones: currentTransposition) }
    }
    
    /// 获取指定索引和弦的名称
    func getChordName(at index: Int) -> String {
        guard let progression = currentProgression,
              index >= 0 && index < progression.chords.count else { return "" }
        return progression.chords[index].getChordName(transpositionSemitones: currentTransposition)
    }
    
    /// 获取指定索引和弦的MIDI音符
    func getChordMIDINotes(at index: Int) -> [Int] {
        guard let progression = currentProgression,
              index >= 0 && index < progression.chords.count else { return [] }
        return progression.chords[index].getMIDINotes(transpositionSemitones: currentTransposition)
    }
    
    /// 获取所有和弦的MIDI音符
    var allChordMIDINotes: [[Int]] {
        guard let progression = currentProgression else { return [] }
        return progression.chords.map { $0.getMIDINotes(transpositionSemitones: currentTransposition) }
    }
    
    /// 获取和弦数量
    var chordCount: Int {
        return currentProgression?.chords.count ?? 0
    }
    
    /// 重新扫描可用的和弦进行文件
    func refreshAvailableProgressions() {
        scanAvailableProgressions()
    }
} 
