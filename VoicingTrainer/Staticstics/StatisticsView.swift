import SwiftUI
import Charts

struct StatisticsView: View {
    @StateObject private var viewModel = StatisticsViewModel()
    @State private var selectedTimeFrame: TimeFrame = .week
    
    enum TimeFrame: String, CaseIterable {
        case day = "天"
        case week = "周"
        case month = "月"
        
        var id: String { rawValue }
    }
    
    var body: some View {
       // NavigationView {
            ScrollView {
                LazyVStack(spacing: 20) {
                    // 顶部总结卡片
                    headerSummarySection
                    
                    // 今日练习数据
                    todayStatsSection
                    
                    // 最近练习的和弦
                    mostPracticedSection
                    
                    // 练习趋势图表
                    chartSection
                    
                    // 最近练习记录
                    /*
                    recentSessionsSection
                    */

                    // 🧪 临时启用测试按钮用于调试
                    #if DEBUG
                    testButtonsSection
                    #endif
                }
                                 .padding(.horizontal)
             }
             .background(Color.adaptiveSystemGroupedBackground)
            // .navigationTitle("练习统计")
             .refreshable {
                 await viewModel.refreshData()
             }
             .animation(.easeInOut(duration: 0.3), value: viewModel.statistics.totalSessions)
             .onAppear {
                 // 📊 页面显示时自动刷新数据
                   // 📊 页面显示时数据已通过ViewModel初始化和通知机制自动刷新
                    print("📊 StatisticsView: 页面已显示，数据通过现有机制刷新")
                /*
                 Task {
                  //   await viewModel.refreshData()
                 }
                 */
             }
             .onReceive(NotificationCenter.default.publisher(for: NSNotification.Name("RefreshStatistics"))) { _ in
                 // 📊 收到刷新通知时更新数据
                 Task {
                     await viewModel.refreshData()
                 }
                 print("📊 StatisticsView: 收到刷新通知，正在更新数据")
             }
            .overlay(
                // Toast消息
                toastOverlay,
                alignment: .top
            )
            .overlay(
                // 加载指示器
                loadingOverlay
            )
        //}
        #if os(iOS)
       // .navigationViewStyle(StackNavigationViewStyle()) // 确保iPad上正确显示
        #endif
    }
    
    // MARK: - 顶部总结卡片
    private var headerSummarySection: some View {
        VStack(spacing: 0) {
            // 恭喜信息
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("你的练琴记录 恭喜您！")
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.primary)
                    
                    Text(viewModel.practiceLevel)
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                // 练习次数
                VStack(alignment: .trailing) {
                    Text("练习次数达到了")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    Text("\(viewModel.statistics.totalSessions)次")
                        .font(.title)
                        .fontWeight(.bold)
                        .foregroundColor(.blue)
                }
            }
            .padding(.vertical, 16)
            
            // 统计数字
            HStack(spacing: 20) {
                StatCard(
                    title: "弹奏了",
                    value: "\(viewModel.statistics.totalChords)个和弦",
                    subtitle: "\(viewModel.statistics.totalNotes)个音符"
                )
                
                StatCard(
                    title: "练习时长",
                    value: "\(viewModel.statistics.totalPracticeTime)分钟",
                    subtitle: "总计时间"
                )
            }
        }
        .padding()
        .background(
            LinearGradient(
                colors: [Color.blue.opacity(0.1), Color.purple.opacity(0.1)],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
        )
        .cornerRadius(16)
        .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
    }
    
    // MARK: - 今日练习数据
    private var todayStatsSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("今天练习数据")
                .font(.headline)
                .fontWeight(.semibold)
            
            // 响应式布局：在较小屏幕上垂直排列，较大屏幕上水平排列
            if #available(iOS 16.0, macOS 13.0, *) {
                ViewThatFits {
                HStack(spacing: 16) {
                    TodayStatCard(
                        title: "练习时长",
                        value: "\(viewModel.statistics.todayPracticeTime) 分钟",
                        icon: "clock.fill",
                        color: .orange
                    )
                    
                    TodayStatCard(
                        title: "弹奏音符个数",
                        value: "\(viewModel.statistics.todayNotes)",
                        icon: "music.note",
                        color: .green
                    )
                    
                    TodayStatCard(
                        title: "弹奏和弦个数",
                        value: "\(viewModel.statistics.todayChords)",
                        icon: "music.note.list",
                        color: .purple
                    )
                }
                
                VStack(spacing: 12) {
                    TodayStatCard(
                        title: "练习时长",
                        value: "\(viewModel.statistics.todayPracticeTime) 分钟",
                        icon: "clock.fill",
                        color: .orange
                    )
                    
                    TodayStatCard(
                        title: "弹奏音符个数",
                        value: "\(viewModel.statistics.todayNotes)",
                        icon: "music.note",
                        color: .green
                    )
                    
                    TodayStatCard(
                        title: "弹奏和弦个数",
                        value: "\(viewModel.statistics.todayChords)",
                        icon: "music.note.list",
                                                 color: .purple
                     )
                 }
                }
            } else {
                // iOS 15 兼容版本
                HStack(spacing: 16) {
                    TodayStatCard(
                        title: "练习时长",
                        value: "\(viewModel.statistics.todayPracticeTime) 分钟",
                        icon: "clock.fill",
                        color: .orange
                    )
                    
                    TodayStatCard(
                        title: "弹奏音符个数",
                        value: "\(viewModel.statistics.todayNotes)",
                        icon: "music.note",
                        color: .green
                    )
                    
                    TodayStatCard(
                        title: "弹奏和弦个数",
                        value: "\(viewModel.statistics.todayChords)",
                        icon: "music.note.list",
                        color: .purple
                    )
                }
            }
            
            // 今日目标消息
            Text(viewModel.todayGoalMessage)
                .font(.subheadline)
                .foregroundColor(.secondary)
                .italic()
        }
        .padding()
        .background(Color.adaptiveSystemBackground)
        .cornerRadius(12)
        .shadow(color: .black.opacity(0.05), radius: 4, x: 0, y: 2)
    }
    
    // MARK: - 最近练习的项目
    private var mostPracticedSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("最近一周练习最多的项目")
                .font(.headline)
                .fontWeight(.semibold)
            
            if viewModel.statistics.mostPracticedItems.isEmpty {
                Text("暂无数据")
                    .foregroundColor(.secondary)
                    .italic()
            } else {
                VStack(spacing: 8) {
                    ForEach(Array(viewModel.statistics.mostPracticedItems.enumerated()), id: \.offset) { index, practiceInfo in
                        PracticeItemRow(itemName: practiceInfo.name, practiceCount: practiceInfo.count, rank: index + 1)
                    }
                }
            }
        }
        .padding()
        .background(Color.adaptiveSystemBackground)
        .cornerRadius(12)
        .shadow(color: .black.opacity(0.05), radius: 4, x: 0, y: 2)
    }
    
    // MARK: - 练习趋势图表
    private var chartSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Text("练习趋势分析")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Spacer()
                
                Text(viewModel.weeklyTrend)
                    .font(.caption)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(Color.blue.opacity(0.1))
                    .cornerRadius(6)
                
                Text("时间单位 天 周 月")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            // 简化的图表
            WeeklyProgressChart(data: viewModel.statistics.weeklyProgress)
                .frame(height: 200)
        }
        .padding()
        .background(Color.adaptiveSystemBackground)
        .cornerRadius(12)
        .shadow(color: .black.opacity(0.05), radius: 4, x: 0, y: 2)
    }
    
    // MARK: - 最近练习记录
    private var recentSessionsSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("最近练习记录")
                .font(.headline)
                .fontWeight(.semibold)
            
            if viewModel.recentSessions.isEmpty {
                Text("暂无练习记录")
                    .foregroundColor(.secondary)
                    .italic()
            } else {
                LazyVStack(spacing: 8) {
                    ForEach(viewModel.recentSessions.prefix(5), id: \.id) { session in
                        SessionRow(session: session, viewModel: viewModel)
                    }
                }
            }
        }
        .padding()
        .background(Color.adaptiveSystemBackground)
        .cornerRadius(12)
        .shadow(color: .black.opacity(0.05), radius: 4, x: 0, y: 2)
    }
    
    // MARK: - 测试按钮区域
    private var testButtonsSection: some View {
        VStack(spacing: 12) {
            Text("测试功能")
                .font(.headline)
                .fontWeight(.semibold)
            
            VStack(spacing: 12) {
                Button {
                    Task {
                        await viewModel.addTestRecord()
                    }
                } label: {
                    HStack {
                        Image(systemName: "plus.circle.fill")
                        Text("增加1条测试记录")
                    }
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(Color.blue)
                    .foregroundColor(.white)
                    .cornerRadius(10)
                }
                
                Button {
                    Task {
                        await viewModel.addRandomTestRecords()
                    }
                } label: {
                    HStack {
                        Image(systemName: "square.stack.3d.up.fill")
                        Text("增加随机1000条测试记录")
                    }
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(Color.green)
                    .foregroundColor(.white)
                    .cornerRadius(10)
                }
                
                Button {
                    Task {
                        await viewModel.refreshData()
                    }
                } label: {
                    HStack {
                        Image(systemName: "chart.bar.fill")
                        Text("显示测试数据")
                    }
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(Color.orange)
                    .foregroundColor(.white)
                    .cornerRadius(10)
                }
            }
        }
        .padding()
        .background(Color.adaptiveSystemBackground)
        .cornerRadius(12)
        .shadow(color: .black.opacity(0.05), radius: 4, x: 0, y: 2)
    }
    
    // MARK: - Toast覆盖层
    private var toastOverlay: some View {
        Group {
            if viewModel.showToast {
                Text(viewModel.toastMessage)
                    .padding()
                    .background(Color.black.opacity(0.8))
                    .foregroundColor(.white)
                    .cornerRadius(8)
                    .transition(.move(edge: .top).combined(with: .opacity))
                    .animation(.easeInOut, value: viewModel.showToast)
            }
        }
    }
    
    // MARK: - 加载覆盖层
    private var loadingOverlay: some View {
        Group {
            if viewModel.isLoading {
                Color.black.opacity(0.3)
                    .ignoresSafeArea()
                    .overlay(
                        ProgressView("加载中...")
                            .padding()
                            .background(Color.adaptiveSystemBackground)
                            .cornerRadius(8)
                    )
            }
        }
    }
}

// MARK: - 辅助视图组件

struct StatCard: View {
    let title: String
    let value: String
    let subtitle: String
    
    var body: some View {
        VStack(alignment: .leading, spacing: 4) {
            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
            
            Text(value)
                .font(.title3)
                .fontWeight(.semibold)
                .foregroundColor(.primary)
            
            Text(subtitle)
                .font(.caption2)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity, alignment: .leading)
        .padding()
        .background(Color.adaptiveSystemBackground)
        .cornerRadius(8)
    }
}

struct TodayStatCard: View {
    let title: String
    let value: String
    let icon: String
    let color: Color
    @State private var isVisible = false
    
    var body: some View {
        VStack(spacing: 8) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(color)
                .scaleEffect(isVisible ? 1.0 : 0.5)
            
            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
            
            Text(value)
                .font(.headline)
                .fontWeight(.semibold)
                .foregroundColor(.primary)
        }
        .frame(maxWidth: .infinity)
        .padding()
        .background(color.opacity(0.1))
        .cornerRadius(8)
        .scaleEffect(isVisible ? 1.0 : 0.8)
        .opacity(isVisible ? 1.0 : 0.0)
        .animation(.spring(response: 0.6, dampingFraction: 0.8), value: isVisible)
        .onAppear {
            withAnimation(.spring(response: 0.6, dampingFraction: 0.8).delay(Double.random(in: 0...0.3))) {
                isVisible = true
            }
        }
    }
}

struct PracticeItemRow: View {
    let itemName: String
    let practiceCount: Int
    let rank: Int
    @State private var progressWidth: CGFloat = 0

    var body: some View {
        HStack {
            Text("\(rank)")
                .font(.headline)
                .fontWeight(.bold)
                .foregroundColor(.blue)
                .frame(width: 30)

            VStack(alignment: .leading, spacing: 2) {
                Text(itemName)
                    .font(.body)
                    .foregroundColor(.primary)

                Text("\(practiceCount)次练习")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }

            Spacer()

            // 动画进度条装饰
            RoundedRectangle(cornerRadius: 2)
                .fill(Color.blue.opacity(0.6 - Double(rank-1) * 0.2))
                .frame(width: progressWidth, height: 4)
                .animation(.easeInOut(duration: 0.8).delay(Double(rank) * 0.1), value: progressWidth)
        }
        .padding(.vertical, 4)
        .onAppear {
            progressWidth = CGFloat(min(120, 40 + practiceCount * 8)) // 根据练习次数调整进度条长度
        }
    }
}

struct SessionRow: View {
    let session: WorkoutSession
    let viewModel: StatisticsViewModel
    
    var body: some View {
        HStack {
            VStack(alignment: .leading, spacing: 2) {
                Text(session.workoutName)
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                Text(session.workoutType.displayName)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            VStack(alignment: .trailing, spacing: 2) {
                Text(viewModel.formatPercentage(session.accuracy))
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(session.accuracy > 0.8 ? .green : session.accuracy > 0.6 ? .orange : .red)
                
                Text(viewModel.formatDate(session.startTime))
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
        .padding(.vertical, 4)
    }
}

struct WeeklyProgressChart: View {
    let data: [Int]
    
    var body: some View {
        VStack {
            if #available(iOS 16.0, macOS 13.0, *) {
                Chart {
                    ForEach(Array(data.enumerated()), id: \.offset) { index, value in
                        BarMark(
                            x: .value("Day", dayLabel(for: index)),
                            y: .value("Minutes", value)
                        )
                        .foregroundStyle(
                            LinearGradient(
                                colors: [.blue, .purple],
                                startPoint: .bottom,
                                endPoint: .top
                            )
                        )
                        .cornerRadius(4)
                    }
                }
                .chartYAxis {
                    AxisMarks(position: .leading)
                }
                .chartXAxis {
                    AxisMarks(position: .bottom)
                }
            } else {
                // iOS 15 兼容版本 - 简单的条形图
                HStack(alignment: .bottom, spacing: 8) {
                    ForEach(Array(data.enumerated()), id: \.offset) { index, value in
                        VStack {
                            RoundedRectangle(cornerRadius: 4)
                                .fill(
                                    LinearGradient(
                                        colors: [.blue, .purple],
                                        startPoint: .bottom,
                                        endPoint: .top
                                    )
                                )
                                .frame(width: 25, height: max(CGFloat(value) * 2, 4))
                                .animation(.easeInOut(duration: 0.6), value: value)
                            
                            Text(dayLabel(for: index))
                                .font(.caption2)
                                .foregroundColor(.secondary)
                        }
                    }
                }
                .frame(height: 160)
            }
        }
    }
    
    private func dayLabel(for index: Int) -> String {
        let weekdays = ["一", "二", "三", "四", "五", "六", "日"]
        return weekdays[index % 7]
    }
}

// MARK: - 平台特定颜色扩展
extension Color {
    static var adaptiveSystemBackground: Color {
        #if os(iOS)
        return Color(.systemBackground)
        #else
        return Color(.controlBackgroundColor)
        #endif
    }
    
    static var adaptiveSystemGroupedBackground: Color {
        #if os(iOS)
        return Color(.systemGroupedBackground)
        #else
        return Color(.controlBackgroundColor)
        #endif
    }
}

#Preview {
    StatisticsView()
} 
