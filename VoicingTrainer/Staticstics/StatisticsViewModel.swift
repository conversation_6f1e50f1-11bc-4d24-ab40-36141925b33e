import Foundation
import SwiftUI

@MainActor
class StatisticsViewModel: ObservableObject {
    @Published var statistics: StatisticsData
    @Published var recentSessions: [WorkoutSession] = []
    @Published var isLoading = false
    @Published var showToast = false
    @Published var toastMessage = ""
    
    private let database: StatisticsDatabase
    
    init() {
        self.database = StatisticsDatabase()
        self.statistics = StatisticsData(
            totalSessions: 0,
            totalPracticeTime: 0,
            totalNotes: 0,
            totalChords: 0,
            todayPracticeTime: 0,
            todayNotes: 0,
            todayChords: 0,
            mostPracticedItems: [],
            weeklyProgress: Array(repeating: 0, count: 7)
        )
        
        Task {
            await refreshData()
        }
    }
    
    // MARK: - 公共接口方法
    
    /// 添加练习记录的主要接口
    func addWorkoutItem(
        startTime: Date,
        duration: Int,
        workoutType: WorkoutType,
        workoutName: String,
        totalNoteCount: Int,
        totalCount: Int,
        rightCount: Int
    ) async {
        isLoading = true
        
        let success = await Task.detached { [weak self] in
            return await self?.database.addWorkoutSession(
                startTime: startTime,
                duration: duration,
                workoutType: workoutType,
                workoutName: workoutName,
                totalNoteCount: totalNoteCount,
                totalCount: totalCount,
                rightCount: rightCount
            ) ?? false
        }.value
        
        if success {
            await refreshData()
            showToastMessage("练习记录保存成功！")
        } else {
            showToastMessage("保存失败，请重试")
        }
        
        isLoading = false
    }
    
    /// 刷新所有数据
    func refreshData() async {
        isLoading = true
        
        let (newStatistics, newSessions) = await Task.detached { [weak self] in
            guard let self = self else { 
                return (StatisticsData(
                    totalSessions: 0, totalPracticeTime: 0, totalNotes: 0, totalChords: 0,
                    todayPracticeTime: 0, todayNotes: 0, todayChords: 0,
                    mostPracticedItems: [], weeklyProgress: Array(repeating: 0, count: 7)
                ), [WorkoutSession]())
            }
            
            let stats = await self.database.getStatistics()
            let sessions = Array(await self.database.getAllSessions().prefix(20)) // 最近20条记录
            return (stats, sessions)
        }.value
        
        statistics = newStatistics
        recentSessions = newSessions
        isLoading = false
    }
    
    // MARK: - 测试功能
    
    /// 添加单条测试记录
    func addTestRecord() async {
        isLoading = true
        
        let success = await Task.detached { [weak self] in
            return await self?.database.addTestRecord() ?? false
        }.value
        
        if success {
            await refreshData()
            showToastMessage("测试记录添加成功！")
        } else {
            showToastMessage("添加失败")
        }
        
        isLoading = false
    }
    
    /// 添加1000条随机测试记录
    func addRandomTestRecords() async {
        isLoading = true
        showToastMessage("正在生成1000条测试数据...")
        
        await Task.detached { [weak self] in
            await self?.database.addRandomTestRecords(count: 1000)
        }.value
        
        await refreshData()
        showToastMessage("1000条测试数据生成完成！")
        isLoading = false
    }
    
    // MARK: - 计算属性
    
    /// 总体正确率
    var overallAccuracy: Double {
        let sessions = recentSessions
        guard !sessions.isEmpty else { return 0 }
        
        let totalRight = sessions.reduce(0) { $0 + $1.rightCount }
        let totalAttempts = sessions.reduce(0) { $0 + $1.totalCount }
        
        guard totalAttempts > 0 else { return 0 }
        return Double(totalRight) / Double(totalAttempts)
    }
    
    /// 格式化练习时间
    func formatDuration(_ seconds: Int) -> String {
        let hours = seconds / 3600
        let minutes = (seconds % 3600) / 60
        
        if hours > 0 {
            return "\(hours)小时\(minutes)分钟"
        } else {
            return "\(minutes)分钟"
        }
    }
    
    /// 格式化日期
    func formatDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.locale = Locale(identifier: "zh_CN")
        formatter.dateFormat = "MM-dd HH:mm"
        return formatter.string(from: date)
    }
    
    /// 格式化百分比
    func formatPercentage(_ value: Double) -> String {
        return String(format: "%.1f%%", value * 100)
    }
    
    // MARK: - 私有方法
    
    private func showToastMessage(_ message: String) {
        toastMessage = message
        showToast = true
        
        // 3秒后自动隐藏
        Task {
            try? await Task.sleep(nanoseconds: 3_000_000_000)
            showToast = false
        }
    }
}

// MARK: - 扩展：便利方法
extension StatisticsViewModel {
    
    /// 练习成就等级
    var practiceLevel: String {
        let totalMinutes = statistics.totalPracticeTime
        
        switch totalMinutes {
        case 0..<60:
            return "初学者 🌱"
        case 60..<300:
            return "入门者 🎵"
        case 300..<1000:
            return "练习者 🎹"
        case 1000..<3000:
            return "熟练者 🎼"
        case 3000..<6000:
            return "专家级 🏆"
        default:
            return "大师级 👑"
        }
    }
    
    /// 今日练习建议
    var todayGoalMessage: String {
        let todayMinutes = statistics.todayPracticeTime
        
        if todayMinutes == 0 {
            return "今天还没开始练习呢，加油！💪"
        } else if todayMinutes < 30 {
            return "今天练习了\(todayMinutes)分钟，再坚持一下！🎯"
        } else if todayMinutes < 60 {
            return "今天练习了\(todayMinutes)分钟，很不错！⭐"
        } else {
            return "今天练习了\(todayMinutes)分钟，太棒了！🔥"
        }
    }
    
    /// 本周练习趋势
    var weeklyTrend: String {
        let progress = statistics.weeklyProgress
        guard progress.count >= 2 else { return "稳定中" }
        
        let recent3Days = Array(progress.suffix(3))
        let earlier3Days = Array(progress.prefix(3))
        
        let recentAvg = recent3Days.reduce(0, +) / 3
        let earlierAvg = earlier3Days.reduce(0, +) / 3
        
        if Double(recentAvg) > Double(earlierAvg) * 1.2 {
            return "📈 上升中"
        } else if Double(recentAvg) < Double(earlierAvg) * 0.8 {
            return "📉 需要加油"
        } else {
            return "📊 稳定中"
        }
    }
} 