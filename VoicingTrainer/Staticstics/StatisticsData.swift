import Foundation
import SQLite3

// MARK: - 练习类型枚举
enum WorkoutType: UInt8, CaseIterable {
    case note = 0
    case chord = 1
    case progression = 2
    
    var displayName: String {
        switch self {
        case .note: return "音符练习"
        case .chord: return "和弦练习"
        case .progression: return "进行练习"
        }
    }
}

// MARK: - 练习记录数据模型
struct WorkoutSession {
    let id: Int
    let startTime: Date
    let duration: Int // 秒
    let workoutType: WorkoutType
    let workoutName: String
    let totalNoteCount: Int
    let totalCount: Int // 音符模式=音符个数，其他模式=和弦个数
    let rightCount: Int // 正确个数
    
    var accuracy: Double {
        guard totalCount > 0 else { return 0 }
        return Double(rightCount) / Double(totalCount)
    }
}

// MARK: - 统计数据模型
struct PracticeItemInfo {
    let name: String
    let count: Int
}

struct StatisticsData {
    let totalSessions: Int
    let totalPracticeTime: Int // 总练习时间（分钟）
    let totalNotes: Int
    let totalChords: Int
    let todayPracticeTime: Int
    let todayNotes: Int
    let todayChords: Int
    let mostPracticedItems: [PracticeItemInfo]
    let weeklyProgress: [Int] // 最近7天每天的练习时间
}

// MARK: - SQLite数据库管理器
class StatisticsDatabase: ObservableObject {
    private var db: OpaquePointer?
    private let dbPath: String
    
    init() {
        // 获取文档目录路径
        let fileManager = FileManager.default
        let documentsPath = fileManager.urls(for: .documentDirectory, in: .userDomainMask).first!
        dbPath = documentsPath.appendingPathComponent("statistics.db").path
        
        print("📁 SQLite数据库路径: \(dbPath)")
        
        openDatabase()
        createTable()
        printDatabaseInfo()
    }
    
    deinit {
        closeDatabase()
    }
    
    private func openDatabase() {
        if sqlite3_open(dbPath, &db) != SQLITE_OK {
            print("❌ 无法打开数据库")
            return
        }
        print("✅ 数据库连接成功")
    }
    
    private func closeDatabase() {
        if sqlite3_close(db) != SQLITE_OK {
            print("❌ 无法关闭数据库")
        }
    }
    
    private func createTable() {
        let createTableSQL = """
            CREATE TABLE IF NOT EXISTS workout_sessions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                start_time INTEGER NOT NULL,
                duration INTEGER NOT NULL,
                workout_type INTEGER NOT NULL,
                workout_name TEXT NOT NULL,
                total_note_count INTEGER NOT NULL,
                total_count INTEGER NOT NULL,
                right_count INTEGER NOT NULL
            );
        """
        
        if sqlite3_exec(db, createTableSQL, nil, nil, nil) != SQLITE_OK {
            let errmsg = String(cString: sqlite3_errmsg(db)!)
            print("❌ 创建表失败: \(errmsg)")
            return
        }
        print("✅ 数据库表创建成功")
    }
    
    private func printDatabaseInfo() {
        let fileManager = FileManager.default
        do {
            let attributes = try fileManager.attributesOfItem(atPath: dbPath)
            let fileSize = attributes[.size] as? NSNumber ?? 0
            print("📊 数据库文件大小: \(fileSize) bytes")
        } catch {
            print("❌ 无法获取文件信息: \(error)")
        }
    }
    
    // MARK: - 添加练习记录
    func addWorkoutSession(
        startTime: Date,
        duration: Int,
        workoutType: WorkoutType,
        workoutName: String,
        totalNoteCount: Int,
        totalCount: Int,
        rightCount: Int
    ) -> Bool {
        let startMeasure = CFAbsoluteTimeGetCurrent()
        
        let insertSQL = """
            INSERT INTO workout_sessions 
            (start_time, duration, workout_type, workout_name, total_note_count, total_count, right_count)
            VALUES (?, ?, ?, ?, ?, ?, ?);
        """
        
        var statement: OpaquePointer?
        
        if sqlite3_prepare_v2(db, insertSQL, -1, &statement, nil) == SQLITE_OK {
            sqlite3_bind_int64(statement, 1, Int64(startTime.timeIntervalSince1970))
            sqlite3_bind_int(statement, 2, Int32(duration))
            sqlite3_bind_int(statement, 3, Int32(workoutType.rawValue))
            sqlite3_bind_text(statement, 4, workoutName, -1, nil)
            sqlite3_bind_int(statement, 5, Int32(totalNoteCount))
            sqlite3_bind_int(statement, 6, Int32(totalCount))
            sqlite3_bind_int(statement, 7, Int32(rightCount))
            
            if sqlite3_step(statement) == SQLITE_DONE {
                let timeElapsed = CFAbsoluteTimeGetCurrent() - startMeasure
                print("✅ 练习记录添加成功，耗时: \(String(format: "%.4f", timeElapsed))秒")
                sqlite3_finalize(statement)
                printDatabaseInfo()
                return true
            } else {
                let errmsg = String(cString: sqlite3_errmsg(db)!)
                print("❌ 插入失败: \(errmsg)")
            }
        } else {
            let errmsg = String(cString: sqlite3_errmsg(db)!)
            print("❌ 准备语句失败: \(errmsg)")
        }
        
        sqlite3_finalize(statement)
        return false
    }
    
    // MARK: - 查询所有记录
    func getAllSessions() -> [WorkoutSession] {
        let startMeasure = CFAbsoluteTimeGetCurrent()
        var sessions: [WorkoutSession] = []
        
        let querySQL = "SELECT * FROM workout_sessions ORDER BY start_time DESC;"
        var statement: OpaquePointer?
        
        if sqlite3_prepare_v2(db, querySQL, -1, &statement, nil) == SQLITE_OK {
            while sqlite3_step(statement) == SQLITE_ROW {
                let id = Int(sqlite3_column_int(statement, 0))
                let startTime = Date(timeIntervalSince1970: sqlite3_column_double(statement, 1))
                let duration = Int(sqlite3_column_int(statement, 2))
                let workoutTypeRaw = sqlite3_column_int(statement, 3)
                let workoutType = WorkoutType(rawValue: UInt8(workoutTypeRaw)) ?? .note
                let workoutName = String(cString: sqlite3_column_text(statement, 4))
                let totalNoteCount = Int(sqlite3_column_int(statement, 5))
                let totalCount = Int(sqlite3_column_int(statement, 6))
                let rightCount = Int(sqlite3_column_int(statement, 7))
                
                let session = WorkoutSession(
                    id: id,
                    startTime: startTime,
                    duration: duration,
                    workoutType: workoutType,
                    workoutName: workoutName,
                    totalNoteCount: totalNoteCount,
                    totalCount: totalCount,
                    rightCount: rightCount
                )
                sessions.append(session)
            }
        }
        
        sqlite3_finalize(statement)
        
        let timeElapsed = CFAbsoluteTimeGetCurrent() - startMeasure
        print("📊 查询\(sessions.count)条记录，耗时: \(String(format: "%.4f", timeElapsed))秒")
        
        return sessions
    }
    
    // MARK: - 获取统计数据
    func getStatistics() -> StatisticsData {
        let startMeasure = CFAbsoluteTimeGetCurrent()
        
        let sessions = getAllSessions()
        let today = Calendar.current.startOfDay(for: Date())
        
        // 基础统计
        let totalSessions = sessions.count
        let totalPracticeTime = sessions.reduce(0) { $0 + $1.duration } / 60 // 转为分钟
        let totalNotes = sessions.reduce(0) { $0 + $1.totalNoteCount }
        let totalChords = sessions.filter { $0.workoutType != .note }.reduce(0) { $0 + $1.totalCount }
        
        // 今日统计
        let todayEnd = Calendar.current.date(byAdding: .day, value: 1, to: today)!
        let todaySessions = sessions.filter { $0.startTime >= today && $0.startTime < todayEnd }
        let todayPracticeTime = todaySessions.reduce(0) { $0 + $1.duration } / 60
        let todayNotes = todaySessions.reduce(0) { $0 + $1.totalNoteCount }
        let todayChords = todaySessions.filter { $0.workoutType != .note }.reduce(0) { $0 + $1.totalCount }
        
        // 最近一周练习最多的项目（按练习次数排序）
        let oneWeekAgo = Calendar.current.date(byAdding: .day, value: -7, to: today)!
        let weeklyPracticeSessions = sessions.filter {
            $0.startTime >= oneWeekAgo
        }

        print("📊 统计调试信息:")
        print("   - 总记录数: \(sessions.count)")
        print("   - 一周内记录数: \(weeklyPracticeSessions.count)")
        print("   - 一周前时间: \(oneWeekAgo)")

        // 统计每个练习项目的练习次数
        var practiceItemCounts: [String: Int] = [:]
        for session in weeklyPracticeSessions {
            let workoutName = session.workoutName.isEmpty ? "未命名练习" : session.workoutName
            practiceItemCounts[workoutName, default: 0] += 1
            print("   - 练习记录: \(workoutName) (类型: \(session.workoutType.displayName))")
        }

        print("   - 练习项目统计: \(practiceItemCounts)")

        // 按练习次数排序，取前3个
        let mostPracticedItems = Array(practiceItemCounts.sorted { $0.value > $1.value }.prefix(3).map {
            PracticeItemInfo(name: $0.key, count: $0.value)
        })

        print("   - 最多练习项目: \(mostPracticedItems.map { "\($0.name)(\($0.count)次)" })")
        
        // 最近7天练习时间
        var weeklyProgress: [Int] = []
        for i in 0..<7 {
            let dayStart = Calendar.current.date(byAdding: .day, value: i-6, to: today)!
            let dayEnd = Calendar.current.date(byAdding: .day, value: i-5, to: today)!
            let dayPracticeTime = sessions.filter { 
                $0.startTime >= dayStart && $0.startTime < dayEnd 
            }.reduce(0) { $0 + $1.duration } / 60
            weeklyProgress.append(dayPracticeTime)
        }
        
        let timeElapsed = CFAbsoluteTimeGetCurrent() - startMeasure
        print("📈 统计数据计算完成，耗时: \(String(format: "%.4f", timeElapsed))秒")
        
        return StatisticsData(
            totalSessions: totalSessions,
            totalPracticeTime: totalPracticeTime,
            totalNotes: totalNotes,
            totalChords: totalChords,
            todayPracticeTime: todayPracticeTime,
            todayNotes: todayNotes,
            todayChords: todayChords,
            mostPracticedItems: mostPracticedItems,
            weeklyProgress: weeklyProgress
        )
    }
    
    // MARK: - 添加测试数据
    func addTestRecord() -> Bool {
        let workoutTypes: [WorkoutType] = [.note, .chord, .progression]
        let workoutNames = [
            "Major Triad原位", 
            "Minor Seventh(Kenny Barron Voicing)", 
            "Diminished Triad(原位)", 
            "Dominant 7th", 
            "Sus4 Chord",
            "Soulful Neosoul progression",
            "Jazz ii-V-I progression",
            "Blues progression",
            "Cmaj7 Spread Voicing",
            "Am7b5 Close Voicing"
        ]
        
        let randomType = workoutTypes.randomElement()!
        let randomName = workoutNames.randomElement()!
        let randomDuration = Int.random(in: 30...600) // 30秒到10分钟
        let randomTotalCount = Int.random(in: 5...50)
        let randomRightCount = Int.random(in: 0...randomTotalCount)
        let randomNoteCount = randomType == .note ? randomTotalCount : Int.random(in: 50...300)
        
        let startTime = Date().addingTimeInterval(TimeInterval.random(in: -604800...0)) // 最近一周内随机时间
        
        return addWorkoutSession(
            startTime: startTime,
            duration: randomDuration,
            workoutType: randomType,
            workoutName: randomName,
            totalNoteCount: randomNoteCount,
            totalCount: randomTotalCount,
            rightCount: randomRightCount
        )
    }
    
    func addRandomTestRecords(count: Int = 1000) {
        let startTime = CFAbsoluteTimeGetCurrent()
        var successCount = 0
        
        for _ in 0..<count {
            if addTestRecord() {
                successCount += 1
            }
        }
        
        let timeElapsed = CFAbsoluteTimeGetCurrent() - startTime
        print("📊 批量添加测试数据完成: \(successCount)/\(count)条，耗时: \(String(format: "%.4f", timeElapsed))秒")
        printDatabaseInfo()
    }
} 