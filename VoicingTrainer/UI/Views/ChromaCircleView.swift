/**
 半音圆ChromaCircleView
 按半音组织的圆，直观表现音程信息
 
 **/

import SwiftUI
import Foundation
import SpriteKit
import Lottie

// MARK: - Circle12NotesView ViewModel
class ChromaCircleViewModel: ObservableObject {
    static var debugEnabled = true  // 临时开启调试模式
    
    // 颜色配置 - 容易修改
    static let color1 = Color.blue      // 预期音符颜色
    static let color2 = Color.yellow    // 按下音符颜色  
    static let color3 = Color.green     // 正确音符颜色
    static let connectionLineColor = Color.green.opacity(0.8)  // 连线颜色
    
    @Published var expectedNotes: Set<Int> = []    // 预期音符（半音，用于显示）
    @Published var pressedNotes: Set<Int> = []     // 按下音符（半音，用于显示）
    @Published var showConnections: Bool = true   // 是否显示连线
    @Published var lineParticlesEnabled: Bool = true // 🎆 线条粒子效果开关
    
    // 🎯 新增：完整MIDI音符用于精确判断
    private var expectedMIDINotes: Set<Int> = []   // 预期的完整MIDI音符
    private var pressedMIDINotes: Set<Int> = []    // 按下的完整MIDI音符
    private var rootNote : Int = -1                  //  和弦的根音
    
    // 音符名称数组
    let noteNames = ["C", "C#/Db", "D", "D#/Eb", "E", "F", "F#/Gb", "G", "G#/Ab", "A", "A#/Bb", "B"]
    
    // 粒子效果回调
    var onParticleExplosion: ((CGPoint) -> Void)?
    var onLineParticleExplosion: ((CGPoint, Color) -> Void)? // 🎆 线条粒子回调
    
    /// 正确音符集合 (预期 ∩ 按下)
    var correctNotes: Set<Int> {
        return expectedNotes.intersection(pressedNotes)
    }
    
    /// 是否全部正确
    var allNotesCorrect: Bool {
        return !expectedNotes.isEmpty && expectedNotes == correctNotes
    }
    
    /// 是否完全匹配 (预期音符和按下音符完全相同)
    /// 🎯 修复：比较完整的MIDI音符，而不是只比较半音
    var perfectMatch: Bool {
        return !expectedMIDINotes.isEmpty && expectedMIDINotes == pressedMIDINotes
    }
    
    init() {
        // 🎆 默认启用线条粒子效果
        lineParticlesEnabled = true
        
        print("🎯🎆 ChromaCircleViewModel 初始化完成")
        print("🎯🎆 lineParticlesEnabled 默认设置为: \(lineParticlesEnabled)")
        print("🎯🎆 debugEnabled: \(Self.debugEnabled)")
    }
    
    /// 设置预期音符 (每个演奏和弦到来时调用)
    /// - Parameter midiNotes: MIDI音符值数组  可选参数:根音
    func setExpectedNotes(_ midiNotes: [Int],rootNote: Int? = nil) {
        // 🎯 存储完整的MIDI音符用于精确判断
        expectedMIDINotes = Set(midiNotes)
        
        
        
        
        // 存储半音用于显示
        expectedNotes = Set(midiNotes.map { $0 % 12 })
        
        // 🎯 设置根音逻辑
               if let explicitRoot = rootNote {
                   // 使用明确指定的根音
                   self.rootNote = explicitRoot % 12
                   if Self.debugEnabled {
                       let rootName = noteNames[explicitRoot % 12]
                       print("🎯 使用指定根音: \(rootName) (MIDI: \(explicitRoot))")
                   }
               } else if !midiNotes.isEmpty {
                   // 使用最低音作为根音
                   let lowestNote = midiNotes.min()!
                   self.rootNote = lowestNote % 12
                   if Self.debugEnabled {
                       let rootName = noteNames[lowestNote % 12]
                       print("🎯 使用最低音作为根音: \(rootName) (MIDI: \(lowestNote))")
                   }
               } else {
                   // 没有音符时清除根音
                   self.rootNote = 0
                   if Self.debugEnabled {
                       print("🎯 清除根音（无音符）")
                   }
               }
        
        
        // 🎯 重要：当设置新的预期音符时，清除之前的按下音符状态
        // 这确保新和弦开始时，只显示蓝色预期音符，不显示之前的黄色/绿色状态
        pressedNotes.removeAll()
        pressedMIDINotes.removeAll()
        
        updateConnectionDisplay()
        
        if Self.debugEnabled {
            let noteNames = midiNotes.map { self.noteNames[$0 % 12] }
            print("🎯 设置预期音符: \(noteNames) (完整MIDI: \(midiNotes.sorted())), 清除按下状态")
        }
    }
    
    /// 设置按下音符 (琴键按下时调用)
    /// - Parameter midiNotes: MIDI音符值数组
    func setPressedNotes(_ midiNotes: [Int]) {
        // 🎯 存储完整的MIDI音符用于精确判断
        pressedMIDINotes = Set(midiNotes)
        
        // 存储半音用于显示
        pressedNotes = Set(midiNotes.map { $0 % 12 })
        
        updateConnectionDisplay()
        
        if Self.debugEnabled {
            let noteNames = midiNotes.map { self.noteNames[$0 % 12] }
            print("🎯 设置按下音符: \(noteNames)")
            print("🎯 正确音符: \(correctNotes)")
            print("🎯 全部正确: \(allNotesCorrect)")
            print("🎯 完全匹配: \(perfectMatch)")
        }
        
        // 🎯 注意：和弦匹配逻辑和粒子爆炸现在由统一的ChordMatchManager处理
        // 这里只负责视觉显示
    }
    
    /// 清除所有状态
    func clearAll() {
        expectedNotes.removeAll()
        pressedNotes.removeAll()
        expectedMIDINotes.removeAll()
        pressedMIDINotes.removeAll()
        rootNote = -1
        //showConnections = false

        if Self.debugEnabled {
            print("🎯 清除所有状态（包括完整MIDI音符）")
        }
    }

    /// 🎬 触发和弦成功动画（在正确音符位置）
    /// - Parameter correctNotes: 正确的音符半音数组
    func triggerChordSuccessAnimation(for correctNotes: [Int]) {
        // 🎬 暂时注释粒子爆炸，改用和弦成功动画
        // onWhiteParticleExplosion?(correctNotes)

        // 🎬 触发和弦成功动画
        triggerChordSuccessAnimation()

        if Self.debugEnabled {
            print("🎬 ChromaCircleViewModel: 触发和弦成功动画，音符: \(correctNotes)")
        }
    }

    /// 🎬 触发和弦成功动画（默认为单个和弦动画）
    func triggerChordSuccessAnimation() {
        triggerChordSuccessAnimation(type: .singleChord)
    }

    /// 🎯 触发指定类型的和弦成功动画
    func triggerChordSuccessAnimation(type: AnimationType) {
        // 🎬 每次都重新生成UUID，这会强制SwiftUI重新创建视图
        chordSuccessAnimationKey = UUID()
        currentAnimationType = type
        showChordSuccessAnimation = true

        if Self.debugEnabled {
            let animationName = type == .singleChord ? "Circular Burst" : "burst1"
            print("🎬 ChromaCircleViewModel: \(animationName) 和弦成功动画已触发，UUID: \(chordSuccessAnimationKey)")
        }
    }

    /// 🎬 隐藏和弦成功动画
    func hideChordSuccessAnimation() {
        showChordSuccessAnimation = false

        if Self.debugEnabled {
            print("🎬 ChromaCircleViewModel: 和弦成功动画已隐藏")
        }
    }

    /// � 预创建动画组件（游戏开始时调用一次）
    func initializeAnimations() {
        if singleChordAnimation == nil {
            singleChordAnimation = LottieAnimationView.chordMatchAnimation { [weak self] in
                self?.hideChordSuccessAnimation()
            }
            if Self.debugEnabled {
                print("🎬 ChromaCircleViewModel: 预创建单和弦动画组件")
            }
        }

        if progressionAnimation == nil {
            progressionAnimation = LottieAnimationView.progressionMatchAnimation { [weak self] in
                self?.hideChordSuccessAnimation()
            }
            if Self.debugEnabled {
                print("🎬 ChromaCircleViewModel: 预创建进行完成动画组件")
            }
        }
    }

    /// 🎬 获取预创建的动画组件
    func getAnimationView(for type: AnimationType) -> LottieAnimationView? {
        switch type {
        case .singleChord:
            return singleChordAnimation
        case .progression:
            return progressionAnimation
        }
    }

    /// �🎆 白色粒子爆炸回调
    var onWhiteParticleExplosion: (([Int]) -> Void)?

    /// 🎬 和弦成功动画状态
    @Published var showChordSuccessAnimation = false
    @Published var chordSuccessAnimationKey = UUID() // 用于重新触发动画

    /// � 预创建的动画组件（避免频繁创建）
    private var singleChordAnimation: LottieAnimationView?
    private var progressionAnimation: LottieAnimationView?

    /// �🎯 新增：动画类型枚举
    enum AnimationType {
        case singleChord    // 单个和弦正确 - Circular Burst
        case progression    // 整个进行完成 - burst1
    }

    /// 🎯 新增：当前动画类型
    @Published var currentAnimationType: AnimationType = .singleChord
    
    /// 更新连线显示状态
    private func updateConnectionDisplay() {
        withAnimation(.easeInOut(duration: 0.5)) {
            showConnections = allNotesCorrect
        }
    }
    
    /// 触发粒子爆炸效果
    private func triggerParticleExplosion() {
        if Self.debugEnabled {
            print("🎆 触发粒子爆炸 - 完全匹配!")
        }
        
        // 通知视图触发粒子爆炸，传递圆心位置
        onParticleExplosion?(CGPoint(x: 0, y: 0)) // 圆心位置，在视图中会被转换为实际位置
    }
    
    /// 获取音符状态
    func getNoteState(_ noteIndex: Int) -> chromaCircleNoteState {
        let isExpected = expectedNotes.contains(noteIndex)
        let isPressed = pressedNotes.contains(noteIndex)
        let isCorrect = correctNotes.contains(noteIndex)
        let isRoot = rootNote == noteIndex
        
        if isCorrect {
            return isRoot ? .correctRoot : .correct
        } else if isPressed {
            return isRoot ? .pressedRoot : .pressed
        } else if isExpected {
            return isRoot ? .expectedRoot : .expected
        } else if isRoot {
            return .rootOnly  // 只是根音，不在演奏音符中
        } else {
            return .inactive
        }
        
        
    }
    
    /// 获取排序后的正确音符（用于连线）
    func getSortedCorrectNotes() -> [Int] {
        return correctNotes.sorted()
    }
    
    /// 获取排序后的预期音符（用于蓝色连线）
    func getSortedExpectedNotes() -> [Int] {
        return expectedNotes.sorted()
    }
    
    /// 获取排序后的按下音符（用于黄色连线）
    func getSortedPressedNotes() -> [Int] {
        return pressedNotes.sorted()
    }
}

// MARK: 粒子效果系统

/**
 * 专门为Circle12NotesView设计的粒子效果
 * 特点：绿色粒子、无重力、可配置消失时间
 * 🎆 新增：线条粒子效果
 */
class ChromaCircleEffects: SKScene {
    
    // 可配置参数 - 爆炸粒子
    static var particleLifetime: TimeInterval = 3.0  // 粒子生命周期（秒）
    static var particleCount: Int = 30               // 粒子数量
    static var explosionForceRange: ClosedRange<CGFloat> = 800...1200  // 爆炸力范围
    static var particleSize: CGFloat = 2             // 粒子大小
    
    // 🎆 新增：线条粒子配置
    static var lineParticleCount: Int = 5            // 线条粒子数量
    static var lineParticleLifetime: TimeInterval = 2.0  // 线条粒子生命周期
    static var lineParticleSize: CGFloat = 1.5       // 线条粒子大小
    static var lineParticleDriftRange: ClosedRange<CGFloat> = 20...50  // 飘散距离
    private var debugInfo:Bool = false
    override func didMove(to view: SKView) {
        backgroundColor = .clear
        physicsWorld.gravity = CGVector(dx: 0, dy: 0)  // 🎯 无重力
        
        // 应用配置
        applyConfiguration()
        
        if ChromaCircleViewModel.debugEnabled {
            if debugInfo{
                print("🎆 Circle12ParticleScene 初始化完成")
            }
        }
    }
    
    /// 应用配置参数
    private func applyConfiguration() {
        Self.particleLifetime = DebugConfig.circle12ParticleLifetime
        Self.particleCount = DebugConfig.circle12ParticleCount
        Self.particleSize = CGFloat(DebugConfig.circle12ParticleSize)
        Self.explosionForceRange = CGFloat(DebugConfig.circle12ParticleMinForce)...CGFloat(DebugConfig.circle12ParticleMaxForce)
        
        if DebugConfig.circle12ParticleDebugEnabled {
            if debugInfo {
                print("🎆 Circle12粒子效果配置已应用:")
                print("   - 生命周期: \(Self.particleLifetime)秒")
                print("   - 粒子数量: \(Self.particleCount)")
                print("   - 粒子大小: \(Self.particleSize)")
                print("   - 爆炸力范围: \(Self.explosionForceRange)")
            }
        }
    }
    
    /// 在指定位置触发粒子爆炸
    /// - Parameter position: 爆炸位置
    func explodeAt(_ position: CGPoint) {
        if ChromaCircleViewModel.debugEnabled {
            if debugInfo{
                print("🎆 Circle12粒子爆炸 at: \(position)")
            }
        }
        
        // 创建指定数量的绿色粒子
        for _ in 0..<Self.particleCount {
            createParticle(at: position)
        }
    }
    
    /// 创建单个粒子
    /// - Parameter position: 粒子初始位置
    private func createParticle(at position: CGPoint) {
        // 创建圆形粒子
        let particle = SKShapeNode(circleOfRadius: Self.particleSize)
        particle.fillColor = .green
        particle.strokeColor = .white
        particle.lineWidth = 1
        particle.position = position
        particle.zPosition = 1000
        
        // 添加物理体（无重力）
        particle.physicsBody = SKPhysicsBody(circleOfRadius: Self.particleSize)
        particle.physicsBody?.isDynamic = true
        particle.physicsBody?.mass = 0.01
        
        // 设置随机爆炸方向和力度
        let explosionForce = CGFloat.random(in: Self.explosionForceRange)
        let angle = CGFloat.random(in: 0...(2 * .pi))
        let dx = cos(angle) * explosionForce
        let dy = sin(angle) * explosionForce
        
        particle.physicsBody?.velocity = CGVector(dx: dx, dy: dy)
        particle.physicsBody?.angularVelocity = CGFloat.random(in: -3...3)
        
        // 设置阻尼（让粒子速度逐渐减慢）
        particle.physicsBody?.linearDamping = 0.9  // 🎯 线性阻尼，让粒子逐渐减速
        particle.physicsBody?.angularDamping = 0.3
        
        addChild(particle)
        
        // 粒子生命周期动画
        let fadeDelay = SKAction.wait(forDuration: Self.particleLifetime * 0.6)  // 60%时间保持满透明度
        let fadeOut = SKAction.fadeOut(withDuration: Self.particleLifetime * 0.4)  // 40%时间淡出
        let remove = SKAction.removeFromParent()
        let sequence = SKAction.sequence([fadeDelay, fadeOut, remove])
        particle.run(sequence)
    }
    
    /// 🎆 在指定位置触发线条粒子效果
    /// - Parameters:
    ///   - position: 粒子起始位置
    ///   - color: 粒子颜色
    #if os(iOS)
    func createLineParticles(at position: CGPoint, color: UIColor) {
        print("🎯🎆 ChromaCircleEffects.createLineParticles (iOS) 被调用")
        print("🎯🎆 position: \(position), color: \(color)")
        print("🎯🎆 lineParticleCount: \(Self.lineParticleCount)")
        
        // 创建指定数量的线条粒子
        for i in 0..<Self.lineParticleCount {
            print("🎯🎆 创建第 \(i+1) 个线条粒子")
            createDriftingParticle(at: position, color: color)
        }
    }
    
    /// 创建飘散粒子
    /// - Parameters:
    ///   - position: 起始位置
    ///   - color: 粒子颜色
    private func createDriftingParticle(at position: CGPoint, color: UIColor) {
        print("🎯🎆 createDriftingParticle (iOS) 被调用 - position: \(position), color: \(color)")
        
        // 创建圆形粒子
        let particle = SKShapeNode(circleOfRadius: Self.lineParticleSize)
        particle.fillColor = color
        particle.strokeColor = .white
        particle.lineWidth = 0.5
        particle.position = position
        particle.zPosition = 999
        
        print("🎯🎆 粒子创建完成 - radius: \(Self.lineParticleSize), position: \(position), zPosition: 999")
        
        // 添加物理体（无重力）
        particle.physicsBody = SKPhysicsBody(circleOfRadius: Self.lineParticleSize)
        particle.physicsBody?.isDynamic = true
        particle.physicsBody?.mass = 0.005
        
        // 设置随机飘散方向和力度（更轻柔）
        let driftForce = CGFloat.random(in: Self.lineParticleDriftRange)
        let angle = CGFloat.random(in: 0...(2 * .pi))
        let dx = cos(angle) * driftForce
        let dy = sin(angle) * driftForce
        
        particle.physicsBody?.velocity = CGVector(dx: dx, dy: dy)
        particle.physicsBody?.angularVelocity = CGFloat.random(in: -1...1)
        
        // 设置更强的阻尼（让粒子更快减速，营造轻柔飘散感）
        particle.physicsBody?.linearDamping = 2.0  // 🎆 更强的线性阻尼
        particle.physicsBody?.angularDamping = 0.5
        
        print("🎯🎆 物理属性设置完成 - velocity: \(particle.physicsBody?.velocity ?? CGVector.zero), driftForce: \(driftForce)")
        
        addChild(particle)
        print("🎯🎆 粒子已添加到场景，当前子节点数: \(children.count)")
        
        // 粒子生命周期动画 - 更温和的淡出
        let initialDelay = SKAction.wait(forDuration: Self.lineParticleLifetime * 0.3)  // 30%时间保持满透明度
        let fadeOut = SKAction.fadeOut(withDuration: Self.lineParticleLifetime * 0.7)   // 70%时间淡出
        let remove = SKAction.removeFromParent()
        let sequence = SKAction.sequence([initialDelay, fadeOut, remove])
        particle.run(sequence)
        
        print("🎯🎆 粒子动画序列已启动 - lifetime: \(Self.lineParticleLifetime)秒")
    }
    #else
    func createLineParticles(at position: CGPoint, color: NSColor) {
        
        if debugInfo{
            print("🎯🎆 ChromaCircleEffects.createLineParticles (macOS) 被调用")
            print("🎯🎆 position: \(position), color: \(color)")
            print("🎯🎆 lineParticleCount: \(Self.lineParticleCount)")
        }
        
        // 创建指定数量的线条粒子
        for i in 0..<Self.lineParticleCount {
            if debugInfo{
                print("🎯🎆 创建第 \(i+1) 个线条粒子")
            }
            createDriftingParticle(at: position, color: color)
        }
    }
    
    /// 创建飘散粒子
    /// - Parameters:
    ///   - position: 起始位置
    ///   - color: 粒子颜色
    private func createDriftingParticle(at position: CGPoint, color: NSColor) {
        
        if debugInfo{
            print("🎯🎆 createDriftingParticle (macOS) 被调用 - position: \(position), color: \(color)")
        }
        
        // 创建圆形粒子
        let particle = SKShapeNode(circleOfRadius: Self.lineParticleSize)
        particle.fillColor = color
        particle.strokeColor = .white
        particle.lineWidth = 0.5
        particle.position = position
        particle.zPosition = 999
        
        if debugInfo {
            print("🎯🎆 粒子创建完成 - radius: \(Self.lineParticleSize), position: \(position), zPosition: 999")
        }
        
        // 添加物理体（无重力）
        particle.physicsBody = SKPhysicsBody(circleOfRadius: Self.lineParticleSize)
        particle.physicsBody?.isDynamic = true
        particle.physicsBody?.mass = 0.005
        
        // 设置随机飘散方向和力度（更轻柔）
        let driftForce = CGFloat.random(in: Self.lineParticleDriftRange)
        let angle = CGFloat.random(in: 0...(2 * .pi))
        let dx = cos(angle) * driftForce
        let dy = sin(angle) * driftForce
        
        particle.physicsBody?.velocity = CGVector(dx: dx, dy: dy)
        particle.physicsBody?.angularVelocity = CGFloat.random(in: -1...1)
        
        // 设置更强的阻尼（让粒子更快减速，营造轻柔飘散感）
        particle.physicsBody?.linearDamping = 2.0  // 🎆 更强的线性阻尼
        particle.physicsBody?.angularDamping = 0.5
        
        if debugInfo{
            print("🎯🎆 物理属性设置完成 - velocity: \(particle.physicsBody?.velocity ?? CGVector.zero), driftForce: \(driftForce)")
        }
        
        addChild(particle)
        
        if debugInfo {
            print("🎯🎆 粒子已添加到场景，当前子节点数: \(children.count)")
        }
        
        // 粒子生命周期动画 - 更温和的淡出
        let initialDelay = SKAction.wait(forDuration: Self.lineParticleLifetime * 0.3)  // 30%时间保持满透明度
        let fadeOut = SKAction.fadeOut(withDuration: Self.lineParticleLifetime * 0.7)   // 70%时间淡出
        let remove = SKAction.removeFromParent()
        let sequence = SKAction.sequence([initialDelay, fadeOut, remove])
        particle.run(sequence)
        if debugInfo{
            print("🎯🎆 粒子动画序列已启动 - lifetime: \(Self.lineParticleLifetime)秒")
        }
    }
    #endif
    
    /// 清除所有粒子
    func clearAllParticles() {
        enumerateChildNodes(withName: "*") { node, _ in
            if node is SKShapeNode {
                node.removeFromParent()
            }
        }
    }
}

/**
 * Circle12粒子效果协调器
 */
/*
class Circle12ParticleCoordinator: ObservableObject {
    var scene: ChromaCircleEffects?
    
    func explode(at position: CGPoint) {
        DispatchQueue.main.async {
            self.scene?.explodeAt(position)
        }
    }
    
    /// 🎆 触发线条粒子效果
    /// - Parameters:
    ///   - position: 粒子位置
    ///   - color: 粒子颜色
    func createLineParticles(at position: CGPoint, color: Color) {
        print("🎯🎆 Circle12ParticleCoordinator.createLineParticles 被调用")
        print("🎯🎆 position: \(position), color: \(color)")
        print("🎯🎆 scene 是否存在: \(scene != nil)")
        
        DispatchQueue.main.async {
            #if os(iOS)
            let platformColor = UIColor(color)
            print("🎯🎆 iOS: 转换颜色为 UIColor: \(platformColor)")
            self.scene?.createLineParticles(at: position, color: platformColor)
            #else
            // macOS上需要手动转换Color到NSColor
            if let cgColor = color.cgColor {
                let platformColor = NSColor(cgColor: cgColor) ?? NSColor.blue
                print("🎯🎆 macOS: 转换颜色为 NSColor: \(platformColor)")
                self.scene?.createLineParticles(at: position, color: platformColor)
            } else {
                print("🎯🎆 macOS: 颜色转换失败，使用默认蓝色")
                self.scene?.createLineParticles(at: position, color: NSColor.blue)
            }
            #endif
        }
    }
    
    func clearParticles() {
        DispatchQueue.main.async {
            self.scene?.clearAllParticles()
        }
    }
}

/**
 * Circle12粒子效果SwiftUI包装器
 */
#if os(iOS)
struct Circle12ParticleView: UIViewRepresentable {
    @ObservedObject var coordinator: Circle12ParticleCoordinator
    
    func makeUIView(context: Context) -> SKView {
        print("🎯🎆 Circle12ParticleView.makeUIView (iOS) 被调用")
        
        let skView = SKView()
        skView.backgroundColor = .clear
        skView.allowsTransparency = true
        skView.isOpaque = false
        
        // 🔧 修复：应该创建 ChromaCircleEffects 而不是 ChromaCircleView
        let scene = ChromaCircleEffects()
        scene.scaleMode = SKSceneScaleMode.resizeFill
        scene.backgroundColor = SKColor.clear
        
        coordinator.scene = scene
        skView.presentScene(scene)
        
        print("🎯🎆 SKView 和场景创建完成，scene: \(scene)")
        
        return skView
    }
    
    func updateUIView(_ uiView: SKView, context: Context) {
        if let scene = coordinator.scene {
            scene.size = uiView.bounds.size
            print("🎯🎆 更新场景尺寸: \(uiView.bounds.size)")
        }
    }
}
#else
struct Circle12ParticleView: NSViewRepresentable {
    @ObservedObject var coordinator: Circle12ParticleCoordinator
    
    func makeNSView(context: Context) -> SKView {
        print("🎯🎆 Circle12ParticleView.makeNSView (macOS) 被调用")
        
        let skView = SKView()
        skView.allowsTransparency = true
        
        let scene = ChromaCircleEffects()
        scene.scaleMode = .resizeFill
        scene.backgroundColor = .clear
        
        coordinator.scene = scene
        skView.presentScene(scene)
        
        print("🎯🎆 SKView 和场景创建完成，scene: \(scene)")
        
        return skView
    }
    
    func updateNSView(_ nsView: SKView, context: Context) {
        if let scene = coordinator.scene {
            scene.size = nsView.bounds.size
            print("🎯🎆 更新场景尺寸: \(nsView.bounds.size)")
        }
    }
}
#endif
*/





// MARK: - 音符状态枚举
enum chromaCircleNoteState {
    case inactive   // 未激活 - 灰色
    case expected   // 预期 - 蓝色
    case pressed    // 按下 - 黄色
    case correct    // 正确 - 绿色
    case expectedRoot   // 预期根音 - 蓝色加大
    case pressedRoot    // 按下根音 - 黄色加大
    case correctRoot    // 正确根音 - 绿色加大
    case rootOnly       // 仅根音（不在演奏中）- 橙色
    
    var color: Color {
        switch self {
        case .inactive: return .gray
        case .expected, .expectedRoot: return ChromaCircleViewModel.color1
        case .pressed, .pressedRoot: return ChromaCircleViewModel.color2
        case .correct, .correctRoot: return ChromaCircleViewModel.color3
        case .rootOnly: return .orange.opacity(0.8)
        }
    }
    
    var isRoot: Bool {
        switch self {
        case .expectedRoot, .pressedRoot, .correctRoot, .rootOnly:
            return true
        default:
            return false
        }
    }
    
    var dotSize: CGFloat {
        return isRoot ? 14 : (self == .inactive ? 3 : 10)
    }
    
    var fontSize: CGFloat {
        return isRoot ? 18 : (self == .correct ? 16 : 14)
    }
    
    var fontWeight: Font.Weight {
        return isRoot ? .heavy : (self == .correct ? .bold : .medium)
    }
    
}

// MARK: - ChromaCircleView
struct ChromaCircleView: View {
    @ObservedObject var viewModel: ChromaCircleViewModel
    private var debugInfo :Bool = false
    //@StateObject private var particleCoordinator = Circle12ParticleCoordinator()
    
    init() {
        self.viewModel = ChromaCircleViewModel()
    }
    
    init(viewModel: ChromaCircleViewModel) {
        self.viewModel = viewModel
    }
    
    var body: some View {
        GeometryReader { geometry in
            let circleLayout = calculateCircleLayout(geometry: geometry)
            
            ZStack {
                // 半音圆圈
                createWhiteCircle(layout: circleLayout)
                    .zIndex(1)

                // 多种连线类型 (在音符下方)
                createAllConnectionLines(layout: circleLayout)
                    .zIndex(2)

                // 12个音符点和标签
                createNoteElements(layout: circleLayout)
                    .zIndex(3)

                // 🎬 和弦成功动画层（在最顶层）
                if viewModel.showChordSuccessAnimation {
                    createChordSuccessAnimationView(layout: circleLayout)
                        .allowsHitTesting(false)
                        .zIndex(1000)
                }

                // 粒子效果层（暂时注释）
                /*
                Circle12ParticleView(coordinator: particleCoordinator)
                    .allowsHitTesting(false)
                    .zIndex(1000)
                 */
                
            }
            .onAppear {

                if debugInfo{
                    print("🎯🎆 ChromaCircleView onAppear - 开始设置粒子回调")
                }

                // � 预创建动画组件（游戏开始时调用一次）
                viewModel.initializeAnimations()

                // �🎆 设置白色粒子爆炸回调 (暂时注释)
                // viewModel.onWhiteParticleExplosion = { correctNotes in
                //     print("🎆 ChromaCircleView: 收到白色粒子爆炸请求，音符: \(correctNotes)")
                //     self.triggerWhiteParticlesAtNotePositions(correctNotes, layout: circleLayout)
                // }

//                if debugInfo{
//                    print("🎯🎆 ChromaCircleView onAppear - 白色粒子回调设置完成")
//                }
            }
        }
    }
    
    // MARK: - Helper Functions
    
    /// 计算圆形布局参数
    private func calculateCircleLayout(geometry: GeometryProxy) -> CircleLayout {
        let size = min(geometry.size.width, geometry.size.height)
        let center = CGPoint(x: geometry.size.width / 2, y: geometry.size.height / 2)
        let radius = size * 0.35
        
        return CircleLayout(center: center, radius: radius)
    }
    
    /// 创建白色圆圈
    private func createWhiteCircle(layout: CircleLayout) -> some View {
        
        let active = !viewModel.pressedNotes.isEmpty
        let strokeColor: Color = active ? .white : .gray
        let strokeWidth: CGFloat = active ? 2 : 1          //激活宽度2
        return Circle()
            .stroke(strokeColor,lineWidth: strokeWidth)
            .frame(width: layout.radius * 2, height: layout.radius * 2)
            .position(layout.center)
            .opacity(active ? 1.0 : 0.3)
            .animation(.easeInOut(duration: 0.3), value: active)
    }
    
    
    /// 创建所有类型的连线
    private func createAllConnectionLines(layout: CircleLayout) -> some View {
        // 🔧 移动print语句到函数开始，避免ViewBuilder问题
        DispatchQueue.main.async {
            if debugInfo{
                print("🎯🎆 createAllConnectionLines 被调用")
                print("🎯🎆 expectedNotes: \(viewModel.expectedNotes)")
                print("🎯🎆 pressedNotes: \(viewModel.pressedNotes)")
                print("🎯🎆 correctNotes: \(viewModel.correctNotes)")
                print("🎯🎆 showConnections: \(viewModel.showConnections)")
            }
        }
        
        return ZStack {
            // 1. 预期音符连线 (蓝色)
            if !viewModel.expectedNotes.isEmpty {
                createConnectionLines(
                    notes: viewModel.getSortedExpectedNotes(),
                    color: ChromaCircleViewModel.color1,
                    layout: layout
                )
                .onAppear {
                    if debugInfo{
                        print("🎯🎆 创建预期音符连线 (蓝色)")
                    }
                }
            }
            
            // 2. 按下音符连线 (黄色)
            if !viewModel.pressedNotes.isEmpty {
                createConnectionLines(
                    notes: viewModel.getSortedPressedNotes(),
                    color: ChromaCircleViewModel.color2,
                    layout: layout
                )
                .onAppear {
                    if debugInfo{
                        print("🎯🎆 创建按下音符连线 (黄色)")
                    }
                }
            }
            
            // 3. 正确音符连线 (绿色) - 优先级最高，覆盖其他连线
            if viewModel.showConnections && !viewModel.correctNotes.isEmpty {
                createConnectionLines(
                    notes: viewModel.getSortedCorrectNotes(),
                    color: ChromaCircleViewModel.color3,
                    layout: layout
                )
                .onAppear {
                    if debugInfo{
                        print("🎯🎆 创建正确音符连线 (绿色)")
                    }
                }
            }
        }
    }
    
    /// 创建指定音符和颜色的连线
    private func createConnectionLines(notes: [Int], color: Color, layout: CircleLayout) -> some View {
        // 🔧 移动print语句避免ViewBuilder问题
        DispatchQueue.main.async {
            if debugInfo{
                print("🎯🎆 createConnectionLines 被调用 - notes: \(notes), color: \(color)")
                print("🎯🎆 lineParticlesEnabled: \(viewModel.lineParticlesEnabled), showConnections: \(viewModel.showConnections)")
            }
        }
        
        // 🔧 修复1: 将复杂表达式拆分为多个步骤
        let path = createLinePath(notes: notes, layout: layout)
        let styledPath = applyLineStyles(to: path, color: color)
        return styledPath
            .onAppear {
                if debugInfo {
                    print("🎯🎆 连线 onAppear - notes: \(notes), color: \(color)")
                }
                // 🎆 触发线条粒子效果
                if viewModel.lineParticlesEnabled && !notes.isEmpty && viewModel.showConnections {
                    if debugInfo{
                        print("🎯🎆 onAppear 条件满足，触发线条粒子")
                    }
                    triggerLineParticles(for: notes, color: color, layout: layout)
                } else {
                    if debugInfo{
                        print("🎯🎆 onAppear 条件不满足 - lineParticlesEnabled: \(viewModel.lineParticlesEnabled), notes.isEmpty: \(notes.isEmpty), showConnections: \(viewModel.showConnections)")
                    }
                }
            }
            .onChange(of: viewModel.showConnections) {newValue in
                
                if debugInfo{
                    print("🎯🎆 showConnections 变化 - 新值: \(newValue), notes: \(notes)")
                }
                // 🔧 修复: 监听showConnections变化，这是最关键的触发时机
                if viewModel.lineParticlesEnabled && !notes.isEmpty && newValue {
                    if debugInfo{
                        print("🎯🎆 showConnections 条件满足，触发线条粒子")
                    }
                    triggerLineParticles(for: notes, color: color, layout: layout)
                } else {
                    if debugInfo{
                        print("🎯🎆 showConnections 条件不满足")
                    }
                }
            }
            .onChange(of: notes) { newNotes in
                
                if debugInfo{
                    print("🎯🎆 notes 变化 - 新值: \(newNotes)")
                }
                // 🔧 修复2: 监听notes数组变化而不是count
                if viewModel.lineParticlesEnabled && !newNotes.isEmpty && viewModel.showConnections {
                   // print("🎯🎆 notes 条件满足，触发线条粒子")
                    triggerLineParticles(for: newNotes, color: color, layout: layout)
                } else {
                    //print("🎯🎆 notes 条件不满足")
                }
            }
    }
    
    /// 创建线条路径
    private func createLinePath(notes: [Int], layout: CircleLayout) -> Path {
        Path { path in
            guard notes.count > 1 else { return }
            
            // 计算第一个点的位置
            let firstNoteIndex = notes[0]
            let firstAngle = Double(firstNoteIndex) * .pi / 6 - .pi / 2
            let firstPoint = CGPoint(
                x: layout.center.x + CGFloat(cos(firstAngle)) * layout.radius,
                y: layout.center.y + CGFloat(sin(firstAngle)) * layout.radius
            )
            path.move(to: firstPoint)
            
            // 连接到后续所有点，形成闭合回路
            for i in 1...notes.count {
                let noteIndex = notes[i % notes.count]
                let angle = Double(noteIndex) * .pi / 6 - .pi / 2
                let point = CGPoint(
                    x: layout.center.x + CGFloat(cos(angle)) * layout.radius,
                    y: layout.center.y + CGFloat(sin(angle)) * layout.radius
                )
                path.addLine(to: point)
            }
        }
    }
    
    /// 应用线条样式
    private func applyLineStyles(to path: Path, color: Color) -> some View {
        path
            .stroke(color, lineWidth: 2)
            .shadow(color: color, radius: 10, x: 0, y: 0)
            
            //.blendMode(.screen)
            // 🎆 添加弹性出现动画
            //.scaleEffect(viewModel.showConnections ? 1.0 : 0.5)
            //.animation(.spring(response: 0.6, dampingFraction: 0.7, blendDuration: 0.3), value: viewModel.showConnections)
            // 🎆 添加渐变透明度动画
        //    .opacity(viewModel.showConnections ? 0.8 : 0.0)
            .animation(.easeOut(duration: 0.8), value: true)//viewModel.showConnections)
    }
    
    /// 🎆 触发线条粒子效果
    private func triggerLineParticles(for notes: [Int], color: Color, layout: CircleLayout) {

        if debugInfo{
            print("🎯🎆 triggerLineParticles 被调用 - notes: \(notes), color: \(color)")
            print("🎯🎆 onLineParticleExplosion 回调是否存在: \(viewModel.onLineParticleExplosion != nil)")
        }

        guard notes.count > 0 else {
            //print("🎯🎆 notes 为空，无法创建粒子")
            return
        }

        if notes.count == 1 {
            // 如果只有一个音符，在该音符位置创建粒子
            //print("🎯🎆 单个音符，在音符位置创建粒子")
            let noteIndex = notes[0]
            let angle = Double(noteIndex) * .pi / 6 - .pi / 2
            let point = CGPoint(
                x: layout.center.x + CGFloat(cos(angle)) * layout.radius,
                y: layout.center.y + CGFloat(sin(angle)) * layout.radius
            )
            //print("🎯🎆 单音符粒子位置: \(point)")
            viewModel.onLineParticleExplosion?(point, color)
            return
        }

        print("🎯🎆 多音符(\(notes.count))，为每条线段创建粒子")

        // 为每两个连续音符之间的线段创建粒子
        for i in 0..<notes.count {
            let currentNote = notes[i]
            let nextNote = notes[(i + 1) % notes.count]

            // 计算线段中点
            let currentAngle = Double(currentNote) * .pi / 6 - .pi / 2
            let nextAngle = Double(nextNote) * .pi / 6 - .pi / 2

            let currentPoint = CGPoint(
                x: layout.center.x + CGFloat(cos(currentAngle)) * layout.radius,
                y: layout.center.y + CGFloat(sin(currentAngle)) * layout.radius
            )
            let nextPoint = CGPoint(
                x: layout.center.x + CGFloat(cos(nextAngle)) * layout.radius,
                y: layout.center.y + CGFloat(sin(nextAngle)) * layout.radius
            )

            let midPoint = CGPoint(
                x: (currentPoint.x + nextPoint.x) / 2,
                y: (currentPoint.y + nextPoint.y) / 2
            )

            print("🎯🎆 线段 \(i): \(currentNote)->\(nextNote), 中点: \(midPoint)")

            // 延迟触发，创建错落有致的效果
            DispatchQueue.main.asyncAfter(deadline: .now() + Double(i) * 0.1) {
                //print("🎯🎆 延迟 \(Double(i) * 0.1)s 后在位置 \(midPoint) 创建粒子")
                self.viewModel.onLineParticleExplosion?(midPoint, color)
            }
        }
    }

    /// 🎆 在音符位置触发白色粒子爆炸
    private func triggerWhiteParticlesAtNotePositions(_ correctNotes: [Int], layout: CircleLayout) {
        print("🎆 ChromaCircleView: 开始在音符位置触发白色粒子爆炸")
        print("🎆 正确音符: \(correctNotes)")
        print("🎆 ChromaCircle布局 - 中心: \(layout.center), 半径: \(layout.radius)")

        guard !correctNotes.isEmpty else {
            print("❌ 没有正确音符，跳过粒子爆炸")
            return
        }

        // 为每个正确音符计算精确位置并触发粒子
        var particlePositions: [CGPoint] = []

        for noteIndex in correctNotes {
            let angle = Double(noteIndex) * .pi / 6 - .pi / 2 // 与音符位置计算保持一致

            // 🔧 计算在ChromaCircleView坐标系中的位置
            let localPosition = CGPoint(
                x: layout.center.x + CGFloat(cos(angle)) * layout.radius,
                y: layout.center.y + CGFloat(sin(angle)) * layout.radius
            )

            // 🔧 转换为全局坐标系（考虑ChromaCircleView在父视图中的位置）
            // 这里需要获取ChromaCircleView在窗口中的实际位置
            let globalPosition = convertToGlobalCoordinates(localPosition)

            particlePositions.append(globalPosition)

            print("🎆 音符 \(noteIndex) - 本地位置: \(localPosition), 全局位置: \(globalPosition)")
        }

        print("🎆 总共 \(particlePositions.count) 个粒子位置")

        // 调用白色粒子协调器
        WhiteParticleCoordinator.shared.explodeWhiteParticles(at: particlePositions)

        print("✅ 白色粒子爆炸触发完成")
    }

    /// 🔧 将ChromaCircleView的本地坐标转换为全局坐标
    private func convertToGlobalCoordinates(_ localPosition: CGPoint) -> CGPoint {
        // 这里需要考虑ChromaCircleView在ProgressionsView中的实际偏移
        // 由于ProgressionsView有工具栏、状态栏等，ChromaCircleView不在顶部

        // 🔧 临时方案：添加一个估算的偏移量
        // 这个偏移量需要根据实际的UI布局调整
        let estimatedYOffset: CGFloat = 100 // 估算ChromaCircleView距离顶部的距离

        return CGPoint(
            x: localPosition.x,
            y: localPosition.y + estimatedYOffset
        )
    }

    /// 🎬 创建和弦成功动画视图 - 使用预创建的动画组件
    private func createChordSuccessAnimationView(layout: CircleLayout) -> AnyView {
        // 🔧 使用预创建的动画组件，避免频繁创建
        if let animationView = viewModel.getAnimationView(for: viewModel.currentAnimationType) {
            return AnyView(
                animationView
                    .position(layout.center)
                    .id(viewModel.chordSuccessAnimationKey) // 🔧 只有UUID变化时才重新触发动画
            )
        } else {
            // 🚨 如果预创建失败，回退到即时创建（不应该发生）
            print("⚠️ 预创建的动画组件不存在，回退到即时创建")
            return AnyView(
                Group {
                    switch viewModel.currentAnimationType {
                    case .singleChord:
                        LottieAnimationView.chordMatchAnimation(scale: 1.0) {  // 🎯 
                            self.viewModel.hideChordSuccessAnimation()
                        }
                        .position(layout.center)
                    case .progression:
                        LottieAnimationView.progressionMatchAnimation(scale: 1.0) {  // 🎯 更有庆祝感
                            self.viewModel.hideChordSuccessAnimation()
                        }
                        .position(layout.center)
                    }
                }
                .id(viewModel.chordSuccessAnimationKey)
            )
        }
    }
    
    /// 创建所有音符元素
    private func createNoteElements(layout: CircleLayout) -> some View {
        ForEach(0..<12, id: \.self) { noteIndex in
            createSingleNoteElement(noteIndex: noteIndex, layout: layout)
        }
    }
    
    /// 创建单个音符元素（点 + 标签）
    private func createSingleNoteElement(noteIndex: Int, layout: CircleLayout) -> some View {
        let positions = calculateNotePositions(noteIndex: noteIndex, layout: layout)
        let noteState = viewModel.getNoteState(noteIndex)
        
        return ZStack {
            // 音符点
            createNoteDot(position: positions.dotPosition, state: noteState)
            
            // 音符标签
            createNoteLabel(noteIndex: noteIndex, position: positions.labelPosition, state: noteState)
        }
    }
    
    /// 计算音符位置
    private func calculateNotePositions(noteIndex: Int, layout: CircleLayout) -> NotePositions {
        let angle = Double(noteIndex) * .pi / 6 - .pi / 2 // 从12点开始，顺时针
        
        let dotPosition = CGPoint(
            x: layout.center.x + CGFloat(cos(angle)) * layout.radius,
            y: layout.center.y + CGFloat(sin(angle)) * layout.radius
        )
        
        let labelPosition = CGPoint(
            x: layout.center.x + CGFloat(cos(angle)) * (layout.radius + 30),
            y: layout.center.y + CGFloat(sin(angle)) * (layout.radius + 30)
        )
        
        return NotePositions(dotPosition: dotPosition, labelPosition: labelPosition)
    }
    
    /// 创建音符点
    private func createNoteDot(position: CGPoint, state: chromaCircleNoteState) -> some View {
        
        let active = !viewModel.pressedNotes.isEmpty
        let dotColor = active ? Color.white : Color.gray
        return Circle()
            .fill(state == .inactive ? dotColor : state.color)
            .frame(width: state == .inactive ? 3 : 10, height: state == .inactive ? 3 : 10)
            .position(position)
            //  正确音符不要放大，就在原地
            //.scaleEffect(state == .correct ? 1.2 : 1.0)
            .overlay(
                        // 根音添加额外的边框强调
                state.isRoot ?
                    
                    Circle()
                        .stroke(Color.white, lineWidth: 2)
                        .frame(width: state.dotSize + 4, height: state.dotSize + 4)
                        .position(position)
                
                        : nil
                    )
            .animation(.easeInOut(duration: 0.3), value: state)
    }
    
    /// 创建音符标签
    private func createNoteLabel(noteIndex: Int, position: CGPoint, state: chromaCircleNoteState) -> some View {
        Text(viewModel.noteNames[noteIndex])
            .font(.system(size: state == .correct ? 16 : 14, weight: state == .correct ? .bold : .medium))
            .foregroundColor(state == .inactive ? .gray : state.color)
        /*
            .overlay(
                        // 根音添加阴影效果
                        state.isRoot ?
                        Text(viewModel.noteNames[noteIndex])
                            .font(.system(size: state.fontSize, weight: state.fontWeight))
                            .foregroundColor(.white)
                            .blur(radius: 1)
                            .offset(x: 1, y: 1)
                        : nil
                    )
         */
            .position(position)
            .animation(.easeInOut(duration: 0.3), value: state)
    }
}

// MARK: - Helper Structures

/// 圆形布局参数
private struct CircleLayout {
    let center: CGPoint
    let radius: CGFloat
}

/// 音符位置信息
private struct NotePositions {
    let dotPosition: CGPoint
    let labelPosition: CGPoint
}

// MARK: - 粒子效果配置扩展
/*
extension ChromaCircleView {
    /// 配置爆炸粒子效果参数
    /// - Parameters:
    ///   - particleLifetime: 粒子生命周期（秒）
    ///   - particleCount: 粒子数量
    ///   - explosionForce: 爆炸力范围
    ///   - particleSize: 粒子大小
    static func configureExplosionParticles(
        particleLifetime: TimeInterval = 3.0,
        particleCount: Int = 30,
        explosionForce: ClosedRange<CGFloat> = 100...200,
        particleSize: CGFloat = 6
    ) {
        ChromaCircleEffects.particleLifetime = particleLifetime
        ChromaCircleEffects.particleCount = particleCount
        ChromaCircleEffects.explosionForceRange = explosionForce
        ChromaCircleEffects.particleSize = particleSize
    }
    
    /// 🎆 配置线条粒子效果参数
    /// - Parameters:
    ///   - lineParticleCount: 线条粒子数量
    ///   - lineParticleLifetime: 线条粒子生命周期（秒）
    ///   - lineParticleSize: 线条粒子大小
    ///   - lineParticleDriftRange: 飘散距离范围
    static func configureLineParticles(
        lineParticleCount: Int = 5,
        lineParticleLifetime: TimeInterval = 2.0,
        lineParticleSize: CGFloat = 1.5,
        lineParticleDriftRange: ClosedRange<CGFloat> = 20...50
    ) {
        ChromaCircleEffects.lineParticleCount = lineParticleCount
        ChromaCircleEffects.lineParticleLifetime = lineParticleLifetime
        ChromaCircleEffects.lineParticleSize = lineParticleSize
        ChromaCircleEffects.lineParticleDriftRange = lineParticleDriftRange
    }
    
    /// 配置所有粒子效果参数（兼容性方法）
    /// - Parameters:
    ///   - particleLifetime: 爆炸粒子生命周期（秒）
    ///   - particleCount: 爆炸粒子数量
    ///   - explosionForce: 爆炸力范围
    ///   - particleSize: 爆炸粒子大小
    static func configureParticles(
        particleLifetime: TimeInterval = 3.0,
        particleCount: Int = 30,
        explosionForce: ClosedRange<CGFloat> = 100...200,
        particleSize: CGFloat = 6
    ) {
        configureExplosionParticles(
            particleLifetime: particleLifetime,
            particleCount: particleCount,
            explosionForce: explosionForce,
            particleSize: particleSize
        )
    }
}
*/
// MARK: - 预览
struct Circle12NotesView_Previews: PreviewProvider {
    static var previews: some View {
        VStack(spacing: 20) {
            // 普通预览
            ChromaCircleView()
                .frame(width: 300, height: 300)
                .background(Color.black)
            
            // 测试预览 - 显示粒子爆炸效果
            Circle12NotesViewTestView()
                .frame(width: 300, height: 300)
                .background(Color.black)
        }
    }
}

// MARK: - 测试视图
struct Circle12NotesViewTestView: View {
    @StateObject private var viewModel = ChromaCircleViewModel()
    
    var body: some View {
        VStack(spacing: 10) {
            ChromaCircleView(viewModel: viewModel)
                .frame(width: 250, height: 250)
            
            VStack(spacing: 10) {
                HStack(spacing: 10) {
                    Button("设置C大三和弦") {
                        // 设置C大三和弦 (C-E-G)
                        viewModel.setExpectedNotes([60, 64, 67]) // C4, E4, G4
                    }
                    .buttonStyle(.borderedProminent)
                    .foregroundColor(.white)
                    
                    Button("触发完全匹配") {
                        // 模拟按下完全匹配的音符
                        viewModel.setPressedNotes([60, 64, 67]) // C4, E4, G4
                    }
                    .buttonStyle(.borderedProminent)
                    .foregroundColor(.white)
                    
                    Button("清除") {
                        viewModel.clearAll()
                    }
                    .buttonStyle(.bordered)
                }
                
                // 🎆 线条粒子效果控制
                HStack(spacing: 10) {
                    Button("🎆 \(viewModel.lineParticlesEnabled ? "关闭" : "开启")线条粒子") {
                        viewModel.lineParticlesEnabled.toggle()
                    }
                    .buttonStyle(.bordered)
                    .foregroundColor(viewModel.lineParticlesEnabled ? .green : .gray)
                    
                    Button("🎆 测试粒子") {
                        // 直接测试粒子效果
                        print("🎯🎆 手动触发测试粒子")
                        let testPosition = CGPoint(x: 125, y: 125) // 视图中心
                        viewModel.onLineParticleExplosion?(testPosition, .red)
                    }
                    .buttonStyle(.bordered)
                    .foregroundColor(.red)
                }
            }
            .font(.caption)
            
            VStack(alignment: .leading, spacing: 5) {
                Text("使用说明:")
                    .font(.caption)
                    .fontWeight(.bold)
                Text("1. 点击'设置C大三和弦'显示预期音符(蓝色)")
                    .font(.caption2)
                Text("2. 点击'触发完全匹配'会触发绿色粒子爆炸")
                    .font(.caption2)
                Text("3. 🎆 线条粒子：从连线上徐徐飘散的彩色粒子")
                    .font(.caption2)
                Text("4. 粒子特点：无重力、速度逐渐减慢、轻柔飘散")
                    .font(.caption2)
            }
            .foregroundColor(.white)
            .padding(.horizontal)
        }
        .onAppear {
            // 启用调试模式以便查看日志
            //ChromaCircleViewModel.debugEnabled = true
            
            // 🎯🎆 测试：立即设置一些音符来测试粒子效果
            DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                print("🎯🎆 测试：设置预期音符")
                viewModel.setExpectedNotes([60, 64, 67]) // C大三和弦
            }
            
            DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
                print("🎯🎆 测试：设置按下音符（应该触发粒子）")
                viewModel.setPressedNotes([60, 64, 67]) // 匹配的音符
            }
        }
    }
} 


// 添加扩展（在单独文件中）
extension Shape {
    func strokeGradient(
        colors: [Color],
        width: CGFloat,
        lineCap: CGLineCap
    ) -> some View {
        let gradient = LinearGradient(
            colors: colors,
            startPoint: .leading,
            endPoint: .trailing
        )
        return self.stroke(
            gradient,
            style: StrokeStyle(
                lineWidth: width,
                lineCap: lineCap
            )
        )
    }
}
