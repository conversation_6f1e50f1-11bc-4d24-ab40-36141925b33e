import SwiftUI

struct HelpView: View {
    @Environment(\.dismiss) private var dismiss
    @StateObject private var helpManager = HelpContentManager.shared
    @State private var currentPageIndex: Int = 0
    @State private var dragOffset: CGSize = .zero
    
    var body: some View {
        NavigationView {
            ZStack {
                // 背景
                Color.gray.opacity(0.05)
                    .ignoresSafeArea()
                
                if helpManager.isLoading {
                    // 加载状态
                    VStack {
                        ProgressView()
                            .scaleEffect(1.5)
                        Text("加载帮助内容...")
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .padding(.top, 8)
                    }
                } else if let helpContent = helpManager.helpContent {
                    // 帮助内容
                    VStack(spacing: 0) {
                        // 页面内容
                        TabView(selection: $currentPageIndex) {
                            ForEach(Array(helpContent.pages.enumerated()), id: \.element.id) { index, page in
                                HelpPageView(page: page)
                                    .tag(index)
                            }
                        }
                        #if os(iOS)
                        .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never))
                        #endif
                        .frame(maxWidth: .infinity, maxHeight: .infinity)
                        
                        // 底部导航区域
                        VStack(spacing: 16) {
                            // 页面指示器
                            HStack(spacing: 8) {
                                ForEach(0..<helpContent.pages.count, id: \.self) { index in
                                    Circle()
                                        .fill(index == currentPageIndex ? Color.blue : Color.gray.opacity(0.3))
                                        .frame(width: 8, height: 8)
                                        .scaleEffect(index == currentPageIndex ? 1.2 : 1.0)
                                        .animation(.easeInOut(duration: 0.2), value: currentPageIndex)
                                        .onTapGesture {
                                            withAnimation(.easeInOut(duration: 0.3)) {
                                                currentPageIndex = index
                                            }
                                        }
                                }
                            }
                            .padding(.horizontal)
                            
                            // 导航按钮
                            HStack(spacing: 20) {
                                // 上一页按钮
                                Button(action: {
                                    if currentPageIndex > 0 {
                                        withAnimation(.easeInOut(duration: 0.3)) {
                                            currentPageIndex -= 1
                                        }
                                    }
                                }) {
                                    HStack {
                                        Image(systemName: "chevron.left")
                                        Text("上一页")
                                    }
                                    .font(.system(size: 16, weight: .medium))
                                    .foregroundColor(currentPageIndex > 0 ? .blue : .gray)
                                    .padding(.horizontal, 16)
                                    .padding(.vertical, 8)
                                    .background(
                                        RoundedRectangle(cornerRadius: 8)
                                            .fill(currentPageIndex > 0 ? Color.blue.opacity(0.1) : Color.gray.opacity(0.1))
                                    )
                                }
                                .disabled(currentPageIndex == 0)
                                
                                Spacer()
                                
                                // 页面计数
                                Text("\(currentPageIndex + 1) / \(helpContent.pages.count)")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                                
                                Spacer()
                                
                                // 下一页按钮
                                Button(action: {
                                    if currentPageIndex < helpContent.pages.count - 1 {
                                        withAnimation(.easeInOut(duration: 0.3)) {
                                            currentPageIndex += 1
                                        }
                                    }
                                }) {
                                    HStack {
                                        Text("下一页")
                                        Image(systemName: "chevron.right")
                                    }
                                    .font(.system(size: 16, weight: .medium))
                                    .foregroundColor(currentPageIndex < helpContent.pages.count - 1 ? .blue : .gray)
                                    .padding(.horizontal, 16)
                                    .padding(.vertical, 8)
                                    .background(
                                        RoundedRectangle(cornerRadius: 8)
                                            .fill(currentPageIndex < helpContent.pages.count - 1 ? Color.blue.opacity(0.1) : Color.gray.opacity(0.1))
                                    )
                                }
                                .disabled(currentPageIndex == helpContent.pages.count - 1)
                            }
                            .padding(.horizontal)
                        }
                        .padding(.bottom, 20)
                        .background(
                            RoundedRectangle(cornerRadius: 16)
                                .fill(.ultraThinMaterial)
                                .shadow(color: .black.opacity(0.1), radius: 10, x: 0, y: -5)
                        )
                    }
                } else {
                    // 错误状态
                    VStack {
                        Image(systemName: "exclamationmark.triangle")
                            .font(.system(size: 40))
                            .foregroundColor(.orange)
                        Text("加载帮助内容失败")
                            .font(.headline)
                            .padding(.top, 8)
                        if let errorMessage = helpManager.errorMessage {
                            Text(errorMessage)
                                .font(.caption)
                                .foregroundColor(.secondary)
                                .padding(.top, 4)
                        }
                        Button("重试") {
                            helpManager.loadHelpContent()
                        }
                        .padding(.top, 16)
                    }
                }
            }
            .navigationTitle("使用帮助")
            #if os(iOS)
            .navigationBarTitleDisplayMode(.inline)
            #endif
            .toolbar {
                #if os(iOS)
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("完成") {
                        dismiss()
                    }
                }
                #else
                ToolbarItem(placement: .primaryAction) {
                    Button("完成") {
                        dismiss()
                    }
                }
                #endif
            }
        }
        #if os(macOS)
        .frame(minWidth: 600, minHeight: 500)
        #endif
    }
}

// MARK: - 单页帮助内容视图

struct HelpPageView: View {
    let page: HelpPage
    
    var body: some View {
        ScrollView {
            VStack(alignment: .leading, spacing: 20) {
                // 标题
                Text(page.title)
                    .font(.largeTitle)
                    .fontWeight(.bold)
                    .multilineTextAlignment(.center)
                    .frame(maxWidth: .infinity)
                    .padding(.bottom, 10)
                
                // 图片（如果有）
                if page.hasImage, let imageName = page.image {
                    Image(imageName)
                        .resizable()
                        .aspectRatio(contentMode: .fit)
                        .frame(maxHeight: 300)
                        .clipShape(RoundedRectangle(cornerRadius: 12))
                        .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
                }
                
                // 内容文本
                Text(page.content)
                    .font(.body)
                    .lineSpacing(6)
                    .multilineTextAlignment(.leading)
                    .fixedSize(horizontal: false, vertical: true)
                
                Spacer(minLength: 40)
            }
            .padding(.horizontal, 24)
            .padding(.top, 20)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
}

// MARK: - 预览

#Preview {
    HelpView()
} 