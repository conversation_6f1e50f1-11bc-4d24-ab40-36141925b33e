//
//  FXChordView.swift
//  FXChordViewDemo
//
//  Created by <PERSON> Li on 2025/6/16.
//

import SwiftUI
import Foundation

// MARK: - 音符状态枚举
enum NoteState {
    case hide       // 隐藏
    case show       // 显示
    case highlight  // 高亮
    case match      //  刚好输入正确了
}

// MARK: - 场景状态枚举
enum SceneState {
    case normal         // 正常状态
    case sceneHighlight // 场景高亮（所有音符都高亮时）
}

// MARK: - 音符数据模型
struct NoteData: Identifiable {
    let id = UUID()
    let midiValue: Int
    let noteName: String
    var state: NoteState = .hide
    var bIsExploded: Bool = false  // 防止重复爆炸
    
    init(midiValue: Int) {
        self.midiValue = midiValue
        self.noteName = NoteNameGenerator.getNoteNameOnly(note: midiValue)
        self.bIsExploded = false
    }
}

// MARK: - 主题颜色配置
struct ThemeColors {
    let colors: [Color]
    
    init(colorStrings: [String]) {
        var tempColors = colorStrings.compactMap { Color(hex: $0) }
        // 确保至少有18个颜色，不足的用默认颜色补充（增加了2个渐变背景色）
        while tempColors.count < 18 {
            tempColors.append(Color.blue)
        }
        self.colors = tempColors
    }
    
    // 颜色索引定义
    var backgroundColors: [Color] { Array(colors[0...2]) }    // 颜色0-2: 渐变背景色
    var inactiveNoteTextColor: Color { colors[3] }             // 颜色3: 非激活音符文本颜色
    var inactiveThemeColor1: Color { colors[4] }               // 颜色4: 非激活主题颜色1(圆填充)
    var inactiveThemeColor2: Color { colors[5] }               // 颜色5: 非激活主题颜色2(圆轮廓)
    var highlightedNoteTextColor: Color { colors[6] }          // 颜色6: 激活音符文本颜色
    var highlightedThemeColor1: Color { colors[7] }            // 颜色7: 激活主题颜色1(圆填充)
    var highlightedThemeColor2: Color { colors[8] }            // 颜色8: 激活主题颜色2(圆轮廓)
    var highlightedThemeColor3: Color { colors[9] }            // 颜色9: 激活主题颜色3(圆发光)
    var inactiveChordNameColor: Color { colors[10] }           // 颜色10: 非激活和弦名称颜色
    var highlightedChordNameColor: Color { colors[11] }        // 颜色11: 激活和弦名称颜色
    
    // 背景方块随机颜色 (颜色12-17)
    var backgroundSquareColors: [Color] {
        return Array(colors[12...17])
    }
}

// MARK: - 粒子数据模型
struct ParticleData: Identifiable {
    let id = UUID()
    var position: CGPoint
    var color: Color
    var velocity: CGVector
    var size: Double
    var lifetime: Double
    var opacity: Double = 1.0
    var currentTime: Double = 0.0
}

// MARK: - FXChordView ViewModel
class FXChordViewModel: ObservableObject {
    static var debugEnabled = false  // 重新开启调试模式
    
    @Published var chordName: String = ""
    @Published var notes: [NoteData] = []
    @Published var sceneState: SceneState = .normal
    @Published var animationTrigger: Bool = false
    
    // 粒子系统
    @Published var particles: [ParticleData] = []
    
    // 存储实际的几何信息用于正确计算粒子位置
    var containerSize: CGSize = CGSize(width: 400, height: 100)
    
    // 添加用于非高亮圆形脉动动画的状态
    @Published var circlePulseScale: CGFloat = 1.0
    
    // 🎨 动画锁定机制 - 用于成功动画期间保持状态
    @Published var isAnimationLocked: Bool = false
    private var animationUnlockTimer: Timer?
    private var responseTime: Double = 2.0 // 默认响应时间，会从GameConfig更新
    
    // 🎹 动画结束回调 - 用于通知外部清除键盘按键提示
    #if os(macOS)
    var onAnimationEnded: (() -> Void)?
    #endif
    
    // 独立管理bounce动画状态，避免影响其他音符
    @Published var showBouncingIndices: Set<Int> = []
    @Published var highlightBouncingIndices: Set<Int> = []
    
    let themeColors: ThemeColors
    private var animationTimer: Timer?
    
    // 添加脉动动画定时器
    private var pulseTimer: Timer?
    
    // 预计算的背景正方形数据，避免每次重绘时重新计算
    private var backgroundSquares: [(color: Color, delay: Double, duration: Double)] = []
    
    // 颜色变化动画状态
    @Published var colorChangeIndex: Int = 0
    
    // 构造函数
    //颜色表
    init(colorStrings: [String] = [
        //  渐变背景色（3个颜色）
        "#03045e", "#023e8a", "#0077b6",
        //  非激活：音符，圆，轮廓线
        "#FAFAFF", "#4361EE", "#4CC9F0",
        //  激活：音符，圆，轮廓线,光晕
        "#004854", "#98CE97", "#008969", "#7EBEAB",
        //  和弦名称：非激活，激活
        "#C6C0C1", "#FAFF15",
        //  下面6个背景方块颜色
        //青柠汁
        "#006400", "#007200", "#008000", "#38B000",
        "#2B9348", "#007F5F"


    ]) {
        self.themeColors = ThemeColors(colorStrings: colorStrings)
        if FXChordViewModel.debugEnabled {
            print("🎨 FXChordViewModel 初始化完成")
        }
        // 预计算背景正方形数据
        prepareBackgroundSquares()
        // 启动脉动动画
        //startPulseAnimation()
    }
    
    // 预计算背景正方形数据
    private func prepareBackgroundSquares() {
        backgroundSquares.removeAll()
        let maxSquares = 50 // 限制最大正方形数量以提升性能
        
        for _ in 0..<maxSquares {
            let color = themeColors.backgroundSquareColors.randomElement() ?? themeColors.highlightedThemeColor1
            let delay = Double.random(in: 0...0.2)
            let duration = Double.random(in: 0.8...1.2)
            backgroundSquares.append((color: color, delay: delay, duration: duration))
        }
        
        if FXChordViewModel.debugEnabled {
            print("🎨 预计算了 \(backgroundSquares.count) 个背景正方形")
        }
    }
    
    // 获取预计算的背景正方形数据
    func getBackgroundSquare(at index: Int) -> (color: Color, delay: Double, duration: Double) {
        let safeIndex = index % backgroundSquares.count
        return backgroundSquares[safeIndex]
    }
    
    // 获取动态颜色 - 基于时间和位置变化
    func getDynamicColor(for index: Int) -> Color {
        let colorCount = themeColors.backgroundSquareColors.count
        let colorIndex = Int.random(in:0..<colorCount)
        //let colorIndex = (index + colorChangeIndex) % colorCount
        return themeColors.backgroundSquareColors[colorIndex]
    }
    
    deinit {
        if FXChordViewModel.debugEnabled {
            print("🎨 FXChordViewModel 销毁")
        }
        stopAnimation()
        stopPulseAnimation()
        stopParticleAnimation()
    }
    
    // 设置和弦
    func setChord(chordName: String, notes: [Int]) {
        if FXChordViewModel.debugEnabled {
            print("🎨 设置和弦: \(chordName), MIDI音符: \(notes)")
        }
        self.chordName = chordName
        self.notes = notes.map { NoteData(midiValue: $0) }
        
        // 清空所有粒子
        particles.removeAll()
        stopParticleAnimation()
        
        if FXChordViewModel.debugEnabled {
            print("🎨 和弦设置完成，音符名称: \(self.notes.map { $0.noteName })")
            print("🎆 所有音符爆炸状态已重置")
        }
    }
    
    // 设置单个音符状态
    func setSingleNoteState(index: Int, state: NoteState) {
        guard index < notes.count else { 
            if FXChordViewModel.debugEnabled {
                print("🎨 警告: 音符索引 \(index) 超出范围")
            }
            return 
        }
        
        // 🎨 动画锁定期间忽略外部状态变化
        if isAnimationLocked && state != .match {
            if FXChordViewModel.debugEnabled {
                print("🎨 动画锁定中，忽略状态变化: 音符 \(index) -> \(state)")
            }
            return
        }
        
        if FXChordViewModel.debugEnabled {
            print("🎨 设置音符 \(index) (\(notes[index].noteName)) 状态为: \(state)")
        }
            // 🔧 UI更新修复：创建新数组来强制触发SwiftUI重绘
            /*
        var newNotes = notes
        let oldState = notes[index].state
        for i in 0..<newNotes.count {
            newNotes[i].state = state
        }
        newNotes[index].state = state

        // 通过替换整个数组来触发@Published的didChange
        notes = newNotes
        */

        let oldState = notes[index].state
        notes[index].state = state
        
        // 检测状态变化并触发bounce动画和粒子爆炸
        if oldState != .highlight && state == .highlight {
            triggerHighlightBounce(index: index)
                 }
         else if oldState != .match && state == .match{
             triggerHighlightBounce(index: index)
             // 只有没有爆炸过才能触发粒子爆炸
             if !notes[index].bIsExploded {
                 notes[index].bIsExploded = true
                 triggerParticleExplosion(for: index)
             }
         }
        else if oldState != .show && state == .show {
            // 从非show变为show时触发bounce
            triggerShowBounce(index: index)
        }
        
        updateSceneState()
    }
    
    // 设置所有音符状态
    func setAllNotesState(state: NoteState) {
        // 🎨 动画锁定期间忽略外部状态变化（除非是重置）
        if isAnimationLocked && state != .hide {
            if FXChordViewModel.debugEnabled {
                print("🎨 动画锁定中，忽略批量状态变化: \(state)")
            }
            return
        }
        
        if FXChordViewModel.debugEnabled {
            print("🎨 设置\(notes.count)个音符状态为: \(state)")
        }
        
        for i in 0..<notes.count {
            let oldState = notes[i].state
            notes[i].state = state
            
            // 检测状态变化并触发粒子爆炸
            if oldState != .match && state == .match {
                // 只有没有爆炸过才能触发粒子爆炸
                if !notes[i].bIsExploded {
                    notes[i].bIsExploded = true
                    triggerParticleExplosion(for: i)
                }
            }
        }
        
            // 🔧 UI更新修复：创建新数组来强制触发SwiftUI重绘
            /*
        var newNotes = notes
        for i in 0..<newNotes.count {
            newNotes[i].state = state
        }
        // 通过替换整个数组来触发@Published的didChange
        notes = newNotes
        */

        updateSceneState()
    }
    
    // 🎨 设置响应时间 - 从GameConfig更新
    func setResponseTime(_ time: Double) {
        responseTime = time
        if FXChordViewModel.debugEnabled {
            print("🎨 更新响应时间为: \(responseTime)秒")
        }
    }
    
    // 设置容器大小用于正确计算粒子位置
    func setContainerSize(_ size: CGSize) {
        containerSize = size
        if FXChordViewModel.debugEnabled {
            print("🎨 更新容器大小: \(size)")
        }
    }
    
    // 🎨 启动成功动画锁定
    private func startAnimationLock() {
        if FXChordViewModel.debugEnabled {
            print("🎨 启动动画锁定，持续 \(responseTime) 秒")
        }
        
        isAnimationLocked = true
        
        // 清除之前的计时器
        animationUnlockTimer?.invalidate()
        
        // 启动解锁计时器
        animationUnlockTimer = Timer.scheduledTimer(withTimeInterval: responseTime, repeats: false) { [weak self] _ in
            DispatchQueue.main.async {
                self?.unlockAnimation()
            }
        }
    }
    
    // 🎨 解锁动画
    private func unlockAnimation() {
        if FXChordViewModel.debugEnabled {
            print("🎨 动画解锁，隐藏所有音符")
        }
        
        isAnimationLocked = false
        animationUnlockTimer?.invalidate()
        animationUnlockTimer = nil
        
        // 🎨 动画播放完成后隐藏所有音符
        for i in 0..<notes.count {
            notes[i].state = .hide
        }
        updateSceneState()
        
        // 🎹 通知外部动画结束，清除键盘按键提示
        #if os(macOS)
        onAnimationEnded?()
        #endif
        
        if FXChordViewModel.debugEnabled {
            print("🎨 成功动画完成，所有音符已隐藏，已通知外部清除键盘提示")
        }
    }
    
    // 更新场景状态
    private func updateSceneState() {
        let allHighlighted = !notes.isEmpty && notes.allSatisfy { $0.state == .match }
        let newState: SceneState = allHighlighted ? .sceneHighlight : .normal
        
        if FXChordViewModel.debugEnabled {
            print("🎨 场景状态检查: 所有音符高亮=\(allHighlighted), 新状态=\(newState), 锁定状态=\(isAnimationLocked)")
        }
        
        if newState != sceneState {
            sceneState = newState
            if sceneState == .sceneHighlight {
                if FXChordViewModel.debugEnabled {
                    print("🎨 进入场景高亮模式，启动动画")
                }
                startAnimation()
                // 🎨 启动动画锁定，保护成功动画
                startAnimationLock()
            } else {

               
                if FXChordViewModel.debugEnabled {
                    print("🎨 退出场景高亮模式，停止动画")
                }
                stopAnimation()
            }
        }
    }
    
    // 启动动画
    private func startAnimation() {
        
        stopAnimation() // 确保没有重复的定时器
        
        // 立即触发一次动画
        triggerAnimation()
        
        // 启动定时器，调整为定时触发一次实现10帧/秒的颜色变化
        animationTimer = Timer.scheduledTimer(withTimeInterval: 0.2, repeats: true) { _ in
            DispatchQueue.main.async {
                self.triggerColorChange()
            }
        }
        if FXChordViewModel.debugEnabled {
            print("🎨 颜色变化定时器已启动 (10帧/秒)")
        }
    }
    
    // 停止动画
    private func stopAnimation() {
        animationTimer?.invalidate()
        animationTimer = nil
        if FXChordViewModel.debugEnabled {
            print("🎨 动画定时器已停止")
        }
    }
    
    // 触发颜色变化
    private func triggerColorChange() {
        if FXChordViewModel.debugEnabled {
            print("🎨 触发颜色变化")
        }
        colorChangeIndex = (colorChangeIndex + 1) % 1000
    }
    
    // 触发动画（保留用于其他动画）
    private func triggerAnimation() {
        if FXChordViewModel.debugEnabled {
            print("🎨 触发动画效果")
        }
        animationTrigger.toggle()
    }
    
    // 为每个音符计算独立的脉动缩放值
    func getPulseScale(for noteIndex: Int) -> CGFloat {
        let time = Date().timeIntervalSince1970
        // 🔧 [脉动异步优化] 每个音符使用不同的频率和相位偏移，避免同步脉动
        let phaseOffset = Double(noteIndex) * 0.8 // 相位偏移，让不同音符错开
        let frequencyVariation = 1.5 + Double(noteIndex) * 0.3 // 频率变化，让周期不同
        let pulseValue = sin(time * frequencyVariation + phaseOffset)
        // 将脉动值从 [-1, 1] 映射到 [0.9, 1.1]，实现轻微的大小变化
        return 1.0 + pulseValue * 0.1
    }
    
    // 启动脉动动画 - 用于非高亮圆形的周期性大小变化
    private func startPulseAnimation() {
        stopPulseAnimation() // 确保没有重复的定时器
        
        // 🔧 [帧率优化] 降低到5fps (0.2秒)，进一步减少CPU占用
        // 注意：SwiftUI本身60fps刷新是正常的，我们的定时器只是触发数据更新
        pulseTimer = Timer.scheduledTimer(withTimeInterval: 0.15, repeats: true) { _ in
            DispatchQueue.main.async {
                // 触发视图更新，让每个音符重新计算自己的脉动值
                self.circlePulseScale = 1.0 // 这只是触发更新，实际值由getPulseScale计算
            }
        }
        
        if FXChordViewModel.debugEnabled {
            print("🎨 脉动动画定时器已启动 (5fps)")
            print("🎨 注意：SwiftUI显示的60fps是正常的屏幕刷新率，不是我们的定时器频率")
        }
    }
    
    // 停止脉动动画
    private func stopPulseAnimation() {
        pulseTimer?.invalidate()
        pulseTimer = nil
        if FXChordViewModel.debugEnabled {
            print("🎨 脉动动画定时器已停止")
        }
    }
    
    // 获取自适应的音符大小比例
    func getAdaptiveNoteScale() -> CGFloat {
        let noteCount = notes.count
        if noteCount <= 8 {
            return 1.0  // 8个及以下音符使用正常大小
        } else if noteCount <= 12 {
            return 0.8  // 9-12个音符缩小到80%
        } else if noteCount <= 16 {
            return 0.65 // 13-16个音符缩小到65%
        } else {
            return 0.5  // 16个以上音符缩小到50%
        }
    }
    
    // 获取自适应的圆圈大小
    func getAdaptiveCircleSize() -> CGFloat {
        let noteCount = notes.count
        if noteCount <= 8 {
            return 80   // 8个及以下音符使用80像素圆圈
        } else if noteCount <= 12 {
            return 65   // 9-12个音符使用65像素圆圈
        } else if noteCount <= 16 {
            return 50   // 13-16个音符使用50像素圆圈
        } else {
            return 40   // 16个以上音符使用40像素圆圈
        }
    }
    
    // 获取自适应的字体大小
    func getAdaptiveFontSize() -> Font {
        let noteCount = notes.count
        if noteCount <= 8 {
            return .largeTitle     // 8个及以下音符使用大标题字体
        } else if noteCount <= 12 {
            return .title2         // 9-12个音符使用标题2字体
        } else if noteCount <= 16 {
            return .title3         // 13-16个音符使用标题3字体
        } else {
            return .headline       // 16个以上音符使用标题字体
        }
    }
    
    // 触发highlight bounce动画
    private func triggerHighlightBounce(index: Int) {
        
        if FXChordViewModel.debugEnabled {
            print("触发 bounce，当前集合: \(highlightBouncingIndices)")
        }
        
        guard index < notes.count else { return }
        
        if FXChordViewModel.debugEnabled {
            print("🎨 触发highlight bounce动画 - 音符 \(index)")
        }
        
        highlightBouncingIndices.insert(index)
        
        // 使用延迟重置bounce状态，配合SwiftUI动画
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.6) {
            self.highlightBouncingIndices.remove(index)
        }
    }
    
    // 触发show bounce动画
    private func triggerShowBounce(index: Int) {
        guard index < notes.count else { return }
        
        if FXChordViewModel.debugEnabled {
            print("🎨 触发show bounce动画 - 音符 \(index)")
        }
        
        showBouncingIndices.insert(index)
        
        // 使用延迟重置bounce状态，配合SwiftUI动画
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.6) {
            self.showBouncingIndices.remove(index)
        }
    }
    
    // 触发粒子爆炸效果
    private func triggerParticleExplosion(for index: Int) {
        guard index < notes.count else { return }
        
        let noteName = notes[index].noteName
        
        if FXChordViewModel.debugEnabled {
            print("🎆 触发粒子爆炸 - 音符 \(index) (\(noteName))")
        }
        
        // 计算音符位置
        let notePosition = calculateNotePosition(for: index)
        
        if FXChordViewModel.debugEnabled {
            print("🎆 粒子爆炸位置: \(notePosition)")
        }
        
        // 生成20个各种彩色的正方形粒子
        for _ in 0..<20 {
            createParticle(at: notePosition)
        }
    }
    
    // 计算音符在屏幕上的位置（与noteView中的计算保持一致）
    private func calculateNotePosition(for index: Int) -> CGPoint {
        let noteCount = notes.count
        // 使用实际的容器宽度
        let containerWidth = containerSize.width
        let containerHeight = containerSize.height
        
        // 使用与noteView完全相同的位置计算逻辑
        let xPosition: CGFloat = noteCount > 1 ? 
            (containerWidth / CGFloat(noteCount)) * (CGFloat(index) + 0.5) :
            containerWidth / 2
        
        // Y位置固定为50，与noteView中的position一致
        return CGPoint(x: xPosition, y: containerHeight/2)
    }
    
    // 创建单个粒子
    private func createParticle(at position: CGPoint) {
        // 随机颜色
        let colors: [Color] = [
            .red, .orange, .yellow, .green, .blue, .purple, .pink, .cyan, .mint, .indigo
        ]
        let randomColor = colors.randomElement() ?? .blue
        
        let speed = Double.random(in: 300 ... 400)
        let angle = Double.random(in: 0 ... 360)
        // 创建粒子数据
        let particle = ParticleData(
            position: position,
            color: randomColor,
            velocity: CGVector(
                dx: speed*cos(angle) ,
                dy: speed*sin(angle)
            ),
            size: Double.random(in: 3...8),
            lifetime: 2.0
        )
        
        // 添加到粒子数组
        particles.append(particle)
        
        // 启动粒子动画
        animateParticle(particle)
    }
    
    // 动画化粒子
    private func animateParticle(_ particle: ParticleData) {
        // 启动粒子动画定时器
        startParticleAnimation()
        
        // 2秒后移除粒子
        DispatchQueue.main.asyncAfter(deadline: .now() + particle.lifetime) {
            if let index = self.particles.firstIndex(where: { $0.id == particle.id }) {
                self.particles.remove(at: index)
            }
        }
    }
    
    // 粒子动画定时器
    private var particleTimer: Timer?
    
    // 启动粒子动画
    private func startParticleAnimation() {
        guard particleTimer == nil else { return }
        
        particleTimer = Timer.scheduledTimer(withTimeInterval: 1/60.0, repeats: true) { _ in
            self.updateParticles()
        }
    }
    
    // 停止粒子动画
    private func stopParticleAnimation() {
        particleTimer?.invalidate()
        particleTimer = nil
    }
    
    // 更新粒子状态
    private func updateParticles() {
        let deltaTime = 1/60.0
        
        for i in particles.indices.reversed() {
            // 更新时间
            particles[i].currentTime += deltaTime
            
            // 更新位置
            particles[i].position.x += particles[i].velocity.dx * deltaTime
            particles[i].position.y += particles[i].velocity.dy * deltaTime
            
            // 添加重力效果
            particles[i].velocity.dy += 300 * deltaTime
            
            // 计算透明度（2秒内线性淡出）
            let progress = particles[i].currentTime / particles[i].lifetime
            particles[i].opacity = max(0, 1.0 - progress)
            
            // 移除生命周期结束的粒子
            if particles[i].currentTime >= particles[i].lifetime {
                particles.remove(at: i)
            }
        }
        
        // 如果没有粒子了，停止动画
        if particles.isEmpty {
            stopParticleAnimation()
        }
    }
}

// MARK: - FXChordView 控制器
class FXChordViewController: ObservableObject {
    @Published var viewModel = FXChordViewModel()
    
    init(colors: [String] = [
        //  渐变背景色（3个颜色）
        "#03045e", "#023e8a", "#0077b6",
        //  非激活：音符，圆，轮廓线
        "#FAFAFF", "#4361EE", "#4CC9F0",
        //  激活：音符，圆，轮廓线,光晕
        "#004854", "#98CE97", "#008969", "#7EBEAB",
        //  和弦名称：非激活，激活
        "#C6C0C1", "#FAFF15",
        //  下面6个背景方块颜色
        "#006400", "#007200", "#008000", "#38B000",
        "#2B9348", "#007F5F"
    ]) {
        self.viewModel = FXChordViewModel(colorStrings: colors)
        print("🎮 FXChordViewController 初始化完成")
    }
    
    // 设置和弦
    func setChord(chordName: String, notes: [Int]) {
        if FXChordViewModel.debugEnabled {
            print("🎮 设置和弦: \(chordName), 音符: \(notes)")
        }
        viewModel.setChord(chordName: chordName, notes: notes)
        if FXChordViewModel.debugEnabled {
            print("🎮 和弦设置完成，当前音符数: \(viewModel.notes.count)")
        }
    }
    
    // 设置单个音符状态
    func setSingleNoteState(index: Int, state: NoteState) {
        if FXChordViewModel.debugEnabled {
            print("🎮 设置音符 \(index) 状态为: \(state)")
        }
        viewModel.setSingleNoteState(index: index, state: state)
    }
    
    // 设置所有音符状态
    func setAllNotesState(state: NoteState) {
        if FXChordViewModel.debugEnabled {
            print("🎮 设置所有音符状态为: \(state)")
        }
        viewModel.setAllNotesState(state: state)
    }
}

// MARK: - FXChordView SwiftUI View
struct FXChordView: View {
    @ObservedObject var viewModel: FXChordViewModel
    
    // 构造函数
    init(colors: [String] = [
        //  渐变背景色（3个颜色）
        "#03045e", "#023e8a", "#0077b6",
        //  非激活：音符，圆，轮廓线
        "#FAFAFF", "#4361EE", "#4CC9F0",
        //  激活：音符，圆，轮廓线,光晕
        "#004854", "#98CE97", "#008969", "#7EBEAB",
        //  和弦名称：非激活，激活
        "#C6C0C1", "#FAFF15",
        //  下面6个背景方块颜色
        "#006400", "#007200", "#008000", "#38B000",
        "#2B9348", "#007F5F"
    ]) {
        self.viewModel = FXChordViewModel(colorStrings: colors)
        if FXChordViewModel.debugEnabled {
            print("🎵 FXChordView 初始化完成")
        }
    }
    
    // 使用外部ViewModel的构造函数
    init(viewModel: FXChordViewModel) {
        self.viewModel = viewModel
        if FXChordViewModel.debugEnabled {
            print("🎵 FXChordView 使用外部ViewModel初始化")
        }
    }
    
    var body: some View {
        if FXChordViewModel.debugEnabled {
            print("🎵 FXChordView body 被调用")
            print("🎵 当前和弦: \(viewModel.chordName)")
            print("🎵 音符数量: \(viewModel.notes.count)")
        }
        
        return GeometryReader { geometry in
            if FXChordViewModel.debugEnabled {
                print("🎵 GeometryReader 大小: \(geometry.size)")
            }
            
            return ZStack {
                // 调试边框
                if FXChordViewModel.debugEnabled {
                    Rectangle()
                        .stroke(viewModel.themeColors.highlightedThemeColor2, lineWidth: 2)
                        .background(viewModel.themeColors.highlightedThemeColor1.opacity(0.1))
                }
                
                // 背景
                backgroundView(geometry: geometry)
                
                // 粒子效果层
                particlesView(geometry: geometry)
                
                VStack {
                    // 调试信息
                    if FXChordViewModel.debugEnabled {
                        Text("调试: FXChordView 正在渲染")
                            .font(.caption)
                            .foregroundColor(viewModel.themeColors.highlightedNoteTextColor)
                            .padding(.top, 5)
                    }
                    
                    // 和弦名称 - 只在场景高亮时显示
                    if viewModel.sceneState == .sceneHighlight {
                        Text(viewModel.chordName.isEmpty ? "无和弦" : viewModel.chordName)
                            .font(.system(size: 60, weight: .bold, design: .default)) // 相当大的字体
                            .foregroundColor(chordNameColor())
                            .padding(.top, 20)
                            .scaleEffect(1.2) // 进一步放大
                            .shadow(color: viewModel.themeColors.highlightedThemeColor3, radius: 10)
                            .onAppear {
                                if FXChordViewModel.debugEnabled {
                                    print("🎵 场景高亮时显示大字体和弦名称: \(viewModel.chordName)")
                                }
                            }
                            .transition(.scale.combined(with: .opacity))
                    }
                    
                    Spacer()
                    
                    // 音符显示区域
                    notesView(geometry: geometry)
                    
                    // 调试音符信息
                    /*
                    if FXChordViewModel.debugEnabled {
                        Text("音符数: \(viewModel.notes.count)")
                            .font(.caption)
                            .foregroundColor(viewModel.themeColors.highlightedNoteTextColor)
                    }
                    */
                    Spacer()
                }
            }
        }
        .onAppear {
            if FXChordViewModel.debugEnabled {
                print("🎵 FXChordView onAppear 被调用")
                print("🎵 ViewModel状态: \(viewModel.chordName), 音符: \(viewModel.notes.count)")
            }
        }
        .animation(.easeInOut(duration: 0.6), value: viewModel.sceneState)
        .animation(.easeInOut(duration: 0.3), value: viewModel.animationTrigger)
    }
    
    // 和弦名称颜色
    private func chordNameColor() -> Color {
        return viewModel.sceneState == .sceneHighlight ? 
            viewModel.themeColors.highlightedChordNameColor : 
            viewModel.themeColors.inactiveChordNameColor
    }
    
    // 背景视图
    @ViewBuilder
    private func backgroundView(geometry: GeometryProxy) -> some View {
        if viewModel.sceneState == .sceneHighlight {
            // 场景高亮时的彩色正方形背景
            animatedSquaresBackground(geometry: geometry)
                .onAppear {
                    if FXChordViewModel.debugEnabled {
                        print("🎵 显示场景高亮背景")
                    }
                }
        } else {
            // 正常渐变背景
            LinearGradient(
                gradient: Gradient(colors: viewModel.themeColors.backgroundColors),
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
            .onAppear {
                if FXChordViewModel.debugEnabled {
                    print("🎵 显示渐变背景")
                }
            }
        }
    }
    
    // 动画正方形背景 - 性能优化版本
    private func animatedSquaresBackground(geometry: GeometryProxy) -> some View {
        let squareSize: CGFloat = 40 // 增大正方形尺寸以减少数量
        let rows = min(Int(geometry.size.height / squareSize) + 1, 80) // 限制最大行数
        let columns = min(Int(geometry.size.width / squareSize) + 1, 80) // 限制最大列数
        let totalSquares = rows * columns
        
        if FXChordViewModel.debugEnabled {
            print("🎵 动画背景: \(rows)x\(columns) = \(totalSquares) 个正方形 (性能优化)")
        }
        
        return ZStack {
            LinearGradient(
                gradient: Gradient(colors: viewModel.themeColors.backgroundColors),
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
            
            ForEach(0..<rows, id: \.self) { row in
                ForEach(0..<columns, id: \.self) { column in
                    let index = row * columns + column
                    
                    Rectangle()
                        .fill(viewModel.getDynamicColor(for: index))
                        .frame(width: squareSize, height: squareSize)
                        .position(
                            x: CGFloat(column) * squareSize + squareSize/2,
                            y: CGFloat(row) * squareSize + squareSize/2
                        )
                        // 移除所有缩放和透明度动画，只保留颜色变化
                }
            }
        }
        .onAppear {
            if FXChordViewModel.debugEnabled {
                print("🎵 动画背景已显示: \(totalSquares) 个正方形")
            }
        }
    }
    
    // 音符视图
    private func notesView(geometry: GeometryProxy) -> some View {
        if FXChordViewModel.debugEnabled {
            print("🎵 渲染音符视图，音符数量: \(viewModel.notes.count)")
        }
        
        // 更新容器大小用于正确计算粒子位置
        viewModel.setContainerSize(geometry.size)
        
        return ZStack {
            // 调试边框
            /*
            if FXChordViewModel.debugEnabled {
                Rectangle()
                    .stroke(viewModel.themeColors.highlightedThemeColor2, lineWidth: 1)
                    .background(viewModel.themeColors.highlightedThemeColor1.opacity(0.1))
            }
            */
            ForEach(Array(viewModel.notes.enumerated()), id: \.element.id) { index, note in
                noteView(note: note, index: index, geometry: geometry)
                    .onAppear {
                        if FXChordViewModel.debugEnabled {
                            print("🎵 音符 \(index): \(note.noteName) - 状态: \(note.state)")
                        }
                    }
            }
            
            // 如果没有音符，显示占位符
            /*
            if viewModel.notes.isEmpty {
                Text("没有音符")
                    .font(.headline)
                    .foregroundColor(viewModel.themeColors.inactiveNoteTextColor)
                    .onAppear {
                        if FXChordViewModel.debugEnabled {
                            print("🎵 显示没有音符占位符")
                        }
                    }
            }
             */
        }
        .frame(height: 100)
    }
    
    // 单个音符视图
    private func noteView(note: NoteData, index: Int, geometry: GeometryProxy) -> some View {
        let noteCount = viewModel.notes.count
        let xPosition = noteCount > 1 ? 
           // (geometry.size.width / CGFloat(noteCount - 1)) * CGFloat(index) :
        (geometry.size.width / CGFloat(noteCount)) * (CGFloat(index)  + 0.5):
            geometry.size.width / 2
        
        if FXChordViewModel.debugEnabled {
            print("🎵 音符 \(index) 位置: x=\(Int(xPosition)), 状态=\(note.state)")
        }
        
        // 计算bounce缩放效果
        let bounceScale: CGFloat = {
            if viewModel.highlightBouncingIndices.contains(index) {
                return 1.2  // highlight bounce时额外放大20%
            } else if viewModel.showBouncingIndices.contains(index) {
                return 1.15 // show bounce时额外放大15%
            }
            return 1.0
        }()
        
        // 获取自适应大小参数
        let adaptiveScale = viewModel.getAdaptiveNoteScale()
        let circleSize = viewModel.getAdaptiveCircleSize()
        let fontSize = viewModel.getAdaptiveFontSize()
        
        return Group {
            if note.state != .hide {
                ZStack {
                    Circle()
                        .fill(noteCircleFillColor(for: note.state))
                        .frame(width: circleSize, height: circleSize)
                        .overlay(
                            Circle()
                                .stroke(noteCircleStrokeColor(for: note.state), lineWidth: note.state == .highlight ? 4 : 2)
                        )
                        .scaleEffect({
                            let baseScale = note.state == .highlight ? 1.3 : 1.0
                            return baseScale * bounceScale * adaptiveScale
                        }())
                        .shadow(
                            color: note.state == .highlight ? viewModel.themeColors.highlightedThemeColor3 : Color.clear,
                            radius: note.state == .highlight ? 10 : 0
                        )
                    
                    Text(note.noteName)
                        .font(fontSize)
                        .scaleEffect({
                            let textBaseScale = note.state == .highlight ? 1.5 : 1.0
                            return textBaseScale * bounceScale * adaptiveScale
                        }())
                        .fontWeight(.bold)
                        .foregroundColor(noteTextColor(for: note.state))
                }
                .position(x: xPosition, y: 50)
                .animation(.spring(response: 0.6, dampingFraction: 0.8), value: note.state)
                .animation(.spring(response: 0.4, dampingFraction: 0.6), value: viewModel.highlightBouncingIndices)
                .animation(.spring(response: 0.4, dampingFraction: 0.6), value: viewModel.showBouncingIndices)
                .onTapGesture {
                    if FXChordViewModel.debugEnabled {
                        print("🎵 点击音符: \(note.noteName)")
                    }
                    let newState: NoteState = note.state == .show ? .highlight : .show
                    viewModel.setSingleNoteState(index: index, state: newState)
                }
                .onAppear {
                    if FXChordViewModel.debugEnabled {
                        let xPos = Int(xPosition)
                        print("🎵 音符圆圈显示: \(note.noteName) at (\(xPos), 50)")
                    }
                }
            } else {
                // 即使隐藏也显示调试信息
                if FXChordViewModel.debugEnabled {
                    Text("隐藏: \(note.noteName)")
                        .font(.caption)
                        .foregroundColor(viewModel.themeColors.highlightedNoteTextColor)
                        .position(x: xPosition, y: 50)
                        .onAppear {
                            print("🎵 音符隐藏: \(note.noteName)")
                        }
                }
            }
        }
    }
    
    // 音符圆圈填充颜色
    private func noteCircleFillColor(for state: NoteState) -> Color {
        switch state {
        case .hide:
            return Color.clear
        case .show:
            return viewModel.themeColors.inactiveThemeColor1
        case .highlight:
            return viewModel.themeColors.highlightedThemeColor1
        case .match:
            return viewModel.themeColors.highlightedThemeColor1
        }
    }
    
    // 音符圆圈轮廓颜色
    private func noteCircleStrokeColor(for state: NoteState) -> Color {
        switch state {
        case .hide:
            return Color.clear
        case .show:
            return viewModel.themeColors.inactiveThemeColor2
        case .highlight:
            return viewModel.themeColors.highlightedThemeColor2
        case .match:
            return viewModel.themeColors.highlightedThemeColor2
        }
    }
    
    // 音符文本颜色
    private func noteTextColor(for state: NoteState) -> Color {
        switch state {
        case .hide:
            return Color.clear
        case .show:
            return viewModel.themeColors.inactiveNoteTextColor
        case .highlight:
            return viewModel.themeColors.highlightedNoteTextColor
        case .match:
            return viewModel.themeColors.highlightedNoteTextColor
        }
    }
    
    // 粒子效果视图
    private func particlesView(geometry: GeometryProxy) -> some View {
        ForEach(viewModel.particles) { particle in
            Rectangle()
                .fill(particle.color.opacity(particle.opacity))
                .frame(width: particle.size, height: particle.size)
                .position(particle.position)
                .allowsHitTesting(false)
        }
    }
}

// MARK: - Color扩展 - 支持十六进制颜色

extension Color {
    init(hex: String) {
        let hex = hex.trimmingCharacters(in: CharacterSet.alphanumerics.inverted)
        var int: UInt64 = 0
        Scanner(string: hex).scanHexInt64(&int)
        let a, r, g, b: UInt64
        switch hex.count {
        case 3: // RGB (12-bit)
            (a, r, g, b) = (255, (int >> 8) * 17, (int >> 4 & 0xF) * 17, (int & 0xF) * 17)
        case 6: // RGB (24-bit)
            (a, r, g, b) = (255, int >> 16, int >> 8 & 0xFF, int & 0xFF)
        case 8: // ARGB (32-bit)
            (a, r, g, b) = (int >> 24, int >> 16 & 0xFF, int >> 8 & 0xFF, int & 0xFF)
        default:
            (a, r, g, b) = (1, 1, 1, 0)
        }

        self.init(
            .sRGB,
            red: Double(r) / 255,
            green: Double(g) / 255,
            blue:  Double(b) / 255,
            opacity: Double(a) / 255
        )
    }
}




// MARK: - 预览
struct FXChordView_Previews: PreviewProvider {
    static var previews: some View {
        FXChordView()
            .frame(width: 400, height: 300)
    }
} 

