import SwiftUI
import Lottie

/// 🎮 游戏信息显示视图 - 显示正确和弦计数等信息
struct GameInfoView: View {
    let correctChordCount: Int
    let totalChords: Int?

    // 🎆 动画状态
    @State private var previousCount: Int = 0
    @State private var showBurstAnimation: Bool = false
    
    var body: some View {
        ZStack {
            // 主要内容
            HStack(spacing: 12) {
                // 🎯 正确和弦计数
                correctChordCountView

                // 如果有总数，显示进度
                if let total = totalChords, total > 0 {
                    progressView(current: correctChordCount, total: total)
                }
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 10)
            .background(backgroundGradient)
            .cornerRadius(20)
            .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)

            // 🎆 星星爆炸动画层
            if showBurstAnimation {
                starBurstAnimation
            }
        }
        .onChange(of: correctChordCount) { newCount in
            // 检测分数增加
            if newCount > previousCount {
                triggerBurstAnimation()
            }
            previousCount = newCount
        }
        .onAppear {
            previousCount = correctChordCount
        }
    }
    
    /// 🎯 正确和弦计数显示
    private var correctChordCountView: some View {
        HStack(spacing: 8) {
            // 图标
            Image(systemName: "music.note")
                .font(.system(size: 16, weight: .semibold))
                .foregroundColor(.white)
            
            // 计数文本
            Text("\(correctChordCount)")
                .font(.system(size: 20, weight: .bold, design: .rounded))
                .foregroundColor(.white)
            
            // 标签
            Text("正确")
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(.white.opacity(0.9))
        }
    }
    
    /// 📊 进度显示
    private func progressView(current: Int, total: Int) -> some View {
        HStack(spacing: 6) {
            // 分隔线
            Rectangle()
                .fill(.white.opacity(0.3))
                .frame(width: 1, height: 20)
            
            // 进度文本
            Text("\(current)/\(total)")
                .font(.system(size: 14, weight: .semibold, design: .rounded))
                .foregroundColor(.white.opacity(0.9))
            
            // 进度条
            ProgressView(value: Double(current), total: Double(total))
                .progressViewStyle(LinearProgressViewStyle(tint: .white))
                .frame(width: 40)
                .scaleEffect(y: 0.8)
        }
    }
    
    /// 🎨 背景渐变
    private var backgroundGradient: some View {
        LinearGradient(
            gradient: Gradient(colors: [
                Color(red: 1.0, green: 0.6, blue: 0.0),    // 橙色
                Color(red: 1.0, green: 0.8, blue: 0.2)     // 黄色
            ]),
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
    }

    /// 🎆 星星爆炸动画 - 使用新的动画组件播放 stars.json
    private var starBurstAnimation: some View {
        LottieAnimationView.scoreAnimation {
            print("🎆 计分动画播放完成")
        }
        .allowsHitTesting(false)
        .offset(y: 180) // 🎯 进一步增加偏移量，确保完全避开得分文字
        .zIndex(10)
        .onAppear {
            print("🎆 星星爆炸动画开始显示")
        }
    }

    /// 🎆 触发爆炸动画
    private func triggerBurstAnimation() {
        print("🎆 触发星星爆炸动画！correctChordCount: \(correctChordCount)")
        showBurstAnimation = true

        // 动画持续时间后隐藏
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.5) {
            print("🎆 隐藏星星爆炸动画")
            showBurstAnimation = false
        }
    }
}

/// 🎮 简化版本 - 只显示计数
struct SimpleGameInfoView: View {
    let correctChordCount: Int
    let totalChordCount: Int? // 🎯 总和弦数，用于检测一轮完成
    let isTestMode: Bool // 🧪 测试模式标志
    let onRoundComplete: (() -> Void)? // 🎉 一轮完成时的回调
    let chordsPerProgression: Int? // 🎵 每个和弦进行的和弦数（用于12个调模式）

    // 🎆 动画状态
    @State private var previousCount: Int = 0
    @State private var showBurstAnimation: Bool = false

    // 🧪 初始化器
    init(correctChordCount: Int, totalChordCount: Int? = nil, isTestMode: Bool = false, onRoundComplete: (() -> Void)? = nil, chordsPerProgression: Int? = nil) {
        self.correctChordCount = correctChordCount
        self.totalChordCount = totalChordCount
        self.isTestMode = isTestMode
        self.onRoundComplete = onRoundComplete
        self.chordsPerProgression = chordsPerProgression
    }

    var body: some View {
        VStack(spacing: 8) {
            // 主要得分内容
            HStack(spacing: 8) {
                Image(systemName: "checkmark.circle.fill")
                    .font(.system(size: 14, weight: .semibold))
                    .foregroundColor(.white)

                Text("\(correctChordCount)")
                    .font(.system(size: 18, weight: .bold, design: .rounded))
                    .foregroundColor(.white)
                    .frame(minWidth: 20) // 确保文本有最小宽度，防止跳动
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
            .background(
                LinearGradient(
                    gradient: Gradient(colors: [
                        Color(red: 1.0, green: 0.6, blue: 0.0),
                        Color(red: 1.0, green: 0.8, blue: 0.2)
                    ]),
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
            )
            .cornerRadius(16)
            .shadow(color: .black.opacity(0.1), radius: 3, x: 0, y: 1)

            // 🎆 星星爆炸动画层 - 自然排列在得分下方，不重合
            if showBurstAnimation {
                LottieAnimationView.scoreAnimation {
                    print("🎆 SimpleGameInfoView 计分动画播放完成")
                    showBurstAnimation = false // 动画完成后隐藏
                }
                .allowsHitTesting(false)
                .frame(width: 60, height: 60) // 🎯 给动画一个合适的尺寸
                .onAppear {
                    print("🎆 SimpleGameInfoView 星星爆炸动画开始显示")
                }
            }
        }
        .onChange(of: correctChordCount) { newCount in
            print("🎯 SimpleGameInfoView onChange: newCount=\(newCount), previousCount=\(previousCount), totalChordCount=\(totalChordCount ?? -1), chordsPerProgression=\(chordsPerProgression ?? -1), isTestMode=\(isTestMode)")

            // 🧪 测试模式下不触发自动动画，防止干扰
            if !isTestMode {
                // 🎯 检测和弦进行完成的逻辑
                if newCount > previousCount {
                    let shouldTriggerAnimation: Bool

                    if let chordsPerProg = chordsPerProgression {
                        // 🎵 12个调模式：每完成一个和弦进行就触发动画
                        shouldTriggerAnimation = (newCount % chordsPerProg == 0)
                        print("🎵 12个调模式检测: newCount=\(newCount), chordsPerProgression=\(chordsPerProg), 模运算结果=\(newCount % chordsPerProg)")
                    } else if let total = totalChordCount {
                        // 🎯 普通模式：完成所有和弦时触发动画
                        shouldTriggerAnimation = (newCount == total)
                        print("🎯 普通模式检测: newCount=\(newCount), total=\(total)")
                    } else {
                        shouldTriggerAnimation = false
                        print("🎯 无法确定触发条件")
                    }

                    if shouldTriggerAnimation {
                        print("🎉 和弦进行完成！触发 Thumbs Up 动画 (\(newCount))")
                        triggerBurstAnimation()
                    } else {
                        print("🎯 不满足动画触发条件")
                    }
                }
            } else {
                print("🧪 测试模式下跳过动画触发")
            }
            previousCount = newCount
        }
        .onAppear {
            previousCount = correctChordCount
        }
    }

    /// 🎆 触发爆炸动画
    private func triggerBurstAnimation() {
        print("🎆 SimpleGameInfoView 触发星星爆炸动画！correctChordCount: \(correctChordCount)")
        showBurstAnimation = true

        // 动画持续时间后隐藏
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.5) {
            print("🎆 SimpleGameInfoView 隐藏星星爆炸动画")
            showBurstAnimation = false
        }
    }
}

// MARK: - 预览
#Preview("GameInfoView") {
    VStack(spacing: 20) {
        GameInfoView(correctChordCount: 5, totalChords: 8)
        GameInfoView(correctChordCount: 12, totalChords: nil)
        SimpleGameInfoView(correctChordCount: 7)
    }
    .padding()
    .background(Color.gray.opacity(0.1))
}
