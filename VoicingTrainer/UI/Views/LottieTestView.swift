import SwiftUI
import Lottie

/// 🧪 Lottie动画测试视图
struct LottieTestView: View {
    @State private var showAnimation = false
    
    var body: some View {
        VStack(spacing: 30) {
            Text("Lottie动画测试")
                .font(.title)
                .padding()
            
            // 测试按钮
            But<PERSON>("触发星星爆炸动画") {
                showAnimation = true
                
                // 3秒后隐藏
                DispatchQueue.main.asyncAfter(deadline: .now() + 3.0) {
                    showAnimation = false
                }
            }
            .padding()
            .background(Color.blue)
            .foregroundColor(.white)
            .cornerRadius(10)
            
            // 动画显示区域
            ZStack {
                Rectangle()
                    .fill(Color.gray.opacity(0.2))
                    .frame(width: 200, height: 200)
                    .cornerRadius(10)
                
                if showAnimation {
                    // 测试不同的动画文件
                    VStack {
                        // 测试 starburst.json
                        LottieView(animation: .named("starburst"))
                            .playing(loopMode: .playOnce)
                            .frame(width: 100, height: 100)
                            .onAppear {
                                print("🎆 starburst.json 动画开始显示")
                            }

                        Text("starburst.json")
                            .font(.caption)
                    }
                }
            }
            
            // 测试其他动画
            HStack(spacing: 20) {
                // 测试 burst1.json
                <PERSON>("测试 burst1.json") {
                    testAnimation("burst1")
                }
                .padding()
                .background(Color.green)
                .foregroundColor(.white)
                .cornerRadius(8)
                
                // 测试 starsburst.lottie
                Button("测试 starsburst.lottie") {
                    testAnimation("starsburst")
                }
                .padding()
                .background(Color.orange)
                .foregroundColor(.white)
                .cornerRadius(8)
            }
            
            Spacer()
        }
        .padding()
    }
    
    private func testAnimation(_ name: String) {
        print("🧪 测试动画: \(name)")
        
        // 创建测试动画视图
        let testView = LottieView(animation: .named(name))
            .playing(loopMode: .playOnce)
            .frame(width: 100, height: 100)
        
        print("🧪 动画创建完成: \(name)")
    }
}

#Preview {
    LottieTestView()
}
