import SwiftUI
import StoreKit
#if os(iOS)
import MessageUI
#endif

struct AboutPopoverView: View {
    @Binding var isPresented: Bool
    @State private var showingMailComposer = false
    @State private var showingAlert = false
    @State private var alertMessage = ""
    
    var body: some View {
        VStack(spacing: 0) {
            // 标题栏
            HStack {
                Text("VoicingWorkout")
                    .font(.title2)
                    .fontWeight(.bold)
                Spacer()
                Button("×") {
                    isPresented = false
                }
                .font(.title2)
                .foregroundColor(.secondary)
            }
            .padding()
            .background(Color.gray.opacity(0.1))
            
            // 版本信息
            VStack(spacing: 4) {
                Image(systemName: "music.note")
                    .font(.system(size: 40))
                    .foregroundColor(.blue)
                
                Text("1.0.1")
                    .font(.title3)
                    .foregroundColor(.secondary)
            }
            .padding()
            
            // 功能按钮
            VStack(spacing: 12) {
                // 发邮件给我们
                Button(action: {
                    sendEmail()
                }) {
                    HStack {
                        Image(systemName: "envelope")
                            .font(.title3)
                        Text("发邮件给我们")
                            .font(.system(size: 16, weight: .medium))
                        Spacer()
                        Image(systemName: "chevron.right")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    .foregroundColor(.primary)
                    .padding()
                    .background(
                        RoundedRectangle(cornerRadius: 8)
                            .stroke(Color.gray.opacity(0.3), lineWidth: 1)
                    )
                }
                .contentShape(Rectangle()) // 确保整个按钮区域都可以点击
                
                // 请评价我们
                Button(action: {
                    requestAppStoreReview()
                }) {
                    HStack {
                        Image(systemName: "star")
                            .font(.title3)
                        Text("请评价我们")
                            .font(.system(size: 16, weight: .medium))
                        Spacer()
                        Image(systemName: "chevron.right")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    .foregroundColor(.primary)
                    .padding()
                    .background(
                        RoundedRectangle(cornerRadius: 8)
                            .stroke(Color.gray.opacity(0.3), lineWidth: 1)
                    )
                }
                .contentShape(Rectangle()) // 确保整个按钮区域都可以点击
            }
            .padding()
            
            Spacer()
        }
        .frame(width: 300, height: 400)
        .background(Color.clear)
        #if os(iOS)
        .sheet(isPresented: $showingMailComposer) {
            MailComposeView(
                isPresented: $showingMailComposer,
                alertMessage: $alertMessage,
                showingAlert: $showingAlert
            )
        }
        #endif
        .alert("提示", isPresented: $showingAlert) {
            Button("确定") { }
        } message: {
            Text(alertMessage)
        }
    }
    
    // MARK: - 邮件功能
    private func sendEmail() {
        #if os(iOS)
        if MFMailComposeViewController.canSendMail() {
            isPresented = false // 先关闭关于面板
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                showingMailComposer = true
            }
        } else {
            // 如果设备无法发送邮件，复制邮箱地址到剪贴板
            UIPasteboard.general.string = "<EMAIL>"
            alertMessage = "邮箱地址已复制到剪贴板：<EMAIL>"
            showingAlert = true
        }
        #elseif os(macOS)
        // macOS使用mailto URL scheme
        let emailURL = URL(string: "mailto:<EMAIL>?subject=VoicingWorkout 反馈")!
        if NSWorkspace.shared.open(emailURL) {
            isPresented = false
        } else {
            // 复制邮箱地址到剪贴板
            NSPasteboard.general.clearContents()
            NSPasteboard.general.setString("<EMAIL>", forType: .string)
            alertMessage = "邮箱地址已复制到剪贴板：<EMAIL>"
            showingAlert = true
        }
        #endif
    }
    
    // MARK: - App Store评价功能
    private func requestAppStoreReview() {
        #if os(iOS)
        if let scene = UIApplication.shared.connectedScenes.first(where: { $0.activationState == .foregroundActive }) as? UIWindowScene {
            SKStoreReviewController.requestReview(in: scene)
        }
        #elseif os(macOS)
        SKStoreReviewController.requestReview()
        #endif
        isPresented = false
    }
}

// MARK: - iOS邮件编写器
#if os(iOS)
struct MailComposeView: UIViewControllerRepresentable {
    @Binding var isPresented: Bool
    @Binding var alertMessage: String
    @Binding var showingAlert: Bool
    
    func makeUIViewController(context: Context) -> MFMailComposeViewController {
        let mailComposer = MFMailComposeViewController()
        mailComposer.mailComposeDelegate = context.coordinator
        mailComposer.setToRecipients(["<EMAIL>"])
        mailComposer.setSubject("VoicingWorkout 反馈")
        mailComposer.setMessageBody("", isHTML: false)
        return mailComposer
    }
    
    func updateUIViewController(_ uiViewController: MFMailComposeViewController, context: Context) {}
    
    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }
    
    class Coordinator: NSObject, MFMailComposeViewControllerDelegate {
        var parent: MailComposeView
        
        init(_ parent: MailComposeView) {
            self.parent = parent
        }
        
        func mailComposeController(_ controller: MFMailComposeViewController, didFinishWith result: MFMailComposeResult, error: Error?) {
            parent.isPresented = false
            
            switch result {
            case .sent:
                parent.alertMessage = "邮件发送成功！"
                parent.showingAlert = true
            case .failed:
                parent.alertMessage = "邮件发送失败，请稍后重试。"
                parent.showingAlert = true
            default:
                break
            }
        }
    }
}
#endif

#Preview {
    AboutPopoverView(isPresented: .constant(true))
} 