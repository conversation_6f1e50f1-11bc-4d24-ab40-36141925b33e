//
//  SettingsView.swift
//  FullUI
//
//  Created by <PERSON> Li on 2025/5/26.
//

import SwiftUI
// MARK: - 设置面板
struct SettingsView: View {
    @Binding var isPresented: Bool
    @ObservedObject private var configManager = GameConfigManager.shared
    @State private var selectedTheme = 0
    @State private var metronomeEnabled = true
    //let themes = ["Auto", "Light", "Dark"]
    
    var body: some View {
        // 半透明背景覆盖整个屏幕
        Color.black.opacity(0.3)
            .ignoresSafeArea()
            .onTapGesture {
                // 点击背景关闭设置面板
                isPresented = false
            }
            .overlay(
                // 设置面板内容
                VStack(alignment: .leading, spacing: 20) {
                    // 标题栏
                    HStack {
                        Text("Settings")
                            .font(.title2.bold())
                        Spacer()
                        Button(action: { isPresented = false }) {
                            Image(systemName: "xmark.circle.fill")
                                .font(.title2)
                                .foregroundColor(.gray)
                        }
                    }
                    .padding(.bottom, 10)
                    
                    // 主题选择
                    /*
                    VStack(alignment: .leading, spacing: 8) {
                        Text("THEME")
                            .font(.caption)
                            .foregroundColor(.gray)
                        Picker("Theme", selection: $selectedTheme) {
                            ForEach(0..<themes.count, id: \.self) {
                                Text(themes[$0])
                            }
                        }
                        .pickerStyle(.segmented)
                    }
                    */
                    
                    
                    // 节拍器开关
                    Toggle(isOn: $metronomeEnabled) {
                        Text("Enable Metronome")
                            .font(.body)
                    }
                    .toggleStyle(SwitchToggleStyle(tint: .blue))
                    
                    
                    
                    // SoundFont选择
                    soundFontSection
                    
                    // 键盘布局选择
                    keyboardLayoutSection
                    
                    
                    // 音频设置
                    NavigationLink {
                        Text("Audio Settings View")
                            .navigationTitle("Audio Settings")
                    } label: {
                        HStack {
                            Text("Audio Settings")
                            Spacer()
                            Image(systemName: "chevron.right")
                                .foregroundColor(.gray)
                        }
                    }
                    
                    Spacer()
                    
                    // 版本信息
                    Text("Version 2.1.0")
                        .font(.caption)
                        .foregroundColor(.gray)
                        .frame(maxWidth: .infinity, alignment: .center)
                }
                .padding(25)
                .frame(width: 300)
                .background(Color("AppBackground"))
                .cornerRadius(15)
                .shadow(color: .black.opacity(0.2), radius: 10, x: -5, y: 0)
                .onTapGesture {
                    // 阻止设置面板本身的点击传递到背景
                }
            )
    }
    
    // 键盘布局选择区域
    private var keyboardLayoutSection: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("KEYBOARD LAYOUT")
                .font(.caption)
                .foregroundColor(.gray)
            Picker("Keyboard Layout", selection: Binding(
                get: { configManager.config.keyboardSettings.selectedLayout },
                set: { newLayout in
                    configManager.updateKeyboardLayout(newLayout)
                }
            )) {
                ForEach(KeyboardLayout.allCases) { layout in
                    Text(layout.name).tag(layout)
                }
            }
            .pickerStyle(.menu)
        }
    }
    
    // SoundFont选择区域
    private var soundFontSection: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("SOUNDFONT")
                .font(.caption)
                .foregroundColor(.gray)
            Picker("SoundFont", selection: Binding(
                get: { 
                    // 根据当前的soundFontName找到对应的SoundFontOption
                    let currentFilename = configManager.config.audioSettings.soundFontName
                    return configManager.config.soundFontSettings.soundFonts.first { $0.filename == currentFilename + ".sf2" } ?? configManager.config.soundFontSettings.soundFonts.first!
                },
                set: { (newSoundFont: SoundFontOption) in
                    // 提取文件名（去掉.sf2后缀）
                    let filename = String(newSoundFont.filename.dropLast(4))
                    configManager.updateSoundFont(filename)
                }
            )) {
                ForEach(configManager.config.soundFontSettings.soundFonts) { soundFont in
                    Text(soundFont.name).tag(soundFont)
                }
            }
            .pickerStyle(.segmented)
        }
    }
}

// MARK: - Popover版本的设置视图
struct SettingsPopoverView: View {
    @Binding var isPresented: Bool
    @ObservedObject private var configManager = GameConfigManager.shared
    @ObservedObject var midiManager: MIDIManager
    @State private var selectedTheme = 0
    @State private var metronomeEnabled = true
    // 移除本地状态，直接使用配置中的值
    
    var body: some View {
        
        let _ = print("SettingsPopoverView CP1.1")
        
        ScrollView {
            VStack(alignment: .leading, spacing: 16) {
            // 标题栏
            HStack {
                Text("Settings")
                    .font(.title3.bold())
                Spacer()
                Button(action: { 
                    configManager.saveConfigIfNeeded()
                    isPresented = false 
                }) {
                    Image(systemName: "xmark.circle.fill")
                        .font(.title3)
                        .foregroundColor(.gray)
                }
            }
      
                let _ = print("SettingsPopoverView 1.2")
            
            // SoundFont选择
            soundFontPopoverSection
            
                let _ = print("SettingsPopoverView CP1.3")
                
            // 键盘布局选择
            keyboardLayoutPopoverSection
            
            let _ = print("SettingsPopoverView CP2")
                
            // MIDI输入设备选择
            midiInputDeviceSection
            
            // MIDI输出设备选择
            midiOutputDeviceSection
            
            // 🎵 全局BPM设置
            VStack(alignment: .leading, spacing: 6) {
                Text("GLOBAL BPM")
                    .font(.caption)
                    .foregroundColor(.gray)

                HStack {
                    Button("-") {
                        let currentBpm = configManager.config.gameSettings.globalBpm
                        let newBpm = max(configManager.config.gameSettings.minBpm, currentBpm - 10)
                        configManager.updateGlobalBpm(newBpm)
                    }
                    .disabled(configManager.config.gameSettings.globalBpm <= configManager.config.gameSettings.minBpm)

                    Spacer()

                    VStack(spacing: 2) {
                        Text("\(configManager.config.gameSettings.globalBpm)")
                            .font(.title2.bold())
                        Text("BPM")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }

                    Spacer()

                    Button("+") {
                        let currentBpm = configManager.config.gameSettings.globalBpm
                        let newBpm = min(configManager.config.gameSettings.maxBpm, currentBpm + 10)
                        configManager.updateGlobalBpm(newBpm)
                    }
                    .disabled(configManager.config.gameSettings.globalBpm >= configManager.config.gameSettings.maxBpm)
                }

                // 显示各模式的实际时间
                VStack(alignment: .leading, spacing: 2) {
                    Text("Note: \(String(format: "%.1f", configManager.getNoteWaitTime()))s")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    Text("Chord: \(String(format: "%.1f", configManager.getChordWaitTime()))s")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    Text("Progression: \(String(format: "%.1f", configManager.getProgressionWaitTime()))s")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }

            // 回放音符的音量
            VStack(alignment: .leading, spacing: 6) {
                Text("PLAYBACK VOLUME")
                    .font(.caption)
                    .foregroundColor(.gray)

                Slider(value: Binding(
                    get: { configManager.config.audioSettings.playbackVolume },
                    set: { newVolume in
                        configManager.updatePlaybackVolume(newVolume)
                    }
                ), in: 0...127) {
                    Text("Playback Volume")
                }

                Text("Volume: \(Int(configManager.config.audioSettings.playbackVolume))")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
                
            //清除练习成就
            Button(action: {
                AchievementManager.shared.resetAllAchievements()
            }){
                Text("Clear All Achievement")
                    .foregroundColor(.red)
            }
            
            // 音频设置
            NavigationLink {
                Text("Audio Settings View")
                    .navigationTitle("Audio Settings")
            } label: {
                HStack {
                    Text("Audio Settings")
                    Spacer()
                    Image(systemName: "chevron.right")
                        .foregroundColor(.gray)
                }
            }
            

            
            // 版本信息
            Text("Version 2.1.0")
                .font(.caption)
                .foregroundColor(.gray)
                .frame(maxWidth: .infinity, alignment: .center)
            }
        }
        .padding(20)
        .frame(width: 320, height: 600)
        .background(.regularMaterial)
    }
    
    // 键盘布局选择区域 - Popover版本
    private var keyboardLayoutPopoverSection: some View {
        VStack(alignment: .leading, spacing: 6) {
            Text("KEYBOARD LAYOUT")
                .font(.caption)
                .foregroundColor(.gray)
            Picker("Keyboard Layout", selection: Binding(
                get: { configManager.config.keyboardSettings.selectedLayout },
                set: { newLayout in
                    configManager.updateKeyboardLayout(newLayout)
                }
            )) {
                ForEach(KeyboardLayout.allCases) { layout in
                    Text(layout.name).tag(layout)
                }
            }
            .pickerStyle(.menu)
        }
    }
    
    // SoundFont选择区域 - Popover版本
    private var soundFontPopoverSection: some View {
        VStack(alignment: .leading, spacing: 6) {
            Text("SOUNDFONT")
                .font(.caption)
                .foregroundColor(.gray)
            
            if configManager.config.soundFontSettings.soundFonts.isEmpty {
                Text("No sound fonts configured")
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .italic()
            } else {
                Picker("SoundFont", selection: Binding(
                    get: { 
                        // 根据当前的soundFontName找到对应的SoundFontOption
                        let currentFilename = configManager.config.audioSettings.soundFontName
                        let foundSoundFont = configManager.config.soundFontSettings.soundFonts.first { $0.filename == currentFilename + ".sf2" }
                        return foundSoundFont ?? configManager.config.soundFontSettings.soundFonts.first ?? SoundFontOption(name: "Default", filename: "piano.sf2")
                    },
                    set: { (newSoundFont: SoundFontOption) in
                        // 提取文件名（去掉.sf2后缀）
                        let filename = String(newSoundFont.filename.dropLast(4))
                        configManager.updateSoundFont(filename)
                    }
                )) {
                    ForEach(configManager.config.soundFontSettings.soundFonts) { soundFont in
                        Text(soundFont.name).tag(soundFont)
                    }
                }
                .pickerStyle(.segmented)
            }
        }
    }
    
    // MIDI输入设备选择区域
    private var midiInputDeviceSection: some View {
        VStack(alignment: .leading, spacing: 6) {
            HStack {
                Text("MIDI INPUT DEVICES")
                    .font(.caption)
                    .foregroundColor(.gray)

                Spacer()

                // 🎛️ 自动重连按钮
                Button(action: {
                    midiManager.triggerDeviceReconnection()
                }) {
                    Image(systemName: "link")
                        .font(.caption)
                        .foregroundColor(.green)
                }
                .help("自动重连设置的MIDI设备")

                Button(action: {
                    midiManager.scanAvailableDevices(force: true)
                }) {
                    Image(systemName: "arrow.clockwise")
                        .font(.caption)
                        .foregroundColor(.blue)
                }
                .help("刷新MIDI设备列表")
            }
            
            if midiManager.availableInputDevices.isEmpty {
                Text("No MIDI input devices found")
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .italic()
            } else {
                ScrollView {
                    LazyVStack(alignment: .leading, spacing: 4) {
                        ForEach(midiManager.availableInputDevices, id: \.self) { device in
                            HStack {
                                Button(action: {
                                    toggleInputDevice(device)
                                }) {
                                    HStack(spacing: 8) {
                                        Image(systemName: midiManager.selectedInputDevices.contains(device) ? "checkmark.square.fill" : "square")
                                            .foregroundColor(midiManager.selectedInputDevices.contains(device) ? .blue : .gray)
                                        
                                        Text(device)
                                            .font(.caption)
                                            .foregroundColor(.primary)
                                            .lineLimit(1)
                                        
                                        Spacer()
                                    }
                                }
                                .buttonStyle(PlainButtonStyle())
                            }
                        }
                    }
                    .padding(.vertical, 2)
                }
                .frame(maxHeight: 80)
            }
        }
        .onAppear {
            // 🛠️ 修复：只在真正需要时扫描设备，避免重复扫描
            // 启动时已经扫描过，这里不需要再扫描
        }
    }
    
    // MIDI输出设备选择区域（单选）
    private var midiOutputDeviceSection: some View {
        VStack(alignment: .leading, spacing: 6) {
            HStack {
                Text("MIDI OUTPUT DEVICE")
                    .font(.caption)
                    .foregroundColor(.gray)

                Spacer()

                // 🎛️ 自动重连按钮
                Button(action: {
                    midiManager.triggerDeviceReconnection()
                }) {
                    Image(systemName: "link")
                        .font(.caption)
                        .foregroundColor(.green)
                }
                .help("自动重连设置的MIDI设备")

                Button(action: {
                    midiManager.scanAvailableDevices(force: true)
                }) {
                    Image(systemName: "arrow.clockwise")
                        .font(.caption)
                        .foregroundColor(.blue)
                }
                .help("刷新MIDI设备列表")
            }
            
            if midiManager.availableOutputDevices.isEmpty {
                Text("No MIDI output devices found")
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .italic()
            } else {
                ScrollView {
                    LazyVStack(alignment: .leading, spacing: 4) {
                        ForEach(midiManager.availableOutputDevices, id: \.self) { device in
                            HStack {
                                Button(action: {
                                    selectOutputDevice(device)
                                }) {
                                    HStack(spacing: 8) {
                                        Image(systemName: midiManager.selectedOutputDevice == device ? "largecircle.fill.circle" : "circle")
                                            .foregroundColor(midiManager.selectedOutputDevice == device ? .blue : .gray)
                                        
                                        Text(device)
                                            .font(.caption)
                                            .foregroundColor(.primary)
                                            .lineLimit(1)
                                        
                                        Spacer()
                                    }
                                }
                                .buttonStyle(PlainButtonStyle())
                            }
                        }
                    }
                    .padding(.vertical, 2)
                }
                .frame(maxHeight: 80)
            }
        }
        .onAppear {
            // 🛠️ 修复：只在真正需要时扫描设备，避免重复扫描
            // 启动时已经扫描过，这里不需要再扫描
        }
    }
    
    // 切换输入设备选择状态
    private func toggleInputDevice(_ device: String) {
        var newSelection = midiManager.selectedInputDevices
        if newSelection.contains(device) {
            newSelection.remove(device)
        } else {
            newSelection.insert(device)
        }
        midiManager.updateSelectedInputDevices(newSelection)
    }
    
    // 选择输出设备（单选）
    private func selectOutputDevice(_ device: String) {
        midiManager.updateSelectedOutputDevice(device)
    }
}

