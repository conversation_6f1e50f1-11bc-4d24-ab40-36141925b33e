import SwiftUI

/// 功耗测试结果查看器 - 显示详细的测试结果和分析
struct PowerTestResultsView: View {
    @StateObject private var powerTestManager = PowerTestManager.shared
    @State private var selectedResult: PowerTestManager.PowerTestResult?
    @State private var showingDetailView = false
    
    var body: some View {
        NavigationView {
            VStack(spacing: 16) {
                // 测试状态卡片
                testStatusCard
                
                // 测试结果列表
                if !powerTestManager.testResults.isEmpty {
                    testResultsList
                } else {
                    emptyStateView
                }
                
                Spacer()
                
                // 控制按钮
                controlButtons
            }
            .padding()
            .navigationTitle("功耗测试结果")
            #if os(iOS)
            .navigationBarTitleDisplayMode(.large)
            #endif
        }
        .sheet(isPresented: $showingDetailView) {
            if let result = selectedResult {
                PowerTestDetailView(result: result)
            }
        }
    }
    
    // MARK: - Test Status Card
    private var testStatusCard: some View {
        VStack(spacing: 12) {
            HStack {
                Image(systemName: powerTestManager.isRunning ? "play.circle.fill" : "stop.circle.fill")
                    .foregroundColor(powerTestManager.isRunning ? .green : .gray)
                    .font(.title2)
                
                Text(powerTestManager.testStatus)
                    .font(.headline)
                    .foregroundColor(.primary)
                
                Spacer()
                
                if powerTestManager.isRunning {
                    Text("\(Int(powerTestManager.testProgress * 100))%")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            
            if powerTestManager.isRunning {
                ProgressView(value: min(1.0, max(0.0, powerTestManager.testProgress)), total: 1.0)
                    .progressViewStyle(LinearProgressViewStyle())
                    .scaleEffect(y: 0.5)
            }
            
            // 当前测试数据
            if let currentResult = powerTestManager.currentResult {
                currentDataView(currentResult)
            }
        }
        .padding()
        #if os(iOS)
        .background(Color(.systemBackground))
        #else
        .background(Color.white)
        #endif
        .cornerRadius(12)
        .shadow(radius: 2)
    }
    
    // MARK: - Current Data View
    private func currentDataView(_ result: PowerTestManager.PowerTestResult) -> some View {
        VStack(spacing: 8) {
            Text("当前数据")
                .font(.subheadline)
                .fontWeight(.semibold)
                .foregroundColor(.secondary)
            
            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 8) {
                dataItem("CPU", "\(String(format: "%.1f", result.cpuUsage))%", cpuColor(result.cpuUsage))
                dataItem("内存", "\(String(format: "%.1f", result.memoryUsage))%", memoryColor(result.memoryUsage))
                dataItem("效率", "\(String(format: "%.1f", result.powerEfficiencyScore))%", efficiencyColor(result.powerEfficiencyScore))
            }
        }
    }
    
    private func dataItem(_ title: String, _ value: String, _ color: Color) -> some View {
        VStack(spacing: 4) {
            Text(title)
                .font(.caption2)
                .foregroundColor(.secondary)
            Text(value)
                .font(.caption)
                .fontWeight(.medium)
                .foregroundColor(color)
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 4)
        .background(color.opacity(0.1))
        .cornerRadius(6)
    }
    
    // MARK: - Test Results List
    private var testResultsList: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Text("测试历史 (\(powerTestManager.testResults.count))")
                    .font(.headline)
                    .foregroundColor(.primary)
                
                Spacer()
                
                Button("清空") {
                    powerTestManager.clearResults()
                }
                .font(.caption)
                .foregroundColor(.red)
            }
            
            ScrollView {
                LazyVStack(spacing: 8) {
                    ForEach(Array(powerTestManager.testResults.enumerated().reversed()), id: \.element.id) { index, result in
                        testResultRow(result, index: powerTestManager.testResults.count - index)
                    }
                }
            }
            .frame(maxHeight: 300)
        }
    }
    
    private func testResultRow(_ result: PowerTestManager.PowerTestResult, index: Int) -> some View {
        HStack(spacing: 12) {
            // 序号
            Text("\(index)")
                .font(.caption)
                .fontWeight(.medium)
                .foregroundColor(.secondary)
                .frame(width: 30)
            
            // 时间
            Text(timeFormatter.string(from: result.timestamp))
                .font(.caption)
                .foregroundColor(.secondary)
                .frame(width: 60)
            
            // CPU
            Text("\(String(format: "%.1f", result.cpuUsage))%")
                .font(.caption)
                .foregroundColor(cpuColor(result.cpuUsage))
                .frame(width: 50)
            
            // 内存
            Text("\(String(format: "%.1f", result.memoryUsage))%")
                .font(.caption)
                .foregroundColor(memoryColor(result.memoryUsage))
                .frame(width: 50)
            
            // 效率
            Text("\(String(format: "%.1f", result.powerEfficiencyScore))%")
                .font(.caption)
                .foregroundColor(efficiencyColor(result.powerEfficiencyScore))
                .frame(width: 50)
            
            Spacer()
            
            // 详情按钮
            Button("详情") {
                selectedResult = result
                showingDetailView = true
            }
            .font(.caption2)
            .padding(.horizontal, 8)
            .padding(.vertical, 4)
            .background(Color.blue.opacity(0.1))
            .foregroundColor(.blue)
            .cornerRadius(4)
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 8)
        #if os(iOS)
        .background(Color(.systemGray6))
        #else
        .background(Color.gray.opacity(0.1))
        #endif
        .cornerRadius(8)
    }
    
    // MARK: - Empty State
    private var emptyStateView: some View {
        VStack(spacing: 16) {
            Image(systemName: "chart.line.uptrend.xyaxis")
                .font(.system(size: 48))
                .foregroundColor(.gray)
            
            Text("暂无测试数据")
                .font(.title3)
                .foregroundColor(.secondary)
            
            Text("点击开始测试按钮进行功耗测试")
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
    
    // MARK: - Control Buttons
    private var controlButtons: some View {
        HStack(spacing: 16) {
            Button(action: {
                if powerTestManager.isRunning {
                    powerTestManager.stopTest()
                } else {
                    powerTestManager.startTest()
                }
            }) {
                HStack {
                    Image(systemName: powerTestManager.isRunning ? "stop.fill" : "play.fill")
                    Text(powerTestManager.isRunning ? "停止测试" : "开始测试")
                }
                .frame(maxWidth: .infinity)
                .padding()
                .background(powerTestManager.isRunning ? Color.red : Color.blue)
                .foregroundColor(.white)
                .cornerRadius(10)
            }
            
            Button("清空结果") {
                powerTestManager.clearResults()
            }
            .frame(maxWidth: .infinity)
            .padding()
            .background(Color.gray.opacity(0.2))
            .foregroundColor(.primary)
            .cornerRadius(10)
            .disabled(powerTestManager.testResults.isEmpty)
        }
    }
    
    // MARK: - Helper Methods
    private func cpuColor(_ value: Double) -> Color {
        switch value {
        case 0..<30: return .green
        case 30..<60: return .orange
        default: return .red
        }
    }
    
    private func memoryColor(_ value: Double) -> Color {
        switch value {
        case 0..<50: return .green
        case 50..<80: return .orange
        default: return .red
        }
    }
    
    private func efficiencyColor(_ value: Double) -> Color {
        switch value {
        case 80...100: return .green
        case 60..<80: return .orange
        default: return .red
        }
    }
    
    private var timeFormatter: DateFormatter {
        let formatter = DateFormatter()
        formatter.timeStyle = .medium
        return formatter
    }
}

// MARK: - Power Test Detail View

struct PowerTestDetailView: View {
    let result: PowerTestManager.PowerTestResult
    @Environment(\.presentationMode) var presentationMode
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: 20) {
                    // 基本信息
                    basicInfoSection
                    
                    // 性能指标
                    performanceMetricsSection
                    
                    // 系统状态
                    systemStatusSection
                    
                    // 建议
                    recommendationsSection
                }
                .padding()
            }
            .navigationTitle("测试详情")
                        #if os(iOS)
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarItems(trailing: Button("关闭") {
                presentationMode.wrappedValue.dismiss()
            })
            #else
            .toolbar {
                ToolbarItem(placement: .cancellationAction) {
                    Button("关闭") {
                        presentationMode.wrappedValue.dismiss()
                    }
                }
            }
            #endif
        }
    }
    
    private var basicInfoSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("基本信息")
                .font(.headline)
                .foregroundColor(.primary)
            
            infoRow("测试时间", DateFormatter.fullFormatter.string(from: result.timestamp))
            infoRow("运行模式", result.isBackgroundMode ? "后台" : "前台")
            infoRow("热状态", result.thermalState.description)
        }
    }
    
    private var performanceMetricsSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("性能指标")
                .font(.headline)
                .foregroundColor(.primary)
            
            metricRow("CPU使用率", "\(String(format: "%.1f", result.cpuUsage))%", cpuColor(result.cpuUsage))
            metricRow("内存使用率", "\(String(format: "%.1f", result.memoryUsage))%", memoryColor(result.memoryUsage))
            metricRow("功耗效率", "\(String(format: "%.1f", result.powerEfficiencyScore))%", efficiencyColor(result.powerEfficiencyScore))
        }
    }
    
    private var systemStatusSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("系统状态")
                .font(.headline)
                .foregroundColor(.primary)
            
            infoRow("电池电量", "\(String(format: "%.0f", result.batteryLevel * 100))%")
            infoRow("热状态", result.thermalState.description)
            infoRow("运行环境", result.isBackgroundMode ? "后台运行" : "前台运行")
        }
    }
    
    private var recommendationsSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("优化建议")
                .font(.headline)
                .foregroundColor(.primary)
            
            let recommendations = getRecommendations()
            if recommendations.isEmpty {
                Text("当前性能表现良好，无需特别优化")
                    .font(.subheadline)
                    .foregroundColor(.green)
            } else {
                ForEach(recommendations, id: \.self) { recommendation in
                    HStack(alignment: .top, spacing: 8) {
                        Image(systemName: "exclamationmark.triangle.fill")
                            .foregroundColor(.orange)
                            .font(.caption)
                        Text(recommendation)
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                    }
                }
            }
        }
    }
    
    private func infoRow(_ title: String, _ value: String) -> some View {
        HStack {
            Text(title)
                .font(.subheadline)
                .foregroundColor(.secondary)
            Spacer()
            Text(value)
                .font(.subheadline)
                .fontWeight(.medium)
                .foregroundColor(.primary)
        }
        .padding(.vertical, 4)
    }
    
    private func metricRow(_ title: String, _ value: String, _ color: Color) -> some View {
        HStack {
            Text(title)
                .font(.subheadline)
                .foregroundColor(.secondary)
            Spacer()
            Text(value)
                .font(.subheadline)
                .fontWeight(.medium)
                .foregroundColor(color)
        }
        .padding(.vertical, 4)
    }
    
    private func getRecommendations() -> [String] {
        var recommendations: [String] = []
        
        if result.cpuUsage > 60 {
            recommendations.append("CPU使用率较高，建议优化算法或减少后台任务")
        }
        
        if result.memoryUsage > 70 {
            recommendations.append("内存使用率较高，建议优化内存管理")
        }
        
        if result.powerEfficiencyScore < 75 {
            recommendations.append("功耗效率较低，建议启用省电模式")
        }
        
        if result.thermalState == .serious || result.thermalState == .critical {
            recommendations.append("设备温度过高，建议减少CPU密集型操作")
        }
        
        return recommendations
    }
    
    // Helper methods (same as parent view)
    private func cpuColor(_ value: Double) -> Color {
        switch value {
        case 0..<30: return .green
        case 30..<60: return .orange
        default: return .red
        }
    }
    
    private func memoryColor(_ value: Double) -> Color {
        switch value {
        case 0..<50: return .green
        case 50..<80: return .orange
        default: return .red
        }
    }
    
    private func efficiencyColor(_ value: Double) -> Color {
        switch value {
        case 80...100: return .green
        case 60..<80: return .orange
        default: return .red
        }
    }
}

// MARK: - Extensions

extension ProcessInfo.ThermalState {
    var description: String {
        switch self {
        case .nominal: return "正常"
        case .fair: return "一般"
        case .serious: return "严重"
        case .critical: return "危险"
        @unknown default: return "未知"
        }
    }
}

extension DateFormatter {
    static let fullFormatter: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .medium
        return formatter
    }()
} 