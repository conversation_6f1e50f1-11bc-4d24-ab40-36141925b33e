import SwiftUI

/// 简化版绿色爆发测试
struct SimpleGreenBurst: View {
    @State private var scale: CGFloat = 0.1
    @State private var opacity: Double = 0
    @State private var rotation: Double = 0
    
    let lightBandCount = 16  // 增加光带数量
    
    var body: some View {
        ZStack {
            // 🔧 移除红色调试边框
            
            // 🌟 多层光带效果 - 创造立体感
            
            // 第一层：主要光带（最亮）
            ForEach(0..<lightBandCount, id: \.self) { index in
                Rectangle()
                    .fill(
                        LinearGradient(
                            gradient: Gradient(stops: [
                                .init(color: Color.green.opacity(0.95), location: 0.0),
                                .init(color: Color.green.opacity(0.8), location: 0.3),
                                .init(color: Color.green.opacity(0.4), location: 0.7),
                                .init(color: Color.clear, location: 1.0)
                            ]),
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                    )
                    .frame(width: 180, height: 12)
                    .offset(x: 90)
                    .rotationEffect(.degrees(Double(index) * 22.5))  // 16条光带，每22.5度
                    .blur(radius: 1)  // 轻微模糊增加光感
            }
            
            // 第二层：次要光带（偏移角度，增加密度）
            ForEach(0..<lightBandCount, id: \.self) { index in
                Rectangle()
                    .fill(
                        LinearGradient(
                            gradient: Gradient(stops: [
                                .init(color: Color.green.opacity(0.7), location: 0.0),
                                .init(color: Color.green.opacity(0.5), location: 0.4),
                                .init(color: Color.green.opacity(0.2), location: 0.8),
                                .init(color: Color.clear, location: 1.0)
                            ]),
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                    )
                    .frame(width: 150, height: 8)
                    .offset(x: 75)
                    .rotationEffect(.degrees(Double(index) * 22.5 + 11.25))  // 偏移11.25度
                    .blur(radius: 2)
            }
            
            // 第三层：细光带（更多密度）
            ForEach(0..<(lightBandCount * 2), id: \.self) { index in
                Rectangle()
                    .fill(
                        LinearGradient(
                            gradient: Gradient(stops: [
                                .init(color: Color.green.opacity(0.6), location: 0.0),
                                .init(color: Color.green.opacity(0.3), location: 0.5),
                                .init(color: Color.clear, location: 1.0)
                            ]),
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                    )
                    .frame(width: 120, height: 4)
                    .offset(x: 60)
                    .rotationEffect(.degrees(Double(index) * 11.25))  // 32条细光带
                    .blur(radius: 1.5)
            }
            
            // 🌟 中心爆炸核心
            ZStack {
                // 内核光晕
                Circle()
                    .fill(
                        RadialGradient(
                            gradient: Gradient(stops: [
                                .init(color: Color.white.opacity(0.9), location: 0.0),
                                .init(color: Color.green.opacity(0.8), location: 0.3),
                                .init(color: Color.green.opacity(0.4), location: 0.7),
                                .init(color: Color.clear, location: 1.0)
                            ]),
                            center: .center,
                            startRadius: 0,
                            endRadius: 50
                        )
                    )
                    .frame(width: 100, height: 100)
                    .blur(radius: 3)
                
                // 中心亮点
                Circle()
                    .fill(Color.white.opacity(0.95))
                    .frame(width: 20, height: 20)
                    .blur(radius: 2)
                
                // 中心核心
                Circle()
                    .fill(Color.green.opacity(0.9))
                    .frame(width: 30, height: 30)
            }
            .scaleEffect(scale)
            .opacity(opacity)
            
            // 🌟 外层光环效果
            ForEach(0..<3, id: \.self) { ringIndex in
                Circle()
                    .stroke(
                        LinearGradient(
                            gradient: Gradient(stops: [
                                .init(color: Color.green.opacity(0.8), location: 0.0),
                                .init(color: Color.green.opacity(0.4), location: 0.5),
                                .init(color: Color.clear, location: 1.0)
                            ]),
                            startPoint: .top,
                            endPoint: .bottom
                        ),
                        lineWidth: 3
                    )
                    .frame(width: CGFloat(80 + ringIndex * 40), height: CGFloat(80 + ringIndex * 40))
                    .opacity(0.6 - Double(ringIndex) * 0.2)
                    .blur(radius: CGFloat(ringIndex + 1))
            }
        }
        .scaleEffect(scale)
        .opacity(opacity)
        .rotationEffect(.degrees(rotation))
        // 暂时注释掉绿色光芒效果监听器
        // .onReceive(NotificationCenter.default.publisher(for: NSNotification.Name("TriggerGreenBurst"))) { _ in
        //     print("🌟 收到绿色爆炸触发通知")
        //     triggerBurstAnimation()
        // }
    }
    
    /// 触发爆炸动画
    private func triggerBurstAnimation() {
        print("🌟 开始绿色爆炸动画")
        
        // 重置到初始状态
        scale = 0.1
        opacity = 0
        rotation = 0
        
        // 第一阶段：快速放大并显现 (0.3秒)
        withAnimation(.easeOut(duration: 0.3)) {
            scale = 1.5
            opacity = 1.0
            rotation = 90
        }
        
        // 第二阶段：继续放大到最大 (0.4秒，延迟0.2秒)
        withAnimation(.easeOut(duration: 0.4).delay(0.2)) {
            scale = 2.2
            rotation = 180
        }
        
        // 第三阶段：快速缩小并淡出 (0.5秒，延迟0.5秒)
        withAnimation(.easeIn(duration: 0.5).delay(0.5)) {
            scale = 0.8
            opacity = 0
            rotation = 360
        }
    }
}


