//
//  WhiteParticleSystem.swift
//  VoicingTrainer
//
//  Created by <PERSON> on 2025/7/27.
//  专门为ProgressionsView设计的白色粒子系统
//

import SwiftUI
import SpriteKit

// MARK: - 白色粒子场景

/// 🎆 PARTICLE ANIMATION: 专门的白色粒子SpriteKit场景
class WhiteParticleScene: SKScene {
    
    // 🎨 CUSTOMIZE COLOR: 高性能粒子配置参数
    private let particleCountPerNote = 8        // 每个音符位置的粒子数量（5-8个）
    private let particleSize: CGFloat = 1.0       // 粒子大小（几个像素）
    private let explosionForce: CGFloat = 550   // 爆炸力度（快速扩散）
    private let gravity: CGFloat = -1         // 重力强度（轻微下落）
    private let particleLifetime: TimeInterval = 1.5  // 粒子生命周期（快速消失）
    
    override func didMove(to view: SKView) {
        // 设置场景属性
        backgroundColor = .clear
        
        // 设置物理世界
        physicsWorld.gravity = CGVector(dx: 0, dy: gravity)
        
        print("🎆 WhiteParticleScene 初始化完成")
    }
    
    // 🎆 PARTICLE ANIMATION: 在多个位置同时触发白色粒子爆炸
    /// 在多个位置同时创建白色粒子爆炸效果（高性能版本）
    /// - Parameter positions: 爆炸位置数组（SpriteKit坐标系）
    func explodeWhiteParticles(at positions: [CGPoint]) {
        print("🎆 在 \(positions.count) 个位置同时触发白色粒子爆炸")

        var totalParticles = 0
        for position in positions {
            for i in 0..<particleCountPerNote {
                createWhiteParticle(at: position, index: i)
                totalParticles += 1
            }
        }

        print("✅ 创建了 \(totalParticles) 个白色粒子（\(positions.count) 个位置 × \(particleCountPerNote) 个粒子）")
    }

    // 🎆 PARTICLE ANIMATION: 单个位置爆炸（保持兼容性）
    /// 在指定位置创建白色粒子爆炸效果
    /// - Parameter position: 爆炸位置（SpriteKit坐标系）
    func explodeWhiteParticles(at position: CGPoint) {
        explodeWhiteParticles(at: [position])
    }
    
    // 🎆 PARTICLE ANIMATION: 创建高性能白色粒子
    /// 创建单个白色粒子（高性能版本）
    /// - Parameters:
    ///   - position: 粒子初始位置
    ///   - index: 粒子索引（用于随机化）
    private func createWhiteParticle(at position: CGPoint, index: Int) {
        // 🎨 CUSTOMIZE COLOR: 创建极小的白色圆形粒子（高性能）
        let particle = SKShapeNode(circleOfRadius: particleSize)
        particle.fillColor = .white
        particle.strokeColor = .clear  // 移除边框提高性能
        particle.position = position
        particle.zPosition = 1000  // 确保在最顶层

        // 🎆 PARTICLE ANIMATION: 简化物理体（提高性能）
        particle.physicsBody = SKPhysicsBody(circleOfRadius: particleSize)
        particle.physicsBody?.isDynamic = true
        particle.physicsBody?.mass = 0.01  // 更轻的质量
        particle.physicsBody?.restitution = 0.1  // 减少弹性
        particle.physicsBody?.friction = 0.05  // 减少摩擦

        // 🎆 PARTICLE ANIMATION: 快速爆炸扩散
        let angle = CGFloat.random(in: 0...(2 * .pi))
        let force = CGFloat.random(in: explosionForce * 0.8...explosionForce * 1.2)
        let dx = cos(angle) * force
        let dy = sin(angle) * force

        particle.physicsBody?.velocity = CGVector(dx: dx, dy: dy)
        particle.physicsBody?.angularVelocity = CGFloat.random(in: -2...2)  // 减少旋转

        // 设置更高的阻尼，快速停止
        particle.physicsBody?.linearDamping = 0.2
        particle.physicsBody?.angularDamping = 0.3

        addChild(particle)

        // 🎆 PARTICLE ANIMATION: 快速生命周期动画
        let fadeDelay = SKAction.wait(forDuration: particleLifetime * 0.4)  // 40%时间后开始淡出
        let fadeOut = SKAction.fadeOut(withDuration: particleLifetime * 0.6)  // 60%时间淡出
        let remove = SKAction.removeFromParent()
        let sequence = SKAction.sequence([fadeDelay, fadeOut, remove])
        particle.run(sequence)
    }
    
    /// 清理所有粒子
    func clearAllParticles() {
        enumerateChildNodes(withName: "*") { node, _ in
            if node is SKShapeNode {
                node.removeFromParent()
            }
        }
    }
    
    /// 转换SwiftUI坐标到SpriteKit坐标
    func convertSwiftUIToSpriteKit(_ swiftUIPosition: CGPoint) -> CGPoint {
        return CGPoint(
            x: swiftUIPosition.x,
            y: size.height - swiftUIPosition.y  // Y轴翻转
        )
    }
}

// MARK: - SwiftUI包装器

/// 🎆 PARTICLE ANIMATION: 白色粒子系统的SwiftUI包装器
#if os(iOS)
struct WhiteParticleView: UIViewRepresentable {
    @EnvironmentObject var coordinator: WhiteParticleCoordinator
    
    func makeUIView(context: Context) -> SKView {
        print("🎬 创建iOS WhiteParticleView")
        let skView = SKView()
        skView.backgroundColor = .clear
        skView.allowsTransparency = true
        skView.isOpaque = false
        
        let scene = WhiteParticleScene()
        scene.scaleMode = .resizeFill
        scene.backgroundColor = .clear
        
        coordinator.scene = scene
        skView.presentScene(scene)
        
        print("✅ iOS WhiteParticleView创建完成")
        return skView
    }
    
    func updateUIView(_ uiView: SKView, context: Context) {
        if let scene = coordinator.scene {
            let newSize = uiView.bounds.size
            scene.size = newSize
            print("📐 iOS WhiteParticleView 大小更新: \(newSize)")
        }
    }
}
#else
struct WhiteParticleView: NSViewRepresentable {
    @EnvironmentObject var coordinator: WhiteParticleCoordinator
    
    func makeNSView(context: Context) -> SKView {
        print("🎬 创建macOS WhiteParticleView")
        let skView = SKView()
        skView.allowsTransparency = true
        
        let scene = WhiteParticleScene()
        scene.scaleMode = .resizeFill
        scene.backgroundColor = .clear
        
        coordinator.scene = scene
        skView.presentScene(scene)
        
        print("✅ macOS WhiteParticleView创建完成")
        return skView
    }
    
    func updateNSView(_ nsView: SKView, context: Context) {
        if let scene = coordinator.scene {
            let newSize = nsView.bounds.size
            scene.size = newSize
            print("📐 macOS WhiteParticleView 大小更新: \(newSize)")
        }
    }
}
#endif

// MARK: - 协调器

/// 🎆 PARTICLE ANIMATION: 白色粒子系统协调器
class WhiteParticleCoordinator: ObservableObject {
    static let shared = WhiteParticleCoordinator()
    var scene: WhiteParticleScene?

    /// 在多个位置同时触发白色粒子爆炸（高性能版本）
    func explodeWhiteParticles(at positions: [CGPoint]) {
        print("🎆 WhiteParticleCoordinator.explodeWhiteParticles at \(positions.count) positions")

        guard let scene = scene else {
            print("❌ WhiteParticleScene is nil!")
            return
        }

        DispatchQueue.main.async {
            let spriteKitPositions = positions.map { scene.convertSwiftUIToSpriteKit($0) }
            scene.explodeWhiteParticles(at: spriteKitPositions)
        }
    }

    /// 在指定位置触发白色粒子爆炸（兼容性方法）
    func explodeWhiteParticles(at position: CGPoint) {
        explodeWhiteParticles(at: [position])
    }

    /// 清理所有粒子
    func clearParticles() {
        print("🧹 WhiteParticleCoordinator.clearParticles")
        DispatchQueue.main.async {
            self.scene?.clearAllParticles()
        }
    }
}

