import SwiftUI

/// 绿色光芒爆发效果视图
struct GreenBurstEffectView: View {
    @State private var isAnimating = true
    @State private var opacity: Double = 0
    @State private var scale: CGFloat = 0.1
    @State private var rotation: Double = 0
    
    let lightBandCount = 12  // 光带数量
    let animationDuration: Double = 1.2
    
    var body: some View {
        ZStack {
            // 多条光带组成的爆发效果
            ForEach(0..<lightBandCount, id: \.self) { index in
                LightBandView(
                    index: index,
                    totalBands: lightBandCount,
                    isAnimating: isAnimating,
                    rotation: rotation,
                    scale: scale,
                    opacity: opacity
                )
            }
        }
        .opacity(opacity)
        .scaleEffect(scale)
        .rotationEffect(.degrees(rotation))
        .allowsHitTesting(false)  // 不拦截触摸事件
        .onAppear {
                   print("🌟 GreenBurstEffectView 出现")
                   // 自动触发爆发效果
                   DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                       self.triggerBurst()
                   }
               }
    }
    
    /// 触发爆发效果
    func triggerBurst() {
        
        print("🌟 开始触发绿色光芒爆发")
        // 重置状态
        isAnimating = false
        opacity = 0
        scale = 0.1
        rotation = 0
        
        // 开始动画
        withAnimation(.easeOut(duration: 0.1)) {
            opacity = 1.0
            scale = 0.3
        }
        
        // 主要爆发动画
        withAnimation(.easeOut(duration: animationDuration * 0.6)) {
            scale = 1.2
            rotation = 180
        }
        
        // 旋转和缩小消失
        withAnimation(.easeInOut(duration: animationDuration * 0.4).delay(animationDuration * 0.6)) {
            scale = 0.8
            rotation = 360
            opacity = 0
        }
        
        isAnimating = true
    }
}

/// 单条光带视图
struct LightBandView: View {
    let index: Int
    let totalBands: Int
    let isAnimating: Bool
    let rotation: Double
    let scale: CGFloat
    let opacity: Double
    
    private var bandAngle: Double {
        return Double(index) * (360.0 / Double(totalBands))
    }
    
    private var isLightGreen: Bool {
        return index % 2 == 0
    }
    
    private var bandColor: Color {
        return isLightGreen ? 
        Color.green.opacity(0.9) :
        Color(red: 0, green: 0.8, blue: 0).opacity(0.95)
    }
    
    var body: some View {
        Rectangle()
            .fill(
                LinearGradient(
                    gradient: Gradient(stops: [
                        .init(color: bandColor, location: 0.0),
                        .init(color: bandColor.opacity(0.8), location: 0.5),
                        .init(color: Color.clear, location: 1.0)
                    ]),
                    startPoint: .center,
                    endPoint: .trailing
                )
            )
            .frame(width: 200, height: 8)
            .offset(x: 100)  // 从中心向外延伸
            .rotationEffect(.degrees(bandAngle))
            .blur(radius: isAnimating ? 2 : 0)
            .animation(
                .easeInOut(duration: 0.3).delay(Double(index) * 0.05),
                value: isAnimating
            )
    }
}

/// 绿色光芒效果管理器
class GreenBurstEffectManager: ObservableObject {
    @Published var shouldTrigger = false
    
    /// 触发爆发效果
    func triggerBurst() {
        shouldTrigger = true
        
        // 重置状态
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            self.shouldTrigger = false
        }
    }
}
