import SwiftUI

/// 功耗调试界面 - 用于监控和调试iOS应用的功耗状态
struct PowerDebugView: View {
    @StateObject private var powerManager = PowerManager.shared
    @StateObject private var powerTestManager = PowerTestManager.shared
    @State private var isExpanded = false
    
    var body: some View {
        VStack(spacing: 0) {
            // 折叠/展开按钮
            Button(action: {
                withAnimation(.easeInOut(duration: 0.3)) {
                    isExpanded.toggle()
                }
            }) {
                HStack {
                    Image(systemName: "bolt.circle.fill")
                        .foregroundColor(batteryColor)
                    
                    Text("功耗监控")
                        .font(.headline)
                        .foregroundColor(.primary)
                    
                    Spacer()
                    
                    Text(String(format: "%.0f%%", powerManager.powerEfficiencyScore))
                        .font(.caption)
                        .foregroundColor(efficiencyColor)
                    
                    Image(systemName: isExpanded ? "chevron.up" : "chevron.down")
                        .foregroundColor(.secondary)
                }
                .padding(.horizontal, 16)
                .padding(.vertical, 12)
                #if os(iOS)
                .background(Color(.systemGray6))
                #else
                .background(Color.gray.opacity(0.1))
                #endif
                .cornerRadius(8)
            }
            .buttonStyle(PlainButtonStyle())
            
            // 详细信息面板
            if isExpanded {
                VStack(spacing: 16) {
                    // 电池状态
                    batteryStatusSection
                    
                    // 系统性能
                    systemPerformanceSection
                    
                    // 后台活动
                    backgroundActivitySection
                    
                    // 功耗建议
                    recommendationsSection
                    
                    // 功耗测试区域
                    powerTestSection
                    
                    // 控制按钮
                    controlButtonsSection
                }
                .padding(16)
                #if os(iOS)
                .background(Color(.systemBackground))
                #else
                .background(Color.white)
                #endif
                .cornerRadius(12)
                .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
                .transition(.opacity.combined(with: .scale(scale: 0.95)))
            }
        }
        .padding(.horizontal, 16)
    }
    
    // MARK: - Battery Status Section
    private var batteryStatusSection: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Image(systemName: "battery.100")
                    .foregroundColor(batteryColor)
                Text("电池状态")
                    .font(.headline)
                Spacer()
            }
            
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("电量: \(String(format: "%.0f%%", powerManager.batteryLevel * 100))")
                        .font(.subheadline)
                    
                    Text("状态: \(batteryStateText)")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                VStack(alignment: .trailing, spacing: 4) {
                    if powerManager.isLowPowerModeEnabled {
                        Label("省电模式", systemImage: "battery.25")
                            .font(.caption)
                            .foregroundColor(.orange)
                    }
                    
                    Text("热状态: \(thermalStateText)")
                        .font(.caption)
                        .foregroundColor(thermalColor)
                }
            }
            
            // 电池电量进度条
            ProgressView(value: Double(powerManager.batteryLevel), total: 1.0)
                .progressViewStyle(LinearProgressViewStyle(tint: batteryColor))
                .scaleEffect(y: 2.0)
        }
        .padding(12)
        #if os(iOS)
        .background(Color(.systemGray6))
        #else
        .background(Color.gray.opacity(0.1))
        #endif
        .cornerRadius(8)
    }
    
    // MARK: - System Performance Section
    private var systemPerformanceSection: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Image(systemName: "cpu")
                    .foregroundColor(.blue)
                Text("系统性能")
                    .font(.headline)
                Spacer()
            }
            
            VStack(spacing: 8) {
                // CPU使用率
                HStack {
                    Text("CPU使用率")
                        .font(.subheadline)
                    Spacer()
                    Text(String(format: "%.1f%%", powerManager.cpuUsage))
                        .font(.subheadline)
                        .foregroundColor(cpuColor)
                }
                
                                    ProgressView(value: min(100.0, max(0.0, powerManager.cpuUsage)), total: 100.0)
                        .progressViewStyle(LinearProgressViewStyle(tint: cpuColor))
                
                // 内存使用率
                HStack {
                    Text("内存使用率")
                        .font(.subheadline)
                    Spacer()
                    Text(String(format: "%.1f%%", powerManager.memoryUsage))
                        .font(.subheadline)
                        .foregroundColor(memoryColor)
                }
                
                                    ProgressView(value: min(100.0, max(0.0, powerManager.memoryUsage)), total: 100.0)
                        .progressViewStyle(LinearProgressViewStyle(tint: memoryColor))
                
                // 功耗效率评分
                HStack {
                    Text("功耗效率")
                        .font(.subheadline)
                    Spacer()
                    Text(String(format: "%.1f%%", powerManager.powerEfficiencyScore))
                        .font(.subheadline)
                        .foregroundColor(efficiencyColor)
                }
                
                                    ProgressView(value: min(100.0, max(0.0, powerManager.powerEfficiencyScore)), total: 100.0)
                        .progressViewStyle(LinearProgressViewStyle(tint: efficiencyColor))
            }
        }
        .padding(12)
        #if os(iOS)
        .background(Color(.systemGray6))
        #else
        .background(Color.gray.opacity(0.1))
        #endif
        .cornerRadius(8)
    }
    
    // MARK: - Background Activity Section
    private var backgroundActivitySection: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Image(systemName: "moon.circle")
                    .foregroundColor(powerManager.isBackgroundMode ? .purple : .gray)
                Text("后台活动")
                    .font(.headline)
                Spacer()
            }
            
            VStack(alignment: .leading, spacing: 4) {
                HStack {
                    Text("后台模式:")
                        .font(.subheadline)
                    Spacer()
                    Text(powerManager.isBackgroundMode ? "是" : "否")
                        .font(.subheadline)
                        .foregroundColor(powerManager.isBackgroundMode ? .purple : .green)
                }
                
                HStack {
                    Text("音频会话:")
                        .font(.subheadline)
                    Spacer()
                    Text(powerManager.isAudioSessionActive ? "活跃" : "非活跃")
                        .font(.subheadline)
                        .foregroundColor(powerManager.isAudioSessionActive ? .orange : .gray)
                }
                
                HStack {
                    Text("MIDI活动:")
                        .font(.subheadline)
                    Spacer()
                    Text(powerManager.isMIDIActive ? "活跃" : "非活跃")
                        .font(.subheadline)
                        .foregroundColor(powerManager.isMIDIActive ? .blue : .gray)
                }
            }
        }
        .padding(12)
        #if os(iOS)
        .background(Color(.systemGray6))
        #else
        .background(Color.gray.opacity(0.1))
        #endif
        .cornerRadius(8)
    }
    
    // MARK: - Recommendations Section
    private var recommendationsSection: some View {
        let recommendations = powerManager.getPowerRecommendations()
        
        return Group {
            if !recommendations.isEmpty {
                VStack(alignment: .leading, spacing: 8) {
                    HStack {
                        Image(systemName: "lightbulb")
                            .foregroundColor(.yellow)
                        Text("功耗建议")
                            .font(.headline)
                        Spacer()
                    }
                    
                    VStack(alignment: .leading, spacing: 4) {
                        ForEach(recommendations, id: \.self) { recommendation in
                            HStack(alignment: .top, spacing: 8) {
                                Image(systemName: "exclamationmark.triangle.fill")
                                    .foregroundColor(.orange)
                                    .font(.caption)
                                
                                Text(recommendation)
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                                    .multilineTextAlignment(.leading)
                                
                                Spacer()
                            }
                        }
                    }
                }
                .padding(12)
                #if os(iOS)
                .background(Color(.systemGray6))
                #else
                .background(Color.gray.opacity(0.1))
                #endif
                .cornerRadius(8)
            } else {
                EmptyView()
            }
        }
    }
    
    // MARK: - Power Test Section
    private var powerTestSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Image(systemName: "chart.line.uptrend.xyaxis")
                    .foregroundColor(.blue)
                Text("功耗测试")
                    .font(.headline)
                Spacer()
            }
            
            // 测试状态
            HStack {
                Circle()
                    .fill(powerTestManager.isRunning ? .green : .gray)
                    .frame(width: 8, height: 8)
                
                Text(powerTestManager.testStatus)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                
                Spacer()
                
                if powerTestManager.isRunning {
                    Text(String(format: "%.0f%%", powerTestManager.testProgress * 100))
                        .font(.caption)
                        .foregroundColor(.blue)
                }
            }
            
            // 进度条
            if powerTestManager.isRunning {
                ProgressView(value: min(1.0, max(0.0, powerTestManager.testProgress)), total: 1.0)
                    .progressViewStyle(LinearProgressViewStyle())
                    .scaleEffect(y: 0.5)
            }
            
            // 当前测试结果
            if let currentResult = powerTestManager.currentResult {
                VStack(alignment: .leading, spacing: 4) {
                    Text("当前样本:")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    HStack {
                        VStack(alignment: .leading) {
                            Text("CPU: \(String(format: "%.1f%%", currentResult.cpuUsage))")
                            Text("内存: \(String(format: "%.1f%%", currentResult.memoryUsage))")
                        }
                        .font(.caption)
                        
                        Spacer()
                        
                        VStack(alignment: .trailing) {
                            Text("电池: \(String(format: "%.1f%%", currentResult.batteryLevel * 100))")
                            Text("效率: \(String(format: "%.1f%%", currentResult.powerEfficiencyScore))")
                        }
                        .font(.caption)
                    }
                }
                .padding(8)
                .background(Color.blue.opacity(0.1))
                .cornerRadius(6)
            }
            
            // 测试控制按钮
            HStack {
                Button(action: {
                    if powerTestManager.isRunning {
                        powerTestManager.stopTest()
                    } else {
                        powerTestManager.startTest()
                    }
                }) {
                    HStack {
                        Image(systemName: powerTestManager.isRunning ? "stop.circle" : "play.circle")
                        Text(powerTestManager.isRunning ? "停止测试" : "开始测试")
                    }
                }
                .buttonStyle(.bordered)
                .disabled(false)
                
                Spacer()
                
                Button("清除结果") {
                    powerTestManager.clearResults()
                }
                .buttonStyle(.bordered)
                .disabled(powerTestManager.testResults.isEmpty)
            }
            
            // 测试结果摘要
            if !powerTestManager.testResults.isEmpty {
                VStack(alignment: .leading, spacing: 4) {
                    Text("测试结果 (\(powerTestManager.testResults.count) 样本)")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    let avgCPU = powerTestManager.testResults.map { $0.cpuUsage }.reduce(0, +) / Double(powerTestManager.testResults.count)
                    let avgMemory = powerTestManager.testResults.map { $0.memoryUsage }.reduce(0, +) / Double(powerTestManager.testResults.count)
                    let avgEfficiency = powerTestManager.testResults.map { $0.powerEfficiencyScore }.reduce(0, +) / Double(powerTestManager.testResults.count)
                    
                    HStack {
                        Text("平均CPU: \(String(format: "%.1f%%", avgCPU))")
                        Spacer()
                        Text("平均内存: \(String(format: "%.1f%%", avgMemory))")
                    }
                    .font(.caption2)
                    
                    Text("平均效率: \(String(format: "%.1f%%", avgEfficiency))")
                        .font(.caption2)
                }
                .padding(8)
                .background(Color.green.opacity(0.1))
                .cornerRadius(6)
            }
        }
        .padding(12)
        #if os(iOS)
        .background(Color(.systemGray6))
        #else
        .background(Color.gray.opacity(0.1))
        #endif
        .cornerRadius(8)
    }
    
    // MARK: - Control Buttons Section
    private var controlButtonsSection: some View {
        HStack(spacing: 12) {
            Button("强制优化") {
                powerManager.forcePowerOptimization()
            }
            .buttonStyle(.bordered)
            .foregroundColor(.blue)
            
            Spacer()
            
            Button("重置监控") {
                // 重置监控数据
                print("🔄 PowerManager - Reset monitoring")
            }
            .buttonStyle(.bordered)
            .foregroundColor(.orange)
        }
    }
    
    // MARK: - Computed Properties
    private var batteryColor: Color {
        if powerManager.batteryLevel < 0.1 {
            return .red
        } else if powerManager.batteryLevel < 0.2 {
            return .orange
        } else {
            return .green
        }
    }
    
    private var batteryStateText: String {
        #if os(iOS)
        switch powerManager.batteryState {
        case .charging:
            return "充电中"
        case .full:
            return "已充满"
        case .unplugged:
            return "未充电"
        default:
            return "未知"
        }
        #else
        // macOS fallback
        switch powerManager.batteryState {
        case 1:
            return "充电中"
        case 2:
            return "已充满"
        case 0:
            return "未充电"
        default:
            return "未知"
        }
        #endif
    }
    
    private var thermalStateText: String {
        switch powerManager.thermalState {
        case .nominal:
            return "正常"
        case .fair:
            return "良好"
        case .serious:
            return "严重"
        case .critical:
            return "危险"
        @unknown default:
            return "未知"
        }
    }
    
    private var thermalColor: Color {
        switch powerManager.thermalState {
        case .nominal, .fair:
            return .green
        case .serious:
            return .orange
        case .critical:
            return .red
        @unknown default:
            return .gray
        }
    }
    
    private var cpuColor: Color {
        if powerManager.cpuUsage > 80 {
            return .red
        } else if powerManager.cpuUsage > 60 {
            return .orange
        } else {
            return .green
        }
    }
    
    private var memoryColor: Color {
        if powerManager.memoryUsage > 85 {
            return .red
        } else if powerManager.memoryUsage > 70 {
            return .orange
        } else {
            return .green
        }
    }
    
    private var efficiencyColor: Color {
        if powerManager.powerEfficiencyScore < 50 {
            return .red
        } else if powerManager.powerEfficiencyScore < 75 {
            return .orange
        } else {
            return .green
        }
    }
}

// MARK: - Preview
struct PowerDebugView_Previews: PreviewProvider {
    static var previews: some View {
        PowerDebugView()
            .padding()
            .previewLayout(.sizeThatFits)
    }
} 