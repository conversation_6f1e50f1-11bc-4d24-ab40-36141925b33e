//
//  HapticFeedback.swift
//  FullUI
//
//  Created by <PERSON> on 2025/5/26.
//  跨平台触觉反馈工具类
//

import Foundation
#if os(iOS)
import UIKit
#endif

// MARK: - 触觉反馈工具类

/**
 * 跨平台触觉反馈工具类
 * 在iOS上提供触觉反馈，在macOS上静默处理
 */
struct HapticFeedback {
    
    /**
     * 触觉反馈强度枚举
     */
    enum Style {
        case light
        case medium
        case heavy
        case success
        case warning
        case error
    }
    
    /**
     * 触发触觉反馈
     * - Parameter style: 反馈强度类型
     */
    static func impact(_ style: Style) {
        
        #if os(iOS)
        switch style {
        case .light:
            let generator = UIImpactFeedbackGenerator(style: .light)
            generator.impactOccurred()
        case .medium:
            let generator = UIImpactFeedbackGenerator(style: .medium)
            generator.impactOccurred()
        case .heavy:
            let generator = UIImpactFeedbackGenerator(style: .heavy)
            generator.impactOccurred()
        case .success:
            let generator = UINotificationFeedbackGenerator()
            generator.notificationOccurred(.success)
        case .warning:
            let generator = UINotificationFeedbackGenerator()
            generator.notificationOccurred(.warning)
        case .error:
            let generator = UINotificationFeedbackGenerator()
            generator.notificationOccurred(.error)
        }
        #else
        // macOS上不支持触觉反馈，静默处理
        // 可以在这里添加其他形式的反馈，比如声音或视觉效果
        #endif
         
    }
    
    /**
     * 选择反馈（轻微点击感）
     */
    static func selection() {
        #if os(iOS)
        let generator = UISelectionFeedbackGenerator()
        generator.selectionChanged()
        #endif
    }
}

// MARK: - 便捷扩展

extension HapticFeedback {
    
    /**
     * 音符击中反馈
     */
    static func noteHit() {
        impact(.medium)
    }
    
    /**
     * 游戏开始反馈
     */
    static func gameStart() {
        impact(.heavy)
    }
    
    /**
     * 游戏完成反馈
     */
    static func gameComplete() {
        impact(.success)
    }
    
    /**
     * 错误反馈
     */
    static func error() {
        impact(.error)
    }
    
    /**
     * 按钮点击反馈
     */
    static func buttonTap() {
        impact(.light)
    }
} 
