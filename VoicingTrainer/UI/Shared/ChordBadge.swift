//
//  ChordBadge.swift
//  FullUI
//
//  Created by <PERSON> Li on 2025/5/26.
//
import SwiftUI

// 和弦徽章组件
struct ChordBadge: View {
    let name: String
    
    var body: some View {
        Text(name)
            .font(.title2)
            .padding(.horizontal, 20)
            .padding(.vertical, 10)
            .background(
                RoundedRectangle(cornerRadius: 10)
                    .stroke(Color.blue, lineWidth: 2)
            )
    }
}

// 音符显示组件
struct NoteView: View {
    let note: String
    
    var body: some View {
        Text(note)
            .font(.title)
            .frame(width: 40, height: 40)
            .background(Circle().fill(Color.blue.opacity(0.2)))
    }
}
