//
//  PremiumPlayStopButton.swift
//  VoicingTrainer
//
//  Created by Assistant on 2025/6/20.
//

import SwiftUI

/// 高质感的播放/停止按钮组件
/// 用于NotesView, ChordsView, ProgressionsView的统一播放控制
struct PremiumPlayStopButton: View {
    let isPlaying: Bool
    let isDisabled: Bool
    let action: () -> Void
    
    @State private var isPressed = false
    @State private var pulseScale: CGFloat = 1.0
    
    init(isPlaying: Bool, isDisabled: Bool = false, action: @escaping () -> Void) {
        self.isPlaying = isPlaying
        self.isDisabled = isDisabled
        self.action = action
    }
    
    var body: some View {
        Button(action: {
            // 添加触觉反馈
            #if os(iOS)
            let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
            impactFeedback.impactOccurred()
            #endif
            
            withAnimation(.spring(response: 0.3, dampingFraction: 0.6)) {
                action()
            }
        }) {
            ZStack {
                // 外层光圈效果
                Circle()
                    .fill(
                        RadialGradient(
                            gradient: Gradient(colors: [
                                buttonColor.opacity(0.3),
                                buttonColor.opacity(0.1),
                                Color.clear
                            ]),
                            center: .center,
                            startRadius: 30,
                            endRadius: 50
                        )
                    )
                    .frame(width: 100, height: 100)
                    .scaleEffect(pulseScale)
                    .opacity(isPlaying ? 1 : 0.6)
                
                // 主按钮体
                Circle()
                    .fill(
                        LinearGradient(
                            gradient: Gradient(colors: [
                                buttonColor,
                                buttonColor.opacity(0.8)
                            ]),
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .frame(width: 64, height: 64)
                    .overlay(
                        Circle()
                            .stroke(
                                LinearGradient(
                                    gradient: Gradient(colors: [
                                        Color.white.opacity(0.3),
                                        Color.white.opacity(0.1)
                                    ]),
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                ),
                                lineWidth: 2
                            )
                    )
                    .shadow(color: buttonColor.opacity(0.4), radius: 8, x: 0, y: 4)
                    .shadow(color: Color.black.opacity(0.1), radius: 2, x: 0, y: 1)
                    .scaleEffect(isPressed ? 0.95 : 1.0)
                
                // 图标
                Image(systemName: isPlaying ? "stop.fill" : "play.fill")
                    .font(.system(size: 24, weight: .bold))
                    .foregroundColor(.white)
                    .shadow(color: Color.black.opacity(0.3), radius: 1, x: 0, y: 1)
                    .offset(x: isPlaying ? 0 : 2) // play图标向右偏移居中
            }
        }
        .disabled(isDisabled)
        .opacity(isDisabled ? 0.5 : 1.0)
        .scaleEffect(isDisabled ? 0.9 : 1.0)
        #if os(macOS)
        //  去掉方框：这个风格让MacOS的播放按钮呈现圆形，很重要。
        .buttonStyle(.plain)
        #endif
        .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
            withAnimation(.easeInOut(duration: 0.1)) {
                isPressed = pressing
            }
        }, perform: {})
        .onAppear {
            startPulseAnimation()
        }
        .animation(.spring(response: 0.5, dampingFraction: 0.8), value: isPlaying)
        .animation(.easeInOut(duration: 0.2), value: isDisabled)
    }
    
    private var buttonColor: Color {
        if isPlaying {
            return Color(red: 0.95, green: 0.26, blue: 0.21) // 优雅的红色
        } else {
            return Color(red: 0.20, green: 0.78, blue: 0.35) // 优雅的绿色
        }
    }
    
    private func startPulseAnimation() {
        withAnimation(
            Animation
                .easeInOut(duration: 2.0)
                .repeatForever(autoreverses: true)
        ) {
            pulseScale = 1.1
        }
    }
}

#Preview {
    VStack(spacing: 40) {
        PremiumPlayStopButton(isPlaying: false) {
            print("Play tapped")
        }
        
        PremiumPlayStopButton(isPlaying: true) {
            print("Stop tapped")
        }
        
        PremiumPlayStopButton(isPlaying: false, isDisabled: true) {
            print("Disabled tapped")
        }
    }
    .padding()
    .background(Color.black.opacity(0.1))
} 
