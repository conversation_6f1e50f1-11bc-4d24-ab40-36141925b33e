//
//  CircleOfFifthsView.swift
//  FifthCircleView
//
//  Created by <PERSON> on 2025/6/15.
//

import SwiftUI

// MARK: - Key Model
public struct Key: Equatable, Hashable {
    public let name: String
    public let relativeMinor: String
    public let color: Color
    
    public static let allKeys: [Key] = [
        Key(name: "C", relativeMinor: "Am", color: Color(red: 0.85, green: 0.33, blue: 0.31)), // Red
        Key(name: "G", relativeMinor: "Em", color: Color(red: 0.91, green: 0.12, blue: 0.39)), // Pink
        Key(name: "D", relativeMinor: "Bm", color: Color(red: 0.46, green: 0.20, blue: 0.57)), // Purple
        Key(name: "A", relativeMinor: "F♯m", color: Color(red: 0.29, green: 0.35, blue: 0.75)), // Blue
        Key(name: "E", relativeMinor: "C♯m", color: Color(red: 0.20, green: 0.60, blue: 0.86)), // Light Blue
        Key(name: "B", relativeMinor: "G♯m", color: Color(red: 0.00, green: 0.74, blue: 0.83)), // Cyan
        //Key(name: "F♯/G♭", relativeMinor: "E♭m", color: Color(red: 0.00, green: 0.59, blue: 0.53)), // Teal
        Key(name: "F♯", relativeMinor: "E♭m", color: Color(red: 0.00, green: 0.59, blue: 0.53)), // Teal
        Key(name: "D♭", relativeMinor: "B♭m", color: Color(red: 0.30, green: 0.69, blue: 0.31)), // Green
        Key(name: "A♭", relativeMinor: "Fm", color: Color(red: 0.54, green: 0.77, blue: 0.29)), // Light Green
        Key(name: "E♭", relativeMinor: "Cm", color: Color(red: 0.80, green: 0.86, blue: 0.22)), // Yellow Green
        Key(name: "B♭", relativeMinor: "Gm", color: Color(red: 1.00, green: 0.76, blue: 0.03)), // Yellow
        Key(name: "F", relativeMinor: "Dm", color: Color(red: 1.00, green: 0.60, blue: 0.00)) // Orange
    ]
    
    // 按半音顺序排列的调式数组 (0-11)
    // 0=C, 1=C#/Db, 2=D, 3=D#/Eb, 4=E, 5=F, 6=F#/Gb, 7=G, 8=G#/Ab, 9=A, 10=A#/Bb, 11=B
    public static let chromaticKeys: [Key] = [
        Key(name: "C", relativeMinor: "Am", color: Color(red: 0.85, green: 0.33, blue: 0.31)),   // 0
        Key(name: "D♭", relativeMinor: "B♭m", color: Color(red: 0.30, green: 0.69, blue: 0.31)), // 1
        Key(name: "D", relativeMinor: "Bm", color: Color(red: 0.46, green: 0.20, blue: 0.57)),   // 2
        Key(name: "E♭", relativeMinor: "Cm", color: Color(red: 0.80, green: 0.86, blue: 0.22)),  // 3
        Key(name: "E", relativeMinor: "C♯m", color: Color(red: 0.20, green: 0.60, blue: 0.86)),  // 4
        Key(name: "F", relativeMinor: "Dm", color: Color(red: 1.00, green: 0.60, blue: 0.00)),   // 5
        Key(name: "F♯", relativeMinor: "E♭m", color: Color(red: 0.00, green: 0.59, blue: 0.53)), // 6
        Key(name: "G", relativeMinor: "Em", color: Color(red: 0.91, green: 0.12, blue: 0.39)),   // 7
        Key(name: "A♭", relativeMinor: "Fm", color: Color(red: 0.54, green: 0.77, blue: 0.29)),  // 8
        Key(name: "A", relativeMinor: "F♯m", color: Color(red: 0.29, green: 0.35, blue: 0.75)),  // 9
        Key(name: "B♭", relativeMinor: "Gm", color: Color(red: 1.00, green: 0.76, blue: 0.03)),  // 10
        Key(name: "B", relativeMinor: "G♯m", color: Color(red: 0.00, green: 0.74, blue: 0.83))   // 11
    ]
    
    // 根据整数值获取调式 (0-11)
    public static func keyFromValue(_ value: Int) -> Key? {
        guard value >= 0 && value < chromaticKeys.count else { return nil }
        return chromaticKeys[value]
    }
    
    // 根据调式获取整数值 (0-11)
    public static func valueFromKey(_ key: Key) -> Int? {
        return chromaticKeys.firstIndex(of: key)
    }
}

// MARK: - Circle of Fifths View
public struct CircleOfFifthsView: View {
    @Binding public var activeKey: Key?
    
    // Public properties for customization
    public let size: CGFloat
    public let showLabels: Bool
    public let touchSelect: Bool
    public let onKeyTapped: ((Key) -> Void)?
    
    public init(
        size: CGFloat = 300,
        activeKey: Binding<Key?> = .constant(nil),
        showLabels: Bool = true,
        touchSelect: Bool = true,
        onKeyTapped: ((Key) -> Void)? = nil
    ) {
        self.size = size
        self._activeKey = activeKey
        self.showLabels = showLabels
        self.touchSelect = touchSelect
        self.onKeyTapped = onKeyTapped
    }
    
    public var body: some View {
        ZStack {
            // Background circle
            Circle()
                .fill(Color.clear)
                .frame(width: size, height: size)
            
            // Key segments
            
            ForEach(Array(Key.allKeys.enumerated()), id: \.element) { index, key in
                let segmentView = KeySegmentView(
                    key: key,
                    index: index,
                    totalKeys: Key.allKeys.count,
                    isActive: activeKey == key,
                    size: size,
                    showLabels: showLabels
                )
                
                if touchSelect {
                    segmentView
                        .onTapGesture {
                            activeKey = key
                            onKeyTapped?(key)
                        }
                } else {
                    segmentView
                }
            }
            
            // Center circle with "CIRCLE OF FIFTHS" text
            ZStack {
                Circle()
                    .fill(Color.white)
                    .frame(width: size * 0.25, height: size * 0.25)
                
                Circle()
                    .stroke(Color.gray.opacity(0.3), lineWidth: 1)
                    .frame(width: size * 0.25, height: size * 0.25)
                
                VStack(spacing: 2) {
                    Text("CIRCLE")
                        .font(.system(size: size * 0.025, weight: .bold))
                        .foregroundColor(.black)
                    Text("OF")
                        .font(.system(size: size * 0.02, weight: .medium))
                        .foregroundColor(.black)
                    Text("FIFTHS")
                        .font(.system(size: size * 0.025, weight: .bold))
                        .foregroundColor(.black)
                }
            }
        }
        .frame(width: size, height: size)
    }
    
    // Public method to set active key
    public func setActiveKey(_ key: Key?) {
        self.activeKey = key
    }
    
    // Public method to set active key by integer value (0-11)
    public func setActiveKeyValue(_ value: Int) {
        self.activeKey = Key.keyFromValue(value)
    }
}

// MARK: - Key Segment View
private struct KeySegmentView: View {
    let key: Key
    let index: Int
    let totalKeys: Int
    let isActive: Bool
    let size: CGFloat
    let showLabels: Bool
    
    private var angle: Double {
        return Double(index) * (360.0 / Double(totalKeys)) - 90 // Start from top
    }
    
    private var outerRadius: CGFloat { size * 0.5 }
    private var innerRadius: CGFloat { size * 0.32 }
    private var minorRadius: CGFloat { size * 0.125 }
    
    var body: some View {
        ZStack {
            // Major key segment (outer)
            SegmentShape(
                startAngle: .degrees(angle - 15),
                endAngle: .degrees(angle + 15),
                outerRadius: outerRadius,
                innerRadius: innerRadius
            )
            .fill(isActive ? key.color : Color.gray.opacity(0.3))
            
            // Minor key segment (inner)
            SegmentShape(
                startAngle: .degrees(angle - 15),
                endAngle: .degrees(angle + 15),
                outerRadius: innerRadius,
                innerRadius: minorRadius
            )
            .fill(isActive ? key.color.opacity(0.7) : Color.gray.opacity(0.6))
            
            // Divider lines
            Path { path in
                let center = CGPoint(x: size / 2, y: size / 2)
                let startAngle = angle - 15
                let endAngle = angle + 15
                
                // Left divider
                let leftStartX = center.x + cos(startAngle * .pi / 180) * minorRadius
                let leftStartY = center.y + sin(startAngle * .pi / 180) * minorRadius
                let leftEndX = center.x + cos(startAngle * .pi / 180) * outerRadius
                let leftEndY = center.y + sin(startAngle * .pi / 180) * outerRadius
                
                path.move(to: CGPoint(x: leftStartX, y: leftStartY))
                path.addLine(to: CGPoint(x: leftEndX, y: leftEndY))
                
                // Right divider
                let rightStartX = center.x + cos(endAngle * .pi / 180) * minorRadius
                let rightStartY = center.y + sin(endAngle * .pi / 180) * minorRadius
                let rightEndX = center.x + cos(endAngle * .pi / 180) * outerRadius
                let rightEndY = center.y + sin(endAngle * .pi / 180) * outerRadius
                
                path.move(to: CGPoint(x: rightStartX, y: rightStartY))
                path.addLine(to: CGPoint(x: rightEndX, y: rightEndY))
            }
            .stroke(Color.white, lineWidth: 2)
            
            if showLabels {
                // Major key label
                Text(key.name)
                    //.font(.system(size: size * 0.04, weight: .bold))
                    .font(.system(size: size * 0.1, weight: .bold))
                    .foregroundColor(isActive ? .white : .white)
                    .position(
                        x: size / 2 + cos(angle * .pi / 180) * (outerRadius - (outerRadius - innerRadius) / 2),
                        y: size / 2 + sin(angle * .pi / 180) * (outerRadius - (outerRadius - innerRadius) / 2)
                    )
                
                // Minor key label
                Text(key.relativeMinor)
                    .font(.system(size: size * 0.03, weight: .medium))
                    .foregroundColor(isActive ? .white : .white)
                    .position(
                        x: size / 2 + cos(angle * .pi / 180) * (innerRadius - (innerRadius - minorRadius) / 2),
                        y: size / 2 + sin(angle * .pi / 180) * (innerRadius - (innerRadius - minorRadius) / 2)
                    )
            }
        }
    }
}

// MARK: - Segment Shape
private struct SegmentShape: Shape {
    let startAngle: Angle
    let endAngle: Angle
    let outerRadius: CGFloat
    let innerRadius: CGFloat
    
    func path(in rect: CGRect) -> Path {
        var path = Path()
        let center = CGPoint(x: rect.midX, y: rect.midY)
        
        // Outer arc
        path.addArc(
            center: center,
            radius: outerRadius,
            startAngle: startAngle,
            endAngle: endAngle,
            clockwise: false
        )
        
        // Line to inner arc start
        let innerStartX = center.x + cos(endAngle.radians) * innerRadius
        let innerStartY = center.y + sin(endAngle.radians) * innerRadius
        path.addLine(to: CGPoint(x: innerStartX, y: innerStartY))
        
        // Inner arc (reverse direction)
        path.addArc(
            center: center,
            radius: innerRadius,
            startAngle: endAngle,
            endAngle: startAngle,
            clockwise: true
        )
        
        // Close the path
        path.closeSubpath()
        
        return path
    }
}

// MARK: - Preview
#Preview {
    struct PreviewWrapper: View {
        @State private var selectedKey: Key? = Key.allKeys[0]
        
        var body: some View {
            VStack(spacing: 30) {
                Text("Circle of Fifths - Interactive")
                    .font(.title)
                    .padding()
                
                CircleOfFifthsView(
                    size: 350,
                    activeKey: $selectedKey
                ) { key in
                    print("Tapped key: \(key.name)")
                }
                
                Text("Tap any segment to activate it")
                    .font(.caption)
                    .foregroundColor(.gray)
                
                Spacer()
            }
            .padding()
        }
    }
    
    return PreviewWrapper()
} 
