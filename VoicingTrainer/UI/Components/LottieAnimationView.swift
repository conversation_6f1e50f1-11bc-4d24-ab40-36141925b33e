import SwiftUI
import Lottie

#if os(macOS)
import AppKit
#else
import UIKit
#endif

/// 🎬 专用的 Lottie 动画组件
/// 负责加载和播放指定的动画文件
struct LottieAnimationView: View {
    
    // MARK: - 配置
    let animationFileName: String
    let size: CGSize
    let loopMode: LottieLoopMode
    let animationSpeed: CGFloat
    let scale: CGFloat  // 🎯 新增：动画缩放比例
    let onComplete: (() -> Void)?
    
    // MARK: - 状态
    @State private var animationKey = UUID()
    
    // MARK: - 初始化
    init(
        fileName: String,
        size: CGSize = CGSize(width: 200, height: 200),
        loopMode: LottieLoopMode = .playOnce,
        animationSpeed: CGFloat = 1.0,
        scale: CGFloat = 1.0,  // 🎯 新增：默认不缩放
        onComplete: (() -> Void)? = nil
    ) {
        self.animationFileName = fileName
        self.size = size
        self.loopMode = loopMode
        self.animationSpeed = animationSpeed
        self.scale = scale
        self.onComplete = onComplete
    }
    
    // MARK: - 视图
    var body: some View {
        Group {
            if let animation = loadAnimation() {
                LottieView(animation: animation)
                    .configure { animationView in
                        animationView.loopMode = loopMode
                        animationView.animationSpeed = animationSpeed
                        animationView.contentMode = .scaleAspectFit
                        
                        print("🎬 LottieAnimationView: 开始播放 \(animationFileName)")
                        
                        // 播放动画并处理完成回调
                        animationView.play { finished in
                            if finished {
                                print("🎬 LottieAnimationView: \(animationFileName) 播放完成")
                                DispatchQueue.main.async {
                                    onComplete?()
                                }
                            }
                        }
                    }
                    .frame(width: size.width, height: size.height)
                    .scaleEffect(scale)  // 🎯 应用缩放效果
                    .id(animationKey) // 使用UUID确保每次都重新创建
                    .allowsHitTesting(false)
                    .onAppear {
                        print("🎬 LottieAnimationView: \(animationFileName) 视图出现")
                    }
                    .onDisappear {
                        print("🎬 LottieAnimationView: \(animationFileName) 视图消失")
                    }
            } else {
                // 动画加载失败时的占位视图
                Rectangle()
                    .fill(Color.clear)
                    .frame(width: size.width, height: size.height)
                    .onAppear {
                        print("❌ LottieAnimationView: 无法加载 \(animationFileName)")
                    }
            }
        }
    }
    
    // MARK: - 公共方法
    
    /// 重新触发动画
    func replay() {
        animationKey = UUID()
    }
    
    // MARK: - 私有方法
    
    /// 加载动画文件
    private func loadAnimation() -> LottieAnimation? {
        // 尝试加载 .json 文件
        if let path = Bundle.main.path(forResource: animationFileName, ofType: "json") {
            if let animation = LottieAnimation.filepath(path) {
                print("🎬 LottieAnimationView: 成功加载 \(animationFileName).json")
                return animation
            }
        }
        
        // 尝试加载 .lottie 文件
        if let path = Bundle.main.path(forResource: animationFileName, ofType: "lottie") {
            if let animation = LottieAnimation.filepath(path) {
                print("🎬 LottieAnimationView: 成功加载 \(animationFileName).lottie")
                return animation
            }
        }
        
        // 尝试直接通过名称加载（Lottie 4.x 的方式）
        if let animation = LottieAnimation.named(animationFileName) {
            print("🎬 LottieAnimationView: 成功加载 \(animationFileName) (named)")
            return animation
        }
        
        print("❌ LottieAnimationView: 无法加载 \(animationFileName) 动画文件")
        return nil
    }
}

// MARK: - 便利构造器

extension LottieAnimationView {
    
    /// 创建和弦匹配动画 (Circular Burst.json)
    static func chordMatchAnimation(scale: CGFloat = 1.0, onComplete: (() -> Void)? = nil) -> LottieAnimationView {
        LottieAnimationView(
            fileName: "Circular Burst",
            size: CGSize(width: 400, height: 400), // 🎯 基础容器大小
            loopMode: LottieLoopMode.playOnce,
            animationSpeed: 1.0,
            scale: scale,  // 🎯 支持缩放
            onComplete: onComplete
        )
    }

    /// 创建计分动画 (Thumbs Up.json) - 整个进行完成时播放
    static func scoreAnimation(onComplete: (() -> Void)? = nil) -> LottieAnimationView {
        print("🎆 创建 Thumbs Up 动画组件")
        return LottieAnimationView(
            fileName: "Thumbs Up",
            size: CGSize(width: 100, height: 100), // 🎯 增大尺寸便于调试
            loopMode: LottieLoopMode.playOnce,
            animationSpeed: 1.0,
            onComplete: onComplete
        )
    }

    /// 🎯 创建进行匹配动画 (Success Burst.json) - 整个进行完成时播放
    static func progressionMatchAnimation(scale: CGFloat = 1.0, onComplete: (() -> Void)? = nil) -> LottieAnimationView {
        print("🎉 创建 Success Burst 进行完成动画组件")
        return LottieAnimationView(
            fileName: "Success Burst",
            size: CGSize(width: 300, height: 300), // 🎯 较大尺寸，适合进行完成的庆祝效果
            loopMode: LottieLoopMode.playOnce,
            animationSpeed: 1.0,
            scale: scale,  // 🎯 支持缩放
            onComplete: onComplete
        )
    }

    /// 🎵 创建单个和弦匹配动画 (Circular Burst.json) - 每个和弦正确时播放
    /*
    static func singleChordMatchAnimation(scale: CGFloat = 1.0, onComplete: (() -> Void)? = nil) -> LottieAnimationView {
        print("🎵 创建 Circular Burst 单和弦匹配动画组件")
        return LottieAnimationView(
            fileName: "Circular Burst",
            size: CGSize(width: 300, height: 300), // 🎯 中等尺寸，适合单个和弦的反馈
            loopMode: LottieLoopMode.playOnce,
            animationSpeed: 1.2, // 🎯 稍快一些，因为会频繁播放
            scale: scale,  // 🎯 支持缩放
            onComplete: onComplete
        )
    }
    */
    
    /// 创建自定义动画
    static func custom(
        fileName: String,
        size: CGSize,
        loopMode: LottieLoopMode = .playOnce,
        speed: CGFloat = 1.0,
        scale: CGFloat = 1.0,  // 🎯 支持缩放
        onComplete: (() -> Void)? = nil
    ) -> LottieAnimationView {
        LottieAnimationView(
            fileName: fileName,
            size: size,
            loopMode: loopMode,
            animationSpeed: speed,
            scale: scale,
            onComplete: onComplete
        )
    }
}

// MARK: - 动画容器组件

/// 🎬 动画容器 - 用于管理动画的显示和隐藏
struct AnimationContainer: View {
    
    @Binding var isVisible: Bool
    let animation: LottieAnimationView
    let position: CGPoint
    
    var body: some View {
        ZStack {
            if isVisible {
                animation
                    .position(position)
            }
        }
    }
}

// MARK: - 预览

#Preview("Lottie动画测试") {
    ZStack {
        Color.gray.opacity(0.1)
            .ignoresSafeArea()
        
        VStack(spacing: 50) {
            Text("Lottie 动画测试")
                .font(.title)
            
            // 和弦匹配动画测试
            LottieAnimationView.chordMatchAnimation {
                print("和弦匹配动画完成")
            }
            
            // 计分动画测试
            LottieAnimationView.scoreAnimation {
                print("计分动画完成")
            }
            
            // 自定义动画测试
            LottieAnimationView.custom(
                fileName: "Confetti",
                size: CGSize(width: 200, height: 200)
            ) {
                print("自定义动画完成")
            }
        }
    }
}
