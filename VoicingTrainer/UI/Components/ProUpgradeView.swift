import SwiftUI
import StoreKit

// MARK: - Pro升级提示视图
/*
 
 Text("Play By Ear:和众多专业老师的练习理念完全一致")
 Text("升级为Pro版:解锁所有高级和弦Voicing和各种酷炫的和弦进行")
 Text("体会大师般的非凡的和声，像一个专家一样演奏")
 Text("在12个调上熟练演奏各种和弦和voicing")
 
 Text("一次购买即可，不需要按月付费")
 Button("只需 $8.99 升级为Pro版"){
     
 }
 Button("谢谢,我继续使用免费版"){
 
 */

struct ProUpgradeView: View {
    @Environment(\.dismiss) private var dismiss
    @StateObject private var storeKitManager = StoreKitManager.shared

    let featureName: String
    let onUpgrade: (() -> Void)?
    let onDismiss: (() -> Void)?  // 🔧 新增：关闭回调

    @State private var showingAlert = false
    @State private var alertMessage = ""

    init(featureName: String, onUpgrade: (() -> Void)? = nil, onDismiss: (() -> Void)? = nil) {
        self.featureName = featureName
        self.onUpgrade = onUpgrade
        self.onDismiss = onDismiss
    }
    
    var body: some View {
        VStack(spacing: 24) {
            // 标题
            VStack(spacing: 8) {
                Image(systemName: "lock.fill")
                    .font(.system(size: 48))
                    .foregroundColor(.orange)
                
                Text("升级到Pro版")
                    .font(.title.bold())
                    .foregroundColor(.primary)
                
                Text("解锁 \(featureName)")
                    .font(.title3)
                    .foregroundColor(.secondary)
            }
            
            // 功能说明
            VStack(alignment: .leading, spacing: 16) {
                Text("Pro版功能包括：")
                    .font(.headline)
                    .foregroundColor(.primary)
                
                VStack(alignment: .leading, spacing: 12) {
                    FeatureRow(icon: "music.note", text: "所有练习项目")
                    FeatureRow(icon: "music.quarternote.3", text: "高级和弦配置")
                    FeatureRow(icon: "arrow.triangle.2.circlepath", text: "自定义和弦进行")
                    FeatureRow(icon: "infinity", text: "无限制使用")
                    FeatureRow(icon: "wand.and.stars", text: "专业练习模式")
                }
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color.blue.opacity(0.1))
            )
            
            // 按钮区域
            VStack(spacing: 12) {
                if storeKitManager.isLoading {
                    // 加载状态
                    VStack(spacing: 8) {
                        ProgressView()
                        Text("加载产品信息...")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    .padding()
                } else if let proProduct = storeKitManager.product(for: .pro) {
                    // 升级按钮（使用真实 StoreKit）
                    Button(action: {
                        Task {
                            await purchaseProVersion(proProduct)
                        }
                    }) {
                        HStack {
                            if case .purchasing = storeKitManager.purchaseState {
                                ProgressView()
                                    .scaleEffect(0.8)
                                    .foregroundColor(.white)
                            } else {
                                Image(systemName: "crown.fill")
                            }
                            
                            Text("升级到Pro版")
                                .fontWeight(.semibold)
                            
                            Text(storeKitManager.priceText(for: proProduct))
                                .fontWeight(.bold)
                        }
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(
                            LinearGradient(
                                colors: [Color.blue, Color.purple],
                                startPoint: .leading,
                                endPoint: .trailing
                            )
                        )
                        .cornerRadius(12)
                    }
                    .disabled({
                        if case .purchasing = storeKitManager.purchaseState {
                            return true
                        }
                        return false
                    }())
                    
                    // 恢复购买按钮
                    Button("恢复购买") {
                        Task {
                            await restorePurchases()
                        }
                    }
                    .font(.caption)
                    .foregroundColor(.blue)
                    .disabled(storeKitManager.isLoading)
                } else {
                    // 产品加载失败
                    VStack(spacing: 8) {
                        Text("暂时无法加载产品信息")
                            .font(.caption)
                            .foregroundColor(.red)

                        if let errorMessage = storeKitManager.errorMessage {
                            Text(errorMessage)
                                .font(.caption2)
                                .foregroundColor(.secondary)
                                .multilineTextAlignment(.center)
                        }

                        HStack(spacing: 12) {
                            Button("重试") {
                                Task {
                                    await storeKitManager.requestProducts()
                                }
                            }
                            .font(.caption)
                            .foregroundColor(.blue)

                            #if DEBUG
                            Button("开发模式解锁") {
                                storeKitManager.simulatePurchase(.pro)
                                alertMessage = "开发模式：Pro功能已解锁！"
                                showingAlert = true
                                onUpgrade?()
                                DispatchQueue.main.asyncAfter(deadline: .now() + 1.5) {
                                    if let onDismiss = onDismiss {
                                        onDismiss()
                                    } else {
                                        dismiss()
                                    }
                                }
                            }
                            .font(.caption)
                            .foregroundColor(.green)
                            #endif
                        }

                        #if DEBUG
                        Text("需要在App Store Connect中设置内购产品")
                            .font(.caption2)
                            .foregroundColor(.orange)
                            .multilineTextAlignment(.center)
                            .padding(.top, 4)
                        #endif
                    }
                }
                
                // 稍后再说按钮
                Button("稍后再说") {
                    // 🔧 优先使用回调，如果没有回调则使用dismiss
                    if let onDismiss = onDismiss {
                        onDismiss()
                    } else {
                        dismiss()
                    }
                }
                .foregroundColor(.gray)
            }
        }
        .padding(24)
        .frame(width: 320)
        .background(.regularMaterial)
        .cornerRadius(16)
        .shadow(radius: 8)
        .alert("购买提示", isPresented: $showingAlert) {
            Button("确定", role: .cancel) { }
        } message: {
            Text(alertMessage)
        }
        .onReceive(storeKitManager.$purchaseState) { state in
            handlePurchaseStateChange(state)
        }
    }
    
    // MARK: - Private Methods
    
    private func purchaseProVersion(_ product: Product) async {
        do {
            try await storeKitManager.purchase(product)
        } catch {
            alertMessage = "购买失败: \(error.localizedDescription)"
            showingAlert = true
        }
    }
    
    private func restorePurchases() async {
        await storeKitManager.restorePurchases()
    }
    
    private func handlePurchaseStateChange(_ state: PurchaseState) {
        switch state {
        case .purchased:
            alertMessage = "购买成功！Pro功能已解锁。"
            showingAlert = true
            onUpgrade?()
            DispatchQueue.main.asyncAfter(deadline: .now() + 1.5) {
                if let onDismiss = onDismiss {
                    onDismiss()
                } else {
                    dismiss()
                }
            }

        case .restored:
            alertMessage = "购买已恢复！Pro功能已解锁。"
            showingAlert = true
            onUpgrade?()
            DispatchQueue.main.asyncAfter(deadline: .now() + 1.5) {
                if let onDismiss = onDismiss {
                    onDismiss()
                } else {
                    dismiss()
                }
            }
            
        case .failed(let error):
            switch error {
            case .userCancelled:
                // 用户取消，不显示错误
                break
            default:
                alertMessage = error.localizedDescription
                showingAlert = true
            }
            
        case .notPurchased, .purchasing:
            break
        }
    }
}

// MARK: - 功能行视图
struct FeatureRow: View {
    let icon: String
    let text: String
    
    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: icon)
                .font(.title3)
                .foregroundColor(.blue)
                .frame(width: 24)
            
            Text(text)
                .font(.body)
                .foregroundColor(.primary)
            
            Spacer()
        }
    }
}

// MARK: - 锁定状态指示器
struct LockedItemIndicator: View {
    let description: String
    
    var body: some View {
        HStack(spacing: 8) {
            Image(systemName: "lock.fill")
                .font(.caption)
                .foregroundColor(.orange)
            
            Text(description)
                .font(.caption)
                .foregroundColor(.orange)
                .fontWeight(.medium)
        }
        .padding(.horizontal, 8)
        .padding(.vertical, 4)
        .background(
            Capsule()
                .fill(Color.orange.opacity(0.1))
        )
        .overlay(
            Capsule()
                .stroke(Color.orange.opacity(0.3), lineWidth: 1)
        )
    }
}

// 预览
#Preview {
    ZStack {
        Color.black.opacity(0.3)
            .ignoresSafeArea()
        
        ProUpgradeView(featureName: "高级和弦练习")
    }
} 
