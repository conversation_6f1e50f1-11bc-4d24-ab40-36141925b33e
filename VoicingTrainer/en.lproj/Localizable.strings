/* 
  Localizable.strings (English)
  VoicingTrainer
  
  Created by <PERSON> on 2025/1/5.
*/

// MARK: - Game buttons
"start_practice" = "Start Practice";
"start_level" = "Start Level";
"select_level" = "Select Level";
"next_level" = "Next Level";
"replay" = "Replay";
"menu" = "Menu";

// MARK: - Game states
"press_start" = "Press Start!";
"select_level_first" = "Select Level";
"playing" = "Playing...";
"get_ready" = "Get Ready!";
"complete" = "Complete!";
"listen_and_play" = "Listen and Play:";

// MARK: - Game interface
"level_complete" = "🎉 Level Complete!";
"correct" = "Correct";
"wrong" = "Wrong";
"round" = "Round";
"time_remaining" = "Time Remaining";

// MARK: - Note related
"middle_c" = "Middle C";
"note_name" = "Note Name";

// MARK: - Common buttons
"done" = "Done";
"cancel" = "Cancel";
"save" = "Save";
"clear" = "Clear";
"ok" = "OK";
"retry" = "Retry";
"close" = "Close";
"select" = "Select";
"start" = "Start";
"stop" = "Stop";
"reset" = "Reset";

// MARK: - Chord related
"select_chord" = "Select Chord";
"loading_chords" = "Loading chords...";
"chord_browser" = "Chord Browser";
"voicing" = "Voicing";
"create_voicing" = "Create Voicing";
"intervals" = "Intervals";
"difficulty" = "Difficulty";
"family" = "Family";
"structure" = "Structure";
"hands" = "Hands";
"chord_count" = "Chord Count";

// MARK: - Progression related
"select_progression" = "Select a Progression to Practice";
"progressions" = "Progressions";
"styles" = "Styles";
"mode" = "Mode";
"playback" = "Playback";
"workout_types" = "Workout Types";
"practice_pattern" = "Practice Pattern";
"settings" = "Settings";
"bpm" = "BPM";
"infinite_loop" = "Infinite Loop";
"ready_to_play" = "Ready to Play";
"current_chord" = "Current Chord";
"recorded_chords" = "Recorded Chords";
"save_progression" = "Save Progression";
"create_new_progressions" = "Create New Progressions";

// MARK: - Recording features
"pedal_pressed_record" = "Pedal Pressed Record";
"waiting_chord_play" = "Waiting for chord play...";
"chord_components" = "Components";
"no_chords_recorded" = "No chords recorded yet";
"play_chord_then_pedal" = "Play chord, then press pedal to record";
"clear_recording" = "Clear Recording";
"recorded_count" = "Recorded %d chords";

// MARK: - Usage instructions
"usage_instructions" = "Usage Instructions";
"step1_play_chord" = "Play Chord";
"step2_press_pedal" = "Press Pedal to Record";

// MARK: - Game status messages
"prepare_practice" = "Prepare Practice";
"playing_status" = "Playing...";
"waiting_input" = "Waiting Input: %@";
"response_time" = "Response Time...";
"practice_complete" = "Practice Complete!";
"progress_text" = "Progress: %@";
"infinite_loop_playing" = "Infinite Loop Playing...";
"infinite_loop_current_chord" = "Infinite Loop Mode - Current %d chord";

// MARK: - Navigation titles
"navigation_help" = "Help";
"navigation_audio_settings" = "Audio Settings";
"navigation_save_progression" = "Save Progression";
"navigation_select_chord" = "Select Chord";
"navigation_power_test_results" = "Power Test Results";
"navigation_test_details" = "Test Details";
"navigation_songs" = "Songs";

// MARK: - Settings interface
"playback_volume" = "Playback Volume";
"volume_label" = "Volume: %d";
"clear_all_achievement" = "Clear All Achievement";
"audio_settings" = "Audio Settings";
"version" = "Version 2.1.0";
"sustain_pedal" = "Sustain Pedal: ";

// MARK: - Errors and tips
"error" = "Error";
"tip" = "Tip";
"loading" = "Loading...";
"no_level_selected" = "Please select practice group";
"select_chord_group_tip" = "Click top-left button to select chord group";
"select_voicing_start_game" = "Select a voicing and start the game";
"permanent_wait_mode" = "Permanent Wait Mode";
"select_progression_start" = "Select progression to start practice";

// MARK: - Statistics
"current_voicing" = "Current: %d";
"right_notes" = "Right Notes";
"error_notes" = "Error Notes";
"right_chords" = "Right Chords";
"error_chords" = "Error Chords";
"current_round" = "Current Round";
"accuracy" = "Accuracy";

// MARK: - Help content
"help_loading" = "Loading help content...";
"help_load_failed" = "Failed to load help content";
"welcome_title" = "Welcome to VoicingWorkout";
"getting_started" = "Getting Started";
"midi_setup" = "MIDI Setup";
"practice_modes" = "Practice Modes";

// MARK: - Testing and debugging
"test_reminder" = "Test Reminder";
"run_test" = "Run Test";
"remind_later" = "Remind Later";
"force_optimization" = "Force Optimization";
"reset_monitoring" = "Reset Monitoring";
"clear_results" = "Clear Results";
"no_test_data" = "No test data";
"start_test_tip" = "Click start test button to perform power test";
"test_particle" = "🎆 Test Particle";
"enable_line_particles" = "🎆 Enable Line Particles";
"disable_line_particles" = "🎆 Disable Line Particles";
"set_c_major" = "Set C Major Chord";
"trigger_perfect_match" = "Trigger Perfect Match";

// MARK: - Battery and system status
"charging" = "Charging";
"full" = "Full";
"unplugged" = "Unplugged";
"unknown" = "Unknown";
"thermal_normal" = "Normal";
"thermal_fair" = "Fair";
"thermal_serious" = "Serious";
"thermal_critical" = "Critical";

// MARK: - About interface
"app_name" = "VoicingWorkout";

// MARK: - Congratulation messages
"perfect_performance" = "🎉 Perfect! Outstanding Performance!";
"excellent_job" = "⭐️ Excellent! Great Job!";
"good_work" = "👍 Good Work! Keep Practicing!";
"not_bad" = "💪 Not Bad! You Can Do Better!";
"keep_trying" = "🎯 Keep Trying! Practice Makes Perfect!";

// MARK: - Progression naming
"name_your_progression" = "Name your chord progression";
"name_limit" = "Name limit: within 64 characters";
"note_count" = "Note count: %d";

// MARK: - Complexity and categories
"all_structures" = "All Structures";
"complexity_count" = "%d items";
"category_chord_count" = "%d";

// MARK: - Debug instructions
"debug_instructions_title" = "Usage Instructions:";
"debug_instruction_1" = "1. Click 'Set C Major Chord' to show expected notes (blue)";
"debug_instruction_2" = "2. Click 'Trigger Perfect Match' will trigger green particle explosion";
"debug_instruction_3" = "3. 🎆 Line particles: Colorful particles gently drifting from lines";
"debug_instruction_4" = "4. Particle features: No gravity, gradually slowing speed, gentle drift";

// MARK: - Developer tools
"developer_internal_tool" = "Developer Internal Tool";
"press_keys_capture" = "Press keys and pedal to capture notes";
"captured_notes" = "Captured notes:";
"lowest_note" = "Lowest note (root_note): %@";
"intervals_label" = "Intervals: %@";
"generated_voicing_data" = "Generated Voicing data:";
"save_result" = "Save Result";

// MARK: - Countdown
"countdown_number" = "%d";

// MARK: - Others
"level_number" = "Level %d";
"playing_chord_index" = "Playing %d/%d";
"current_status" = "Current: %@";
"round_info" = "(%d/%d)";
"details" = "Details";

// MARK: - In-App Purchase
"PRO_VERSION" = "VoicingTrainer Pro";
"JAZZ_PROGRESSIONS_PACK" = "Jazz Progressions Pack";
"MASTER_VOICINGS_PACK" = "Master Voicings Pack";
"PRODUCT_NOT_FOUND" = "Product not found";
"PURCHASE_FAILED" = "Purchase failed: %@";
"VERIFICATION_FAILED" = "Purchase verification failed";
"USER_CANCELLED" = "Purchase cancelled by user";
"NETWORK_ERROR" = "Network error";
"UNKNOWN_ERROR" = "Unknown error occurred";
"NO_PURCHASES_TO_RESTORE" = "No purchases to restore"; 