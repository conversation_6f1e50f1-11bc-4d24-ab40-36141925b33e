#!/usr/bin/env python3
"""
动态和弦名称生成器
根据新的数据格式生成移调后的和弦名称
"""

class ChordNameGenerator:
    def __init__(self):
        self.note_names = ["C", "C#", "D", "D#", "E", "F", "F#", "G", "G#", "A", "A#", "B"]
        self.note_to_pitch_class = {name: i for i, name in enumerate(self.note_names)}
    
    def note_name_to_pitch_class(self, note_name):
        """将音符名称转换为音高类别"""
        note_name = note_name.upper()
        if note_name in self.note_to_pitch_class:
            return self.note_to_pitch_class[note_name]
        return 0
    
    def pitch_class_to_note_name(self, pitch_class):
        """将音高类别转换为音符名称"""
        return self.note_names[pitch_class % 12]
    
    def generate_chord_name(self, progression_root, chord_data, transposition_semitones=0):
        """
        生成移调后的和弦名称
        
        参数:
        - progression_root: 和弦进行的根音 (如"C")
        - chord_data: 和弦数据 (新格式)
        - transposition_semitones: 移调半音数
        
        返回: 移调后的和弦名称
        """
        # 计算新的进行根音
        old_root_pc = self.note_name_to_pitch_class(progression_root)
        new_root_pc = (old_root_pc + transposition_semitones) % 12
        new_progression_root = self.pitch_class_to_note_name(new_root_pc)
        
        # 计算和弦根音
        chord_root_pc = (new_root_pc + chord_data["rootOffset"]) % 12
        chord_root_name = self.pitch_class_to_note_name(chord_root_pc)
        
        # 获取和弦类型
        chord_type = chord_data["chordType"]
        
        # 构建基本和弦名称
        chord_name = f"{chord_root_name}{chord_type}"
        
        # 如果有低音，添加Slash和弦部分
        if "bassOffset" in chord_data:
            bass_pc = (new_root_pc + chord_data["bassOffset"]) % 12
            bass_name = self.pitch_class_to_note_name(bass_pc)
            chord_name = f"{chord_name}/{bass_name}"
        
        return chord_name
    
    def generate_progression_names(self, progression_data, transposition_semitones=0):
        """
        生成整个和弦进行的移调名称
        
        参数:
        - progression_data: 和弦进行数据 (新格式)
        - transposition_semitones: 移调半音数
        
        返回: 移调后的和弦名称列表
        """
        progression_root = progression_data["progression"]["rootNote"]
        chords = progression_data["progression"]["chords"]
        
        chord_names = []
        for chord in chords:
            name = self.generate_chord_name(progression_root, chord, transposition_semitones)
            chord_names.append(name)
        
        return chord_names

# 测试函数
def test_chord_name_generation():
    generator = ChordNameGenerator()
    
    # 模拟一个和弦进行数据 (Big.progression 的简化版本)
    test_progression = {
        "progression": {
            "name": "Test Progression",
            "rootNote": "B",
            "chords": [
                {
                    "role": "Root",
                    "chordType": "min",
                    "rootOffset": 0,
                    "intervals": [0, 3, 7, 12, 15]
                },
                {
                    "role": "Normal", 
                    "chordType": "dim",
                    "rootOffset": 2,
                    "intervals": [0, 12, 15, 21, 24],
                    "bassOffset": 5  # Slash和弦
                },
                {
                    "role": "Normal",
                    "chordType": "7",
                    "rootOffset": 7,
                    "intervals": [0, 8, 12, 15, 18],
                    "bassOffset": 11  # Slash和弦
                }
            ]
        }
    }
    
    print("🎵 动态和弦名称生成测试")
    print("=" * 50)
    print(f"原始进行 ({test_progression['progression']['rootNote']}调):")
    
    # 生成原始和弦名称
    original_names = generator.generate_progression_names(test_progression, 0)
    for i, name in enumerate(original_names):
        print(f"  {i+1}. {name}")
    
    print()
    
    # 测试五度圈反向移调 (-5半音)
    circle_of_fifths_offsets = [0, -5, -10, -3, -8, -1, -6, -11, -4, -9, -2, -7]
    keys = ["B", "E", "A", "D", "G", "C", "F", "A#", "D#", "G#", "C#", "F#"]
    
    for offset, key in zip(circle_of_fifths_offsets, keys):
        print(f"{key}调 (移调 {offset:+d} 半音):")
        transposed_names = generator.generate_progression_names(test_progression, offset)
        chord_str = " - ".join(transposed_names)
        print(f"  {chord_str}")
    
    return generator

if __name__ == "__main__":
    test_chord_name_generation() 