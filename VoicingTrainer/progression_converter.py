#!/usr/bin/env python3
"""
和弦进行文件格式转换工具
将现有的绝对音符格式转换为音程偏移格式

使用方法:
python3 progression_converter.py
"""

import json
import os
import sys
from pathlib import Path
import re

class ProgressionConverter:
    def __init__(self):
        # 音符名称映射
        self.note_names = ["C", "C#", "D", "D#", "E", "F", "F#", "G", "G#", "A", "A#", "B"]
        self.note_to_pitch_class = {name: i for i, name in enumerate(self.note_names)}
        
    def midi_to_pitch_class(self, midi_note):
        """将MIDI音符转换为音高类别 (0-11)"""
        return midi_note % 12
    
    def note_name_to_pitch_class(self, note_name):
        """将音符名称转换为音高类别"""
        # 处理可能的变音记号
        note_name = note_name.upper()
        if note_name in self.note_to_pitch_class:
            return self.note_to_pitch_class[note_name]
        return 0  # 默认为C
    
    def pitch_class_to_note_name(self, pitch_class):
        """将音高类别转换为音符名称"""
        return self.note_names[pitch_class % 12]
    
    def calculate_root_offset(self, progression_root, chord_root_midi):
        """计算和弦根音相对于进行根音的偏移"""
        progression_root_pc = self.note_name_to_pitch_class(progression_root)
        chord_root_pc = self.midi_to_pitch_class(chord_root_midi)
        
        # 计算半音偏移
        offset = (chord_root_pc - progression_root_pc) % 12
        return offset
    
    def extract_chord_type(self, chord_name):
        """从和弦名称中提取和弦类型"""
        # 去掉根音，保留和弦类型
        # 例如: "Dm9" -> "m9", "G9(13)" -> "9(13)", "C6/9" -> "6/9"
        
        # 匹配模式：根音 + 可选变音记号 + 和弦类型
        pattern = r'^([A-G][#b]?)(.*)$'
        match = re.match(pattern, chord_name)
        
        if match:
            root_note = match.group(1)
            chord_type = match.group(2)
            return root_note, chord_type if chord_type else "maj"
        
        return chord_name[0], chord_name[1:] if len(chord_name) > 1 else "maj"
    
    def calculate_intervals(self, midi_notes, root_midi):
        """计算音程数组"""
        if not midi_notes:
            return []
        
        # 找到最低音符作为基准
        base_note = min(midi_notes)
        
        # 计算相对音程
        intervals = []
        for note in sorted(midi_notes):
            interval = note - base_note
            intervals.append(interval)
        
        return intervals
    
    def infer_chord_root(self, chord_name, midi_notes):
        """推断和弦根音"""
        root_note_name, chord_type = self.extract_chord_type(chord_name)
        
        # 将根音名称转换为音高类别
        root_pc = self.note_name_to_pitch_class(root_note_name)
        
        # 在MIDI音符中查找匹配的根音
        for midi_note in midi_notes:
            if self.midi_to_pitch_class(midi_note) == root_pc:
                return midi_note
        
        # 如果没找到，使用最低音符
        return min(midi_notes) if midi_notes else 60
    
    def convert_chord(self, chord_data, progression_root):
        """转换单个和弦"""
        chord_name = chord_data["name"]
        midi_notes = chord_data["notes"]
        
        # 推断和弦根音
        chord_root_midi = self.infer_chord_root(chord_name, midi_notes)
        
        # 计算根音偏移
        root_offset = self.calculate_root_offset(progression_root, chord_root_midi)
        
        # 提取和弦类型
        root_note_name, chord_type = self.extract_chord_type(chord_name)
        
        # 计算音程
        intervals = self.calculate_intervals(midi_notes, chord_root_midi)
        
        return {
            "role": chord_data["role"],
            "chordType": chord_type,
            "rootOffset": root_offset,
            "intervals": intervals
        }
    
    def convert_progression(self, old_data):
        """转换整个和弦进行"""
        progression = old_data["progression"]
        
        # 提取基本信息
        name = progression["name"]
        root_note = progression["rootNote"]
        
        # 转换和弦
        new_chords = []
        for chord in progression["chords"]:
            new_chord = self.convert_chord(chord, root_note)
            new_chords.append(new_chord)
        
        # 构建新格式
        new_progression = {
            "progression": {
                "name": name,
                "rootNote": root_note,
                "chords": new_chords
            }
        }
        
        return new_progression
    
    def convert_file(self, input_file, output_file):
        """转换单个文件"""
        try:
            print(f"转换: {input_file} -> {output_file}")
            
            # 读取原文件
            with open(input_file, 'r', encoding='utf-8') as f:
                old_data = json.load(f)
            
            # 转换格式
            new_data = self.convert_progression(old_data)
            
            # 写入新文件
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(new_data, f, indent=2, ensure_ascii=False)
            
            print(f"✅ 转换成功")
            return True
            
        except Exception as e:
            print(f"❌ 转换失败: {e}")
            return False
    
    def convert_directory(self, input_dir, output_dir):
        """转换整个目录"""
        input_path = Path(input_dir)
        output_path = Path(output_dir)
        
        # 确保输出目录存在
        output_path.mkdir(parents=True, exist_ok=True)
        
        # 查找所有.progression文件
        progression_files = list(input_path.glob("*.progression"))
        
        if not progression_files:
            print(f"❌ 在 {input_dir} 中没有找到 .progression 文件")
            return False
        
        print(f"找到 {len(progression_files)} 个和弦进行文件")
        
        success_count = 0
        for file_path in progression_files:
            output_file = output_path / file_path.name
            if self.convert_file(file_path, output_file):
                success_count += 1
        
        print(f"\n转换完成: {success_count}/{len(progression_files)} 个文件成功")
        return success_count == len(progression_files)

def main():
    converter = ProgressionConverter()
    
    # 获取当前脚本所在目录
    script_dir = Path(__file__).parent
    
    # 设置输入和输出目录
    input_dir = script_dir / "Resources" / "Data" / "progressions"
    output_dir = script_dir / "Resources" / "Data" / "progressions_new"
    
    print("🎵 和弦进行文件格式转换工具")
    print("=" * 50)
    print(f"输入目录: {input_dir}")
    print(f"输出目录: {output_dir}")
    print()
    
    if not input_dir.exists():
        print(f"❌ 输入目录不存在: {input_dir}")
        return
    
    # 执行转换
    success = converter.convert_directory(input_dir, output_dir)
    
    if success:
        print("\n🎉 所有文件转换完成！")
        print(f"新格式文件保存在: {output_dir}")
        print("\n请检查转换结果，确认无误后可以替换原文件。")
    else:
        print("\n❌ 转换过程中出现错误，请检查日志。")

if __name__ == "__main__":
    main() 