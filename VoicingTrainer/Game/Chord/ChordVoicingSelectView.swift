import SwiftUI

struct ChordVoicingSelectView: View {
    @StateObject private var databaseManager = ChordVoicingDatabaseManager()
    @StateObject private var chordPlayer: ChordPlayer
    
    @Binding var selectedVoicing: ChordVoicing?
    @Binding var isPresented: Bool
    
    @State private var selectedQuality: QualityCategory?
    @State private var selectedChord: ChordVoicingItem?
    @State private var searchText: String = ""
    @State private var selectedStructureFilter: String? = nil
    
    let midiManager: MIDIManager
    
    init(
        selectedVoicing: Binding<ChordVoicing?>,
        isPresented: Binding<Bool>,
        midiManager: MIDIManager
    ) {
        self._selectedVoicing = selectedVoicing
        self._isPresented = isPresented
        self.midiManager = midiManager
        self._chordPlayer = StateObject(wrappedValue: ChordPlayer(midiManager: midiManager))
    }
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // 顶部搜索和过滤器
                headerSection
                
                if selectedQuality == nil {
                    // 主界面：显示音质分类
                    mainQualitySelection
                } else {
                    // 详细界面：显示选中音质的和弦
                    detailChordSelection
                }
            }
            .navigationTitle("选择和弦")
            .toolbar {
                #if os(iOS)
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("取消") {
                        isPresented = false
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("确定") {
                        if let selectedChord = selectedChord {
                            selectedVoicing = selectedChord.toChordVoicing()
                        }
                        isPresented = false
                    }
                    .disabled(selectedChord == nil)
                    .fontWeight(.semibold)
                }
                #else
                ToolbarItem(placement: .cancellationAction) {
                    Button("取消") {
                        isPresented = false
                    }
                }
                
                ToolbarItem(placement: .confirmationAction) {
                    Button("确定") {
                        if let selectedChord = selectedChord {
                            selectedVoicing = selectedChord.toChordVoicing()
                        }
                        isPresented = false
                    }
                    .disabled(selectedChord == nil)
                    .fontWeight(.semibold)
                }
                #endif
            }
        }
        .onAppear {
            // 如果有当前选择的和弦，尝试定位到对应的音质分类
            locateCurrentSelection()
        }
    }
    
    // MARK: - Header Section
    
    private var headerSection: some View {
        VStack(spacing: 12) {
            // 搜索框
            HStack {
                Image(systemName: "magnifyingglass")
                    .foregroundColor(.secondary)
                
                TextField("搜索和弦...", text: $searchText)
                    .textFieldStyle(.plain)
                
                if !searchText.isEmpty {
                    Button("清除") {
                        searchText = ""
                    }
                    .font(.caption)
                    .foregroundColor(.blue)
                }
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
            .background(Color.gray.opacity(0.1))
            .cornerRadius(8)
            
            // 面包屑导航和过滤器
            HStack {
                // 面包屑导航
                if let selectedQuality = selectedQuality {
                    Button(action: {
                        withAnimation(.easeInOut(duration: 0.3)) {
                            self.selectedQuality = nil
                            self.selectedChord = nil
                        }
                    }) {
                        HStack(spacing: 4) {
                            Image(systemName: "chevron.left")
                                .font(.caption)
                            Text("音质分类")
                                .font(.caption)
                        }
                        .foregroundColor(.blue)
                    }
                    
                    Image(systemName: "chevron.right")
                        .font(.caption2)
                        .foregroundColor(.secondary)
                    
                    Text(selectedQuality.name)
                        .font(.caption)
                        .fontWeight(.medium)
                }
                
                Spacer()
                
                // 结构过滤器
                if selectedQuality != nil {
                    HStack(spacing: 4) {
                        Image(systemName: "line.3.horizontal.decrease.circle")
                            .font(.caption)
                            .foregroundColor(.blue)
                        
                        Picker("结构过滤", selection: $selectedStructureFilter) {
                            Text("全部结构").tag(nil as String?)
                            
                            ForEach(databaseManager.structureCategories, id: \.id) { category in
                                Text(category.name).tag(category.id as String?)
                            }
                        }
                        .pickerStyle(.menu)
                        .font(.caption)
                        .foregroundColor(.blue)
                    }
                }
            }
            .animation(.easeInOut(duration: 0.2), value: selectedQuality)
        }
        .padding(.horizontal)
        .padding(.top, 8)
    }
    
    // MARK: - Main Quality Selection
    
    private var mainQualitySelection: some View {
        ScrollView {
            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 16) {
                ForEach(filteredQualityCategories, id: \.id) { category in
                    QualityCategoryCard(
                        category: category,
                        isSelected: false
                    ) {
                        withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
                            selectedQuality = category
                            selectedChord = nil
                        }
                    }
                }
            }
            .padding(.horizontal)
            .padding(.top, 16)
        }
    }
    
    // MARK: - Detail Chord Selection
    
    private var detailChordSelection: some View {
        ScrollView {
            LazyVStack(spacing: 16) {
                ForEach(groupedChordData, id: \.id) { structureGroup in
                    StructureGroupView(
                        structureGroup: structureGroup,
                        selectedChord: $selectedChord,
                        onChordTap: { chord in
                            selectedChord = chord
                        },
                        onChordPlay: { chord in
                            playChord(chord)
                        }
                    )
                }
            }
            .padding(.horizontal)
            .padding(.top, 16)
        }
    }
    
    // MARK: - Computed Properties
    
    private var filteredQualityCategories: [QualityCategory] {
        if searchText.isEmpty {
            return databaseManager.qualityCategories
        } else {
            return databaseManager.qualityCategories.filter { category in
                category.name.localizedCaseInsensitiveContains(searchText) ||
                category.description.localizedCaseInsensitiveContains(searchText) ||
                category.chords.contains { chord in
                    chord.name.localizedCaseInsensitiveContains(searchText) ||
                    chord.suffix.localizedCaseInsensitiveContains(searchText)
                }
            }
        }
    }
    
    private var groupedChordData: [GroupedChordData] {
        guard let selectedQuality = selectedQuality else { return [] }
        
        // 获取当前音质的所有和弦
        var chords = selectedQuality.chords
        
        // 应用结构过滤器
        if let structureFilter = selectedStructureFilter {
            chords = chords.filter { $0.tags.structure == structureFilter }
        }
        
        // 应用搜索过滤器
        if !searchText.isEmpty {
            chords = chords.filter { chord in
                chord.name.localizedCaseInsensitiveContains(searchText) ||
                chord.suffix.localizedCaseInsensitiveContains(searchText) ||
                chord.description.localizedCaseInsensitiveContains(searchText)
            }
        }
        
        // 按结构分组
        let structureGroups = Dictionary(grouping: chords) { $0.tags.structure }
        
        // 转换为GroupedChordData
        var result: [GroupedChordData] = []
        
        for (structure, structureChords) in structureGroups {
            let structureName = getStructureName(structure)
            
            // 按复杂度分组
            let complexityGroups = Dictionary(grouping: structureChords) { $0.tags.complexity }
            
            var complexityGroupList: [ComplexityGroup] = []
            
            for (complexity, complexityChords) in complexityGroups {
                let complexityName = getComplexityName(complexity)
                let difficulty = getComplexityDifficulty(complexity)
                
                // 按难度数值排序
                let sortedChords = complexityChords.sorted { (chord1, chord2) in
                    let difficulty1 = chord1.metadata["difficulty"] as? Int ?? 0
                    let difficulty2 = chord2.metadata["difficulty"] as? Int ?? 0
                    return difficulty1 < difficulty2
                }
                
                let group = ComplexityGroup(
                    id: "\(structure)_\(complexity)",
                    complexityName: complexityName,
                    difficulty: difficulty,
                    chords: sortedChords
                )
                complexityGroupList.append(group)
            }
            
            // 按难度排序复杂度组
            complexityGroupList.sort { $0.difficulty < $1.difficulty }
            
            let groupedData = GroupedChordData(
                id: structure,
                structureName: structureName,
                complexityGroups: complexityGroupList
            )
            result.append(groupedData)
        }
        
        // 按结构优先级排序
        let structurePriority = ["triad", "seventh", "extended"]
        result.sort { lhs, rhs in
            let lhsIndex = structurePriority.firstIndex(of: lhs.id) ?? Int.max
            let rhsIndex = structurePriority.firstIndex(of: rhs.id) ?? Int.max
            return lhsIndex < rhsIndex
        }
        
        return result
    }
    
    // MARK: - Helper Methods
    
    private func getStructureName(_ structure: String) -> String {
        let mapping = [
            "triad": "三和弦",
            "seventh": "七和弦",
            "extended": "扩展和弦"
        ]
        return mapping[structure] ?? structure
    }
    
    private func getComplexityName(_ complexity: String) -> String {
        let mapping = [
            "basic": "基础级",
            "intermediate": "中级",
            "advanced": "高级",
            "expert": "专家级"
        ]
        return mapping[complexity] ?? complexity
    }
    
    private func getComplexityDifficulty(_ complexity: String) -> Int {
        let mapping = [
            "basic": 1,
            "intermediate": 2,
            "advanced": 3,
            "expert": 4
        ]
        return mapping[complexity] ?? 0
    }
    
    private func locateCurrentSelection() {
        // 如果有当前选择的和弦，尝试在数据库中找到对应的项目
        guard let currentVoicing = selectedVoicing else { return }
        
        if let foundChord = databaseManager.database?.chord_database.first(where: { 
            $0.id == currentVoicing.id || $0.intervals == currentVoicing.intervals 
        }) {
            // 找到对应的音质分类
            if let qualityCategory = databaseManager.qualityCategories.first(where: { 
                $0.id == foundChord.tags.quality 
            }) {
                selectedQuality = qualityCategory
                selectedChord = foundChord
            }
        }
    }
    
    private func playChord(_ chord: ChordVoicingItem) {
        let voicing = chord.toChordVoicing()
        chordPlayer.playChord(voicing, baseNote: nil, playbackType: .arpeggio_chord)
    }
}

// MARK: - Quality Category Card

struct QualityCategoryCard: View {
    let category: QualityCategory
    let isSelected: Bool
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            VStack(spacing: 12) {
                // 顶部：颜色条和图标
                HStack {
                    RoundedRectangle(cornerRadius: 2)
                        .fill(Color(hex: category.color) ?? .blue)
                        .frame(width: 4, height: 24)
                    
                    Spacer()
                    
                    Image(systemName: getQualityIcon(category.id))
                        .font(.title2)
                        .foregroundColor(Color(hex: category.color))
                }
                
                // 中部：名称和描述
                VStack(alignment: .leading, spacing: 4) {
                    HStack {
                        Text(category.name)
                            .font(.headline)
                            .fontWeight(.semibold)
                            .foregroundColor(.black)
                        
                        Spacer()
                        
                        Text("\(category.chordCount)")
                            .font(.caption)
                            .fontWeight(.medium)
                            .foregroundColor(.gray)
                            .padding(.horizontal, 6)
                            .padding(.vertical, 2)
                            .background(Color.gray.opacity(0.2))
                            .cornerRadius(8)
                    }
                    
                    Text(category.description)
                        .font(.caption)
                        .foregroundColor(.gray)
                        .multilineTextAlignment(.leading)
                        .lineLimit(2)
                }
                
                Spacer()
            }
            .padding(16)
            .frame(height: 120)
            .background(Color.white)
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(
                        isSelected ? (Color(hex: category.color) ?? .blue) : Color.gray,
                        lineWidth: isSelected ? 2 : 1
                    )
            )
            .cornerRadius(12)
            .shadow(color: .black.opacity(0.08), radius: 4, x: 0, y: 2)
        }
        .buttonStyle(.plain)
        .scaleEffect(isSelected ? 0.95 : 1.0)
        .animation(.easeInOut(duration: 0.15), value: isSelected)
    }
    
    private func getQualityIcon(_ quality: String) -> String {
        let mapping = [
            "major": "sun.max.fill",
            "minor": "moon.fill",
            "dominant": "bolt.fill",
            "suspended": "pause.fill",
            "diminished": "minus.circle.fill",
            "augmented": "plus.circle.fill"
        ]
        return mapping[quality] ?? "music.note"
    }
}

// MARK: - Structure Group View

struct StructureGroupView: View {
    let structureGroup: GroupedChordData
    @Binding var selectedChord: ChordVoicingItem?
    let onChordTap: (ChordVoicingItem) -> Void
    let onChordPlay: (ChordVoicingItem) -> Void
    
    @State private var isExpanded: Bool = true
    
    var body: some View {
        VStack(spacing: 0) {
            // 结构组标题
            Button(action: {
                withAnimation(.easeInOut(duration: 0.2)) {
                    isExpanded.toggle()
                }
            }) {
                HStack {
                    Text(structureGroup.structureName)
                        .font(.title3)
                        .fontWeight(.semibold)
                        .foregroundColor(.primary)
                    
                    Spacer()
                    
                    Image(systemName: isExpanded ? "chevron.down" : "chevron.right")
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .rotationEffect(.degrees(isExpanded ? 0 : -90))
                        .animation(.easeInOut(duration: 0.2), value: isExpanded)
                }
                .padding(.horizontal, 16)
                .padding(.vertical, 12)
                .background(Color.gray.opacity(0.1))
                .cornerRadius(8)
            }
            .buttonStyle(.plain)
            
            // 复杂度组列表
            if isExpanded {
                VStack(spacing: 12) {
                    ForEach(structureGroup.complexityGroups, id: \.id) { complexityGroup in
                        ComplexityGroupView(
                            complexityGroup: complexityGroup,
                            selectedChord: $selectedChord,
                            onChordTap: onChordTap,
                            onChordPlay: onChordPlay
                        )
                    }
                }
                .padding(.top, 8)
                .transition(.opacity.combined(with: .scale(scale: 0.95)))
            }
        }
        .animation(.easeInOut(duration: 0.2), value: isExpanded)
    }
}

// MARK: - Complexity Group View

struct ComplexityGroupView: View {
    let complexityGroup: ComplexityGroup
    @Binding var selectedChord: ChordVoicingItem?
    let onChordTap: (ChordVoicingItem) -> Void
    let onChordPlay: (ChordVoicingItem) -> Void
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            // 复杂度标题
            HStack {
                Text(complexityGroup.complexityName)
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.secondary)
                
                DifficultyIndicator(difficulty: complexityGroup.difficulty)
                
                Spacer()
                
                Text("\(complexityGroup.chordCount) 个")
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }
            
            // 和弦列表
            VStack(spacing: 8) {
                ForEach(complexityGroup.chords, id: \.id) { chord in
                    ChordItemView(
                        chord: chord,
                        isSelected: selectedChord?.id == chord.id,
                        onTap: { onChordTap(chord) },
                        onPlay: { onChordPlay(chord) }
                    )
                }
            }
        }
        .padding(.leading, 8)
    }
}

// MARK: - Chord Item View

struct ChordItemView: View {
    let chord: ChordVoicingItem
    let isSelected: Bool
    let onTap: () -> Void
    let onPlay: () -> Void
    
    var body: some View {
        HStack(spacing: 12) {
            // 播放按钮
            Button(action: onPlay) {
                Image(systemName: "play.circle.fill")
                    .font(.title3)
                    .foregroundColor(.blue)
            }
            .buttonStyle(.plain)
            
            // 和弦信息
            VStack(alignment: .leading, spacing: 2) {
                Text(chord.suffix)
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.black)
                
                Text(chord.description)
                    .font(.caption)
                    .foregroundColor(.gray)
                    .lineLimit(1)
            }
            
            Spacer()
            
            // 难度指示器
            Text("\(chord.metadata["difficulty"] as? Int ?? 0)")
                .font(.caption2)
                .fontWeight(.medium)
                .foregroundColor(.white)
                .frame(width: 20, height: 20)
                .background(getDifficultyColor(chord.metadata["difficulty"] as? Int ?? 0))
                .clipShape(Circle())
            
            // 选择指示器
            if isSelected {
                Image(systemName: "checkmark.circle.fill")
                    .font(.title3)
                    .foregroundColor(.green)
            }
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 8)
        .background(isSelected ? Color.blue.opacity(0.1) : Color.white)
        .overlay(
            RoundedRectangle(cornerRadius: 8)
                .stroke(isSelected ? Color.blue : Color.clear, lineWidth: 1)
        )
        .cornerRadius(8)
        .onTapGesture {
            onTap()
        }
    }
    
    private func getDifficultyColor(_ difficulty: Int) -> Color {
        switch difficulty {
        case 1...2: return .green
        case 3...4: return .orange
        case 5...6: return .red
        default: return .purple
        }
    }
}

// MARK: - Difficulty Indicator

struct DifficultyIndicator: View {
    let difficulty: Int
    
    var body: some View {
        HStack(spacing: 2) {
            ForEach(1...4, id: \.self) { level in
                Circle()
                    .fill(level <= difficulty ? getDifficultyColor() : Color.gray)
                    .frame(width: 6, height: 6)
            }
        }
    }
    
    private func getDifficultyColor() -> Color {
        switch difficulty {
        case 1: return .green
        case 2: return .orange
        case 3: return .red
        case 4: return .purple
        default: return .gray
        }
    }
}

 
