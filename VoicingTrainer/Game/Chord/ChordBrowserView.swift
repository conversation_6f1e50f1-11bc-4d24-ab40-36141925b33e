//
//  ChordBrowserView.swift
//  FullUI
//
//  Created by <PERSON> on 2025/5/26.
//  三级结构和弦浏览器 - 使用NavigationStack+List显示
//

import SwiftUI

/**
 * 三级结构和弦浏览器
 * 第一级：Triads, Seventh Chords, Extended Chords
 * 第二级：Major, Minor, Diminished 等
 * 第三级：各种 voicing 配置
 */
struct ChordBrowserView: View {
    @StateObject private var chordsParser = ChordsParser()
    @StateObject private var chordPlayer: ChordPlayer
    @Binding var selectedVoicing: ChordVoicing?
    
    let gameState: ChordGameState
    let onStart: () -> Void
    let onStop: () -> Void
    let onReplay: () -> Void
    
    @State private var navigationPath = NavigationPath()
    
    init(
        selectedVoicing: Binding<ChordVoicing?>,
        gameState: ChordGameState,
        midiManager: MIDIManager,
        onStart: @escaping () -> Void,
        onStop: @escaping () -> Void,
        onReplay: @escaping () -> Void
    ) {
        self._selectedVoicing = selectedVoicing
        self.gameState = gameState
        self.onStart = onStart
        self.onStop = onStop
        self.onReplay = onReplay
        self._chordPlayer = StateObject(wrappedValue: ChordPlayer(midiManager: midiManager))
    }
    
    var body: some View {
        #if os(macOS)
        // macOS上使用NavigationStack但自定义标题栏，避免标题被挤出问题
        NavigationStack(path: $navigationPath) {
            VStack(spacing: 0) {
                // 顶部自定义标题栏
                HStack {
                    Text("Chord Browser")
                        .font(.headline)
                        .fontWeight(.semibold)
                        .foregroundColor(.primary)
                    
                    Spacer()
                }
                .padding(.horizontal)
                .padding(.top)
                .padding(.bottom, 8)
                .background(.ultraThinMaterial)
                
                // 主要内容
                if chordsParser.isLoading {
                    loadingView
                } else if let errorMessage = chordsParser.errorMessage {
                    errorView(errorMessage)
                } else {
                    // 分类列表内容
                    List(chordsParser.categories) { category in
                        NavigationLink(value: category) {
                            CategoryRowView(category: category)
                        }
                    }
                    .listStyle(PlainListStyle())
                    .scrollContentBackground(.hidden)
                }
            }
            .navigationDestination(for: ChordCategory.self) { category in
                ChordTypeListView(
                    category: category,
                    selectedVoicing: $selectedVoicing,
                    chordPlayer: chordPlayer,
                    gameState: gameState
                )
            }
            .navigationDestination(for: ChordType.self) { chordType in
                VoicingListView(
                    chordType: chordType,
                    selectedVoicing: $selectedVoicing,
                    chordPlayer: chordPlayer,
                    gameState: gameState,
                    onVoicingSelected: nil // 普通浏览器不需要自动关闭
                )
            }
        }
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(.regularMaterial)
                .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
        )
        .onDisappear {
            // 当视图消失时停止播放
            chordPlayer.stopPlaying()
        }
        #else
        // iOS上保持原有的NavigationStack结构
        NavigationStack(path: $navigationPath) {
            VStack(spacing: 0) {
                // 标题和状态
                headerView
                
                // 主要内容
                if chordsParser.isLoading {
                    loadingView
                } else if let errorMessage = chordsParser.errorMessage {
                    //let _ = print("errorMessage: \(errorMessage)")
                    errorView(errorMessage)
                } else {
                    categoryListView
                }
            }
            .navigationDestination(for: ChordCategory.self) { category in
                ChordTypeListView(
                    category: category,
                    selectedVoicing: $selectedVoicing,
                    chordPlayer: chordPlayer,
                    gameState: gameState
                )
            }
            .navigationDestination(for: ChordType.self) { chordType in
                VoicingListView(
                    chordType: chordType,
                    selectedVoicing: $selectedVoicing,
                    chordPlayer: chordPlayer,
                    gameState: gameState,
                    onVoicingSelected: nil // 普通浏览器不需要自动关闭
                )
            }
        }
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(.ultraThinMaterial)
                .shadow(color: .black.opacity(0.05), radius: 4, x: 0, y: 2)
        )
        .onDisappear {
            // 当视图消失时停止播放
            chordPlayer.stopPlaying()
        }
        #endif
    }
    
    // MARK: - 子视图
    
    private var headerView: some View {
        VStack(spacing: 8) {
            Text("Chord Browser")
                .font(.headline)
                .fontWeight(.semibold)
                .foregroundColor(.primary)
        }
        .padding(.horizontal)
        .padding(.top)
    }
    
    private var loadingView: some View {
        VStack(spacing: 12) {
            ProgressView()
                .scaleEffect(0.8)
            Text("Loading chords...")
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
    
    private func errorView(_ message: String) -> some View {
        VStack(spacing: 12) {
            Image(systemName: "exclamationmark.triangle")
                .font(.title2)
                .foregroundColor(.orange)
            
            Text("Error")
                .font(.headline)
                .foregroundColor(.primary)
            
            Text(message)
                .font(.caption)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
                .padding(.horizontal)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
    
    private var categoryListView: some View {
        List(chordsParser.categories) { category in
            NavigationLink(value: category) {
                CategoryRowView(category: category)
            }
        }
        .listStyle(PlainListStyle())
        .scrollContentBackground(.hidden)
    }
}

// MARK: - 分类行视图

struct CategoryRowView: View {
    let category: ChordCategory
    
    var body: some View {
        HStack(spacing: 12) {
            // 图标
            Image(systemName: categoryIcon)
                .font(.title3)
                .foregroundColor(categoryColor)
                .frame(width: 24)
            
            VStack(alignment: .leading, spacing: 2) {
                Text(category.name)
                    .font(.body)
                    .fontWeight(.medium)
                    .foregroundColor(.primary)
                
                Text(category.description)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .lineLimit(1)
            }
            
            Spacer()
            
            // 数量标识
            Text("\(category.chordTypes.count)")
                .font(.caption)
                .fontWeight(.medium)
                .foregroundColor(.secondary)
                .padding(.horizontal, 8)
                .padding(.vertical, 4)
                .background(
                    RoundedRectangle(cornerRadius: 8)
                        .fill(.secondary.opacity(0.1))
                )
        }
        .padding(.vertical, 4)
    }
    
    private var categoryIcon: String {
        switch category.id {
        case "triads":
            return "music.note.house"
        case "seventh":
            return "music.note.tv"
        case "extended":
            return "music.note.list"
        default:
            return "music.note"
        }
    }
    
    private var categoryColor: Color {
        switch category.id {
        case "triads":
            return .blue
        case "seventh":
            return .green
        case "extended":
            return .purple
        default:
            return .gray
        }
    }
}

// MARK: - 和弦类型列表视图

struct ChordTypeListView: View {
    let category: ChordCategory
    @Binding var selectedVoicing: ChordVoicing?
    let chordPlayer: ChordPlayer
    let gameState: ChordGameState
    
    var body: some View {
        List(category.chordTypes) { chordType in
            NavigationLink(value: chordType) {
                ChordTypeRowView(chordType: chordType)
            }
        }
        .navigationTitle(category.name)
        #if os(iOS)
        .navigationBarTitleDisplayMode(.inline)
        #endif
        .listStyle(PlainListStyle())
        .scrollContentBackground(.hidden)
    }
}

// MARK: - 和弦类型行视图

struct ChordTypeRowView: View {
    let chordType: ChordType
    
    var body: some View {
        HStack(spacing: 12) {
            VStack(alignment: .leading, spacing: 2) {
                HStack(spacing: 4) {
                    Text(chordType.name)
                        .font(.body)
                        .fontWeight(.medium)
                        .foregroundColor(.primary)
                    
                    if !chordType.symbol.isEmpty {
                        Text(chordType.symbol)
                            .font(.caption)
                            .fontWeight(.bold)
                            .foregroundColor(.blue)
                            .padding(.horizontal, 6)
                            .padding(.vertical, 2)
                            .background(
                                RoundedRectangle(cornerRadius: 4)
                                    .fill(.blue.opacity(0.1))
                            )
                    }
                }
                
                Text(chordType.description)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .lineLimit(1)
            }
            
            Spacer()
            
            // 配置数量
            Text("\(chordType.voicings.count)")
                .font(.caption)
                .fontWeight(.medium)
                .foregroundColor(.secondary)
                .padding(.horizontal, 8)
                .padding(.vertical, 4)
                .background(
                    RoundedRectangle(cornerRadius: 8)
                        .fill(.secondary.opacity(0.1))
                )
        }
        .padding(.vertical, 4)
    }
}

// MARK: - 配置列表视图

struct VoicingListView: View {
    let chordType: ChordType
    @Binding var selectedVoicing: ChordVoicing?
    let chordPlayer: ChordPlayer
    let gameState: ChordGameState
    var onVoicingSelected: (() -> Void)? = nil
    
    var body: some View {
        List(chordType.voicings) { voicing in
            VoicingRowView(
                voicing: voicing,
                isSelected: selectedVoicing?.id == voicing.id,
                chordPlayer: chordPlayer,
                gameState: gameState
            ) {
                selectedVoicing = voicing
                HapticFeedback.selection()
                // 选择后调用关闭回调
                onVoicingSelected?()
            }
        }
        .navigationTitle(chordType.name)
        #if os(iOS)
        .navigationBarTitleDisplayMode(.inline)
        #endif
        .listStyle(PlainListStyle())
        .scrollContentBackground(.hidden)
    }
}

// MARK: - 配置行视图

struct VoicingRowView: View {
    let voicing: ChordVoicing
    let isSelected: Bool
    let chordPlayer: ChordPlayer
    let gameState: ChordGameState
    let onSelect: () -> Void
    
    var body: some View {
        HStack(spacing: 12) {
            // 选择指示器
            Image(systemName: isSelected ? "checkmark.circle.fill" : "circle")
                .font(.title3)
                .foregroundColor(isSelected ? .blue : .secondary.opacity(0.5))
                .frame(width: 24)
            
            VStack(alignment: .leading, spacing: 4) {
                Text(voicing.name)
                    .font(.body)
                    .fontWeight(.medium)
                    .foregroundColor(.primary)
                
                Text(voicing.description)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .lineLimit(2)
                
                // 音程显示
                Text("Intervals: \(voicing.intervals.map(String.init).joined(separator: ", "))")
                    .font(.caption2)
                    .foregroundColor(.secondary.opacity(0.8))
            }
            
            Spacer()
            
            // 试听按钮
            Button(action: {
                print("🔵 试听按钮被点击: \(voicing.name)")
                print("🔵 ChordPlayer 实例: \(chordPlayer)")
                print("🔵 准备调用 playChord...")
                chordPlayer.playChord(voicing)
                print("🔵 playChord 调用完成")
                HapticFeedback.buttonTap()
            }) {
                Image(systemName: chordPlayer.isPlaying ? "speaker.wave.2.fill" : "speaker.wave.2")
                    .font(.caption)
                    .foregroundColor(.blue)
                    .frame(width: 20, height: 20)
            }
        }
        .padding(.vertical, 4)
        .contentShape(Rectangle())
        .onTapGesture {
            onSelect()
        }
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(isSelected ? .blue.opacity(0.1) : .clear)
        )
    }
}

// MARK: - 试听按钮

struct PreviewButton: View {
    let voicing: ChordVoicing
    let chordPlayer: ChordPlayer
    let isEnabled: Bool
    
    var body: some View {
        Button(action: {
            if chordPlayer.isPlaying {
                chordPlayer.stopPlaying()
            } else {
                chordPlayer.playChord(voicing)
            }
            HapticFeedback.buttonTap()
        }) {
            HStack(spacing: 6) {
                Image(systemName: chordPlayer.isPlaying ? "stop.fill" : "speaker.wave.2.fill")
                    .font(.caption)
                
                Text(chordPlayer.isPlaying ? "Stop" : "Preview")
                    .font(.caption)
                    .fontWeight(.semibold)
            }
            .foregroundColor(.white)
            .padding(.horizontal, 16)
            .padding(.vertical, 8)
            .background(
                LinearGradient(
                    gradient: Gradient(colors: [.blue, .purple]),
                    startPoint: .leading,
                    endPoint: .trailing
                )
            )
            .cornerRadius(12)
        }
        .disabled(!isEnabled)
        .opacity(isEnabled ? 1.0 : 0.5)
    }
} 
