//
//  ChordsView.swift
//  FullUI
//
//  Created by <PERSON> Li on 2025/5/26.
//
import SwiftUI

struct ChordsView: View {
    @ObservedObject private var configManager = GameConfigManager.shared
    @StateObject private var voicingManager = ChordVoicingManager()
    @StateObject private var newVoicingManager = ChordVoicingDatabaseManager()
    @StateObject private var gameManager: ChordGameManager
    @ObservedObject var midiManager: MIDIManager
    
    // 🎹 键盘按键提示支持
    @ObservedObject var keyboardViewModel: PianoKeyboardViewModel
    
    @State private var showChordSelect = false
    @State private var showNewChordSelect = false
    @State private var showVoicingSelect = false
    @State private var showVoicingCreation = false
    @State private var musicNoteIconPosition: CGPoint = .zero
    @StateObject private var chordViewModel = FXChordViewModel()
    
    // 🎵 五度圆状态
    @State private var circleOfFifthsActiveKey: Key? = nil
    
    // 🔄 无限等待模式
    @State private var infiniteWaitMode: Bool = false

    // 🔄 练习状态管理
    @StateObject private var practiceStateManager = PracticeStateManager.shared
    
    // 🔧 新增：管理ChordsView中的异步任务，确保可以取消
    @State private var viewAnimationTasks: [DispatchWorkItem] = []
    
    // 🔧 防抖动：记录上次处理的和弦音符
    @State private var lastChordNotes: [Int] = []
    
    private var debugCV = false  // 🎵 启用调试输出以验证音符同步功能
    
    
    @StateObject private var chromaCircleViewModel = ChromaCircleViewModel()
    
    // MARK: - 统计系统
    @StateObject private var statisticsViewModel = StatisticsViewModel()
    @State private var practiceStartTime: Date?
    @State private var totalPlayedNotes: Int = 0  // 总音符数（每个和弦音符数的累加）
    @State private var totalChordsAttempted: Int = 0  // 总尝试和弦数
    @State private var statisticsAlreadySaved: Bool = false  // 防止重复保存统计数据
    
    // 内购相关
    @StateObject private var purchaseManager = PurchaseManager.shared
    @State private var showProUpgrade = false
    
    init(midiManager: MIDIManager, keyboardViewModel: PianoKeyboardViewModel) {
        self.midiManager = midiManager
        self.keyboardViewModel = keyboardViewModel
        self._gameManager = StateObject(wrappedValue: ChordGameManager(
            config: GameConfigManager.shared.config,
            midiManager: midiManager
        ))
    }
    

    
    var body: some View {
        ZStack {
            // 🎨 FXChordView作为背景层，全屏显示
            FXChordView(viewModel: chordViewModel)
                .onAppear {
                    
                }
            
            // 主布局层
            VStack {
                // 顶部布局：左上角按钮 + 右上角状态
                HStack {

                    #if DEBUG
                    // 开发者内部工具按钮
                    Button("create voicing"){
                        showVoicingCreation = true
                    }
                    .font(.caption)
                    .foregroundColor(.orange)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(Color.orange.opacity(0.1))
                    .cornerRadius(6)
                    #endif
                    
                    // 🎵 右上角：练习模式选择器和五度圈
                    HStack(spacing: 16) {
                        
                        // 练习模式选择器（只在空闲状态显示）
                        if gameManager.gameState == .idle {
                            
                            levelSelectButton
                            
                            practicePatternPicker
                                
                            infiniteWaitToggle
                            
                        }
                        
                        Spacer()
                        
                        HStack(alignment:.top){
                            // 五度圈（只在游戏进行时且当前练习模式需要时显示）
                            VStack{
                                
                                //  显示和弦名称
                                currentChordName

                                // 永久等待模式状态指示器（游戏进行时显示）
                                if gameManager.gameState != .idle && infiniteWaitMode {
                                    endlessModeIndicator
                                }

                                //  五度圈
                                if gameManager.gameState != .idle {
                                    if let pattern = gameManager.selectedPracticePattern, pattern.useCircleOfFifths {
                                        circleOfFifthsView
                                    }
                                }
                                
                            }

                            //ChromaCircleView(viewModel: chromaCircleViewModel)
                             //   .frame(maxWidth: .infinity, maxHeight: .infinity)


                        }
                    }
                }
                .padding(.horizontal)
                .padding(.top)
                Spacer()
                
            }
            
            Spacer()
            // 练习总结弹窗
            if gameManager.showLevelClearView {
                PracticeSummaryView(
                    rightNoteCount: gameManager.rightChordCount,
                    errorNoteCount: gameManager.errorChordCount,
                    totalTime: gameManager.totalTime,
                    isPresented: $gameManager.showLevelClearView
                    // Chords模式：只显示关闭按钮，不显示重玩和下一关按钮
                )
                .zIndex(1)
            }
            
            // Chord Select 界面
            if showChordSelect {
                ChordsSelectView(
                    selectedVoicing: $voicingManager.selectedVoicing,
                    isPresented: $showChordSelect,
                    gameState: gameManager.gameState,
                    midiManager: midiManager,
                    onStart: {
                        // 选择和弦后的回调
                    },
                   onStop: {
                        gameManager.stopGame()
                    },
                    onReplay: {
                        gameManager.replayCurrentChord()
                    }
                )
                .zIndex(2)
            }
            
            // 新的和弦选择界面
            if showNewChordSelect {
                ChordVoicingSelectView(
                    selectedVoicing: $newVoicingManager.selectedVoicing,
                    isPresented: $showNewChordSelect,
                    midiManager: midiManager
                )
                .zIndex(3)
            }
            
            // 新的VoicingSelectView - 占满整个视图客户区
            if showVoicingSelect {
                VoicingSelectView(
                    selectedVoicing: $newVoicingManager.selectedVoicing,
                    isPresented: $showVoicingSelect,
                    midiManager: midiManager,
                    databaseManager: newVoicingManager
                )
                .zIndex(4)
            }
            
            // 和弦创建界面（开发者工具）
            if showVoicingCreation {
                VoicingCreationView(
                    isPresented: $showVoicingCreation,
                    midiManager: midiManager
                )
                .zIndex(5)
            }
            
            // Pro升级弹窗
            if showProUpgrade {
                ZStack {
                    Color.black.opacity(0.5)
                        .ignoresSafeArea()
                        .onTapGesture {
                            showProUpgrade = false
                        }
                    
                    ProUpgradeView(
                        featureName: "高级和弦练习",
                        onUpgrade: {
                            // 升级完成后的回调
                            showVoicingSelect = true
                        },
                        onDismiss: {
                            // 🔧 关闭ProUpgradeView
                            showProUpgrade = false
                        }
                    )
                }
                .zIndex(6)
            }
            
            // 右下角高质感播放/停止按钮
            VStack {
                Spacer()
                HStack {
                    Spacer()
                    PremiumPlayStopButton(
                        isPlaying: gameManager.gameState == .playingChord || gameManager.gameState == .waitingForResponse,
                        isDisabled: gameManager.gameState == .idle && newVoicingManager.selectedPracticeGroup == nil
                    ) {
                        switch gameManager.gameState {
                        case .idle, .completed:
                            // 🔧 调试信息：检查当前选择状态
                            print("🎯 播放按钮点击 - 检查选择状态:")
                            print("   - selectedPracticeGroup: \(newVoicingManager.selectedPracticeGroup?.name ?? "nil")")
                            print("   - practiceGroupVoicings count: \(newVoicingManager.selectedPracticeGroupVoicings.count)")
                            
                            // 只支持练习组模式
                            if let selectedPracticeGroup = newVoicingManager.selectedPracticeGroup {
                                // 检查内购状态
                                if selectedPracticeGroup.isAvailable() {
                                    let voicings = newVoicingManager.selectedPracticeGroupVoicings
                                    print("🎯 启动练习组游戏: \(selectedPracticeGroup.name)")
                                    
                                    // 📊 统计系统：开始练习时初始化
                                    initializePracticeStatistics()
                                    
                                    gameManager.startGame(with: selectedPracticeGroup, voicings: voicings)
                                } else {
                                    // 需要升级Pro版
                                    showProUpgrade = true
                                }
                            } else {
                                print("⚠️ 没有选择练习组，无法启动游戏")
                            }
                        case .playingChord, .waitingForResponse:
                            // 📊 统计系统：停止练习时保存数据
                            savePracticeStatistics()
                            
                            gameManager.stopGame()
                        }
                    }
                    .padding(.trailing, 0)
                    .padding(.bottom, 0) // 为键盘预留空间
                }
            }
        }
        .onReceive(configManager.$config) { config in
            gameManager.updateConfig(config)
        }
        .onAppear {
            setupGame()

            // 🔄 恢复上次练习状态
            if let chordState = practiceStateManager.getChordState() {
                // 恢复上次选择的Voicing
                if let voicing = voicingManager.voicings.first(where: { $0.name == chordState.selectedVoicingName }) {
                    voicingManager.selectedVoicing = voicing
                    gameManager.selectedVoicing = voicing
                    print("🔄 恢复Chord练习状态: \(chordState.selectedVoicingName), Chord \(chordState.selectedChordIndex)")
                }
            }

            // 🎨 设置FXChordView的响应时间
            chordViewModel.setResponseTime(GameConfigManager.shared.getResponseTime())
            // 🎹 设置动画结束回调
            #if os(macOS)
            chordViewModel.onAnimationEnded = {
                keyboardViewModel.clearTargetNotes()
                print("🎹 FXChordView动画结束，清除键盘按键提示")
            }
            #endif

            // 🎹 注意：ChordGameManager使用内置播放逻辑，不使用ChordPlayer
            // 人性化琶音主要在ProgressionsView和选择界面中使用
            print("🎹 ChordsView: ChordGameManager使用内置播放逻辑")
        }        .onReceive(gameManager.$gameState) { gameState in
            handleGameStateChange(gameState)
        }
        .onReceive(gameManager.$currentChordNotes) { chordNotes in
            // 🔧 添加防抖动机制，避免同一frame内多次处理
            DispatchQueue.main.async {
                self.handleChordNotesChange(chordNotes)
                
                // 📊 统计系统：累计音符数（只在练习进行中）
                if self.practiceStartTime != nil && !chordNotes.isEmpty {
                    self.totalPlayedNotes += chordNotes.count
                    print("📊 统计：累计音符数 +\(chordNotes.count)，总计: \(self.totalPlayedNotes)")
                }
            }
        }
        .onReceive(midiManager.$pressedNotes) { pressedNotes in
            handleMIDIInputChange(pressedNotes)
        }
        // 🎨 监听配置变化，更新响应时间
        .onReceive(configManager.$config) { config in
            chordViewModel.setResponseTime(GameConfigManager.shared.getResponseTime())
        }
        // 🎵 监听currentRootIndex变化，更新五度圆显示
        .onReceive(gameManager.$currentRootIndex) { currentRootIndex in
            updateCircleOfFifthsDisplay(for: currentRootIndex)
        }
        .onChange(of: infiniteWaitMode) { endlessMode in
            gameManager.setEndlessMode(infiniteWaitMode)
        }
        //  bug fix:播放和弦音完成了才出现音符，正常情况是播放一个音符出一个音符
        .onReceive(NotificationCenter.default.publisher(for: NSNotification.Name("ChordNoteStartedPlaying"))) { notification in
            handleNoteStartedPlaying(notification)
        }
        .onReceive(NotificationCenter.default.publisher(for: NSNotification.Name("ChordPlaybackCompleted"))) { notification in
            handleChordPlaybackCompleted(notification)
        }
        // 📊 统计系统：监听练习完成状态
        .onReceive(gameManager.$showLevelClearView) { showLevelClear in
            if showLevelClear && practiceStartTime != nil {
                // 练习自然完成，保存统计数据
                savePracticeStatistics()
                print("📊 统计系统：练习自然完成，已保存数据")
            }
        }
    }
    
    // MARK: - FXChordView 控制方法
    
    private func handleGameStateChange(_ gameState: ChordGameState) {
        //print("🎪 游戏状态变化: \(gameState)")
        
        switch gameState {
        case .idle:
            // 🔧 游戏停止时，立即取消所有异步任务
            cancelViewAnimationTasks()
            
            // 游戏空闲时，隐藏所有音符并清除键盘按键提示
            chordViewModel.setAllNotesState(state: .hide)
            keyboardViewModel.clearTargetNotes()
            
            chromaCircleViewModel.clearAll()  // 🔧 重要：清除所有状态
            //print("🎹 清除键盘按键提示")
            
        case .playingChord:
            // 🎨 修复时序问题：不在这里直接显示音符，等currentChordNotes更新后再显示
            if debugCV {
                print("🎪 播放和弦状态 - 等待和弦设置完成后再显示")
            }
        case .waitingForResponse:
            // 等待响应时，保持显示状态
            if debugCV {
                print("🎪 等待响应状态")
            }
            
        case .completed:
            // 🔧 游戏完成时，也取消所有异步任务
            cancelViewAnimationTasks()
            chromaCircleViewModel.clearAll()  // 🔧 重要：清除所有状态
            // 游戏完成时
            if debugCV {
                print("🎪 游戏完成状态")
            }
            
            #if os(iOS)
            // iOS上没有动画结束回调，在游戏完成时清除键盘按键提示
            keyboardViewModel.clearTargetNotes()
            print("🎹 游戏完成，清除键盘按键提示 (iOS)")
            #endif
        }
        
        // 🎨 输出当前GameConfig时间参数，便于调试
        if gameState == .playingChord {
            let config = configManager.config.gameSettings
            if debugCV {
                print("🎨 GameConfig时间参数:")
                print("   - whiteKeyOnly: \(config.whiteKeyOnly)")
                print("   - defaultBpm: \(config.defaultBpm) (默认BPM)")
                print("   - beatsPerChord: \(config.beatsPerChord) (每个和弦拍数)")
                print("   - responseBeats: \(config.responseBeats) (反应时间拍数)")
                print("   - 计算的和弦等待时间: \(String(format: "%.2f", GameConfigManager.shared.getChordWaitTime()))秒")
                print("   - 计算的反应时间: \(String(format: "%.2f", GameConfigManager.shared.getResponseTime()))秒")
            }
        }
    }
    
    // 🔧 优化：提取和弦音符变化处理到单独方法，避免重复处理
    private func handleChordNotesChange(_ chordNotes: [Int]) {
        // 🔧 检查是否为相同的和弦音符，避免重复处理
        if chordNotes == lastChordNotes {
            return
        }
        lastChordNotes = chordNotes
        
        if !chordNotes.isEmpty {
            // 获取专业完整和弦名称
            let chordName = getCurrentChordName()
            //print("CPB        ----- \(chordName)")
            
            chordViewModel.setChord(chordName: chordName, notes: chordNotes)
            
            // 🎹 设置键盘按键提示
            keyboardViewModel.setTargetNotes(chordNotes)
            
            if debugCV{
                print("🎹 设置键盘按键提示: \(chordNotes)")
            }
            
            // 🎨 优化用户体验：和弦设置完成后，不立即显示所有音符
            // 等待逐个播放通知来逐个显示音符
            if gameManager.gameState == .playingChord {
                if debugCV{
                    print("🎪 和弦设置完成，准备逐个显示音符")
                }
                // 先隐藏所有音符，等待播放通知
                chordViewModel.setAllNotesState(state: .hide)
            }
        }
    }
    
    private func handleMIDIInputChange(_ pressedNotes: Set<Int>) {
        if debugCV {
            print("🎪 MIDI输入变化: \(Array(pressedNotes).sorted())")
        }
        
        // 只在游戏进行中处理MIDI输入
        guard gameManager.gameState == .waitingForResponse || gameManager.gameState == .playingChord else {
            return
        }
        
        // 获取当前期望的音符
        let expectedNotes = gameManager.expectedNotes
        
        // 🎨 移除会干扰动画的全局状态重置
        // 不再调用: chordViewModel.setAllNotesState(state: .show)
        
        // 🎨 智能状态管理：只有在动画未锁定时才重置状态
        if !chordViewModel.isAnimationLocked {
            // 先重置所有音符为显示状态
            chordViewModel.setAllNotesState(state: .show)
        }
        
        // 高亮按下的音符（如果在期望音符中）
        for note in pressedNotes {
            if expectedNotes.contains(note) {
                // 找到音符在FXChordView中的索引
                if let index = chordViewModel.notes.firstIndex(where: { $0.midiValue == note }) {
                    chordViewModel.setSingleNoteState(index: index, state: .match)
                    if debugCV {
                        print("🎪 高亮音符: \(note) (索引: \(index))")
                    }
                }
            }
        }
        
        // 🎨 调试信息：显示动画锁定状态
        if chordViewModel.isAnimationLocked {
            if debugCV {
                print("🎪 动画锁定中，保护成功动画播放")
            }
        }
    }
    
    private func handleExpectedNotesChange(_ expectedNotes: Set<Int>) {
        if debugCV {
            print("🎪 期望音符变化: \(Array(expectedNotes).sorted())")
        }
        
        // 当期望音符改变时，显示所有目标音符
        if !expectedNotes.isEmpty && gameManager.gameState != .idle {
            chordViewModel.setAllNotesState(state: .show)
        }
    }
    
    private func getCurrentChordName() -> String {
        // 🎵 修复：使用现有的NoteNameGenerator.getNoteNameOnly
        let rootName = NoteNameGenerator.getNoteNameOnly(note: gameManager.currentRootIndex % 12)
        
        // 只处理练习组模式
        if !gameManager.currentVoicingInGroup.isEmpty,
           let selectedVoicing = gameManager.selectedVoicing,
           let suffix = selectedVoicing.suffix {
            return "\(rootName)\(suffix)"
        } else {
            // 备用显示
            let voicingName = gameManager.currentVoicingInGroup.isEmpty ? "Chord" : gameManager.currentVoicingInGroup
            return "\(rootName) \(voicingName)"
        }
    }
    
    // MARK: - 音符图标位置报告
    
    private func reportMusicNoteIconPosition(_ geometry: GeometryProxy) {
        let globalFrame = geometry.frame(in: .global)
        let center = CGPoint(
            x: globalFrame.midX,
            y: globalFrame.midY
        )
        
        
        musicNoteIconPosition = center
        if debugCV {
            print("🎵 右上角音符图标位置报告:")
            print("   全局坐标: \(center)")
            print("   框架: \(globalFrame)")
        }
        
        // 通知ParticleCoordinator更新目标位置
        updateParticleTargetPosition(center)
    }
    
    private func updateParticleTargetPosition(_ position: CGPoint) {
        ParticleCoordinator.shared.setMusicNoteIconPosition(position)
    }
    
    // MARK: - 🎵 音符播放通知处理
    
    /// 处理音符开始播放通知
    private func handleNoteStartedPlaying(_ notification: Notification) {
        guard let userInfo = notification.object as? [String: Any],
              let noteIndex = userInfo["noteIndex"] as? Int,
              let note = userInfo["note"] as? Int else {
            print("❌ 音符播放通知格式错误")
            return
        }
        
    /*
        guard gameManager.gameState == .playingChord else{
           print("不是播放状态 handleNoteStartedPlaying")
           return
        }
    */
        if debugCV{
            print("🎵 收到音符播放通知: 索引 \(noteIndex), MIDI值 \(note)")
        }
        
        // 找到对应的音符并显示
        if noteIndex < chordViewModel.notes.count {
            // 🎨 显示当前播放的音符，并给予特殊高亮效果
            chordViewModel.setSingleNoteState(index: noteIndex, state: .highlight)
            if debugCV {
                print("🎵 高亮显示当前播放音符: 索引 \(noteIndex) (\(chordViewModel.notes[noteIndex].noteName))")
            }
            // 🔧 使用DispatchWorkItem管理异步任务，确保可以取消
            let highlightTask = DispatchWorkItem {
                // 🔧 检查游戏状态，如果已停止则不执行
                guard self.gameManager.gameState != .idle else {
                    print("🔧 ChordsView音符高亮: 游戏已停止，取消音符状态更新")
                    return
                }
                
                if noteIndex < self.chordViewModel.notes.count {
                    self.chordViewModel.setSingleNoteState(index: noteIndex, state: .show)
                    if debugCV {
                        print("🎵 音符 \(noteIndex) 高亮结束，转为普通显示")
                    }
                }
            }
            
            // 🔧 将任务添加到管理数组
            viewAnimationTasks.append(highlightTask)
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.8, execute: highlightTask)
        } else {
            print("❌ 音符索引超出范围: \(noteIndex)")
        }
    }
    
    /// 处理和弦播放完成通知
    private func handleChordPlaybackCompleted(_ notification: Notification) {
        if debugCV {
            print("🎵 收到和弦播放完成通知")
        }
        
        // 🔧 使用DispatchWorkItem管理异步任务，确保可以取消
        let completionTask = DispatchWorkItem {
            // 🔧 检查游戏状态，如果已停止则不执行
            guard self.gameManager.gameState == .waitingForResponse else {
                print("🔧 ChordsView和弦完成: 游戏状态不正确，取消显示所有音符")
                return
            }
            
            if debugCV {
                print("🎵 和弦播放完成，确保所有音符都显示")
            }
            self.chordViewModel.setAllNotesState(state: .show)
        }
        
        // 🔧 将任务添加到管理数组
        viewAnimationTasks.append(completionTask)
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.2, execute: completionTask)
    }
    
    var currentChordName: some View{
        VStack(alignment: .leading, spacing: 4) {
            // 只显示练习组模式
           // let _ = print("currentChordName 练习组名称: \(gameManager.practiceGroupName)")
            
            if !gameManager.practiceGroupName.isEmpty {
                Text(gameManager.practiceGroupName)
                    .font(.system(size: 20, weight: .semibold))
                    .foregroundColor(.primary)
                
                // 显示当前练习的voicing和进度
                if !gameManager.currentVoicingInGroup.isEmpty {
                    HStack(spacing: 8) {
                        Text("当前: \(gameManager.currentVoicingInGroup)")
                            .font(.system(size: 16))
                            .foregroundColor(.secondary)
                        
                        // 显示进度
                        Text("(\(gameManager.currentRound)/\(gameManager.totalRound))")
                            .font(.system(size: 14))
                            .foregroundColor(.blue)
                            .padding(.horizontal, 6)
                            .padding(.vertical, 2)
                            .background(
                                RoundedRectangle(cornerRadius: 8)
                                    .fill(Color.blue.opacity(0.1))
                            )
                    }
                }
                
                // 显示当前根音和弦名称
                if gameManager.gameState != .idle {
                    let currentChordName = getCurrentChordName()
                    Text(currentChordName)
                        .font(.system(size: 18, weight: .medium))
                        .foregroundColor(.blue)
                }
            } else {
                // 没有选择练习组时的提示
                VStack(alignment: .leading, spacing: 2) {
                    Text("请选择练习组")
                        .font(.system(size: 20))
                        .foregroundColor(.secondary)
                    
                    Text("点击左上角按钮选择要练习的和弦组合")
                        .font(.system(size: 14))
                        .foregroundColor(.gray)
                }
            }
        }
        .padding(.leading, 16)
    }
    
    
    var levelSelectButton:some View{
        
        
                                Button(action:{
                            //print("chords level select")
                            //showChordSelect = true
                            //showNewChordSelect = true
                            showVoicingSelect = true
                        }){
            Image(systemName:"list.triangle")
                .foregroundColor(gameManager.gameState == .idle ? .white : .gray)
        }
        .frame(width: 44, height: 44)
        .background(
            Circle()
                .fill(.ultraThinMaterial)
                .opacity(gameManager.gameState == .idle ? 1.0 : 0.6)
        )
        .disabled(gameManager.gameState != .idle)
        .animation(.easeInOut(duration: 0.2), value: gameManager.gameState)
    }
    
    
    // MARK: - 紧凑版游戏状态视图（右上角）
    
    var compactGameStatusView: some View {
        HStack(spacing: 8) {
            // 正确音符个数
            HStack(spacing: 4) {
                Image(systemName: "music.note")
                    .foregroundColor(.yellow)
                    .font(.system(size: 16)) // 自定义尺寸
                    .background(
                        GeometryReader { geometry in
                            Color.clear
                                .onAppear {
                                    reportMusicNoteIconPosition(geometry)
                                }
                                .onChange(of: geometry.size) { _ in
                                    reportMusicNoteIconPosition(geometry)
                                }
                        }
                    )
                Text("\(gameManager.rightNoteCount)")
                    .font(.title3)
                    .fontWeight(.bold)
            }
            // 错误音符个数
            HStack(spacing: 4) {
                Image(systemName: "xmark")
                    .foregroundColor(.red)
                    .font(.system(size: 16)) // 自定义尺寸
                Text("\(gameManager.errorNoteCount)")
                    .font(.title3)
                    .fontWeight(.bold)
            }
            
            // 正确次数
            HStack(spacing: 4) {
                Image(systemName: "music.quarternote.3")
                    .foregroundColor(.yellow)
                    .font(.system(size: 16)) // 自定义尺寸
                Text("\(gameManager.rightChordCount)")
                    .font(.title3)
                    .fontWeight(.bold)
            }
            
            // 错误次数  
            HStack(spacing: 4) {
                Image(systemName: "xmark.octagon")
                    .foregroundColor(.red)
                    .font(.system(size: 16)) // 自定义尺寸
                Text("\(gameManager.errorChordCount)")
                    .font(.title3)
                    .fontWeight(.bold)
            }
            
            // 当前轮次
            HStack(spacing: 4) {
                Image(systemName: "figure.boxing.circle")
                    .foregroundColor(.blue)
                    .font(.system(size: 16)) // 自定义尺寸
                Text("\(gameManager.currentRound)/\(gameManager.totalRound)")
                    .font(.title3)
                    .fontWeight(.bold)
            }
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 8)
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(.ultraThinMaterial)
        )
    }
    
    // MARK: - 永久等待模式开关
    
    private var infiniteWaitToggle: some View {
        HStack(spacing: 6) {
            Image(systemName: infiniteWaitMode ? "infinity.circle.fill" : "infinity.circle")
                .font(.system(size: 14))
                .foregroundColor(infiniteWaitMode ? .green : .white)
            
            Toggle("无限等待模式", isOn: $infiniteWaitMode)
            //.font(.caption)
                .font(.system(size: 16))
                .foregroundColor(.white)
                .toggleStyle(SwitchToggleStyle(tint: .green))
                .scaleEffect(0.8)
        }
        .padding(.horizontal, 8)
        .padding(.vertical, 4)
        .background(
            RoundedRectangle(cornerRadius: 6)
                .fill(infiniteWaitMode ? Color.green.opacity(0.2) : Color.gray.opacity(0.2))
        )
        .overlay(
            RoundedRectangle(cornerRadius: 6)
                .stroke(infiniteWaitMode ? Color.green : Color.gray, lineWidth: 1)
        )
        .help(infiniteWaitMode ? "永久等待已开启：练习时没有时间限制，可以按自己的节奏弹奏" : "点击开启永久等待模式：练习时将没有时间限制")

        
    }
    
    
    // MARK: - 永久等待模式状态指示器
    
    private var endlessModeIndicator: some View {
        HStack(spacing: 4) {
            Image(systemName: "infinity")
                .font(.system(size: 16))
                .foregroundColor(.green)
            
            Text("永久等待模式")
                .font(.system(size: 16))
                .foregroundColor(.green)
                .fontWeight(.medium)
        }
        .padding(.horizontal, 6)
        .padding(.vertical, 3)
        .background(
            Capsule()
                .fill(Color.green.opacity(0.2))
        )
        .overlay(
            Capsule()
                .stroke(Color.green, lineWidth: 1)
        )
        .scaleEffect(0.9)
        .opacity(0.8)
    }
    

    
    // MARK: - 设置游戏
    private func setupGame() {
        // 初始化游戏配置
        gameManager.updateConfig(configManager.config)
    }
    
    // MARK: - 🎵 五度圈相关方法
    
    /// 五度圈视图（右上角显示）
    private var circleOfFifthsView: some View {
        CircleOfFifthsView(
            size: 160,  // 紧凑尺寸
            activeKey: $circleOfFifthsActiveKey,
            showLabels: true,
            touchSelect: false  // 禁用触摸，只用于显示
        )
        .opacity(0.8)  // 略微透明，不干扰主要内容
    }
    
    /// 根据当前根音索引更新五度圈显示
    private func updateCircleOfFifthsDisplay(for rootIndex: Int) {
        let pitchClass = rootIndex % 12
        circleOfFifthsActiveKey = Key.keyFromValue(pitchClass)
    }
    
    // MARK: - 🎵 练习模式选择器
    
    private var practicePatternPicker: some View {
        Menu {
            ForEach(gameManager.patternManager.availablePatterns) { pattern in
                Button(action: {
                    gameManager.patternManager.selectedPattern = pattern
                }) {
                    HStack {
                        Text(pattern.name)
                            .font(.system(size: 16))
                        if gameManager.selectedPracticePattern?.id == pattern.id {
                            Spacer()
                            Image(systemName: "checkmark")
                        }
                    }
                }
                .font(.system(size: 16))
            }
        }
        
        label: {
            HStack(spacing: 4) {
                Image(systemName: "arrow.triangle.2.circlepath")
                    .font(.system(size: 20))
                Text(gameManager.selectedPracticePattern?.name ?? "Practice Mode")
                    .font(.system(size: 16))
                    //.font(.caption)
                    .lineLimit(1)
                Image(systemName: "chevron.down")
                    .font(.system(size: 20))
            }
            .foregroundColor(.white)
            .padding(.horizontal, 24)
            .padding(.vertical, 16)
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(.ultraThinMaterial)
            )
        }
        .font(.system(size: 16))
        .frame(width: 300)
    }
    
    // MARK: - 📊 统计系统
    
    /// 初始化练习统计
    private func initializePracticeStatistics() {
        practiceStartTime = Date()
        totalPlayedNotes = 0
        totalChordsAttempted = 0
        statisticsAlreadySaved = false
        
        print("📊 统计系统：开始练习 - \(practiceStartTime?.description ?? "unknown")")
    }
    
    /// 保存练习统计数据
    private func savePracticeStatistics() {
        guard let startTime = practiceStartTime else {
            print("📊 统计系统：未找到开始时间，跳过保存")
            return
        }

        // 防止重复保存
        guard !statisticsAlreadySaved else {
            print("📊 统计系统：数据已保存，跳过重复保存")
            return
        }

        statisticsAlreadySaved = true

        // 获取练习数据
        let currentChordNotes = gameManager.currentChordNotes.count
        let finalTotalNotes = totalPlayedNotes + currentChordNotes
        let workoutName = gameManager.practiceGroupName.isEmpty ?
            (gameManager.selectedVoicing?.name ?? "和弦练习") :
            gameManager.practiceGroupName
        let totalChords = gameManager.rightChordCount + gameManager.errorChordCount
        let rightChords = gameManager.rightChordCount

        // 使用统计工具类保存数据
        PracticeStatisticsHelper.savePracticeData(
            to: statisticsViewModel,
            startTime: startTime,
            workoutType: .chord,
            workoutName: workoutName,
            totalNoteCount: finalTotalNotes,
            totalCount: totalChords,
            rightCount: rightChords
        )

        /* 原有代码已移至 PracticeStatisticsHelper
        let endTime = Date()
        let duration = Int(endTime.timeIntervalSince(startTime))

        print("📊 统计系统：保存练习数据")
        print("  - 练习名称: \(workoutName)")
        print("  - 开始时间: \(startTime)")
        print("  - 持续时间: \(duration)秒")
        print("  - 总音符数: \(finalTotalNotes)")
        print("  - 总和弦数: \(totalChords)")
        print("  - 正确和弦数: \(rightChords)")

        // 异步保存统计数据
        Task {
            await statisticsViewModel.addWorkoutItem(
                startTime: startTime,
                duration: duration,
                workoutType: .chord,
                workoutName: workoutName,
                totalNoteCount: finalTotalNotes,
                totalCount: totalChords,
                rightCount: rightChords
            )

            await MainActor.run {
                print("📊 统计系统：数据保存完成")
            }
        }
        */
    }
    
    // MARK: - 🔧 异步任务管理
    
    /// 取消所有视图动画异步任务
    private func cancelViewAnimationTasks() {
        print("🔧 ChordsView: 取消所有视图动画异步任务")
        for task in viewAnimationTasks {
            task.cancel()
        }
        viewAnimationTasks.removeAll()
    }
}
