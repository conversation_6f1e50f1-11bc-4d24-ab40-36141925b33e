//
//  ChordsSelectView.swift
//  VoicingTrainer
//
//  Created by <PERSON> on 2025/1/8.
//

import SwiftUI

struct ChordsSelectView: View {
    @Binding var selectedVoicing: ChordVoicing?
    @Binding var isPresented: Bool
    let gameState: ChordGameState
    let midiManager: MIDIManager
    let onStart: () -> Void
    let onStop: () -> Void
    let onReplay: () -> Void
    
    var body: some View {
        ZStack {
            // 在最外层添加背景
            LinearGradient(
                gradient: Gradient(colors: [
                    Color.indigo.opacity(0.3),
                    Color.purple.opacity(0.4),
                    Color.pink.opacity(0.3)
                ]),
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
            .ignoresSafeArea()
            
            #if os(macOS)
            // macOS上使用VStack布局，避免NavigationView的标题被挤出问题
            VStack(spacing: 0) {
                // 顶部自定义标题栏
                HStack {
                    Text("Select Chord")
                        .font(.headline)
                        .fontWeight(.semibold)
                        .foregroundColor(.white)
                    
                    Spacer()
                    
                    Button("Done") {
                        isPresented = false
                    }
                    .foregroundColor(.white)
                    .fontWeight(.semibold)
                }
                .padding(.horizontal)
                .padding(.top)
                .padding(.bottom, 8)
                .background(.ultraThinMaterial)
                
                // 主要内容 - 使用NavigationStack但不显示标题
                NavigationStack {
                    ChordsSelectBrowserView(
                        selectedVoicing: $selectedVoicing,
                        gameState: gameState,
                        midiManager: midiManager,
                        onStart: {
                            onStart()
                            isPresented = false
                        },
                        onStop: onStop,
                        onReplay: onReplay
                    )
                }
            }
            #else
            NavigationView {
                // 嵌入ChordBrowserView，使用自定义版本
                ChordsSelectBrowserView(
                    selectedVoicing: $selectedVoicing,
                    gameState: gameState,
                    midiManager: midiManager,
                    onStart: {
                        onStart()
                        isPresented = false
                    },
                    onStop: onStop,
                    onReplay: onReplay
                )
                .navigationTitle("Select Chord")
                .navigationBarTitleDisplayMode(.large)
                .toolbarBackground(.clear, for: .navigationBar)
                .toolbarBackground(.clear, for: .tabBar)
                .toolbar {
                    ToolbarItem(placement: .navigationBarTrailing) {
                        Button("Done") {
                            isPresented = false
                        }
                        .foregroundColor(.white)
                        .fontWeight(.semibold)
                    }
                }
            }
            .navigationViewStyle(StackNavigationViewStyle())
            .background(.clear)
            #endif
        }
    }
}

// MARK: - 自定义和弦浏览器视图（无背景）

/**
 * 专门为ChordsSelectView定制的和弦浏览器
 * 去掉了背景样式，适应全屏NavigationView
 */
struct ChordsSelectBrowserView: View {
    @StateObject private var chordsParser = ChordsParser()
    @StateObject private var chordPlayer: ChordPlayer
    @Binding var selectedVoicing: ChordVoicing?
    
    let gameState: ChordGameState
    let onStart: () -> Void
    let onStop: () -> Void
    let onReplay: () -> Void
    
    @State private var navigationPath = NavigationPath()
    
    init(
        selectedVoicing: Binding<ChordVoicing?>,
        gameState: ChordGameState,
        midiManager: MIDIManager,
        onStart: @escaping () -> Void,
        onStop: @escaping () -> Void,
        onReplay: @escaping () -> Void
    ) {
        self._selectedVoicing = selectedVoicing
        self.gameState = gameState
        self.onStart = onStart
        self.onStop = onStop
        self.onReplay = onReplay
        self._chordPlayer = StateObject(wrappedValue: ChordPlayer(midiManager: midiManager))
    }
    
    var body: some View {
        NavigationStack(path: $navigationPath) {
            VStack(spacing: 0) {
                // 主要内容（去掉标题）
                if chordsParser.isLoading {
                    loadingView
                } else if let errorMessage = chordsParser.errorMessage {
                    errorView(errorMessage)
                } else {
                    categoryListView
                }
            }
            .navigationDestination(for: ChordCategory.self) { category in
                ChordTypeListView(
                    category: category,
                    selectedVoicing: $selectedVoicing,
                    chordPlayer: chordPlayer,
                    gameState: gameState
                )
                .background(.clear)
                #if os(iOS)
                .scrollContentBackground(.hidden)
                #endif
            }
            .navigationDestination(for: ChordType.self) { chordType in
                VoicingListView(
                    chordType: chordType,
                    selectedVoicing: $selectedVoicing,
                    chordPlayer: chordPlayer,
                    gameState: gameState,
                    onVoicingSelected: {
                        // 选择和弦后关闭ChordsSelectView
                        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                            onStart()
                        }
                    }
                )
                .background(.clear)
                #if os(iOS)
                .scrollContentBackground(.hidden)
                #endif
            }
        }
        .background(.clear)
        #if os(iOS)
        .scrollContentBackground(.hidden)
        #endif
        // 移除背景样式，使用父视图的渐变背景
        .onDisappear {
            // 当视图消失时停止播放
            chordPlayer.stopPlaying()
        }
    }
    
    // MARK: - 子视图
    
    private var loadingView: some View {
        VStack(spacing: 12) {
            ProgressView()
                .progressViewStyle(CircularProgressViewStyle(tint: .white))
                .scaleEffect(1.2)
            Text("Loading chords...")
                .font(.title3)
                .foregroundColor(.white)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
    
    private func errorView(_ message: String) -> some View {
        VStack(spacing: 12) {
            Image(systemName: "exclamationmark.triangle")
                .font(.title)
                .foregroundColor(.orange)
            
            Text("Error")
                .font(.title2)
                .foregroundColor(.white)
            
            Text(message)
                .font(.body)
                .foregroundColor(.white.opacity(0.8))
                .multilineTextAlignment(.center)
                .padding(.horizontal)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
    
    private var categoryListView: some View {
        List(chordsParser.categories) { category in
            NavigationLink(value: category) {
                CategoryRowView(category: category)
                    .listRowBackground(Color.clear)
            }
            .listRowBackground(Color.clear)
        }
        .listStyle(PlainListStyle())
        .scrollContentBackground(.hidden)
        .background(.clear)
        .onAppear {
            // 强制移除List的背景色
            #if os(iOS)
            UITableView.appearance().backgroundColor = UIColor.clear
            UITableViewCell.appearance().backgroundColor = UIColor.clear
            #endif
        }
    }
}

#Preview {
    ChordsSelectView(
        selectedVoicing: .constant(nil),
        isPresented: .constant(true),
        gameState: .idle,
        midiManager: MIDIManager(),
        onStart: { print("Start") },
        onStop: { print("Stop") },
        onReplay: { print("Replay") }
    )
} 