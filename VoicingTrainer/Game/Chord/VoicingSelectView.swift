import SwiftUI

// MARK: - 主要数据模型

enum ChordFamily: String, CaseIterable {
    case major = "major"
    case minor = "minor"
    case dominant = "dominant"
    case suspended = "suspended"
    case augmented = "augmented"
    case diminished = "diminished"
    
    var displayName: String {
        switch self {
        case .major: return "Major"
        case .minor: return "Minor"
        case .dominant: return "Dominant"
        case .suspended: return "Suspended"
        case .augmented: return "Augmented"
        case .diminished: return "Diminished"
        }
    }
    
    var color: Color {
        switch self {
        case .major: return Color(hex: "#4A90E2") ?? .blue
        case .minor: return Color(hex: "#7ED321") ?? .green
        case .dominant: return Color(hex: "#F5A623") ?? .orange
        case .diminished: return Color(hex: "#D0021B") ?? .red
        case .augmented: return Color(hex: "#9013FE") ?? .purple
        case .suspended: return Color(hex: "#50E3C2") ?? .teal
        }
    }
}

enum ChordStructure: String, CaseIterable {
    case triad = "triad"
    case seventh = "seventh"
    case extended = "extended"
    
    var displayName: String {
        switch self {
        case .triad: return "Triad"
        case .seventh: return "Seventh"
        case .extended: return "Extended"
        }
    }
}

enum DifficultyLevel: String, CaseIterable {
    case all = "all"
    case basic = "basic"
    case normal = "intermediate"
    case hard = "advanced"
    case expert = "expert"
    
    var displayName: String {
        switch self {
        case .all: return "All"
        case .basic: return "Basic"
        case .normal: return "Intermediate"
        case .hard: return "Advanced"
        case .expert: return "Expert"
        }
    }
}

enum HandType: String, CaseIterable {
    case all = "all"
    case oneHand = "one_hand"
    case twoHands = "two_hands"
    
    var displayName: String {
        switch self {
        case .all: return "All"
        case .oneHand: return "One Hand"
        case .twoHands: return "Two Hands"
        }
    }
    
    var icon: String {
        switch self {
        case .all: return "hand.point.up.braille"
        case .oneHand: return "hand.raised"
        case .twoHands: return "hands.clap"
        }
    }
}

// MARK: - VoicingSelectView

struct VoicingSelectView: View {
    @ObservedObject private var databaseManager: ChordVoicingDatabaseManager
    @StateObject private var chordPlayer: ChordPlayer
    
    @Binding var selectedVoicing: ChordVoicing?
    @Binding var isPresented: Bool
    
    @State private var selectedFamily: ChordFamily? = .major
    @State private var selectedStructure: ChordStructure = .triad
    @State private var selectedDifficulty: DifficultyLevel = .all
    @State private var selectedHands: HandType = .all
    @State private var selectedPracticeGroup: PracticeGroup? // 选中的练习组
    
    let midiManager: MIDIManager
    
    init(
        selectedVoicing: Binding<ChordVoicing?>,
        isPresented: Binding<Bool>,
        midiManager: MIDIManager,
        databaseManager: ChordVoicingDatabaseManager
    ) {
        self._selectedVoicing = selectedVoicing
        self._isPresented = isPresented
        self.midiManager = midiManager
        self.databaseManager = databaseManager
        self._chordPlayer = StateObject(wrappedValue: ChordPlayer(midiManager: midiManager))
    }
    
    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // 背景
                Rectangle()
                    .fill(.ultraThinMaterial)
                    .ignoresSafeArea()
                
                VStack(spacing: 0) {
                    // 标题
                    titleSection
                        .padding(.top, 20)
                        .padding(.bottom, 30)
                    
                    // 主要内容区域
                    HStack(alignment:.top,spacing: 20) {
                        // 左侧：Family 选择
                        familySection
                            .frame(maxWidth: 150)
                        
                        // 中间：Structure, Difficulty, Hands
                        VStack(alignment: .leading,spacing: 30) {
                            structureSection
                            difficultySection
                            handsSection
                        }
                        //.frame(maxWidth: 200)
                        .frame(maxWidth: .infinity)
                        
                        // 右侧：Voicing 列表
                        voicingListSection
                            .frame(maxWidth: 500)
                            //.frame(maxWidth: .infinity)
                    }//HStack
                    .padding(.horizontal, 30)
                    .frame(maxHeight: .infinity)
                    
                    Spacer()
                    
                    // 底部按钮
                    bottomButtons
                        .padding(.bottom, 30)
                }//VStack
            }
        }
        .onAppear {
            loadPracticeGroupData()
        }
        /*
         //iOS 17
        .onChange(of: selectedFamily) { _, _ in updateFilteredPracticeGroups() }
        .onChange(of: selectedStructure) { _, _ in updateFilteredPracticeGroups() }
        .onChange(of: selectedDifficulty) { _, _ in updateFilteredPracticeGroups() }
        .onChange(of: selectedHands) { _, _ in updateFilteredPracticeGroups() }
         */
        //iOS 16
        .onChange(of: selectedFamily) { _ in updateFilteredPracticeGroups() }
        .onChange(of: selectedStructure) {_ in updateFilteredPracticeGroups() }
        .onChange(of: selectedDifficulty) {_ in updateFilteredPracticeGroups() }
        .onChange(of: selectedHands) {  _ in updateFilteredPracticeGroups() }
    }
    
    // MARK: - 标题区域
    
    private var titleSection: some View {
        Text("Select a Voicing to Practice")
            .font(.system(size: 28, weight: .bold, design: .rounded))
            .foregroundColor(.primary)
    }
    
    // MARK: - Family 选择区域
    
    private var familySection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Family")
                .font(.system(size: 18, weight: .semibold))
                .foregroundColor(.primary)
            
            VStack(spacing: 12) {
                ForEach(ChordFamily.allCases, id: \.self) { family in
                    familyButton(for: family)
                }
            }
        }
    }
    
    private func familyButton(for family: ChordFamily) -> some View {
        Button(action: {
            withAnimation(.easeInOut(duration: 0.2)) {
                selectedFamily = family
            }
        }) {
            HStack {
                Text(family.displayName)
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(selectedFamily == family ? .white : family.color)
                Spacer()
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .frame(height: 44)
            .frame(maxWidth: .infinity)
            .contentShape(Rectangle())
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(selectedFamily == family ? family.color : Color.clear)
                    .overlay(
                        RoundedRectangle(cornerRadius: 8)
                            .stroke(family.color, lineWidth: 2)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    // MARK: - Structure 选择区域
    
    private var structureSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Structure")
                .font(.system(size: 18, weight: .semibold))
                .foregroundColor(.primary)
            
            VStack(spacing: 8) {
                ForEach(ChordStructure.allCases, id: \.self) { structure in
                    structureRadioButton(for: structure)
                }
            }
        }
    }
    
    private func structureRadioButton(for structure: ChordStructure) -> some View {
        Button(action: {
            withAnimation(.easeInOut(duration: 0.15)) {
                selectedStructure = structure
            }
        }) {
            HStack(spacing: 8) {
                Image(systemName: selectedStructure == structure ? "largecircle.fill.circle" : "circle")
                    .font(.system(size: 16))
                    .foregroundColor(selectedStructure == structure ? .blue : .secondary)
                
                Text(structure.displayName)
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(.primary)
                
                Spacer()
            }
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    // MARK: - Difficulty 选择区域
    
    private var difficultySection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Difficulty")
                .font(.system(size: 18, weight: .semibold))
                .foregroundColor(.primary)
            
            VStack(spacing: 8) {
                ForEach(DifficultyLevel.allCases, id: \.self) { difficulty in
                    difficultyRadioButton(for: difficulty)
                }
            }
        }
    }
    
    private func difficultyRadioButton(for difficulty: DifficultyLevel) -> some View {
        Button(action: {
            withAnimation(.easeInOut(duration: 0.15)) {
                selectedDifficulty = difficulty
            }
        }) {
            HStack(spacing: 8) {
                Image(systemName: selectedDifficulty == difficulty ? "largecircle.fill.circle" : "circle")
                    .font(.system(size: 16))
                    .foregroundColor(selectedDifficulty == difficulty ? .blue : .secondary)
                
                Text(difficulty.displayName)
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(.primary)
                
                Spacer()
            }
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    // MARK: - Hands 选择区域
    
    private var handsSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Hands")
                .font(.system(size: 18, weight: .semibold))
                .foregroundColor(.primary)
            
            Menu {
                ForEach(HandType.allCases, id: \.self) { handType in
                    Button(action: {
                        withAnimation(.easeInOut(duration: 0.15)) {
                            selectedHands = handType
                        }
                    }) {
                        HStack {
                            Image(systemName: handType.icon)
                            Text(handType.displayName)
                            if selectedHands == handType {
                                Spacer()
                                Image(systemName: "checkmark")
                            }
                        }
                    }
                }
            } label: {
                HStack {
                    Image(systemName: selectedHands.icon)
                        .font(.system(size: 16))
                        .foregroundColor(.blue)
                    
                    Text(selectedHands.displayName)
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(.primary)
                    
                    Spacer()
                    
                    Image(systemName: "chevron.up.chevron.down")
                        .font(.system(size: 12))
                        .foregroundColor(.secondary)
                }
                .padding(.horizontal, 16)
                .padding(.vertical, 12)
                .frame(width: 180, height: 44)
                .background(
                    RoundedRectangle(cornerRadius: 8)
                        .fill(Color.gray.opacity(0.1))
                        .overlay(
                            RoundedRectangle(cornerRadius: 8)
                                .stroke(Color.gray.opacity(0.3), lineWidth: 1)
                        )
                )
            }
            .buttonStyle(PlainButtonStyle())
        }
    }
    
    // MARK: - Voicing 列表区域
    
    private var voicingListSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            // 显示练习组计数
            Text("练习组 (\(filteredPracticeGroups.count))")
                .font(.system(size: 18, weight: .semibold))
                .foregroundColor(.primary)
            
            ScrollView {
                LazyVStack(spacing: 8) {
                    // 只显示练习组
                    ForEach(filteredPracticeGroups, id: \.id) { practiceGroup in
                        practiceGroupRow(for: practiceGroup)
                    }
                }
                .padding(.vertical, 8)
            }
            .frame(maxHeight: 300)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color.gray.opacity(0.05))
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(Color.gray.opacity(0.2), lineWidth: 1)
                    )
            )
        }
    }
    
    private func practiceGroupRow(for practiceGroup: PracticeGroup) -> some View {
        Button(action: {
            withAnimation(.easeInOut(duration: 0.15)) {
                selectedPracticeGroup = practiceGroup
            }
        }) {
            HStack {
                VStack(alignment: .leading, spacing: 2) {
                    Text(practiceGroup.name)
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(.primary)
                        .lineLimit(1)
                    
                    HStack(spacing: 8) {
                        Text(practiceGroup.description)
                            .font(.system(size: 12))
                            .foregroundColor(.secondary)
                            .lineLimit(1)
                        
                        // 显示包含的voicing数量
                        Text("(\(practiceGroup.voicing_ids.count)个按法)")
                            .font(.system(size: 10))
                            .foregroundColor(.blue)
                            .padding(.horizontal, 6)
                            .padding(.vertical, 2)
                            .background(
                                RoundedRectangle(cornerRadius: 8)
                                    .fill(Color.blue.opacity(0.1))
                            )
                    }
                    
                    // 显示内购锁定状态
                    if let lockDescription = practiceGroup.getLockDescription() {
                        LockedItemIndicator(description: lockDescription)
                    }
                }
                
                Spacer()
                
                // 锁定状态图标
                if !practiceGroup.isAvailable() {
                    Image(systemName: "lock.fill")
                        .font(.system(size: 14))
                        .foregroundColor(.orange)
                        .frame(width: 24, height: 24)
                }
                
                // 试听按钮 - 播放第一个voicing作为预览
                Button(action: {
                    playPracticeGroupPreview(practiceGroup)
                }) {
                    Image(systemName: "speaker.wave.2.circle.fill")
                        .font(.system(size: 14))
                        .foregroundColor(.blue)
                        .frame(width: 24, height: 24)
                }
                .buttonStyle(PlainButtonStyle())
                
                // 选中状态图标
                if selectedPracticeGroup?.id == practiceGroup.id {
                    Image(systemName: "checkmark.circle.fill")
                        .font(.system(size: 16))
                        .foregroundColor(.blue)
                }
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
            .frame(maxWidth: .infinity)
            .contentShape(Rectangle())
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(selectedPracticeGroup?.id == practiceGroup.id ? Color.blue.opacity(0.1) : Color.clear)
            )
            .opacity(practiceGroup.isAvailable() ? 1.0 : 0.7)
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    // MARK: - 底部按钮
    
    private var bottomButtons: some View {
        HStack(spacing: 20) {
            Spacer()
            // Cancel 按钮
            Button(action: {
                isPresented = false
            }) {
                Text("Cancel")
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(.primary)
                    .frame(width: 120, height: 44)
                    .background(
                        RoundedRectangle(cornerRadius: 8)
                            .fill(Color.gray.opacity(0.2))
                            .overlay(
                                RoundedRectangle(cornerRadius: 8)
                                    .stroke(Color.gray.opacity(0.4), lineWidth: 1)
                            )
                    )
            }
            .buttonStyle(PlainButtonStyle())
            
            // Select 按钮 - 只支持练习组选择
            Button(action: {
                if let selectedPracticeGroup = selectedPracticeGroup {
                    // 选择练习组 - 确保清除绑定的selectedVoicing
                    selectedVoicing = nil
                    databaseManager.selectPracticeGroup(selectedPracticeGroup)
                    print("🎯 选择了练习组: \(selectedPracticeGroup.name)")
                }
                isPresented = false
            }) {
                Text("Select")
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(.white)
                    .frame(width: 120, height: 44)
                    .background(
                        RoundedRectangle(cornerRadius: 8)
                            .fill(selectedPracticeGroup != nil ? Color.blue : Color.gray)
                    )
            }
            .buttonStyle(PlainButtonStyle())
            .disabled(selectedPracticeGroup == nil)
        }
        .padding(.horizontal, 40)  // 左右边距
        .padding(.vertical, 0)     // 上下边距
    }
    
    // MARK: - 数据过滤和管理
    
    @State private var filteredPracticeGroups: [PracticeGroup] = [] // 过滤后的练习组
    
    private func loadPracticeGroupData() {
        // 等待数据加载完成后更新过滤结果
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            updateFilteredPracticeGroups()
        }
    }
    
    private func updateFilteredPracticeGroups() {
        guard let database = databaseManager.database else {
            filteredPracticeGroups = []
            return
        }
        
        let allPracticeGroups = database.practice_groups ?? []
        
        // 过滤Practice Groups
        filteredPracticeGroups = allPracticeGroups.filter { practiceGroup in
            // 按 Family 过滤
            if let selectedFamily = selectedFamily {
                guard practiceGroup.family == selectedFamily.rawValue else { return false }
            }
            
            // 按 Structure 过滤
            guard practiceGroup.structure == selectedStructure.rawValue ||
                  (selectedStructure == .seventh && ["seventh", "major6", "minor6"].contains(practiceGroup.structure)) ||
                  (selectedStructure == .extended && ["extended", "major", "minor", "major6", "minor6"].contains(practiceGroup.structure))
            else { return false }
            
            // 按 Difficulty 过滤
            if selectedDifficulty != .all {
                let difficultyMap: [DifficultyLevel: [String]] = [
                    .basic: ["basic"],
                    .normal: ["intermediate"],
                    .hard: ["advanced"],
                    .expert: ["expert"]
                ]
                
                if let allowedComplexities = difficultyMap[selectedDifficulty] {
                    guard allowedComplexities.contains(practiceGroup.complexity) else { return false }
                }
            }
            
            // 按 Hands 过滤
            if selectedHands != .all {
                switch selectedHands {
                case .all:
                    break // 不过滤
                case .oneHand:
                    guard practiceGroup.hands == "one_hand" else { return false }
                case .twoHands:
                    guard practiceGroup.hands == "two_hands" else { return false }
                }
            }
            
            return true
        }
        
        print("🔍 过滤结果: \(filteredPracticeGroups.count) 个练习组")
    }
    
    private func playPracticeGroupPreview(_ practiceGroup: PracticeGroup) {
        // 停止当前播放
        chordPlayer.stopPlaying()
        
        // 获取练习组中的第一个voicing ID
        guard let firstVoicingId = practiceGroup.voicing_ids.first,
              let database = databaseManager.database,
              let voicingItem = database.chord_database.first(where: { $0.id == firstVoicingId }) else {
            print("⚠️ 无法找到练习组中的第一个voicing")
            return
        }
        
        // 转换为ChordVoicing并播放
        let chordVoicing = voicingItem.toChordVoicing()
        chordPlayer.playChord(chordVoicing, baseNote: nil, playbackType: .arpeggio_chord)
        
        print("🔊 播放练习组预览: \(practiceGroup.name) - \(voicingItem.name)")
    }
}

 
