import SwiftUI

struct VoicingCreationView: View {
    @Binding var isPresented: Bool
    @ObservedObject var midiManager: MIDIManager
    
    @State private var id: String = ""
    @State private var name: String = ""
    @State private var suffix: String = ""
    @State private var description: String = ""
    @State private var capturedNotes: [Int] = []
    @State private var pedalPressed: Bool = false
    @State private var showingSaveAlert = false
    @State private var saveAlertMessage = ""
    
    // 用于记录最低音作为root_note
    private var rootNote: String {
        guard let lowestNote = capturedNotes.min() else { return "C3" }
        return midiNoteToString(lowestNote)
    }
    
    // 用于计算intervals（相对于最低音的半音数）
    private var intervals: [Int] {
        guard let lowestNote = capturedNotes.min() else { return [] }
        return capturedNotes.map { $0 - lowestNote }.sorted()
    }
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    // Header
                    VStack(spacing: 8) {
                        Text("创建新 Voicing")
                            .font(.title2)
                            .fontWeight(.bold)
                        
                        Text("开发者内部工具")
                            .font(.caption)
                            .foregroundColor(.orange)
                            .padding(.horizontal, 12)
                            .padding(.vertical, 4)
                            .background(Color.orange.opacity(0.1))
                            .cornerRadius(8)
                    }
                    .padding(.top)
                    
                    // Input Fields
                    VStack(spacing: 16) {
                        GroupBox("基本信息") {
                            VStack(spacing: 12) {
                                InputField(title: "ID", text: $id, placeholder: "例如: custom_voicing_1")
                                InputField(title: "名称", text: $name, placeholder: "例如: 自定义和弦")
                                InputField(title: "后缀", text: $suffix, placeholder: "例如: maj (原位)")
                                InputField(title: "描述", text: $description, placeholder: "例如: 自定义和弦描述")
                            }
                            .padding()
                        }
                        
                        // MIDI录制区域
                        GroupBox("MIDI 录制") {
                            VStack(spacing: 16) {
                                HStack {
                                    Image(systemName: "piano")
                                        .font(.title2)
                                        .foregroundColor(.blue)
                                    
                                    Text("按下琴键并踩踏板捕获音符")
                                        .font(.subheadline)
                                        .foregroundColor(.secondary)
                                    
                                    Spacer()
                                }
                                
                                // 踏板状态指示器
                                HStack {
                                    Circle()
                                        .fill(pedalPressed ? Color.green : Color.gray)
                                        .frame(width: 12, height: 12)
                                        .animation(.easeInOut(duration: 0.2), value: pedalPressed)
                                    
                                    Text(pedalPressed ? "踏板已按下" : "等待踏板")
                                        .font(.caption)
                                        .foregroundColor(pedalPressed ? .green : .gray)
                                    
                                    Spacer()
                                    
                                    Button("清除录制") {
                                        capturedNotes.removeAll()
                                    }
                                    .font(.caption)
                                    .foregroundColor(.red)
                                    .disabled(capturedNotes.isEmpty)
                                }
                                
                                // 显示已捕获的音符
                                if !capturedNotes.isEmpty {
                                    VStack(alignment: .leading, spacing: 8) {
                                        Text("已捕获的音符:")
                                            .font(.caption)
                                            .fontWeight(.medium)
                                        
                                        HStack {
                                            ForEach(capturedNotes.sorted(), id: \.self) { note in
                                                Text(midiNoteToString(note))
                                                    .font(.caption)
                                                    .padding(.horizontal, 8)
                                                    .padding(.vertical, 4)
                                                    .background(Color.blue.opacity(0.1))
                                                    .cornerRadius(6)
                                            }
                                            Spacer()
                                        }
                                        
                                        Text("最低音 (root_note): \(rootNote)")
                                            .font(.caption)
                                            .foregroundColor(.secondary)
                                        
                                        Text("音程 (intervals): \(intervals)")
                                            .font(.caption)
                                            .foregroundColor(.secondary)
                                    }
                                    .padding()
                                    .background(Color.gray.opacity(0.05))
                                    .cornerRadius(8)
                                }
                            }
                            .padding()
                        }
                        
                        // 预览信息
                        if !capturedNotes.isEmpty && !id.isEmpty {
                            GroupBox("预览") {
                                VStack(alignment: .leading, spacing: 8) {
                                    Text("生成的 Voicing 数据:")
                                        .font(.caption)
                                        .fontWeight(.medium)
                                    
                                    Text(generatePreviewJSON())
                                        .font(.system(.caption, design: .monospaced))
                                        .foregroundColor(.secondary)
                                        .padding()
                                        .background(Color.gray.opacity(0.05))
                                        .cornerRadius(6)
                                }
                                .padding()
                            }
                        }
                        
                        Spacer(minLength: 20)
                    }
                    .padding(.horizontal)
                }
            }
            .navigationTitle("")
            .toolbar {
                ToolbarItem(placement: .cancellationAction) {
                    Button("取消") {
                        isPresented = false
                    }
                }
                
                ToolbarItem(placement: .confirmationAction) {
                    Button("保存") {
                        saveVoicing()
                    }
                    .disabled(!canSave)
                    .fontWeight(.semibold)
                }
            }
        }
        .onReceive(midiManager.$sustainOn) { pressed in
            pedalPressed = pressed
            if pressed {
                captureCurrentNotes()
            }
        }
        .alert("保存结果", isPresented: $showingSaveAlert) {
            Button("确定") { }
        } message: {
            Text(saveAlertMessage)
        }
    }
    
    // MARK: - Computed Properties
    
    private var canSave: Bool {
        return !id.isEmpty && !name.isEmpty && !capturedNotes.isEmpty
    }
    
    // MARK: - Helper Methods
    
    private func captureCurrentNotes() {
        // 捕获当前按下的MIDI音符
        let currentNotes = Array(midiManager.pressedNotes).sorted()
        if !currentNotes.isEmpty {
            capturedNotes = currentNotes
        }
    }
    
    private func midiNoteToString(_ midiNote: Int) -> String {
        let noteNames = ["C", "C#", "D", "D#", "E", "F", "F#", "G", "G#", "A", "A#", "B"]
        let octave = midiNote / 12 - 1
        let noteIndex = midiNote % 12
        return "\(noteNames[noteIndex])\(octave)"
    }
    
    private func generatePreviewJSON() -> String {
        let voicingItem = ChordVoicingItem(
            id: id,
            name: name,
            suffix: suffix.isEmpty ? name : suffix,
            description: description.isEmpty ? name : description,
            intervals: intervals,
            root_note: rootNote,
            tags: ChordTags(
                quality: "", // 留空给手工填写
                structure: "",
                inversion: "",
                complexity: "",
                family: []
            ),
            metadata: [
                "difficulty": 0, // 留空给手工填写
                "common_progressions": [],
                "jazz_usage": ""
            ]
        )
        
        // 简化的JSON显示
        return """
{
  "id": "\(voicingItem.id)",
  "name": "\(voicingItem.name)",
  "suffix": "\(voicingItem.suffix)",
  "description": "\(voicingItem.description)",
  "intervals": \(voicingItem.intervals),
  "root_note": "\(voicingItem.root_note)",
  "tags": { ... },
  "metadata": { ... }
}
"""
    }
    
    private func saveVoicing() {
        guard canSave else {
            saveAlertMessage = "请填写完整的信息并录制音符"
            showingSaveAlert = true
            return
        }
        
        // 创建新的voicing项目
        let newVoicing = ChordVoicingItem(
            id: id,
            name: name,
            suffix: suffix.isEmpty ? name : suffix,
            description: description.isEmpty ? name : description,
            intervals: intervals,
            root_note: rootNote,
            tags: ChordTags(
                quality: "", // 留空给手工填写
                structure: "",
                inversion: "",
                complexity: "",
                family: []
            ),
            metadata: [
                "difficulty": 0, // 留空给手工填写
                "common_progressions": [],
                "jazz_usage": ""
            ]
        )
        
        // 打印到控制台（保留原有功能）
        let voicingData = generatePreviewJSON()
        print("🎵 保存 Voicing 数据:")
        print(voicingData)
        
        // 尝试安全地保存到文件
        let saveResult = saveToChordDatabase(newVoicing)
        
        if saveResult.success {
            saveAlertMessage = "✅ Voicing 已成功保存到数据库！\n同时已打印到控制台备份。"
        } else {
            saveAlertMessage = "⚠️ 自动保存失败：\(saveResult.error)\n\n数据已打印到控制台，请手工添加到 chord_voicing.json 文件中。"
        }
        
        showingSaveAlert = true
        
        // 如果保存成功，清空表单
        if saveResult.success {
            clearForm()
        }
    }
    
    private func saveToChordDatabase(_ newVoicing: ChordVoicingItem) -> (success: Bool, error: String) {
        guard let url = Bundle.main.url(forResource: "chord_voicing", withExtension: "json") else {
            return (false, "找不到 chord_voicing.json 文件")
        }
        
        // 获取Documents目录的路径（Bundle中的文件是只读的）
        let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask)[0]
        let writableURL = documentsPath.appendingPathComponent("chord_voicing.json")
        
        do {
            // 首先检查Documents目录中是否已有文件
            let fileURL: URL
            if FileManager.default.fileExists(atPath: writableURL.path) {
                fileURL = writableURL
            } else {
                // 如果Documents中没有，从Bundle复制一份
                let data = try Data(contentsOf: url)
                try data.write(to: writableURL)
                fileURL = writableURL
            }
            
            // 读取现有数据
            let data = try Data(contentsOf: fileURL)
            let originalDatabase = try JSONDecoder().decode(ChordVoicingDatabase.self, from: data)
            
            // 检查ID是否已存在
            if originalDatabase.chord_database.contains(where: { $0.id == newVoicing.id }) {
                return (false, "ID '\(newVoicing.id)' 已存在，请使用不同的ID")
            }
            
            // 创建新的数据库实例，包含新的voicing
            var updatedChordDatabase = originalDatabase.chord_database
            updatedChordDatabase.append(newVoicing)
            
            let updatedDatabase = ChordVoicingDatabase(
                chord_database: updatedChordDatabase,
                category_definitions: originalDatabase.category_definitions,
                practice_patterns: originalDatabase.practice_patterns,
                practice_groups: originalDatabase.practice_groups // 新增：保持现有的practice_groups
            )
            
            // 创建备份
            let backupURL = documentsPath.appendingPathComponent("chord_voicing_backup_\(Int(Date().timeIntervalSince1970)).json")
            try data.write(to: backupURL)
            
            // 保存更新后的数据
            let encoder = JSONEncoder()
            encoder.outputFormatting = [.prettyPrinted, .sortedKeys]
            let updatedData = try encoder.encode(updatedDatabase)
            try updatedData.write(to: fileURL)
            
            print("✅ 数据已保存到: \(fileURL.path)")
            print("📁 备份已创建: \(backupURL.path)")
            
            return (true, "")
            
        } catch {
            return (false, error.localizedDescription)
        }
    }
    
    private func clearForm() {
        id = ""
        name = ""
        suffix = ""
        description = ""
        capturedNotes.removeAll()
    }
}

// MARK: - Input Field Component

struct InputField: View {
    let title: String
    @Binding var text: String
    let placeholder: String
    
    var body: some View {
        VStack(alignment: .leading, spacing: 4) {
            Text(title)
                .font(.caption)
                .fontWeight(.medium)
                .foregroundColor(.secondary)
            
            TextField(placeholder, text: $text)
                .textFieldStyle(.roundedBorder)
        }
    }
}

#Preview {
    VoicingCreationView(
        isPresented: .constant(true),
        midiManager: MIDIManager()
    )
} 