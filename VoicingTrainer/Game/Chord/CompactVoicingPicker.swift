//
//  CompactVoicingPicker.swift
//  FullUI
//
//  Created by <PERSON> on 2025/5/26.
//  紧凑和弦选择器组件 - 用于左侧显示
//

import SwiftUI

// MARK: - 紧凑和弦选择器视图

/**
 * 紧凑的和弦选择器组件
 * 设计为在左侧显示，节省空间
 */
struct CompactVoicingPicker: View {
    @ObservedObject var voicingManager: ChordVoicingManager
    let gameState: ChordGameState
    let onStart: () -> Void
    let onStop: () -> Void
    let onReplay: () -> Void
    
    var body: some View {
        VStack(spacing: 16) {
            // 标题
            Text("Voicing")
                .font(.headline)
                .fontWeight(.semibold)
                .foregroundColor(.primary)
            
            // 和弦选择器
            Picker("Chord Voicing", selection: $voicingManager.selectedVoicing) {
                ForEach(voicingManager.voicings) { voicing in
                    Text(voicing.name)
                        .tag(voicing as ChordVoicing?)
                }
            }
            .pickerStyle(MenuPickerStyle())
            .disabled(gameState != .idle)
            .opacity(gameState == .idle ? 1.0 : 0.7)
            
            // 选中的和弦信息
            if let selectedVoicing = voicingManager.selectedVoicing {
                VStack(spacing: 8) {
                    // 和弦类型
                    InfoRow(
                        label: "Type",
                        value: selectedVoicing.type,
                        icon: "music.note"
                    )
                    
                    // 基础音符
                    InfoRow(
                        label: "Base",
                        value: selectedVoicing.base_note,
                        icon: "tuningfork"
                    )
                    
                    // 音符数量
                    InfoRow(
                        label: "Notes",
                        value: "\(selectedVoicing.intervals.count)",
                        icon: "number"
                    )
                }
            }
            
            Spacer()
            
            // 控制按钮
            VStack(spacing: 12) {
                // 开始/停止按钮
                StartStopButton(
                    gameState: gameState,
                    isEnabled: voicingManager.selectedVoicing != nil,
                    onStart: onStart,
                    onStop: onStop
                )
                
                // 重播按钮
                ReplayButton(
                    gameState: gameState,
                    onReplay: onReplay
                )
            }
        }
        .padding()
        .frame(width: 200)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(.ultraThinMaterial)
                .shadow(color: .black.opacity(0.05), radius: 4, x: 0, y: 2)
        )
    }
}

// MARK: - 信息行组件

/**
 * 信息显示行组件
 */
struct InfoRow: View {
    let label: String
    let value: String
    let icon: String
    
    var body: some View {
        HStack(spacing: 8) {
            Image(systemName: icon)
                .font(.caption)
                .foregroundColor(.secondary)
                .frame(width: 16)
            
            Text(label)
                .font(.caption)
                .foregroundColor(.secondary)
            
            Spacer()
            
            Text(value)
                .font(.caption)
                .fontWeight(.medium)
                .foregroundColor(.primary)
        }
    }
}

// MARK: - 开始/停止按钮

/**
 * 开始/停止按钮组件
 */
struct StartStopButton: View {
    let gameState: ChordGameState
    let isEnabled: Bool
    let onStart: () -> Void
    let onStop: () -> Void
    
    var body: some View {
        Button(action: {
            HapticFeedback.buttonTap()
            if gameState == .idle {
                HapticFeedback.gameStart()
                onStart()
            } else {
                onStop()
            }
        }) {
            HStack(spacing: 6) {
                Image(systemName: gameState == .idle ? "play.fill" : "stop.fill")
                    .font(.caption)
                
                Text(gameState == .idle ? "Start" : "Stop")
                    .font(.caption)
                    .fontWeight(.semibold)
            }
            .foregroundColor(.white)
            .padding(.horizontal, 16)
            .padding(.vertical, 8)
            .background(
                LinearGradient(
                    gradient: Gradient(colors: gameState == .idle ? [.green, .blue] : [.red, .orange]),
                    startPoint: .leading,
                    endPoint: .trailing
                )
            )
            .cornerRadius(12)
        }
        .disabled(!isEnabled && gameState == .idle)
        .opacity((!isEnabled && gameState == .idle) ? 0.5 : 1.0)
        .scaleEffect(gameState == .idle ? 1.0 : 0.95)
        .animation(.easeInOut(duration: 0.2), value: gameState)
    }
}

// MARK: - 重播按钮

/**
 * 重播按钮组件
 */
struct ReplayButton: View {
    let gameState: ChordGameState
    let onReplay: () -> Void
    
    var body: some View {
        Button(action: {
            HapticFeedback.buttonTap()
            onReplay()
        }) {
            HStack(spacing: 6) {
                Image(systemName: "speaker.wave.2.fill")
                    .font(.caption)
                
                Text("Replay")
                    .font(.caption)
                    .fontWeight(.semibold)
            }
            .foregroundColor(.white)
            .padding(.horizontal, 16)
            .padding(.vertical, 8)
            .background(
                LinearGradient(
                    gradient: Gradient(colors: gameState == .waitingForResponse ? [.purple, .blue] : [.gray, .gray]),
                    startPoint: .leading,
                    endPoint: .trailing
                )
            )
            .cornerRadius(12)
        }
        .disabled(gameState != .waitingForResponse)
        .opacity(gameState == .waitingForResponse ? 1.0 : 0.6)
        .scaleEffect(0.9)
        .animation(.easeInOut(duration: 0.2), value: gameState)
    }
}

// MARK: - 预览

#Preview {
    CompactVoicingPicker(
        voicingManager: ChordVoicingManager(),
        gameState: .idle,
        onStart: {},
        onStop: {},
        onReplay: {}
    )
    .padding()
    .background(Color.gray.opacity(0.1))
} 