//
//  NewProgressionViewMIDIHandler.swift
//  VoicingTrainer
//
//  Created by AI Assistant on 2025/6/15.
//

import Foundation
import SwiftUI
import Combine

// MARK: - 简化的MIDI处理器

/// 简化的NewProgressionView MIDI处理器 - 直接处理当前按键状态
class ImprovedNewProgressionViewMIDIHandler: MIDIRoutingManager.MIDIInputHandler {
    
    // 绑定到NewProgressionView的状态
    @Binding private var recordingState: RecordingState
    @Binding private var recordedChords: [RecordedChord]
    @Binding private var currentPressedNotes: Set<Int>
    @Binding private var pedalWasPressed: Bool
    @Binding private var currentChordName: String
    @Binding private var currentChordNotes: [String]
    
    // 引用
    private weak var midiManager: MIDIManager?
    
    // 保存功能需要的管理器
    private let userProgressionManager = UserProgressionManager.shared
    
    init(recordingState: Binding<RecordingState>,
         recordedChords: Binding<[RecordedChord]>,
         currentPressedNotes: Binding<Set<Int>>,
         pedalWasPressed: Binding<Bool>,
         currentChordName: Binding<String>,
         currentChordNotes: Binding<[String]>,
         midiManager: MIDIManager) {
        self._recordingState = recordingState
        self._recordedChords = recordedChords
        self._currentPressedNotes = currentPressedNotes
        self._pedalWasPressed = pedalWasPressed
        self._currentChordName = currentChordName
        self._currentChordNotes = currentChordNotes
        self.midiManager = midiManager
        
        print("🎹 ImprovedNewProgressionViewMIDIHandler 初始化完成")
    }
    
    deinit {
        print("🎹 ImprovedNewProgressionViewMIDIHandler 已释放")
    }
    
    // MARK: - MIDIInputHandler 协议实现
    
    func handleMIDIInput(_ notes: Set<Int>) {
        print(">>> Handler: Received notes \(notes.sorted())")
        
        // 直接更新当前按下的音符
        currentPressedNotes = notes
        
        // 实时分析当前和弦
        updateCurrentChordDisplay(notes)
        
        // 处理录制状态
        handleRecordingState(notes)
    }
    
    func handleSustainInput(_ sustainOn: Bool) {
        print("🦶 踏板输入: \(sustainOn)")
        
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }
            
            print("🦶 踏板状态: \(sustainOn), 当前音符: \(self.currentPressedNotes.sorted())")
            
            self.pedalWasPressed = sustainOn
            
            // 踏板按下时，如果有音符就录制
            if sustainOn && !self.currentPressedNotes.isEmpty {
                print("🦶 ✅ 踏板按下，录制当前和弦")
                self.recordChord()
                self.recordingState = .waiting
            }
        }
    }
    
    // MARK: - 私有方法
    
    private func handleRecordingState(_ notes: Set<Int>) {
        if !notes.isEmpty && recordingState == .waiting {
            recordingState = .recording
        } else if notes.isEmpty && recordingState == .recording {
            recordingState = .waiting
        }
    }
    
    /// 更新当前和弦显示
    private func updateCurrentChordDisplay(_ notes: Set<Int>) {
        if notes.isEmpty {
            currentChordName = ""
            currentChordNotes.removeAll()
        } else {
            let sortedNotes = Array(notes).sorted()
            let chordNames = ChordNameParser.analyze(midiNotes: sortedNotes)
            currentChordName = chordNames.first ?? "Unknown"
            
            // 生成音符名称
            currentChordNotes = sortedNotes.map { midiNote in
                let noteNames = ["C", "C#", "D", "D#", "E", "F", "F#", "G", "G#", "A", "A#", "B"]
                let noteIndex = midiNote % 12
                let octave = midiNote / 12 - 1
                return "\(noteNames[noteIndex])\(octave)"
            }
        }
    }
    
    /// 录制和弦
    private func recordChord() {
        guard !currentPressedNotes.isEmpty else { 
            print("🦶 ❌ 无法录制：没有按下的音符")
            return 
        }
        
        let notes = Array(currentPressedNotes).sorted()
        let chordNames = ChordNameParser.analyze(midiNotes: notes)
        let chordName = chordNames.first ?? "Unknown"
        
        let recordedChord = RecordedChord(
            name: chordName,
            notes: notes,
            timestamp: Date()
        )
        
        recordedChords.append(recordedChord)
        
        print("🎵 ✅ 成功录制和弦: \(chordName) - 音符: \(notes)")
        print("🎵 当前已录制和弦总数: \(recordedChords.count)")
    }
    
    // MARK: - 保存功能
    
    /// 保存和弦进行
    func saveProgression(progressionName: String, completion: @escaping (Bool, String) -> Void) {
        // 验证输入
        guard !progressionName.trimmingCharacters(in: .whitespaces).isEmpty else {
            completion(false, "和弦进行名称不能为空")
            return
        }
        
        guard !recordedChords.isEmpty else {
            completion(false, "没有录制任何和弦")
            return
        }
        
        // 转换为用户定义格式
        let chordInfos = recordedChords.map { convertToUserDefinedFormat(chord: $0) }
        
        // 推断调性
        let scale = inferScaleFromChords(chordInfos)
        
        // 创建和弦进行数据
        let progression = Progression(
            name: progressionName.trimmingCharacters(in: .whitespaces),
            style: scale,
            chords: chordInfos
        )
        
        // 保存到文件
        let success = userProgressionManager.saveProgression(progression)
        
        DispatchQueue.main.async {
            if success {
                print("🎵 ✅ 成功保存和弦进行: \(progressionName)")
                completion(true, "保存成功")
            } else {
                print("🎵 ❌ 保存失败")
                completion(false, "保存失败，请稍后重试")
            }
        }
    }
    
    // MARK: - 保存辅助方法
    
    private func convertToUserDefinedFormat(chord: RecordedChord) -> ChordInfo {
        let sortedNotes = chord.notes.sorted()
        guard !sortedNotes.isEmpty else {
            return ChordInfo(suffix: "maj", root: "C4", notes: [0])
        }
        
        let chordNames = ChordNameParser.analyze(midiNotes: sortedNotes)
        guard let bestChordName = chordNames.first else {
            let rootMidi = sortedNotes[0]
            let rootNote = midiToRootNote(rootMidi)
            let intervals = sortedNotes.map { $0 - rootMidi }.sorted()
            return ChordInfo(suffix: "maj", root: rootNote, notes: intervals)
        }
        
        let (rootNoteName, chordSuffix) = parseChordNameComponents(bestChordName)
        let rootMidiNote = findActualRootMidiNote(rootNoteName: rootNoteName, playedNotes: sortedNotes)
        let fullRootNote = midiToRootNote(rootMidiNote)
        let intervals = sortedNotes.map { $0 - rootMidiNote }.sorted()
        
        return ChordInfo(suffix: chordSuffix, root: fullRootNote, notes: intervals)
    }
    
    private func parseChordNameComponents(_ chordName: String) -> (String, String) {
        let mainChordName = chordName.contains("/") ? String(chordName.split(separator: "/")[0]) : chordName
        let noteNames = ["C#", "D#", "F#", "G#", "A#", "C", "D", "E", "F", "G", "A", "B"]
        
        var rootNote = "C"
        var suffix = "maj"
        
        for noteName in noteNames {
            if mainChordName.hasPrefix(noteName) {
                rootNote = noteName
                suffix = String(mainChordName.dropFirst(noteName.count))
                break
            }
        }
        
        if suffix.isEmpty {
            suffix = "maj"
        }
        
        return (rootNote, suffix)
    }
    
    private func findActualRootMidiNote(rootNoteName: String, playedNotes: [Int]) -> Int {
        let noteValue = ["C": 0, "C#": 1, "D": 2, "D#": 3, "E": 4, "F": 5, "F#": 6, "G": 7, "G#": 8, "A": 9, "A#": 10, "B": 11][rootNoteName] ?? 0
        
        return playedNotes.first { $0 % 12 == noteValue } ?? (noteValue + 60)
    }
    
    private func midiToRootNote(_ midiNote: Int) -> String {
        let noteNames = ["C", "C#", "D", "D#", "E", "F", "F#", "G", "G#", "A", "A#", "B"]
        let noteIndex = midiNote % 12
        let octave = midiNote / 12 - 1
        return "\(noteNames[noteIndex])\(octave)"
    }
    
    private func inferScaleFromChords(_ chords: [ChordInfo]) -> String {
        guard let firstChord = chords.first else { return "CM" }
        let rootNote = firstChord.root
        let rootName = String(rootNote.prefix(rootNote.count - 1))
        let suffix = firstChord.suffix
        
        if suffix.contains("m") && !suffix.contains("M") {
            return "\(rootName)m"
        }
        
        return "\(rootName)M"
    }
}


