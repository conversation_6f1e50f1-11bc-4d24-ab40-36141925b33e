//
//  ProgressionSelectView.swift
//  VoicingTrainer
//
//  Created by AI Assistant on 2025/6/19.
//

import SwiftUI

struct ProgressionSelectView: View {
    @Binding var isPresented: Bool
    @Binding var selectedProgressionName: String
    @Binding var bpm: Int
    @Binding var playbackType: ChordPlaybackType
    @Binding var isGameMode: Bool
    @Binding var keepPlayedKeysHighlighted: Bool
    
    @ObservedObject var progressionReader: ProgressionReader
    @ObservedObject var progressionGameManager: ProgressionGameManager
    
    @State private var showingNewProgressionView = false
    @State private var selectedStyle: String = "All"
    @State private var chordCountLimit: String = "No limit"
    
    // 修改：将3个设置变量改为@Binding，从父视图传入
    @Binding var infiniteLoop: Bool  // 无限循环练习
    @Binding var showNotesHints: Bool  // 显示按键提示  
    @Binding var foreverWait: Bool    // 永远等待用户输入
    @Binding var workoutType: String  // 新增：练习类型
    
    // 预览播放状态
    @State private var isPreviewingProgression: String? = nil
    @State private var previewTimer: Timer? = nil
    @State private var currentPreviewChordIndex: Int = 0
    @State private var previewPlayedNotes: [Int] = [] // 跟踪当前播放的音符
    
    // 音频播放器 - 使用传入的MIDIManager
    @StateObject private var chordPlayer: ChordPlayer

    // 直接引用MIDIManager用于预览播放
    private let midiManager: MIDIManager

    // 🛠️ 修复：使用单例BuiltInProgressionManager避免重复创建
    private let builtInManager = BuiltInProgressionManager()

    // 🛠️ 修复：缓存progression可用性状态，避免重复计算
    @State private var progressionAvailabilityCache: [String: Bool] = [:]

    // 🛒 购买弹窗控制
    @State private var showProUpgrade = false
    
    init(
        isPresented: Binding<Bool>,
        selectedProgressionName: Binding<String>,
        bpm: Binding<Int>,
        playbackType: Binding<ChordPlaybackType>,
        isGameMode: Binding<Bool>,
        keepPlayedKeysHighlighted: Binding<Bool>,
        progressionReader: ProgressionReader,
        progressionGameManager: ProgressionGameManager,
        midiManager: MIDIManager,
        infiniteLoop: Binding<Bool>,
        showNotesHints: Binding<Bool>,
        foreverWait: Binding<Bool>,
        workoutType: Binding<String>
    ) {
        self._isPresented = isPresented
        self._selectedProgressionName = selectedProgressionName
        self._bpm = bpm
        self._playbackType = playbackType
        self._isGameMode = isGameMode
        self._keepPlayedKeysHighlighted = keepPlayedKeysHighlighted
        self.progressionReader = progressionReader
        self.progressionGameManager = progressionGameManager
        self.midiManager = midiManager
        self._infiniteLoop = infiniteLoop
        self._showNotesHints = showNotesHints
        self._foreverWait = foreverWait
        self._workoutType = workoutType
        
        // 🛠️ 修复：使用传入的midiManager，避免在init中创建有副作用的对象
        // 这是安全的，因为我们使用的是已存在的MIDIManager实例，不会触发状态更新死循环
        self._chordPlayer = StateObject(wrappedValue: ChordPlayer(midiManager: midiManager))
    }
    
    // 风格分类选项
    private var styleOptions: [String] {
        ["All", "Jazz", "R&B", "Blues", "Funk", "Gospel", "Rock"]
    }
    
    // 和弦数量选项
    private var chordCountOptions: [String] {
        ["No limit", "2 (easy)", "3", "4", "More than 4"]
    }
    
    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // 背景
                Rectangle()
                    .fill(.ultraThinMaterial)
                    .ignoresSafeArea()
                
                VStack(spacing: 0) {
                    // 标题
                    titleSection
                        .padding(.top, 20)
                        .padding(.bottom, 30)
                    
                    // 主要内容区域
                    HStack(alignment: .top, spacing: 20) {
                        // 左侧：风格分类
                        styleSection
                            .frame(maxWidth: 150)
                        
                        // 中间：设置选项
                        VStack(alignment: .leading,spacing: 30) {
                            chordCountSection
                            
                            //mode,workoutType,practice mode:三个是一行，层层递进的关系
                            HStack(spacing: 10){
                                modeSection
                                workoutTypesSection
                                if  workoutType == "All 12 keys" { practicePatternSection }
                                //if isGameMode { workoutTypesSection }
                                //if isGameMode && workoutType == "All 12 keys" { practicePatternSection }
                                Spacer()
                            }
                            
                            playbackSection
                                                        
                            settingsTogglesSection
                        }
                        .frame(maxWidth: 550)
                        
                        // 右侧：和弦进行列表
                        progressionListSection
                            .frame(maxWidth: 300)
                           // .frame(maxWidth: .infinity)
                    }
                    .padding(.horizontal, 30)
                    .frame(maxHeight: .infinity)
                    
                    Spacer()
                    
                    // 底部按钮
                    bottomButtons
                        .padding(.bottom, 30)
                }
            }
        }
        .onAppear {
            progressionReader.scanAvailableProgressions()
        }
        .onDisappear {
            // 🛑 修复：关闭视图时停止任何正在播放的预览音频
            stopProgressionPreview()
            print("🎵 ProgressionSelectView 关闭，停止音频播放")
        }
        .onReceive(NotificationCenter.default.publisher(for: NSNotification.Name("PurchaseStatusChanged"))) { _ in
            // 🛠️ 修复：购买状态变化时清除缓存
            progressionAvailabilityCache.removeAll()
        }
        .overlay(
            // 🛒 Pro升级弹窗
            Group {
                if showProUpgrade {
                    ZStack {
                        Color.black.opacity(0.5)
                            .ignoresSafeArea()
                            .onTapGesture {
                                showProUpgrade = false
                            }

                        ProUpgradeView(
                            featureName: "高级和弦进行",
                            onUpgrade: {
                                // 升级完成后的回调
                                showProUpgrade = false
                                progressionAvailabilityCache.removeAll() // 清除缓存
                            },
                            onDismiss: {
                                // 🔧 关闭ProUpgradeView
                                showProUpgrade = false
                            }
                        )
                    }
                }
            }
        )
    }
    
    // MARK: - 标题区域
    
    private var titleSection: some View {
        Text("Select a Progression to Practice")
            .font(.system(size: 28, weight: .bold, design: .rounded))
            .foregroundColor(.primary)
    }
    
    // MARK: - 风格分类区域
    
    private var styleSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Styles")
                .font(.system(size: 18, weight: .semibold))
                .foregroundColor(.primary)
            
            VStack(spacing: 12) {
                ForEach(styleOptions, id: \.self) { style in
                    styleButton(for: style)
                }
            }
        }
    }
    
    private func styleButton(for style: String) -> some View {
        Button(action: {
            withAnimation(.easeInOut(duration: 0.2)) {
                selectedStyle = style
            }
        }) {
            HStack {
                Text(style)
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(selectedStyle == style ? .white : styleColor(for: style))
                Spacer()
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .frame(height: 44)
            .frame(maxWidth: .infinity)
            .contentShape(Rectangle())
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(selectedStyle == style ? styleColor(for: style) : Color.clear)
                    .overlay(
                        RoundedRectangle(cornerRadius: 8)
                            .stroke(styleColor(for: style), lineWidth: 2)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    private func styleColor(for style: String) -> Color {
        switch style {
        case "Jazz": return Color(hex: "#4A90E2") 
        case "R&B": return Color(hex: "#7ED321") ?? .green
        case "Blues": return Color(hex: "#F5A623") ?? .orange
        case "Funk": return Color(hex: "#9013FE") ?? .purple
        case "Gospel": return Color(hex: "#D0021B") ?? .red
        case "Rock": return Color(hex: "#50E3C2") ?? .teal
        default: return Color.blue
        }
    }
    
    // MARK: - 和弦数量选择区域
    
    private var chordCountSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Chord Count")
                .font(.system(size: 18, weight: .semibold))
                .foregroundColor(.primary)
            
            Menu {
                ForEach(chordCountOptions, id: \.self) { option in
                    Button(option) {
                        chordCountLimit = option
                    }
                }
            } label: {
                HStack {
                    Text(chordCountLimit)
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(.primary)
                    
                    Spacer()
                    
                    Image(systemName: "chevron.up.chevron.down")
                        .font(.system(size: 12))
                        .foregroundColor(.secondary)
                }
                .padding(.horizontal, 16)
                .padding(.vertical, 12)
                .frame(height: 44)
                .background(
                    RoundedRectangle(cornerRadius: 8)
                        .fill(Color.gray.opacity(0.1))
                        .overlay(
                            RoundedRectangle(cornerRadius: 8)
                                .stroke(Color.gray.opacity(0.3), lineWidth: 1)
                        )
                )
            }
            .buttonStyle(PlainButtonStyle())
        }
    }
    
    // MARK: - 模式选择区域
    
    private var modeSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Mode")
                .font(.system(size: 18, weight: .semibold))
                .foregroundColor(.primary)
            
            VStack(spacing: 8) {
                radioButton(title: "Listen", isSelected: !isGameMode) { isGameMode = false }
                radioButton(title: "Practice", isSelected: isGameMode) { isGameMode = true }
            }
        }.frame(maxWidth: 120)
    }
    
    // MARK: - 回放模式区域
    
    private var playbackSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Playback")
                .font(.system(size: 18, weight: .semibold))
                .foregroundColor(.primary)
            
            VStack(spacing: 8) {
                radioButton(title: "Block Chord", isSelected: playbackType == .block_chord) { playbackType = .block_chord }
                radioButton(title: "Arpeggio", isSelected: playbackType == .arpeggio_chord) { playbackType = .arpeggio_chord }
            }
        }
    }
    
    // MARK: - 练习类型区域（条件显示）
    
    private var workoutTypesSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Workout Types")
                .font(.system(size: 18, weight: .semibold))
                .foregroundColor(.primary)
            
            VStack(spacing: 8) {
                radioButton(title: "Just this key", isSelected: workoutType == "just this key") { workoutType = "just this key" }
                radioButton(title: "All 12 keys", isSelected: workoutType == "All 12 keys") { workoutType = "All 12 keys" }
            }
        }.frame(maxWidth: 150)
    }
    
    // MARK: - 练习模式区域（条件显示）
    
    private var practicePatternSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Practice Pattern")
                .font(.system(size: 18, weight: .semibold))
                .foregroundColor(.primary)
            
            Menu {
                ForEach(progressionGameManager.patternManager.availablePatterns) { pattern in
                    Button(action: {
                        progressionGameManager.patternManager.selectedPattern = pattern
                    }) {
                        HStack {
                            Text(pattern.name)
                                .font(.system(size: 16))
                            if progressionGameManager.selectedPracticePattern?.id == pattern.id {
                                Spacer()
                                Image(systemName: "checkmark")
                            }
                        }
                    }
                    .font(.system(size: 16))
                }
            } label: {
                HStack {
                    Text(progressionGameManager.selectedPracticePattern?.name ?? "Practice Mode")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(.primary)
                    
                    Spacer()
                    
                    Image(systemName: "chevron.up.chevron.down")
                        .font(.system(size: 12))
                        .foregroundColor(.secondary)
                }
                .padding(.horizontal, 16)
                .padding(.vertical, 12)
                .frame(height: 44)
                .background(
                    RoundedRectangle(cornerRadius: 8)
                        .fill(Color.gray.opacity(0.1))
                        .overlay(
                            RoundedRectangle(cornerRadius: 8)
                                .stroke(Color.gray.opacity(0.3), lineWidth: 1)
                        )
                )
            }
            .buttonStyle(PlainButtonStyle())
        }
        .frame(maxWidth: .infinity)
    }
    
    // MARK: - 设置开关区域
    
    private var settingsTogglesSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Settings")
                .font(.system(size: 18, weight: .semibold))
                .foregroundColor(.primary)
            
            VStack(alignment: .leading,spacing: 8) {
                toggleRow(title: "Infinite Workout", isOn: $infiniteLoop)
                toggleRow(title: "Note Hints Keyboard", isOn: $showNotesHints)
                if isGameMode {
                    toggleRow(title: "Forever Wait", isOn: $foreverWait)
                }
            }
        }.frame(maxWidth: 400)
    }
    
    // MARK: - 和弦进行列表区域
    
    private var progressionListSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Progressions(\(filteredProgressions.count))")
                .font(.system(size: 18, weight: .semibold))
                .foregroundColor(.primary)
            
            ScrollView {
                LazyVStack(spacing: 8) {
                    ForEach(filteredProgressions, id: \.self) { progressionName in
                        progressionRow(for: progressionName)
                    }
                }
                .padding(.vertical, 8)
            }
            .frame(maxHeight: 400)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color.gray.opacity(0.05))
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(Color.gray.opacity(0.2), lineWidth: 1)
                    )
            )
        }
    }
    
    private func progressionRow(for progressionName: String) -> some View {
        Button(action: {
            // 🛠️ 修复：直接更新状态，避免"Modifying state during view update"警告
            withAnimation(.easeInOut(duration: 0.15)) {
                selectedProgressionName = progressionName
            }
        }) {
            HStack {
                VStack(alignment: .leading, spacing: 2) {
                    Text(progressionName)
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(.primary)
                        .lineLimit(1)
                        .frame(maxWidth: .infinity, alignment: .leading)
                    
                    // 预览进度指示器
                    if isPreviewingProgression == progressionName {
                        HStack(spacing: 4) {
                            Image(systemName: "speaker.wave.2")
                                .font(.system(size: 10))
                                .foregroundColor(.blue)
                            
                            Text("Playing \(currentPreviewChordIndex)/\(progressionReader.currentProgression?.chords.count ?? 0)")
                                .font(.system(size: 10))
                                .foregroundColor(.blue)
                                .animation(.easeInOut(duration: 0.3), value: currentPreviewChordIndex)
                        }
                    }
                    
                    // 显示内购锁定状态（基于当前progression）
                    if progressionReader.currentProgression?.name == progressionName,
                       let lockDescription = progressionReader.currentProgression?.getLockDescription() {
                        LockedItemIndicator(description: lockDescription)
                    }
                }
                
                Spacer()
                
                // 锁定状态图标
                if progressionReader.currentProgression?.name == progressionName,
                   let progression = progressionReader.currentProgression,
                   !progression.isAvailable() {
                    Image(systemName: "lock.fill")
                        .font(.system(size: 16))
                        .foregroundColor(.orange)
                        .frame(width: 24, height: 24)
                }
                
                // 预览按钮
                Button(action: {
                    if isPreviewingProgression == progressionName {
                        stopProgressionPreview()
                    } else {
                        playProgressionPreview(progressionName)
                    }
                }) {
                    let isPlaying = isPreviewingProgression == progressionName
                    Image(systemName: isPlaying ? "stop.circle.fill" : "speaker.wave.2.circle.fill")
                        .font(.system(size: 20))
                        .foregroundColor(isPlaying ? .red : .blue)
                        .frame(width: 24, height: 24)
                        .scaleEffect(isPlaying ? 1.1 : 1.0)
                        .animation(.easeInOut(duration: 0.2), value: isPlaying)
                }
                .buttonStyle(PlainButtonStyle())
                
                // 选中状态图标
                if selectedProgressionName == progressionName {
                    Image(systemName: "checkmark.circle.fill")
                        .font(.system(size: 16))
                        .foregroundColor(.blue)
                }
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
            .frame(maxWidth: .infinity)
            .contentShape(Rectangle())
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(selectedProgressionName == progressionName ? Color.blue.opacity(0.1) : Color.clear)
            )
            .opacity({
                if progressionReader.currentProgression?.name == progressionName,
                   let progression = progressionReader.currentProgression {
                    return progression.isAvailable() ? 1.0 : 0.7
                }
                return 1.0
            }())
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    // MARK: - 底部按钮
    
    private var bottomButtons: some View {
        HStack(spacing: 20) {
            Spacer()

            // Cancel 按钮
            Button(action: {
                // 🛑 修复：取消时停止任何正在播放的预览音频
                stopProgressionPreview()
                isPresented = false
            }) {
                Text("Cancel")
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(.primary)
                    .frame(width: 120, height: 44)
                    .background(
                        RoundedRectangle(cornerRadius: 8)
                            .fill(Color.gray.opacity(0.2))
                            .overlay(
                                RoundedRectangle(cornerRadius: 8)
                                    .stroke(Color.gray.opacity(0.4), lineWidth: 1)
                            )
                    )
            }
            .buttonStyle(PlainButtonStyle())

            // Select 按钮
            Button(action: {
                // 🛑 修复：选择时停止任何正在播放的预览音频
                stopProgressionPreview()

                // 🛒 检查是否需要购买
                if !selectedProgressionName.isEmpty && !isSelectedProgressionAvailable {
                    // 显示购买弹窗
                    showProUpgrade = true
                } else {
                    // 直接选择progression
                    isPresented = false
                }
            }) {
                VStack(spacing: 2) {
                    Text("Select")
                        .font(.system(size: 16, weight: .semibold))
                        .foregroundColor(selectButtonTextColor)

                    // 如果progression需要升级，显示提示文字
                    if !selectedProgressionName.isEmpty && !isSelectedProgressionAvailable {
                        Text("Requires Pro")
                            .font(.system(size: 10, weight: .medium))
                            .foregroundColor(.white.opacity(0.8))
                    }
                }
                .frame(width: 120, height: 44)
                .background(
                    RoundedRectangle(cornerRadius: 8)
                        .fill(selectButtonBackgroundColor)
                )
            }
            .buttonStyle(PlainButtonStyle())
            .disabled(selectedProgressionName.isEmpty) // 🛒 修改：只有未选择时才禁用
        }
        .padding(.horizontal, 40)
    }
    
    // MARK: - 辅助组件
    
    private func radioButton(title: String, isSelected: Bool, action: @escaping () -> Void) -> some View {
        Button(action: {
            // 🔧 延迟动画避免布局递归
            DispatchQueue.main.async {
                withAnimation(.easeInOut(duration: 0.15)) {
                    action()
                }
            }
        }) {
            HStack(spacing: 8) {
                Image(systemName: isSelected ? "largecircle.fill.circle" : "circle")
                    .font(.system(size: 16))
                    .foregroundColor(isSelected ? .blue : .secondary)
                
                Text(title)
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(.primary)
                
                Spacer()
            }
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    private func toggleRow(title: String, isOn: Binding<Bool>) -> some View {
        HStack {
            Text(title)
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(.primary)
            
            Spacer()
            
            Toggle("", isOn: isOn)
                .scaleEffect(0.8)
                .toggleStyle(SwitchToggleStyle(tint: .blue))
        }
    }
    
    // MARK: - 数据过滤

    private var filteredProgressions: [String] {
        // 重构后的版本：直接使用availableProgressions
        return progressionReader.availableProgressions
    }

    // MARK: - Select按钮状态控制

    /// 检查当前选中的progression是否可用
    private var isSelectedProgressionAvailable: Bool {
        guard !selectedProgressionName.isEmpty else { return false }

        // 🛠️ 修复：使用缓存避免重复计算，提高性能并避免重用错误
        return getProgressionAvailability(selectedProgressionName)
    }

    /// 获取progression可用性（带缓存）
    private func getProgressionAvailability(_ progressionName: String) -> Bool {
        // 检查缓存
        if let cached = progressionAvailabilityCache[progressionName] {
            return cached
        }

        // 计算可用性
        let isAvailable: Bool
        if let builtInProgression = builtInManager.loadProgression(progressionName) {
            isAvailable = builtInProgression.isAvailable()
        } else {
            // 如果不是内置文件，假设用户自定义文件都是可用的
            isAvailable = true
        }

        // 🛠️ 修复：异步缓存结果，避免在视图更新期间修改状态
        DispatchQueue.main.async {
            self.progressionAvailabilityCache[progressionName] = isAvailable
        }

        return isAvailable
    }



    /// Select按钮的背景颜色
    private var selectButtonBackgroundColor: Color {
        if selectedProgressionName.isEmpty {
            return Color.gray
        } else if isSelectedProgressionAvailable {
            return Color.blue
        } else {
            return Color.orange // 需要升级的progression用橙色
        }
    }

    /// Select按钮的文字颜色
    private var selectButtonTextColor: Color {
        if selectedProgressionName.isEmpty || !isSelectedProgressionAvailable {
            return .white.opacity(0.8)
        } else {
            return .white
        }
    }
    
    // MARK: - 音频预览
    
    private func playProgressionPreview(_ progressionName: String) {
        print("🎵 开始预览和弦进行: \(progressionName)")
        
        // 停止当前预览
        stopProgressionPreview()
        
        // 设置预览状态
        isPreviewingProgression = progressionName
        currentPreviewChordIndex = 0
        
        // 加载和弦进行
        progressionReader.loadProgression(progressionName)
        
        // 等待加载完成后开始播放
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            self.playNextChordInPreview()
        }
    }
    
    private func stopProgressionPreview() {
        print("🛑 停止和弦进行预览")
        
        // 停止当前播放的音符
        for note in previewPlayedNotes {
            midiManager.stopListenNote(note)
        }
        previewPlayedNotes.removeAll()
        
        // 停止定时器
        previewTimer?.invalidate()
        previewTimer = nil
        
        // 重置状态
        isPreviewingProgression = nil
        currentPreviewChordIndex = 0
    }
    
    private func playNextChordInPreview() {
        // 🛡️ 安全检查：确保预览状态仍然有效
        guard let progressionName = isPreviewingProgression,
              let progression = progressionReader.currentProgression,
              progression.name == progressionName else {
                  print("⚠️ 预览状态无效，停止预览 \(isPreviewingProgression) : \(progressionReader.currentProgression?.name)")
            stopProgressionPreview()
            return
        }
        
        // 🛡️ 边界检查：确保索引在有效范围内
        guard currentPreviewChordIndex >= 0 && currentPreviewChordIndex < progression.chords.count else {
            print("⚠️ 和弦索引越界: \(currentPreviewChordIndex)/\(progression.chords.count)")
            stopProgressionPreview()
            return
        }
        
        // 🛡️ 安全检查：确保和弦名称数组有效
        guard currentPreviewChordIndex < progressionReader.chordNames.count else {
            print("⚠️ 和弦名称数组索引越界: \(currentPreviewChordIndex)/\(progressionReader.chordNames.count)")
            stopProgressionPreview()
            return
        }
        
        // 使用ProgressionReader获取和弦信息
        let chordName = progressionReader.chordNames[currentPreviewChordIndex]
        let chordNotes = progressionReader.getChordMIDINotes(at: currentPreviewChordIndex)
        
        // 🛡️ 验证MIDI音符有效性
        guard !chordNotes.isEmpty else {
            print("⚠️ 和弦音符为空，跳过: \(chordName)")
            currentPreviewChordIndex += 1
            // 递归调用下一个和弦
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                self.playNextChordInPreview()
            }
            return
        }
        
        print("🎵 预览播放和弦 \(currentPreviewChordIndex + 1)/\(progression.chords.count): \(chordName)")
        print("🎵 直接播放MIDI音符: \(chordNotes)")
        
        // 停止上一个和弦的音符
        for note in previewPlayedNotes {
            midiManager.stopListenNote(note)
        }
        previewPlayedNotes.removeAll()
        
        // 🛡️ 安全播放MIDI音符
        previewPlayedNotes = chordNotes
        for note in chordNotes {
            // 🛡️ 验证MIDI音符范围
            guard note >= 0 && note <= 127 else {
                print("⚠️ 无效的MIDI音符: \(note)")
                continue
            }
            midiManager.playListenNote(note, velocity: 80)
        }
        
        // 准备播放下一个和弦
        currentPreviewChordIndex += 1
        
        // 🛡️ 检查是否还有更多和弦要播放
        if currentPreviewChordIndex >= progression.chords.count {
            // 播放完成，停止预览
            print("🎵 预览播放完成")
            DispatchQueue.main.asyncAfter(deadline: .now() + 1.5) {
                self.stopProgressionPreview()
            }
            return
        }
        
        // 设置定时器播放下一个和弦（每个和弦播放1.5秒）
        previewTimer = Timer.scheduledTimer(withTimeInterval: 1.5, repeats: false) { _ in
            // 🛡️ 再次检查预览状态
            guard self.isPreviewingProgression == progressionName else {
                print("⚠️ 预览已停止，取消定时器")
                return
            }
            
            // 短暂间隔后播放下一个和弦
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                self.playNextChordInPreview()
            }
        }
    }
}

 
