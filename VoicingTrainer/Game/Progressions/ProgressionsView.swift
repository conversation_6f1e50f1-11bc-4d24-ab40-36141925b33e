//
//  ProgressionsView.swift
//  FullUI
//
//  Created by <PERSON> Li on 2025/5/26.
//
import SwiftUI

// 🎨 CUSTOMIZE COLOR: 背景状态枚举
enum ProgressionBackgroundState {
    case normal     // 正常状态 - 灰蓝色
    case success    // 成功状态 - 绿色
}

/// ProgressionsView的MIDI输入处理器
class ProgressionsViewMIDIHandler: MIDIRoutingManager.MIDIInputHandler {
    
    // 引用
    private weak var chromaCircleViewModel: ChromaCircleViewModel?
    private weak var keyboardViewModel: PianoKeyboardViewModel?
    
    init(chromaCircleViewModel: ChromaCircleViewModel, keyboardViewModel: PianoKeyboardViewModel) {
        self.chromaCircleViewModel = chromaCircleViewModel
        self.keyboardViewModel = keyboardViewModel
    }
    
    func handleMIDIInput(_ notes: Set<Int>) {
        // 更新 ChromaCircleView 的按键状态
        chromaCircleViewModel?.setPressedNotes(Array(notes))
        
        if !notes.isEmpty {
            if ChromaCircleViewModel.debugEnabled {
                print("🎯 Circle12: 音符按下 \(notes.sorted())")
            }
        }
    }
    
    func handleSustainInput(_ sustainOn: Bool) {
        // ProgressionsView 不需要特殊的踏板处理
    }
}

struct ProgressionsView: View {
    // 使用ProgressionReader来管理和弦进行数据
    @StateObject private var progressionReader = ProgressionReader()
    @State private var selectedChordIndex = 0
    @State private var selectedProgressionName = ""

    // 🎵 配置管理器
    @ObservedObject private var configManager = GameConfigManager.shared

    // 🔄 练习状态管理
    @StateObject private var practiceStateManager = PracticeStateManager.shared

    // 🎵 使用全局BPM，不再需要本地状态
    private var bpm: Int {
        configManager.config.gameSettings.globalBpm
    }

    // 🎵 创建全局BPM的绑定
    private var bpmBinding: Binding<Int> {
        Binding(
            get: { configManager.config.gameSettings.globalBpm },
            set: { newValue in configManager.updateGlobalBpm(newValue) }
        )
    }
    
    // 播放类型相关 : 缺省用 听和弦回放
    @State private var playbackType: ChordPlaybackType = .block_chord

    // 🎆 Stars 动画测试状态
    @State private var showStarsAnimation = false

    // 🧪 测试模式标志 - 防止测试按钮触发游戏逻辑
    @State private var isTestMode = false
    
    private var debugInfo :Bool = false
    
    // MIDI播放相关 - 移除本地播放状态，使用ProgressionGameManager
    let midiManager: MIDIManager
    
    // 键盘显示相关
    @ObservedObject var keyboardViewModel: PianoKeyboardViewModel
    
    // Circle12NotesView 相关
    @StateObject private var chromaCircleViewModel = ChromaCircleViewModel()
    
    // 游戏模式相关 - 统一使用ProgressionGameManager
    @State private var isWorkoutMode = false
    @StateObject private var progressionGameManager: ProgressionGameManager
    
    @State private var keepPlayedKeysHighlighted = true  // 是否保持播放按键高亮
    private var beatDivider = 4.0 //每拍时间再除以这个数。 4.0:16分音符。 2.0:8分音符  1.0:4分音符
    
    // 新和弦进行录制
    @State private var showingNewProgressionView = false
    
    // 显示和弦进行选择界面
    @State private var showingProgressionSelect = false
    
    // Endless mode 支持
    @State private var isEndlessMode = false
    
    // 新增：连接ProgressionSelectView中的4个设置变量
    @State private var infiniteLoop = false  // 无限循环练习
    @State private var showNotesHints = true  // 显示按键提示
    @State private var foreverWait = true    // 永远等待用户输入
    @State private var workoutType = "just this key"  // 练习类型

    // MIDI 处理器相关
    @Environment(\.midiRoutingManager) private var midiRoutingManager
    @State private var midiHandler: ProgressionsViewMIDIHandler?
    
    // 内购相关
    @StateObject private var purchaseManager = PurchaseManager.shared
    @State private var showProUpgrade = false

    // MARK: - 📊 统计系统
    @StateObject private var statisticsViewModel = StatisticsViewModel()
    @State private var practiceStartTime: Date?
    @State private var totalPlayedNotes: Int = 0
    @State private var statisticsAlreadySaved: Bool = false
    
    // 🌟 新增：绿色光芒效果管理器
    @StateObject private var greenBurstManager = GreenBurstEffectManager()

    // 🎆 新增：白色粒子系统管理器 (暂时注释)
    // @StateObject private var whiteParticleCoordinator = WhiteParticleCoordinator.shared

    // 🎨 新增：背景颜色状态管理
    @State private var backgroundState: ProgressionBackgroundState = .normal
    @State private var backgroundAnimationProgress: Double = 0.0

    // 🎛️ 控制变量：是否使用绿色爆炸效果
    @State private var useGreenExplode: Bool = false

    // 🎨 背景渐变色配置 - 深灰蓝渐变，确保绿色成功反馈突出显示
    // 🎨 CUSTOMIZE COLOR: 可自定义的背景颜色配置
    private let normalBackgroundGradient = LinearGradient(
        gradient: Gradient(colors: [
            Color(hex: "#1A1A2E"),  // 深灰蓝
            Color(hex: "#16213E"),  // 中灰蓝
            Color(hex: "#0F3460")   // 深蓝灰
        ]),
        startPoint: .topLeading,
        endPoint: .bottomTrailing
    )

    // 🎨 CUSTOMIZE COLOR: 成功时的绿色背景渐变 - 调整为更柔和的绿色
    private let successBackgroundGradient = LinearGradient(
        gradient: Gradient(colors: [
            Color(hex: "#4a7c59"),  // 柔和的中绿色（降低亮度）
            Color(hex: "#2d5016"),  // 暗绿色（降低亮度）
            Color(hex: "#1a2e1a")   // 深绿灰色（降低亮度）
        ]),
        startPoint: .topLeading,
        endPoint: .bottomTrailing
    )

    init(midiManager: MIDIManager, keyboardViewModel: PianoKeyboardViewModel) {
        self.midiManager = midiManager
        self.keyboardViewModel = keyboardViewModel
        self._progressionGameManager = StateObject(wrappedValue: ProgressionGameManager(midiManager: midiManager, gameConfig: GameConfigManager.shared.config))
    }
    
    private func CallTestLoadProg(){
        // 创建管理器
        let builtInManager = BuiltInProgressionManager()

        // 调试Bundle内容
        builtInManager.debugBundleContents()

        // 查看可用文件
        print("可用文件: \(builtInManager.availableProgressions)")

        // 加载特定文件
        if let progression = builtInManager.loadProgression("lofi03") {
            print("加载成功: \(progression.name)")

            // 🎵 关键修改：将加载的progression设置到progressionReader中
            // 这样UI就能检测到有可用的progression，从而启用播放按钮
            DispatchQueue.main.async {
                self.progressionReader.currentProgression = progression
                print("✅ 已将测试progression设置到progressionReader: \(progression.name)")
                print("   和弦数量: \(progression.chords.count)")
                print("   调性: \(progression.key ?? "未指定")")
            }
        } else {
            print("加载失败")
        }

    }
    
    var body: some View {
        ZStack {
            // 🎨 动态背景 - 根据状态变化颜色
            dynamicBackgroundView
                .ignoresSafeArea(.all)

            // 🎆 白色粒子系统背景层 (暂时注释)
            // WhiteParticleView()
            //     .environmentObject(whiteParticleCoordinator)
            //     .allowsHitTesting(false)
            //     .ignoresSafeArea(.all)
            //     .zIndex(1)

            mainContentLayout
            
            // 🌟 绿色光芒效果层（在ChromaCircleView下方）
            greenBurstEffectLayer
            
            // 🎉 练习总结弹窗 - 练习完成时显示
            if progressionGameManager.showCompletionView {
                PracticeSummaryView(
                    rightNoteCount: progressionGameManager.correctChordCount,
                    errorNoteCount: progressionGameManager.totalChordCount - progressionGameManager.correctChordCount,
                    totalTime: progressionGameManager.totalTime,
                    isPresented: Binding(
                        get: { progressionGameManager.showCompletionView },
                        set: { _ in
                            // 用户关闭时调用专门的方法来重置状态
                            progressionGameManager.dismissCompletionView()
                        }
                    )
                    // Progressions模式：只显示关闭按钮，不显示重玩和下一关按钮
                )
                .zIndex(2)
            }

            // 全屏幕和弦进行选择界面
            if showingProgressionSelect {
                ProgressionSelectView(
                    isPresented: $showingProgressionSelect,
                    selectedProgressionName: $selectedProgressionName,
                    bpm: bpmBinding,
                    playbackType: $playbackType,
                    isGameMode: $isWorkoutMode,
                    keepPlayedKeysHighlighted: $keepPlayedKeysHighlighted,
                    progressionReader: progressionReader,
                    progressionGameManager: progressionGameManager,
                    midiManager: midiManager,
                    infiniteLoop: $infiniteLoop,
                    showNotesHints: $showNotesHints,
                    foreverWait: $foreverWait,
                    workoutType: $workoutType
                )
                .zIndex(1)
            }
            
            // Pro升级弹窗
            if showProUpgrade {
                ZStack {
                    Color.black.opacity(0.5)
                        .ignoresSafeArea()
                        .onTapGesture {
                            showProUpgrade = false
                        }
                    
                    ProUpgradeView(
                        featureName: "高级和弦进行",
                        onUpgrade: {
                            // 升级完成后的回调
                            showingProgressionSelect = true
                        },
                        onDismiss: {
                            // 🔧 关闭ProUpgradeView
                            showProUpgrade = false
                        }
                    )
                }
                .zIndex(2)
            }
        }
        .onAppear {
            // 🔧 延迟初始化避免布局递归
            DispatchQueue.main.async {
                // 设置初始选中的和弦进行
                if !progressionReader.availableProgressions.isEmpty && selectedProgressionName.isEmpty {
                    selectedProgressionName = progressionReader.availableProgressions.first ?? ""
                    if !selectedProgressionName.isEmpty {
                        loadSelectedProgression(selectedProgressionName)
                    }
                }
                
                // 建立游戏管理器和视图模型的连接
                progressionGameManager.keyboardViewModel = keyboardViewModel
                progressionGameManager.chromaCircleViewModel = chromaCircleViewModel
                
                // 🔄 恢复上次练习状态
                if let progressionState = practiceStateManager.getProgressionState() {
                    // 恢复上次选择的和弦进行
                    selectedProgressionName = progressionState.selectedProgressionName
                    workoutType = progressionState.workoutType
                    isWorkoutMode = progressionState.isWorkoutMode
                    playbackType = ChordPlaybackType(rawValue: progressionState.playbackType) ?? .block_chord

                    // 加载和弦进行
                    progressionReader.loadProgression(progressionState.selectedProgressionName)
                    print("🔄 恢复Progression练习状态: \(progressionState.selectedProgressionName), \(progressionState.workoutType)")
                }

                // 🎯 设置统一的和弦匹配管理器的粒子爆炸回调
                setupChordMatchManager()

                // 设置MIDI处理器
                setupMIDIHandler()

                // 初始化显示第一个和弦（如果有的话）
                if !progressionReader.chordNames.isEmpty {
                    updateKeyboardDisplay()
                }
                
                // 🌟 设置绿色光芒效果回调
                setupGreenBurstEffect()

                // 🎨 设置背景动画监听器
                setupBackgroundAnimationListener()

                // 🎬 设置和弦成功动画监听器
                setupChordSuccessAnimationListener()

                // 🎆 设置白色粒子爆炸监听器 (暂时注释)
                // setupWhiteParticleListener()

                // 🎹 配置人性化琶音 - 让琶音听起来更自然
                progressionGameManager.configureHumanizedArpeggio(
                    timingVariation: 0.25,    // ±25% 时间变化
                    velocityVariation: 18,    // ±18 力度变化
                    useCrescendo: true,       // 启用渐强效果
                    randomizeOrder: false     // 保持传统从低到高顺序
                )
                print("🎹 人性化琶音已配置完成")
            }
        }
        .onDisappear {
            // 页面消失时停止播放 - 统一由ProgressionGameManager处理
            if progressionGameManager.gameState != .idle {
                progressionGameManager.stopGame()
            }
            // 取消注册MIDI处理器
            midiRoutingManager.unregisterHandler(for: .progressions)
            midiHandler = nil
        }
        .onChange(of: progressionReader.currentProgression) { _ in
            // 和弦进行加载完成后，显示第一个和弦
            if progressionGameManager.gameState != .idle {
                // 🔧 延迟更新避免布局递归
                DispatchQueue.main.async {
                    updateKeyboardDisplay()
                }
            }
        }
        .onChange(of: progressionReader.availableProgressions) { progressions in
            // 当扫描到和弦进行文件时，自动加载第一个
            if !progressions.isEmpty && selectedProgressionName.isEmpty {
                // 🔧 延迟更新避免布局递归
                DispatchQueue.main.async {
                    selectedProgressionName = progressions.first ?? ""
                    if !selectedProgressionName.isEmpty {
                        loadSelectedProgression(selectedProgressionName)
                    }
                }
            }
        }
        .onChange(of: selectedProgressionName) { newValue in
            // 处理从ProgressionSelectView返回的选择
            if !newValue.isEmpty && progressionGameManager.gameState == .idle {
                // 🔧 延迟更新避免布局递归
                DispatchQueue.main.async {
                    loadSelectedProgression(newValue)
                }
            }
        }
        .onChange(of: infiniteLoop) { newValue in
            // 同步infiniteLoop和isEndlessMode
            // 🔧 延迟更新避免布局递归
            DispatchQueue.main.async {
                isEndlessMode = newValue
                print("🔄 同步无限循环设置: \(newValue)")
            }
        }
        // 📊 统计系统：监听练习完成状态
        .onReceive(progressionGameManager.$showCompletionView) { showCompletion in
            if showCompletion && practiceStartTime != nil && isWorkoutMode {
                // 练习自然完成，保存统计数据
                savePracticeStatistics()
                print("📊 统计系统：练习自然完成，已保存数据")
            }
        }
        // 📊 统计系统：监听和弦变化来累计音符数
        .onReceive(progressionGameManager.$expectedNotes) { expectedNotes in
            if practiceStartTime != nil && isWorkoutMode && !expectedNotes.isEmpty {
                totalPlayedNotes += expectedNotes.count
            }
        }

    }
    
    // MARK: - 布局计算属性
    
    /// 主要内容布局
    private var mainContentLayout: some View {
        VStack(spacing: 0) {
            //
            ProgressionToolbar
            // 顶部：专业和弦进行显示控件(显示多个和弦)
            ChordProgressionDisplayHeader
            // 主内容区域
            mainContentArea
        }
    }
    
    /// 和弦显示头部
    private var ChordProgressionDisplayHeader: some View {
        
      
        return ProfessionalChordDisplayView(
            chords: transposedChordNames,
            currentIndex: getCurrentDisplayIndex(),
            isPlaying: progressionGameManager.gameState != .idle,
            progressPercentage: getCurrentProgressPercentage(),
            showProgress: !isEndlessMode && !infiniteLoop,
            onChordTapped: { index in
                selectChord(at: index)
            }
        )
        .frame(height: 80)
        .background(.ultraThinMaterial)
        .overlay(
            Rectangle()
                .frame(height: 0.5)
                .foregroundColor(.gray.opacity(0.3)),
            alignment: .bottom
        )
    }
    
    /// 主内容区域（修改版）
    private var mainContentArea: some View {
        ZStack(alignment: .topTrailing) {

            // 🎮 游戏信息显示 - 右上角 多学习布局
            ScoreInfoView
                .padding(.top, 10)
                .padding(.trailing, 20)
                .zIndex(100) // 确保在最上层
            
            // 🌟 绿色光芒效果背景层 - 受 useGreenExplode 控制
            if useGreenExplode {
                SimpleGreenBurst()
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
                    .zIndex(50)  // 确保在合适的层级
            }

            //GreenBurstEffectView()
            //    .frame(maxWidth: .infinity, maxHeight: .infinity)
             //   .zIndex(100)
            
            /*
            if greenBurstManager.shouldTrigger {
                let _ = print("🌟 显示 GreenBurstEffectView")
                
                GreenBurstEffectView()
                    .onAppear {
                        // 延迟触发以确保视图已渲染
                        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                            // 通过通知触发爆发效果
                            NotificationCenter.default.post(
                                name: NSNotification.Name("TriggerGreenBurst"), 
                                object: nil
                            )
                        }
                    }
            }
            */
            
            // 和弦名称(支持多个名称) - 🔧 修复：在练习时也显示
            chordNameView
                .zIndex(5) // 🎯 确保和弦名称在动画上方

            // 半音圆（在绿色光芒上方）
            ChromaCircleView(viewModel: chromaCircleViewModel)
                .frame(maxWidth: .infinity, maxHeight: .infinity)
                .zIndex(1)

            // 🎆 Stars 动画层 - 用于测试
            if showStarsAnimation {
                LottieAnimationView.scoreAnimation {
                    print("🎆 ProgressionsView Stars 动画播放完成")
                    showStarsAnimation = false
                }
                .allowsHitTesting(false)
                .offset(y: 50) // 🎯 向下偏移 50 点
                .zIndex(3)
            }

            // 游戏启动/停止
            controlButtons
                .zIndex(2)
        }
    }
    
    /// 内容覆盖层
    private var ProgressionToolbar: some View {
        VStack(spacing: 20) {
            // 🎮 只在idle状态显示顶部控制区域，练习和播放时隐藏以提供更大显示面积
            if progressionGameManager.gameState == .idle {
                topControlArea

                // 无限循环模式说明
                if isEndlessMode {
                    endlessLoopDescription
                }
            }
        }
    }
    
    /// 顶部控制区域
    private var topControlArea: some View {
        HStack(spacing: 15) {
            // 选择练习的和弦进行按钮
            progressionSelectButton

            Spacer()

            // 🎵 BPM控制器
            bpmControlView
        }
        .padding(.horizontal)
        .padding(.top, 10)
    }

    /// 🎮 游戏信息视图
    private var ScoreInfoView: some View {
        SimpleGameInfoView(
            correctChordCount: progressionGameManager.correctChordCount,
            totalChordCount: progressionGameManager.totalChordCount, // 🎯 传递总和弦数
            isTestMode: isTestMode, // 🧪 传递测试模式状态
            chordsPerProgression: getChordsPerProgression() // 🎵 传递每个和弦进行的和弦数
        )
    }

    /// 🎵 获取每个和弦进行的和弦数（用于12个调模式的thumbs up动画）
    private func getChordsPerProgression() -> Int? {
        // 只在12个调模式下返回单个和弦进行的和弦数
        if workoutType == "All 12 keys", let progression = progressionReader.currentProgression {
            return progression.chords.count
        }
        return nil
    }
    
    /// 和弦进行选择按钮
    private var progressionSelectButton: some View {
        Button(action: {
            showingProgressionSelect = true
        }) {
            Image(systemName: "music.note.list")
                .foregroundColor(.blue)
        }
        .frame(width: 44, height: 44)
        .background(
            Circle()
                .fill(.ultraThinMaterial)
                .opacity(progressionGameManager.gameState == .idle ? 1.0 : 0.6)
        )
        .disabled(progressionGameManager.gameState != .idle)
        .animation(.easeInOut(duration: 0.2), value: progressionGameManager.gameState)
    }
    
    /// 无限循环开关
    private var endlessLoopToggle: some View {
        HStack(spacing: 8) {
            Text("无限循环")
                .font(.subheadline)
                .foregroundColor(.primary)
            Toggle("", isOn: $isEndlessMode)
                .labelsHidden()
                .disabled(progressionGameManager.gameState != .idle)
        }
    }
    
    /// 无限循环说明
    private var endlessLoopDescription: some View {
        Text(isWorkoutMode ? "无限循环：可以一直练习每个和弦，直到主动停止" : "无限循环：和弦进行播放完后会自动重新开始")
            .font(.caption)
            .foregroundColor(.blue)
            .multilineTextAlignment(.center)
            .padding(.horizontal)
    }
    
    /// 控制按钮
    private var controlButtons: some View {
        VStack {
            Spacer()
            
            HStack {
                Spacer()
                
                VStack(spacing: 10) {
                    // 重播按钮
                    if (isWorkoutMode || workoutType == "All 12 keys") && progressionGameManager.gameState == .waitingForResponse {
                        replayButton
                    }
                    
                    // 主要控制按钮
                    mainControlButton
                }
            }
        }
        .padding(.trailing, 0)
        .padding(.bottom, 0)
    }
    
    /// 重播按钮
    private var replayButton: some View {
        Button("重播") {
            progressionGameManager.replayCurrentChord()
        }
        .padding(.horizontal, 20)
        .padding(.vertical, 10)
        .background(Color.orange)
        .foregroundColor(.white)
        .cornerRadius(25)
    }
    
    /// 主控制按钮
    private var mainControlButton: some View {
        PremiumPlayStopButton(
            isPlaying: progressionGameManager.gameState != .idle,
            isDisabled: progressionReader.currentProgression == nil
        ) {
            // 检查当前和弦进行的内购状态
            if let progression = progressionReader.currentProgression {
                if progression.isAvailable() {
                    // 可以播放
                    if isWorkoutMode {
                        if progressionGameManager.gameState == .idle {
                            startGame()
                        } else {
                            stopGame()
                        }
                    } else {
                        // Listen模式：如果选择了"All 12 keys"，使用ProgressionGameManager处理移调
                        if workoutType == "All 12 keys" {
                            if progressionGameManager.gameState == .idle {
                                startGame()  // 使用移调逻辑
                            } else {
                                stopGame()
                            }
                        } else {
                            // "just this key"：使用简单的本地播放
                            if progressionGameManager.gameState == .idle {
                                startGame()
                            } else {
                                stopGame()
                            }
                        }
                    }
                } else {
                    // 需要升级Pro版
                    showProUpgrade = true
                }
            }
        }
    }
    
    // MARK: - 计算属性
    
    // 计算属性：当前显示的音符
    private var currentNotes: [String] {
        let displayIndex = getCurrentDisplayIndex()
        let midiNotes = progressionReader.getChordMIDINotes(at: displayIndex)
        return midiNotes.map { midiToNoteName($0) }
    }
    
    // 计算属性：当前和弦名称
    private var currentChordName: String {
        let displayIndex = getCurrentDisplayIndex()
        guard displayIndex < transposedChordNames.count else {
            return "Unknown"
        }
        return transposedChordNames[displayIndex]
    }
    
    // 🔧 新增：获取移调后的和弦名称数组
    private var transposedChordNames: [String] {
        // 直接从ProgressionGameManager获取移调后的和弦名称
        let gameManagerNames = progressionGameManager.getTransposedChordNames()
        return gameManagerNames.isEmpty ? progressionReader.chordNames : gameManagerNames
    }
    
    // 计算属性：当前和弦的MIDI音符
    private var currentChordMidiNotes: [Int] {
        let displayIndex = getCurrentDisplayIndex()
        return progressionReader.getChordMIDINotes(at: displayIndex)
    }
    
    // 计算和弦透明度
    private func getChordOpacity(for index: Int) -> Double {
        let currentIndex = getCurrentDisplayIndex()
        return index == currentIndex ? 1.0 : 0.6
    }
    
    // 计算和弦缩放比例
    private func getChordScale(for index: Int) -> Double {
        let currentIndex = getCurrentDisplayIndex()
        return index == currentIndex ? 1.1 : 1.0
    }
    
    // MARK: - 播放控制函数 - 委托给ProgressionGameManager
    
    func playCurrentProgression() {
        guard let progression = progressionReader.currentProgression, !progression.chords.isEmpty else {
            print("❌ 没有可播放的和弦进行")
            return
        }
        
        print("🎵 ProgressionsView: 委托给ProgressionGameManager播放和弦进行")
        
        // 委托给ProgressionGameManager处理播放
        progressionGameManager.playProgression(
            progression: progression,
            playbackType: playbackType,
            bpm: bpm,
            keepHighlighted: keepPlayedKeysHighlighted,
            endlessMode: isEndlessMode,
            transposeToAllKeys: workoutType == "All 12 keys"
        )
    }
    
    func stopProgression() {
        print("🛑 ProgressionsView: 委托给ProgressionGameManager停止播放")
        
        // 委托给ProgressionGameManager处理停止
        progressionGameManager.stopProgression()
        
        // 清除UI状态
        keyboardViewModel.appReleaseAllKeys()
        chromaCircleViewModel.clearAll()
        
        if ChromaCircleViewModel.debugEnabled {
            print("🎯 播放停止 - 已清除ChromaCircleView状态")
        }
    }
    

    
    /// 加载选中的和弦进行
    private func loadSelectedProgression(_ progressionName: String) {
        print("🎵 加载选中的和弦进行: \(progressionName)")
        
        // 停止当前播放
        if progressionGameManager.gameState != .idle {
            stopProgression()
        }
        
        // 重置选中的和弦索引
        selectedChordIndex = 0
        
        // 加载新的和弦进行 - 支持用户自定义和弦进行
        progressionReader.loadProgression(progressionName)
    }
    
    /// 更新键盘显示当前选中的和弦
    private func updateKeyboardDisplay() {
        guard progressionGameManager.gameState == .idle else { return }
        
        let chordNotes = currentChordMidiNotes
        print("🎹 更新键盘显示: 和弦[\(currentChordName)] 音符\(chordNotes)")
        
        keyboardViewModel.appSetCurrentChord(chordNotes)
        
        // 🎯 同时更新Circle12NotesView显示当前选中的和弦，带上根音
        let displayIndex = getCurrentDisplayIndex()
        
        if let progression = progressionReader.currentProgression,
           displayIndex < progression.chords.count {
            let currentChord = progression.chords[displayIndex]
            let rootMIDI = currentChord.getRootNote()
            chromaCircleViewModel.setExpectedNotes(chordNotes, rootNote:rootMIDI)
        } else {
            chromaCircleViewModel.setExpectedNotes(chordNotes)
        }
        
        // 同时更新Circle12NotesView显示当前选中的和弦
        //chromaCircleViewModel.setExpectedNotes(chordNotes)
    }
    
    // MARK: - 新增视图组件
    
    /// 状态显示视图
    @ViewBuilder
    private var statusView: some View {
        if isWorkoutMode {
            // 游戏模式状态
            VStack(spacing: 5) {
                switch progressionGameManager.gameState {
                case .idle:
                    Text("准备练习")
                        .font(.title3)
                        .foregroundColor(.green)
                case .playingChord:
                    Text("播放中...")
                        .font(.title3)
                        .foregroundColor(.blue)
                case .waitingForResponse:
                    Text("等待输入: \(progressionGameManager.currentChordName)")
                        .font(.title3)
                        .foregroundColor(.orange)
                case .waitingResponse:
                    Text("反应时间...")
                        .font(.title3)
                        .foregroundColor(.purple)
                case .completed:
                    Text("练习完成!")
                        .font(.title3)
                        .foregroundColor(.green)
                }
                
                if progressionGameManager.totalChordCount > 0 {
                    Text("进度: \(progressionGameManager.progressText)")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
        } else {
            StatusInfoView
        }
    }
    
    /// 游戏状态显示视图
    private var gameStatusView: some View {
        VStack(spacing: 10) {
            HStack {
                Text("当前和弦:")
                    .font(.caption)
                    .foregroundColor(.secondary)
                
                Text(progressionGameManager.currentChordName.isEmpty ? "未开始" : progressionGameManager.currentChordName)
                    .font(.title2)
                    .fontWeight(.semibold)
                    .foregroundColor(.primary)
            }
            
            if progressionGameManager.totalChordCount > 0 {
                if progressionGameManager.isEndlessMode {
                    // 无限循环模式显示
                    HStack(spacing: 8) {
                        Image(systemName: "infinity")
                            .foregroundColor(.blue)
                        Text("无限循环模式 - 当前第 \(progressionGameManager.currentChordIndex + 1) 个和弦")
                            .font(.caption)
                            .foregroundColor(.blue)
                    }
                } else {
                    // 进度条
                    ProgressView(value: progressionGameManager.progressPercentage)
                        .progressViewStyle(LinearProgressViewStyle())
                        .frame(height: 8)
                        .accentColor(.blue)
                }
            }
        }
        .padding()
        .background(Color.gray.opacity(0.1))
        .cornerRadius(10)
    }
    
    private var StatusInfoView: some View{
        // 播放模式状态
        VStack(spacing: 5) {
            if progressionGameManager.gameState != .idle {
                if isEndlessMode {
                    HStack(spacing: 8) {
                        Image(systemName: "infinity")
                            .foregroundColor(.blue)
                        Text("无限循环播放...")
                            .font(.title3)
                            .foregroundColor(.blue)
                    }
                } else {
                    Text("Playing...")
                        .font(.title3)
                        .foregroundColor(.blue)
                }
            } else {
                /*
                Text("Ready to Play")
                    .font(.title3)
                    .foregroundColor(.green)
                 */
            }
        }
    }
    
    /// BPM控制视图（使用全局BPM）
    private var bpmControlView: some View {
        VStack {
            Text("BPM")
                .font(.caption)
                .foregroundColor(.secondary)

            HStack {
                Button("-") {
                    if progressionGameManager.gameState == .idle {
                        let currentBpm = configManager.config.gameSettings.globalBpm
                        let minBpm = configManager.config.gameSettings.minBpm
                        let newBpm = max(minBpm, currentBpm - 10)
                        configManager.updateGlobalBpm(newBpm)
                    }
                }
                .disabled(progressionGameManager.gameState != .idle)

                Text("\(configManager.config.gameSettings.globalBpm)")
                    .frame(width: 50)
                    .font(.title3)

                Button("+") {
                    if progressionGameManager.gameState == .idle {
                        let currentBpm = configManager.config.gameSettings.globalBpm
                        let maxBpm = configManager.config.gameSettings.maxBpm
                        let newBpm = min(maxBpm, currentBpm + 10)
                        configManager.updateGlobalBpm(newBpm)
                    }
                }
                .disabled(progressionGameManager.gameState != .idle)
            }

            // 🎵 显示基于BPM的等待时间
            Text(getWaitTimeText())
                .font(.caption2)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
    }

    /// 🎵 获取等待时间显示文本
    private func getWaitTimeText() -> String {
        let waitTime = GameConfigManager.shared.getProgressionWaitTime(bpm: bpm)
        return String(format: "%.1fs/chord", waitTime)
    }
    
    // MARK: - 游戏控制函数
    
    /// 开始游戏
    private func startGame() {
        guard let progression = progressionReader.currentProgression else {
            print("❌ 没有选择和弦进行")
            return
        }
        
        print("🎮 开始和弦进行练习游戏")
        
        // 清除键盘显示
        keyboardViewModel.appReleaseAllKeys()
        
        // 应用设置：infiniteLoop控制isEndlessMode
        let actualEndlessMode = infiniteLoop
        let showKeyboardHints = showNotesHints
        
        // 🎵 Listen和Practice模式都支持移调：选择"All 12 keys"时启用移调功能
        let isTransposeAllKeys = (workoutType == "All 12 keys")
        
        print("🎮 游戏设置:")
        print("🎮 模式: \(isWorkoutMode ? "Practice" : "Listen")")
        print("🎮 无限循环: \(actualEndlessMode)")
        print("🎮 显示按键提示: \(showKeyboardHints)")
        print("🎮 永远等待输入: \(foreverWait)")
        print("🎮 练习类型: \(workoutType)")
        print("🎮 移调功能启用: \(isTransposeAllKeys)")
        print("🎮 移调逻辑: workoutType(\(workoutType)) -> \(isTransposeAllKeys)")

        // 📊 统计系统：开始练习时初始化（只在练习模式下）
        if isWorkoutMode {
            practiceStartTime = Date()
            totalPlayedNotes = 0
            statisticsAlreadySaved = false
        }

        // 🔄 保存练习状态
        practiceStateManager.saveProgressionState(
            progressionName: selectedProgressionName,
            workoutType: workoutType,
            transposition: 0, // 开始时的移调
            chordIndex: 0,    // 开始时的和弦索引
            isWorkoutMode: isWorkoutMode,
            bpm: bpm,
            playbackType: playbackType.rawValue
        )

        // 开始游戏
        progressionGameManager.startGame(
            with: progression,
            workoutMode:isWorkoutMode,
            playbackType: playbackType,             //block和弦或者arpeggio模式
            bpm: bpm,
            keepHighlighted: showKeyboardHints,
            endlessMode: actualEndlessMode,
            transposeToAllKeys: isTransposeAllKeys,
            foreverWait: foreverWait
        )
    }
    
    /// 停止游戏
    private func stopGame() {
        print("🛑 停止和弦进行练习游戏")

        // 📊 统计系统：停止练习时保存数据（只在练习模式下）
        if isWorkoutMode {
            savePracticeStatistics()
        }

        // 停止游戏
        progressionGameManager.stopGame()
        
        // 清除键盘显示
        keyboardViewModel.appReleaseAllKeys()
        
        // 清除ChromaCircleView状态，防止重复触发粒子爆炸
        chromaCircleViewModel.clearAll()
        
        if ChromaCircleViewModel.debugEnabled {
            print("🎯 游戏停止 - 已清除ChromaCircleView状态")
        }
    }
    
    // MARK: - 🎵 练习模式选择器
    
    /// 练习模式选择器
    private var practicePatternPicker: some View {
        Menu {
            ForEach(progressionGameManager.patternManager.availablePatterns) { pattern in
                Button(action: {
                    progressionGameManager.patternManager.selectedPattern = pattern
                }) {
                    HStack {
                        VStack(alignment: .leading, spacing: 2) {
                            Text(pattern.name)
                                .font(.subheadline)
                            Text(pattern.description)
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                        
                        Spacer()
                        
                        if progressionGameManager.selectedPracticePattern?.id == pattern.id {
                            Image(systemName: "checkmark")
                                .foregroundColor(.blue)
                        }
                    }
                }
            }
        } label: {
            HStack(spacing: 8) {
                VStack(alignment: .leading, spacing: 2) {
                    Text(progressionGameManager.selectedPracticePattern?.name ?? "Select Pattern")
                        .font(.subheadline)
                        .foregroundColor(.primary)
                    
                    if let pattern = progressionGameManager.selectedPracticePattern {
                        Text(pattern.description)
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .lineLimit(2)
                    }
                }
                
                Spacer()
                
                Image(systemName: "chevron.down")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(Color.gray.opacity(0.1))
                    .overlay(
                        RoundedRectangle(cornerRadius: 8)
                            .stroke(Color.gray.opacity(0.3), lineWidth: 1)
                    )
            )
        }
        .padding(.horizontal)
    }
    

    //和弦名称显示
    var chordNameView: some View {
        // 🔧 BugFix: 使用@State变量缓存和弦名称，避免SwiftUI状态管理问题
        ChordNameDisplayView(midiManager: midiManager)
    }
    
    // MARK: - 🎯 和弦匹配管理器设置
    
    /// 设置统一的和弦匹配管理器
    private func setupChordMatchManager() {
        // 设置粒子爆炸回调，让ChromaCircleView响应完美匹配
        progressionGameManager.chordMatchManager.onParticleExplosion = {
            // 获取Circle12NotesView的中心位置作为爆炸位置
            // 通过onParticleExplosion回调触发粒子爆炸
            chromaCircleViewModel.onParticleExplosion?(CGPoint(x: 0, y: 0))
        }
        
        if ChromaCircleViewModel.debugEnabled {
            print("🎯 ProgressionsView - 已设置ChordMatchManager粒子爆炸回调")
        }
    }
    
    // MARK: - MIDI 处理器设置
    
    /// 设置MIDI处理器
    private func setupMIDIHandler() {
        midiHandler = ProgressionsViewMIDIHandler(chromaCircleViewModel: chromaCircleViewModel, keyboardViewModel: keyboardViewModel)
        midiRoutingManager.registerHandler(midiHandler!, for: .progressions)
        if ChromaCircleViewModel.debugEnabled {
            print("🎯 ProgressionsView - 已设置MIDI处理器")
        }
    }
    
    // MARK: - 视图组件
    
    /// 和弦显示视图
    private var chordDisplayView: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 30) {
                ForEach(Array(progressionReader.chordNames.enumerated()), id: \.offset) { index, chord in
                    ChordBadge(name: chord)
                        .onTapGesture {
                            if progressionGameManager.gameState == .idle {
                                // 🔧 延迟状态更新避免布局递归
                                DispatchQueue.main.async {
                                    selectedChordIndex = index
                                    updateKeyboardDisplay()
                                }
                            }
                        }
                        .opacity(getChordOpacity(for: index))
                        .scaleEffect(getChordScale(for: index))
                        .animation(.easeInOut(duration: 0.2), value: selectedChordIndex)
                        .animation(.easeInOut(duration: 0.2), value: progressionGameManager.currentPlayingIndex)
                }
            }
            .padding(.horizontal)
        }
        .padding(.vertical, 30)
    }
    
    // MARK: - 辅助方法
    
    /// 将MIDI音符值转换为音符名称
    private func midiToNoteName(_ midiNote: Int) -> String {
        let noteNames = ["C", "C#", "D", "D#", "E", "F", "F#", "G", "G#", "A", "A#", "B"]
        let noteIndex = midiNote % 12
        let octave = (midiNote / 12) - 1
        return "\(noteNames[noteIndex])\(octave)"
    }
    
    /// 获取当前显示索引
    private func getCurrentDisplayIndex() -> Int {
        if progressionGameManager.gameState != .idle {
            return progressionGameManager.currentPlayingIndex
        } else {
            return selectedChordIndex
        }
    }
    
    /// 获取当前进度百分比
    private func getCurrentProgressPercentage() -> Double {
        let totalChords = transposedChordNames.count
        guard totalChords > 0 else { return 0.0 }
        
        let currentIndex = getCurrentDisplayIndex() + 1
        return Double(currentIndex) / Double(totalChords)
    }
    
    /// 选择和弦
    private func selectChord(at index: Int) {
        guard progressionGameManager.gameState == .idle else { return }
        guard index >= 0 && index < transposedChordNames.count else { return }
        
        selectedChordIndex = index
        updateKeyboardDisplay()
    }
    
    /// 将ProgressionGameState转换为ChordGameState
    private func convertGameState(_ state: ProgressionGameState) -> ChordGameState {
        switch state {
        case .idle:
            return .idle
        case .playingChord:
            return .playingChord
        case .waitingForResponse, .waitingResponse:
            return .waitingForResponse
        case .completed:
            return .completed
        }
    }
    
    /// 🌟 绿色光芒效果层 - 受 useGreenExplode 控制
    private var greenBurstEffectLayer: some View {
        ZStack {
            // 绿色光芒效果（在ChromaCircleView下方）- 只有在启用时才显示
            if useGreenExplode && greenBurstManager.shouldTrigger {
                GreenBurstEffectView()
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
                    .onAppear {
                        // 自动触发爆发效果
                        DispatchQueue.main.asyncAfter(deadline: .now() + 0.05) {
                            if let burstView = self.findGreenBurstView() {
                                burstView.triggerBurst()
                            }
                        }
                    }
            }
        }
        .zIndex(0.5)  // 在ChromaCircleView(zIndex: 1)下方
    }
    
    /// 🌟 设置完整的视觉效果回调
    private func setupGreenBurstEffect() {
        // 🔧 保存原来的回调
        let originalCallback = progressionGameManager.chordMatchManager.onPerfectMatch

        // 🎆 注释掉绿色光芒效果，只使用Lottie动画
        progressionGameManager.chordMatchManager.onPerfectMatch = {
            DispatchQueue.main.async {
                print("🌟 只使用Lottie动画，不触发绿色光芒效果")

                // 🌟 注释掉绿色光芒效果
                // NotificationCenter.default.post(
                //     name: NSNotification.Name("TriggerGreenBurst"),
                //     object: nil
                // )

                // 🔧 关键：调用原来的回调逻辑
                originalCallback?()
            }
        }



        if ChromaCircleViewModel.debugEnabled {
            print("🌟🎆 ProgressionsView - 已设置视觉效果回调")
            print("🌟 ChordMatchManager实例: \(progressionGameManager.chordMatchManager)")
        }
    }

    /// 🎨 设置背景动画监听器
    private func setupBackgroundAnimationListener() {
        NotificationCenter.default.addObserver(
            forName: NSNotification.Name("TriggerSuccessBackgroundAnimation"),
            object: nil,
            queue: .main
        ) { _ in
            DispatchQueue.main.async {
                self.triggerSuccessBackgroundAnimation()
            }
        }
    }

    

    
    /// 查找GreenBurstEffectView实例
    private func findGreenBurstView() -> GreenBurstEffectView? {
        // 这里需要通过ViewBuilder或其他方式获取实例
        // 简化实现：直接创建新实例并触发
        return nil  // 临时实现
    }
}

// 🔧 BugFix: 独立的和弦名称显示组件，使用内部状态管理
struct ChordNameDisplayView: View {
    let midiManager: MIDIManager
    @State private var displayedChordNames: [String] = []
    
    var body: some View {
        VStack(spacing: 4) {
            Spacer()
            
            // 第一个和弦名称最大
            Text(displayedChordNames.first ?? "")
                .font(.title)
                .fontWeight(.bold)
                .foregroundColor(.primary)
                .frame(maxWidth: .infinity, alignment: .center) // 固定首行位置
            
            // 其他和弦名称显示在下面，字体略小
            if displayedChordNames.count > 1 {
                ForEach(Array(displayedChordNames.dropFirst().enumerated()), id: \.offset) { index, chordName in
                    Text(chordName)
                        .font(.title2)
                        .fontWeight(.medium)
                        .foregroundColor(.secondary)
                }
            }
            
            Spacer()
        }
        .padding(.horizontal)
        .onReceive(midiManager.$chordNames) { newChordNames in
            // 🔧 BugFix: 延迟更新本地状态，避免竞争条件和布局递归
            if newChordNames != displayedChordNames {
                DispatchQueue.main.async {
                    displayedChordNames = newChordNames
                    //print("🎵 ChordNameDisplayView 状态已更新: \(displayedChordNames)")
                }
            }
        }
        .onAppear {
            // 初始化时同步状态
            DispatchQueue.main.async {
                displayedChordNames = midiManager.chordNames
                //print("🎵 ChordNameDisplayView 初始化: \(displayedChordNames)")
            }
        }
    }
}

// MARK: - 专业和弦显示控件

/// 专业的和弦显示控件
struct ProfessionalChordDisplayView: View {
    let chords: [String]
    let currentIndex: Int
    let isPlaying: Bool
    let progressPercentage: Double
    let showProgress: Bool
    let onChordTapped: (Int) -> Void
    
    @State private var maxVisibleChords = 5
    @State private var progressBarWidth: CGFloat = 300
    
    var body: some View {
        VStack(spacing: 15) {
            // 和弦显示区域
            GeometryReader { geometry in
                let availableWidth = geometry.size.width - 40 // 减去padding
                
                if chords.isEmpty {
                    // 空状态
                    /*
                    HStack {
                        Spacer()
                        
                        Text("选择和弦进行开始练习")
                            .font(.title3)
                            .foregroundColor(.secondary)
                         
                        Spacer()
                         
                    }
                    .frame(maxHeight: .infinity)
                     */
                    
                } else {
                    // 和弦列表
                    ScrollViewReader { proxy in
                        ScrollView(.horizontal, showsIndicators: false) {
                            HStack(spacing: 20) {
                                ForEach(Array(chords.enumerated()), id: \.offset) { index, chord in
                                    ChordDisplayItem(
                                        chord: chord,
                                        index: index,
                                        currentIndex: currentIndex,
                                        isPlaying: isPlaying,
                                        onTapped: onChordTapped
                                    )
                                    .id(index)
                                }
                            }
                            .padding(.horizontal, 20)
                        }
                        .onChange(of: currentIndex) { newIndex in
                            // 🔧 延迟滚动避免布局递归
                            DispatchQueue.main.async {
                                withAnimation(.easeInOut(duration: 0.5)) {
                                    proxy.scrollTo(newIndex, anchor: .center)
                                }
                            }
                        }
                        .onAppear {
                            // 🔧 延迟初始位置设置避免布局递归
                            DispatchQueue.main.async {
                                if currentIndex < chords.count {
                                    proxy.scrollTo(currentIndex, anchor: .center)
                                }
                            }
                        }
                    }
                }
            }
            .frame(height: 60)
            
            #if os(iOS)
                let screenWidth = UIScreen.main.bounds.width
            #elseif os(macOS)
                let screenWidth = NSScreen.main?.visibleFrame.width ?? 0
            #endif
            

        }
        .padding(.vertical, 15)
    }
}

/// 和弦显示项
struct ChordDisplayItem: View {
    let chord: String
    let index: Int
    let currentIndex: Int
    let isPlaying: Bool
    let onTapped: (Int) -> Void
    
    private var isCurrent: Bool {
        index == currentIndex
    }
    
    private var opacity: Double {
        if isCurrent {
            return 1.0
        } else {
            // 根据距离计算透明度
            let distance = abs(index - currentIndex)
            switch distance {
            case 0: return 1.0
            case 1: return 0.8
            case 2: return 0.6
            default: return 0.4
            }
        }
    }
    
    private var scale: Double {
        isCurrent ? 1.2 : 1.0
    }
    
    var body: some View {
        Text(chord)
            .font(isCurrent ? .title2 : .title3)
            .fontWeight(isCurrent ? .bold : .medium)
            .foregroundColor(isCurrent ? .primary : .secondary)
            .padding(.horizontal, 16)
            .padding(.vertical, 8)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(isCurrent ? Color.blue.opacity(0.15) : Color.clear)
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(
                                isCurrent ? Color.blue.opacity(0.5) : Color.clear,
                                lineWidth: isCurrent ? 2 : 0
                            )
                    )
            )
            .scaleEffect(scale)
            .opacity(opacity)
            .animation(.easeInOut(duration: 0.3), value: isCurrent)
            .animation(.easeInOut(duration: 0.3), value: opacity)
            .onTapGesture {
                if !isPlaying {
                    // 🔧 延迟状态更新避免布局递归
                    DispatchQueue.main.async {
                        withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                            onTapped(index)
                        }
                    }
                }
            }
    }
}

// MARK: - ProgressionsView Extensions

extension ProgressionsView {

    // 🎨 CUSTOMIZE COLOR: 动态背景视图
    /// 根据状态动态变化的背景视图
    private var dynamicBackgroundView: some View {
        Rectangle()
            .fill(currentBackgroundGradient)
            .animation(.easeInOut(duration: 0.8), value: backgroundState)
    }

    // 🎨 CUSTOMIZE COLOR: 当前背景渐变计算
    /// 根据背景状态返回对应的渐变色
    private var currentBackgroundGradient: LinearGradient {
        switch backgroundState {
        case .normal:
            return normalBackgroundGradient
        case .success:
            return successBackgroundGradient
        }
    }



    // 🎨 CUSTOMIZE COLOR: 背景颜色变化动画
    /// 触发背景颜色变化为绿色，然后恢复
    private func triggerSuccessBackgroundAnimation() {
        print("🎨 开始背景颜色变化动画")

        // 变为绿色
        withAnimation(.easeInOut(duration: 0.3)) {
            backgroundState = .success
        }

        // 1.5秒后恢复正常
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.5) {
            withAnimation(.easeInOut(duration: 0.8)) {
                self.backgroundState = .normal
            }
        }
    }

    // 🎬 设置和弦成功动画监听器（从白色粒子函数中分离出来）
    /// 监听来自ProgressionGameManager的动画通知
    private func setupChordSuccessAnimationListener() {
        // 🎵 单个和弦正确的动画
        NotificationCenter.default.addObserver(
            forName: NSNotification.Name("TriggerChordSuccessAnimation"),
            object: nil,
            queue: .main
        ) { notification in
            if let correctNotes = notification.object as? [Int] {
                print("🎵 ProgressionsView: 收到和弦成功通知，音符: \(correctNotes)")

                // 🎵 触发和弦成功动画 (Circular Burst)
                self.chromaCircleViewModel.triggerChordSuccessAnimation(type: .singleChord)
            } else {
                print("❌ ProgressionsView: 和弦成功通知数据格式错误")
            }
        }

        // 🎉 整个进行完成的动画
        NotificationCenter.default.addObserver(
            forName: NSNotification.Name("TriggerProgressionCompleteAnimation"),
            object: nil,
            queue: .main
        ) { notification in
            if let correctNotes = notification.object as? [Int] {
                print("🎉 ProgressionsView: 收到进行完成通知，音符: \(correctNotes)")

                // 🎉 触发进行完成动画 (burst1)
                self.chromaCircleViewModel.triggerChordSuccessAnimation(type: .progression)
            } else {
                print("❌ ProgressionsView: 进行完成通知数据格式错误")
            }
        }

        print("🎬 ProgressionsView: 和弦成功动画监听器设置完成")
    }

    // 🎆 设置白色粒子爆炸监听器（保留实现但暂时不调用）
    /// 监听来自ProgressionGameManager的动画通知
    private func setupWhiteParticleListener() {
        // 🎵 单个和弦正确的白色粒子（暂时注释，只保留实现）
        NotificationCenter.default.addObserver(
            forName: NSNotification.Name("TriggerChordSuccessAnimation"),
            object: nil,
            queue: .main
        ) { notification in
            if let correctNotes = notification.object as? [Int] {
                print("🎆 ProgressionsView: 收到白色粒子爆炸通知，音符: \(correctNotes)")
                // 这里可以添加白色粒子逻辑，但目前暂时注释
            } else {
                print("❌ ProgressionsView: 白色粒子通知数据格式错误")
            }
        }

        print("🎆 ProgressionsView: 白色粒子监听器设置完成（暂时无实际效果）")
    }

    // MARK: - 📊 统计系统

    /// 保存练习统计数据
    private func savePracticeStatistics() {
        guard let startTime = practiceStartTime else {
            print("📊 统计系统：未找到开始时间，跳过保存")
            return
        }

        // 防止重复保存
        guard !statisticsAlreadySaved else {
            print("📊 统计系统：数据已保存，跳过重复保存")
            return
        }

        statisticsAlreadySaved = true

        // 获取练习数据
        let workoutName = progressionReader.currentProgression?.name ?? "和弦进行练习"
        let totalChords = progressionGameManager.totalChordCount
        let rightChords = progressionGameManager.correctChordCount

        // 使用统计工具类保存数据
        PracticeStatisticsHelper.savePracticeData(
            to: statisticsViewModel,
            startTime: startTime,
            workoutType: .progression,
            workoutName: workoutName,
            totalNoteCount: totalPlayedNotes,
            totalCount: totalChords,
            rightCount: rightChords
        )

        /* 原有代码已移至 PracticeStatisticsHelper
        let endTime = Date()
        let duration = Int(endTime.timeIntervalSince(startTime))

        print("📊 统计系统：保存练习数据")
        print("  - 练习名称: \(workoutName)")
        print("  - 开始时间: \(startTime)")
        print("  - 持续时间: \(duration)秒")
        print("  - 总音符数: \(totalPlayedNotes)")
        print("  - 总和弦数: \(totalChords)")
        print("  - 正确和弦数: \(rightChords)")

        // 异步保存统计数据
        Task {
            await statisticsViewModel.addWorkoutItem(
                startTime: startTime,
                duration: duration,
                workoutType: .progression,
                workoutName: workoutName,
                totalNoteCount: totalPlayedNotes,
                totalCount: totalChords,
                rightCount: rightChords
            )

            await MainActor.run {
                print("📊 统计系统：数据保存完成")
            }
        }
        */
    }


}

