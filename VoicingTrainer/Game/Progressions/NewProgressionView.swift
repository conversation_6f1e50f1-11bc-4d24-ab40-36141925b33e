//
//  NewProgressionView.swift
//  VoicingTrainer
//
//  Created by AI Assistant on 2025/6/15.
//

import SwiftUI

struct NewProgressionView: View {
    @StateObject private var userProgressionManager = UserProgressionManager.shared
    private var midiManager: MIDIManager
    @Environment(\.midiRoutingManager) private var midiRoutingManager
    
    // 状态变量
    @State private var recordingState: RecordingState = .waiting
    @State private var recordedChords: [RecordedChord] = []
    @State private var currentPressedNotes: Set<Int> = []
    @State private var pedalWasPressed = false
    @State private var currentChordName: String = ""
    @State private var currentChordNotes: [String] = []
    @State private var showingSaveDialog = false
    @State private var progressionName = ""
    @State private var showingAlert = false
    @State private var alertMessage = ""
    
    // MIDI处理器
    @State private var midiHandler: ImprovedNewProgressionViewMIDIHandler?
    
    // ✅ ADDED: A custom initializer to accept the midiManager dependency.
    init(midiManager: MIDIManager) {
        self.midiManager = midiManager
    }
    
    var body: some View {
        let _ = print("🎨 NewProgressionView body re-evaluated at \(Date())")
        
        VStack(spacing: 20) {
            // 当前和弦实时显示区域
            currentChordDisplayView
            
            // 录制的和弦显示区域 - 使用LazyGrid
            recordedChordsGridView
            
            // 底部说明区域
            bottomInstructionsView
        }
        .padding()
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .onAppear {
            setupMIDIHandler()
        }
        .onDisappear {
            midiHandler = nil
        }
        .sheet(isPresented: $showingSaveDialog) {
            SaveProgressionDialog(
                progressionName: $progressionName,
                onSave: saveProgression,
                onCancel: { showingSaveDialog = false }
            )
        }
        .alert("提示", isPresented: $showingAlert) {
            Button("确定") { }
        } message: {
            Text(alertMessage)
        }
    }
    
    // MARK: - 子视图
    
    /// 当前和弦实时显示视图
    private var currentChordDisplayView: some View {
        VStack(spacing: 16) {
            // 状态指示器
            HStack {
                Circle()
                    .fill(progressionStatusColor)
                    .frame(width: 12, height: 12)
                
                Text(progressionStatusText)
                    .font(.headline)
                    .foregroundColor(.primary)
                
                Spacer()
                
                // 简化的踏板状态指示
                HStack {
                    Image(systemName: "opticaldisc")
                        .foregroundColor(pedalWasPressed ? .green : .gray)
                    Text("踏板按下录制")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            
            // 当前和弦名称显示
            VStack(spacing: 8) {
                if !currentChordName.isEmpty {
                    Text("当前和弦")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Text(currentChordName)
                        .font(.title)
                        .fontWeight(.bold)
                        .foregroundColor(.primary)
                        .padding(.horizontal)
                        .padding(.vertical, 8)
                        .background(Color.blue.opacity(0.1))
                        .cornerRadius(8)
                } else {
                    Text("等待弹奏和弦...")
                        .font(.title2)
                        .foregroundColor(.secondary)
                        .padding(.vertical, 8)
                }
                
                // 当前音符显示
                if !currentChordNotes.isEmpty {
                    Text("组成音: \(currentChordNotes.joined(separator: " - "))")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
            }
        }
        .padding()
        .background(Color.gray.opacity(0.05))
        .cornerRadius(12)
        .frame(maxWidth: .infinity)
    }
    
    /// 录制的和弦网格显示视图
    private var recordedChordsGridView: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Text("已录制的和弦 (\(recordedChords.count))")
                    .font(.headline)
                    .foregroundColor(.primary)
                
                Spacer()
                
                if !recordedChords.isEmpty {
                    Button("清空") {
                        recordedChords.removeAll()
                    }
                    .font(.caption)
                    .foregroundColor(.red)
                }
            }
            
            if recordedChords.isEmpty {
                VStack(spacing: 12) {
                    Image(systemName: "music.note.list")
                        .font(.system(size: 48))
                        .foregroundColor(.gray)
                    
                    Text("还未录制任何和弦")
                        .font(.title3)
                        .foregroundColor(.secondary)
                    
                    Text("弹奏和弦，然后踩下踏板录制")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)
                .background(Color.gray.opacity(0.05))
                .cornerRadius(12)
                
            } else {
                ScrollView {
                    LazyVGrid(columns: [
                        GridItem(.adaptive(minimum: 160, maximum: 200), spacing: 16)
                    ], spacing: 16) {
                        
                        // 使用indices避免重复创建数组
                        ForEach(recordedChords.indices, id: \.self) { index in
                            let chord = recordedChords[index]
                            EnhancedChordCardView(
                                chord: chord,
                                index: index + 1,
                                onDelete: {
                                    DispatchQueue.main.async {
                                        recordedChords.removeAll { $0.id == chord.id }
                                    }
                                }
                            )
                        }
                    }
                    .padding(.horizontal, 4)
                    .id(recordedChords.count)
                    
                    // 保存按钮
                    if !recordedChords.isEmpty {
                        VStack(spacing: 16) {
                            Divider()
                                .padding(.horizontal)
                            
                            Button(action: {
                                showingSaveDialog = true
                            }) {
                                HStack {
                                    Image(systemName: "square.and.arrow.down")
                                        .font(.title2)
                                    Text("保存和弦进行")
                                        .font(.headline)
                                        .fontWeight(.semibold)
                                }
                                .foregroundColor(.white)
                                .padding(.horizontal, 32)
                                .padding(.vertical, 12)
                                .background(
                                    RoundedRectangle(cornerRadius: 10)
                                        .fill(
                                            LinearGradient(
                                                gradient: Gradient(colors: [.blue, .purple]),
                                                startPoint: .leading,
                                                endPoint: .trailing
                                            )
                                        )
                                )
                            }
                            .shadow(color: .blue.opacity(0.3), radius: 8, x: 0, y: 4)
                            
                            Text("已录制 \(recordedChords.count) 个和弦")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                        .padding(.top, 20)
                        .padding(.bottom, 20)
                    }
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
    
    /// 底部说明区域
    private var bottomInstructionsView: some View {
        VStack(spacing: 12) {
            Text("使用说明")
                .font(.subheadline)
                .fontWeight(.semibold)
                .foregroundColor(.primary)
            
            HStack(spacing: 20) {
                VStack(spacing: 4) {
                    Image(systemName: "1.circle.fill")
                        .font(.title3)
                        .foregroundColor(.blue)
                    Text("弹奏和弦")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Image(systemName: "arrow.right")
                    .foregroundColor(.gray)
                
                VStack(spacing: 4) {
                    Image(systemName: "2.circle.fill")
                        .font(.title3)
                        .foregroundColor(.blue)
                    Text("踩下踏板录制")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
        }
        .padding()
        .background(Color.blue.opacity(0.05))
        .cornerRadius(8)
    }
    
    // MARK: - 计算属性
    
    private var progressionStatusColor: Color {
        switch recordingState {
        case .waiting:
            return .blue
        case .recording:
            return .orange
        case .pedalPressed:
            return .green
        }
    }
    
    private var progressionStatusText: String {
        switch recordingState {
        case .waiting:
            return "等待弹奏和弦"
        case .recording:
            return "正在弹奏和弦"
        case .pedalPressed:
            return "已录制"
        }
    }
    
    // MARK: - 私有方法
    
    /// 设置MIDI处理器
    private func setupMIDIHandler() {
        midiHandler = ImprovedNewProgressionViewMIDIHandler(
            recordingState: $recordingState,
            recordedChords: $recordedChords,
            currentPressedNotes: $currentPressedNotes,
            pedalWasPressed: $pedalWasPressed,
            currentChordName: $currentChordName,
            currentChordNotes: $currentChordNotes,
            midiManager: midiManager
        )
        
        if let handler = midiHandler {
            midiRoutingManager.registerHandler(handler, for: .newProgression)
        }
    }
    
    /// 保存和弦进行
    private func saveProgression() {
        guard let midiHandler = midiHandler else {
            showAlert("保存功能暂时不可用，请稍后重试")
            showingSaveDialog = false
            return
        }
        
        let trimmedName = progressionName.trimmingCharacters(in: .whitespaces)
        
        // 调用 MIDI 处理器的保存方法
        midiHandler.saveProgression(progressionName: trimmedName) { success, message in
            DispatchQueue.main.async {
                if success {
                    // 保存成功：清空录制的和弦和输入框
                    self.showAlert(message) {
                        self.recordedChords.removeAll()
                        self.progressionName = ""
                    }
                } else {
                    // 保存失败：显示错误信息但保留数据
                    self.showAlert(message)
                }
                
                self.showingSaveDialog = false
            }
        }
    }
    
    /// 显示警告
    private func showAlert(_ message: String, completion: (() -> Void)? = nil) {
        alertMessage = message
        showingAlert = true
        
        if let completion = completion {
            DispatchQueue.main.asyncAfter(deadline: .now() + 1.5) {
                completion()
            }
        }
    }
}

