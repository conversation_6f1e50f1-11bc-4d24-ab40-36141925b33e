//
//  NewProgressionModels.swift
//  VoicingTrainer
//
//  Created by AI Assistant on 2025/6/15.
//

import SwiftUI

// MARK: - 数据模型

/// 录制状态
enum RecordingState {
    case waiting       // 等待用户弹奏
    case recording     // 正在录制和弦
    case pedalPressed  // 踏板已按下，等待松开
}

/// 录制的和弦信息
struct RecordedChord: Identifiable {
    let id = UUID()
    let name: String
    let notes: [Int]
    let timestamp: Date
}

// MARK: - 支持视图

/// 增强的和弦卡片视图
struct EnhancedChordCardView: View {
    let chord: RecordedChord
    let index: Int
    let onDelete: () -> Void
    
    var body: some View {
        VStack(spacing: 8) {
            // 顶部序号和删除按钮
            HStack {
                Text("\(index)")
                    .font(.caption)
                    .fontWeight(.bold)
                    .foregroundColor(.white)
                    .frame(width: 20, height: 20)
                    .background(Circle().fill(Color.blue))
                
                Spacer()
                
                But<PERSON>(action: onDelete) {
                    Image(systemName: "xmark.circle.fill")
                        .font(.caption)
                        .foregroundColor(.red)
                }
            }
            
            // 和弦名称
            Text(chord.name)
                .font(.title3)
                .fontWeight(.semibold)
                .foregroundColor(.primary)
                .multilineTextAlignment(.center)
                .lineLimit(2)
            
            // 音符信息
            VStack(spacing: 4) {
                Text("音符数: \(chord.notes.count)")
                    .font(.caption)
                    .foregroundColor(.secondary)
                
                // 组成音显示
                let noteNames = chord.notes.map { midiNote in
                    let noteNames = ["C", "C#", "D", "D#", "E", "F", "F#", "G", "G#", "A", "A#", "B"]
                    let noteIndex = midiNote % 12
                    return noteNames[noteIndex]
                }
                
                Text(noteNames.joined(separator: " "))
                    .font(.caption2)
                    .foregroundColor(.secondary)
                    .lineLimit(2)
                    .multilineTextAlignment(.center)
            }
        }
        .padding(12)
        .frame(minHeight: 120)
        .background(Color.gray.opacity(0.06))
        .cornerRadius(12)
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(Color.gray.opacity(0.2), lineWidth: 1)
        )
    }
}

/// 保存对话框
struct SaveProgressionDialog: View {
    @Binding var progressionName: String
    let onSave: () -> Void
    let onCancel: () -> Void
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                Text("为你的和弦进行命名")
                    .font(.headline)
                    .padding(.top)
                
                TextField("输入和弦进行名称", text: $progressionName)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
                    .padding(.horizontal)
                
                Text("名称限制：64个字符以内")
                    .font(.caption)
                    .foregroundColor(.secondary)
                
                Spacer()
            }
            .navigationTitle("保存和弦进行")
            #if os(iOS)
            .navigationBarTitleDisplayMode(.inline)
            #endif
            .toolbar {
                #if os(iOS)
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("取消", action: onCancel)
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("保存", action: onSave)
                        .disabled(progressionName.trimmingCharacters(in: .whitespaces).isEmpty)
                }
                #else
                ToolbarItem(placement: .cancellationAction) {
                    Button("取消", action: onCancel)
                }
                
                ToolbarItem(placement: .confirmationAction) {
                    Button("保存", action: onSave)
                        .disabled(progressionName.trimmingCharacters(in: .whitespaces).isEmpty)
                }
                #endif
            }
        }
    }
} 