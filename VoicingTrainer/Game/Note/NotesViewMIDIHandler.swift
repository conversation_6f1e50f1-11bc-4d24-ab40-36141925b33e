//
//  NotesViewMIDIHandler.swift
//  VoicingTrainer
//
//  Created by AI Assistant on 2025/6/15.
//

import Foundation
import SwiftUI

/// NotesView的MIDI输入处理器适配器
class NotesViewMIDIHandler: MIDIRoutingManager.MIDIInputHandler {
    
    // MIDI处理相关状态
    private var previousNotes: Set<Int> = []
    
    // 引用
    private weak var midiManager: MIDIManager?
    private weak var stringController: StringController?
    private weak var waveStringController: WaveStringController?
    
    // 视觉效果选择
    @Binding private var useWaveEffect: Bool
    
    init(midiManager: MIDIManager, 
         stringController: StringController,
         waveStringController: WaveStringController,
         useWaveEffect: Binding<Bool>) {
        self.midiManager = midiManager
        self.stringController = stringController
        self.waveStringController = waveStringController
        self._useWaveEffect = useWaveEffect
    }
    
    func handleMIDIInput(_ notes: Set<Int>) {
        handleMIDINotesForStringEffect(notes)
    }
    
    func handleSustainInput(_ sustainOn: Bool) {
        // NotesView不需要处理踏板输入
    }
    
    // MARK: - 私有方法
    
    private func handleMIDINotesForStringEffect(_ currentNotes: Set<Int>) {
        guard let midiManager = midiManager else { return }
        
        // 获取新按下的音符（避免重复触发）
        let newNotes = currentNotes.subtracting(previousNotes)
        previousNotes = currentNotes
        
        // 为每个新按下的音符触发视觉效果
        for noteNumber in newNotes {
            // 从MIDIManager获取真实的velocity值
            let velocity = midiManager.getVelocity(for: noteNumber)
            // 将MIDI velocity (0-127) 转换为intensity (0.0-10.0)
            // velocity较小时也要有基础效果，所以使用非线性映射
            
            let normalizedVelocity = Double(velocity) / 127.0
            let intensity = 1.0 + normalizedVelocity * 9.0  // 1.0 到 10.0 的范围
            
            let note = MusicalNote.note(for: noteNumber, intensity: intensity)
            
            // 根据当前选择的效果类型触发对应的控制器
            if useWaveEffect {
                waveStringController?.playNote(note)
               // print("🌊 触发波形效果 - 音符: \(noteNumber), velocity: \(velocity), 强度: \(intensity)")
            } else {
                stringController?.playNote(note)
              //  print("🎸 触发琴弦效果 - 音符: \(noteNumber), velocity: \(velocity), 强度: \(intensity)")
            }
        }
        
        // 如果没有按键了，重置状态
        if currentNotes.isEmpty {
            previousNotes.removeAll()
        }
    }
} 
