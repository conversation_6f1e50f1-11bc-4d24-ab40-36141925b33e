//
//  NoteLevelData.swift
//  VoicingTrainer
//
//  Created by <PERSON> on 2025/1/5.
//

import Foundation

// MARK: - Note Level Data Models
struct NoteLevelData: Codable {
    let exerciseType: String
    let exercises: [NoteExercise]
}

struct NoteExercise: Codable, Identifiable {
    let id: String
    let description: String
    let noteSelection: NoteSelection
    let roundCount: Int? // 可选的轮数设置
    let priceType: String? // 价格类型：free 或 paid
    
    // 计算属性：获取价格类型枚举
    var priceTypeEnum: PriceType {
        return PriceType.from(priceType)
    }
    
    // 检查是否可用
    func isAvailable() -> Bool {
        return PurchaseManager.shared.isItemAvailable(priceType: priceTypeEnum)
    }
    
    // 获取锁定描述
    func getLockDescription() -> String? {
        return PurchaseManager.shared.getLockDescription(priceType: priceTypeEnum)
    }
    
    private enum CodingKeys: String, CodingKey {
        case id, description, noteSelection, roundCount
        case priceType = "price_type"
    }
}

struct NoteSelection: Codable {
    let type: String // "specificNotes" or "range"
    let notes: [String]?
    let octaveRange: OctaveRange?
    let startNote: String?
    let endNote: String?
    let keyType: String? // "white", "black", "all"
}

struct OctaveRange: Codable {
    let min: Int
    let max: Int
}

// MARK: - Level Manager
class NoteLevelManager: ObservableObject {
    @Published var currentLevelIndex: Int = 0
    @Published var levels: [NoteExercise] = []
    @Published var isGameActive: Bool = false
    
    init() {
        loadLevels()
    }
    
    var currentLevel: NoteExercise? {
        guard currentLevelIndex < levels.count else { return nil }
        return levels[currentLevelIndex]
    }
    
    var hasNextLevel: Bool {
        return currentLevelIndex < levels.count - 1
    }
    
    func nextLevel() {
        if hasNextLevel {
            currentLevelIndex += 1
        }
    }
    
    func resetToFirstLevel() {
        currentLevelIndex = 0
    }
    
    func selectLevel(_ index: Int) {
        guard index >= 0 && index < levels.count else { return }
        currentLevelIndex = index
    }
    
    private func loadLevels() {
        guard let url = Bundle.main.url(forResource: "notesGame", withExtension: "json"),
              let data = try? Data(contentsOf: url),
              let levelData = try? JSONDecoder().decode(NoteLevelData.self, from: data) else {
            print("Failed to load note levels")
            return
        }
        
        self.levels = levelData.exercises
        print("Loaded \(levels.count) note exercise levels")
    }
    
    // 根据关卡配置生成音符范围
    func generateNotesForCurrentLevel() -> [Int] {
        guard let level = currentLevel else { 
            print("NoteLevelManager: No current level")
            return [] 
        }
        
        print("NoteLevelManager: Generating notes for level \(level.id)")
        
        let notes: [Int]
        switch level.noteSelection.type {
        case "specificNotes":
            notes = generateSpecificNotes(level.noteSelection)
        case "range":
            notes = generateRangeNotes(level.noteSelection)
        default:
            print("NoteLevelManager: Unknown note selection type: \(level.noteSelection.type)")
            notes = []
        }
        
        print("NoteLevelManager: Generated \(notes.count) notes: \(notes)")
        return notes
    }
    
    private func generateSpecificNotes(_ selection: NoteSelection) -> [Int] {
        guard let notes = selection.notes,
              let octaveRange = selection.octaveRange else { return [] }
        
        var result: [Int] = []
        
        for noteName in notes {
            for octave in octaveRange.min...octaveRange.max {
                if let midiNote = noteNameToMIDI(noteName, octave: octave) {
                    result.append(midiNote)
                }
            }
        }
        
        return result.sorted()
    }
    
    private func generateRangeNotes(_ selection: NoteSelection) -> [Int] {
        guard let startNote = selection.startNote,
              let endNote = selection.endNote,
              let keyType = selection.keyType else { return [] }
        
        let startMIDI = parseNoteString(startNote) ?? 60
        let endMIDI = parseNoteString(endNote) ?? 72
        
        let range = min(startMIDI, endMIDI)...max(startMIDI, endMIDI)
        
        switch keyType {
        case "white":
            return range.filter { isWhiteKey($0) }
        case "black":
            return range.filter { !isWhiteKey($0) }
        case "all":
            return Array(range)
        default:
            return Array(range)
        }
    }
    
    private func noteNameToMIDI(_ noteName: String, octave: Int) -> Int? {
        let noteValues = ["C": 0, "D": 2, "E": 4, "F": 5, "G": 7, "A": 9, "B": 11]
        guard let baseValue = noteValues[noteName] else { return nil }
        return (octave + 1) * 12 + baseValue
    }
    
    private func parseNoteString(_ noteString: String) -> Int? {
        // 解析如 "C4", "D#5" 格式的音符
        let pattern = "^([A-G][#b]?)([0-9])$"
        let regex = try? NSRegularExpression(pattern: pattern)
        let range = NSRange(location: 0, length: noteString.count)
        
        guard let match = regex?.firstMatch(in: noteString, range: range),
              match.numberOfRanges == 3 else { return nil }
        
        let noteNameRange = Range(match.range(at: 1), in: noteString)!
        let octaveRange = Range(match.range(at: 2), in: noteString)!
        
        let noteName = String(noteString[noteNameRange])
        let octaveString = String(noteString[octaveRange])
        
        guard let octave = Int(octaveString) else { return nil }
        
        let noteValues = [
            "C": 0, "C#": 1, "Db": 1,
            "D": 2, "D#": 3, "Eb": 3,
            "E": 4,
            "F": 5, "F#": 6, "Gb": 6,
            "G": 7, "G#": 8, "Ab": 8,
            "A": 9, "A#": 10, "Bb": 10,
            "B": 11
        ]
        
        guard let baseValue = noteValues[noteName] else { return nil }
        return (octave + 1) * 12 + baseValue
    }
    
    private func isWhiteKey(_ midiNote: Int) -> Bool {
        let noteClass = midiNote % 12
        return [0, 2, 4, 5, 7, 9, 11].contains(noteClass)
    }
    
    // MARK: - Achievement Support Methods
    
    /**
     * 获取关卡在列表中的索引
     */
    func getLevelIndex(for levelId: String) -> Int? {
        return levels.firstIndex { $0.id == levelId }
    }
    
    /**
     * 获取上一关的ID
     */
    func getPreviousLevelId(for levelId: String) -> String? {
        guard let currentIndex = getLevelIndex(for: levelId),
              currentIndex > 0 else { return nil }
        return levels[currentIndex - 1].id
    }
    
    /**
     * 获取下一关的ID
     */
    func getNextLevelId(for levelId: String) -> String? {
        guard let currentIndex = getLevelIndex(for: levelId),
              currentIndex < levels.count - 1 else { return nil }
        return levels[currentIndex + 1].id
    }
    
    /**
     * 根据关卡ID设置当前关卡
     */
    func setCurrentLevel(by levelId: String) {
        if let index = getLevelIndex(for: levelId) {
            currentLevelIndex = index
        }
    }
} 