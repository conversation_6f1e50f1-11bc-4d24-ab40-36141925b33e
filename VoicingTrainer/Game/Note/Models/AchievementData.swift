//
//  AchievementData.swift
//  VoicingTrainer
//
//  Created by <PERSON> on 2025/1/5.
//

import Foundation

// MARK: - 成就数据模型

/**
 * 游戏得分记录
 */
struct GameScore: Codable, Equatable {
    let correctCount: Int       // 正确次数
    let totalCount: Int         // 总次数
    let accuracy: Double        // 准确率 (0.0 - 100.0)
    let totalTime: Double       // 总用时(秒)
    
    init(correctCount: Int = 0, totalCount: Int = 0, accuracy: Double = 0.0, totalTime: Double = 0.0) {
        self.correctCount = correctCount
        self.totalCount = totalCount
        self.accuracy = accuracy
        self.totalTime = totalTime
    }
    
    /// 是否为满分
    var isPerfect: Bool {
        return totalCount > 0 && correctCount == totalCount
    }
}

/**
 * 单个关卡的成就记录
 */
struct LevelAchievement: Codable, Equatable {
    let isUnlocked: Bool                    // 是否解锁
    let bestScore: GameScore               // 最佳得分
    let firstCompletedAt: Date?            // 首次完成时间
    let lastPlayedAt: Date?                // 最后游玩时间
    let playCount: Int                     // 游玩次数
    let isPerfectCompleted: Bool           // 是否满分通关过
    
    init(isUnlocked: Bool = false,
         bestScore: GameScore = GameScore(),
         firstCompletedAt: Date? = nil,
         lastPlayedAt: Date? = nil,
         playCount: Int = 0,
         isPerfectCompleted: Bool = false) {
        self.isUnlocked = isUnlocked
        self.bestScore = bestScore
        self.firstCompletedAt = firstCompletedAt
        self.lastPlayedAt = lastPlayedAt
        self.playCount = playCount
        self.isPerfectCompleted = isPerfectCompleted
    }
    
    /// 更新成就记录
    func updated(with newScore: GameScore) -> LevelAchievement {
        let now = Date()
        let newPlayCount = playCount + 1
        let newFirstCompleted = firstCompletedAt ?? (newScore.totalCount > 0 ? now : nil)
        let newIsPerfectCompleted = isPerfectCompleted || newScore.isPerfect
        
        // 更新最佳得分：优先考虑准确率，其次考虑用时
        let newBestScore: GameScore
        if bestScore.totalCount == 0 {
            // 第一次游玩
            newBestScore = newScore
        } else if newScore.accuracy > bestScore.accuracy {
            // 准确率更高
            newBestScore = newScore
        } else if newScore.accuracy == bestScore.accuracy && newScore.totalTime < bestScore.totalTime {
            // 准确率相同但用时更短
            newBestScore = newScore
        } else {
            // 保持原有最佳得分
            newBestScore = bestScore
        }
        
        return LevelAchievement(
            isUnlocked: isUnlocked,
            bestScore: newBestScore,
            firstCompletedAt: newFirstCompleted,
            lastPlayedAt: now,
            playCount: newPlayCount,
            isPerfectCompleted: newIsPerfectCompleted
        )
    }
}

/**
 * 完整的成就数据
 */
struct AchievementData: Codable {
    let version: String                                    // 数据版本，用于兼容性
    let lastPlayedLevel: String?                          // 最后游玩的关卡ID
    let achievements: [String: LevelAchievement]          // 关卡ID -> 成就记录
    
    init(version: String = "1.0",
         lastPlayedLevel: String? = nil,
         achievements: [String: LevelAchievement] = [:]) {
        self.version = version
        self.lastPlayedLevel = lastPlayedLevel
        self.achievements = achievements
    }
    
    /// 获取指定关卡的成就记录
    func getAchievement(for levelId: String) -> LevelAchievement {
        return achievements[levelId] ?? LevelAchievement()
    }
    
    /// 检查关卡是否解锁
    func isLevelUnlocked(_ levelId: String, in levelManager: NoteLevelManager) -> Bool {
        // 第一关默认解锁
        if levelManager.getLevelIndex(for: levelId) == 0 {
            return true
        }
        
        // 检查上一关是否满分通关
        if let previousLevelId = levelManager.getPreviousLevelId(for: levelId) {
            return getAchievement(for: previousLevelId).isPerfectCompleted
        }
        
        return false
    }
    
    /// 更新关卡成就
    func updatedWithScore(_ score: GameScore, for levelId: String) -> AchievementData {
        var newAchievements = achievements
        let currentAchievement = getAchievement(for: levelId)
        let updatedAchievement = currentAchievement.updated(with: score)
        newAchievements[levelId] = updatedAchievement
        
        return AchievementData(
            version: version,
            lastPlayedLevel: levelId,
            achievements: newAchievements
        )
    }
    
    /// 解锁关卡
    func withUnlockedLevel(_ levelId: String) -> AchievementData {
        var newAchievements = achievements
        let currentAchievement = getAchievement(for: levelId)
        newAchievements[levelId] = LevelAchievement(
            isUnlocked: true,
            bestScore: currentAchievement.bestScore,
            firstCompletedAt: currentAchievement.firstCompletedAt,
            lastPlayedAt: currentAchievement.lastPlayedAt,
            playCount: currentAchievement.playCount,
            isPerfectCompleted: currentAchievement.isPerfectCompleted
        )
        
        return AchievementData(
            version: version,
            lastPlayedLevel: lastPlayedLevel,
            achievements: newAchievements
        )
    }
} 