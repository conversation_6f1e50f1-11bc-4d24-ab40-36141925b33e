//
//  NotesView.swift
//  VoicingTrainer
//
//  Created by <PERSON> on 2025/1/5.
//
import SwiftUI

struct NotesView: View {
    @ObservedObject private var configManager = GameConfigManager.shared
    @StateObject private var gameManager: NoteGameManager
    @StateObject private var levelManager = NoteLevelManager()
    @ObservedObject var midiManager: MIDIManager
    @StateObject private var stringController = StringController()  //控制琴弦震动
    @StateObject private var waveStringController = WaveStringController()  //控制波形效果
    
    // 视觉效果选择
    @State private var useWaveEffect = false // false=原始琴弦, true=波形效果
    @State private var currentPreset: WavePreset = .dramatic
    
    // 新增：界面控制状态
    @State private var showLevelSelect = false  //显示关卡选择Dialog
    @State private var showProUpgrade = false  // 显示Pro升级界面
    
    // 内购管理
    @StateObject private var purchaseManager = PurchaseManager.shared

    // 🔄 练习状态管理
    @StateObject private var practiceStateManager = PracticeStateManager.shared

    // MARK: - 📊 统计系统
    @StateObject private var statisticsViewModel = StatisticsViewModel()
    @State private var practiceStartTime: Date?
    @State private var statisticsAlreadySaved: Bool = false

    // MIDI输入处理控制
    @Environment(\.midiRoutingManager) private var midiRoutingManager
    @State private var midiHandler: NotesViewMIDIHandler?
    
    init(midiManager: MIDIManager) {
        self.midiManager = midiManager
        self._gameManager = StateObject(wrappedValue: NoteGameManager(
            config: GameConfigManager.shared.config,
            midiManager: midiManager
        ))
    }
    
    var body: some View {
        ZStack {
            //  背景色:根据输入比正确高低，改变颜色
            backgroundColorView
            
            // 渐变背景
            /*
            LinearGradient(
                gradient: Gradient(colors: [
                    Color.indigo.opacity(0.3),
                    Color.purple.opacity(0.4),
                    Color.pink.opacity(0.3)
                ]),
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
            .ignoresSafeArea()
            */
            
            
            // 琴弦振动:动态切换视觉效果
            if useWaveEffect {
                WaveStringView(controller: waveStringController)    //波形振动
            } else {
                NoteStringView(controller: stringController)       //原始琴弦振动
            }
            
            // 主布局
            VStack {
                // 顶部布局：和弦名称显示 + 按钮和状态行
                VStack(spacing: 12) {
                    
                    //  按钮和和弦名称
                    HStack(alignment:.top) {    //  顶部三部分:顶部对齐
                        // 左上角：按钮组
                        HStack(spacing: 12) {
                            levelSelectButton   //  选关卡
                            if gameManager.gameState != .idle {
                                replayButton
                            }
                                  //  重新播放
                            middleCButton       //  中央C
                            //effectToggleButton  // 新增：效果切换按钮
                        }
                        .frame(maxWidth:.infinity,alignment:.leading)
                        //  和弦名称显示，居中对齐显示和弦名称
                        chordNameView
                        
                        // 右上角：游戏状态信息（紧凑版）
                        if gameManager.gameState != .idle {
                            GameScoreView
                            
                        }

                    }
                    .padding(.horizontal)
                    
                    
                }
                .padding(.top, 16)
                
                Spacer()
                
                // 屏幕正中：状态和文字显示，音符音名提示
                VStack(spacing: 20) {
                    // 关卡信息显示
                    /*
                    if let level = gameManager.currentLevel {
                        
                        VStack(spacing: 8) {
                            Text("Level \(levelManager.currentLevelIndex + 1)")
                                .font(.title2)
                                .foregroundColor(.secondary)
                            
                            Text(level.description)
                                .font(.title3)
                                .fontWeight(.semibold)
                                .foregroundColor(.primary)
                        }
                        
                        .padding()
                        
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(.ultraThinMaterial)
                        )
                        
                    }
                    */
                    
                    // 音符音名提示
                    if gameManager.gameState == .waitingForResponse {
                        VStack(spacing: 8) {
                            Text(NSLocalizedString("listen_and_play", value: "Listen and Play:", comment: ""))
                                .font(.title2)
                                .foregroundColor(.secondary)
                            
                            // 本轮听音错了几次就显示音名
                            Text(gameManager.ErrorInCurrentRound > 1 ? NoteNameGenerator.getNoteName(note: UInt8(gameManager.listenNote)) : "♪")
                                .font(.system(size: 48))
                                .fontWeight(.bold)
                                .foregroundColor(.primary)
                                .padding()
                                .background(
                                    RoundedRectangle(cornerRadius: 16)
                                        .fill(.ultraThinMaterial)
                                )
                            
                            // 倒计时进度条
                            waitTimeProgressBar
                        }
                    } else {
                        /*
                        VStack(spacing: 8) {
                            
                            Text(gameStatusText)
                                .font(.system(size: 48))
                                .fontWeight(.bold)
                                .foregroundColor(.primary)
                                .padding()
                                .background(
                                    RoundedRectangle(cornerRadius: 16)
                                        .fill(.ultraThinMaterial)
                                )
                        }
                        */
                    }
                    
                    //playStopButton
                    
                    /*
                    // 开始练习按钮
                    if gameManager.currentLevel != nil {
                        // 有关卡时显示开始练习按钮
                        if gameManager.gameState == .idle || gameManager.gameState == .completed {
                            startPracticeButton
                            
                        }
                    } else {
                        // 如果没有选择关卡，显示选择关卡提示
                        Button(NSLocalizedString("select_level", value: "Select Level", comment: "")) {
                            showLevelSelect = true
                        }
                        .font(.title2)
                        .foregroundColor(.blue)
                        .padding()
                        .background(
                            RoundedRectangle(cornerRadius: 16)
                                .fill(.ultraThinMaterial)
                        )
                    }
                    */
                }
                
                Spacer()
            }
            
            // Level Start 界面
            if gameManager.showLevelStartView, let level = gameManager.currentLevel {
                LevelStartView(
                    level: level,
                    levelNumber: levelManager.currentLevelIndex + 1,
                    isPresented: $gameManager.showLevelStartView
                ) {
                    gameManager.startGameFromLevelStart(levelManager: levelManager)
                }
                .zIndex(2)
            }
            
            // 练习总结弹窗
            if gameManager.showLevelClearView {
                PracticeSummaryView(
                    rightNoteCount: gameManager.rightNoteCount,
                    errorNoteCount: gameManager.errorNoteCount,
                    totalTime: gameManager.totalTime,
                    isPresented: $gameManager.showLevelClearView,
                    onReplay: {
                        gameManager.replayCurrentLevel(levelManager: levelManager)
                    },
                    onNextLevel: {
                        gameManager.nextLevel(levelManager: levelManager)
                    },
                    showReplayButton: true,        // Notes模式：显示重玩按钮
                    showNextLevelButton: levelManager.hasNextLevel  // Notes模式：有下一关时显示
                )
                .zIndex(1)
            }
            
            // Level Select 界面
            if showLevelSelect {
                LevelSelectView(
                    levelManager: levelManager,
                    isPresented: $showLevelSelect
                ) { selectedIndex in
                    // 检查选中关卡的内购状态
                    levelManager.selectLevel(selectedIndex)
                    if let level = levelManager.currentLevel {
                        if level.isAvailable() {
                            // 可以直接开始
                            gameManager.showLevelStart(for: level)
                            showLevelSelect = false
                        } else {
                            // 需要升级Pro版
                            showLevelSelect = false
                            showProUpgrade = true
                        }
                    }
                }
                .zIndex(3)
            }
            
            // Pro升级弹窗
            if showProUpgrade {
                ZStack {
                    Color.black.opacity(0.5)
                        .ignoresSafeArea()
                        .onTapGesture {
                            showProUpgrade = false
                        }
                    
                    ProUpgradeView(
                        featureName: "高级音符练习",
                        onUpgrade: {
                            // 升级完成后的回调
                            showLevelSelect = true
                        },
                        onDismiss: {
                            // 🔧 关闭ProUpgradeView
                            showProUpgrade = false
                        }
                    )
                }
                .zIndex(4)
            }
            
            // 右下角高质感播放/停止按钮
            VStack {
                Spacer()
                HStack {
                    Spacer()
                    PremiumPlayStopButton(
                        isPlaying: gameManager.gameState == .playing || gameManager.gameState == .waitingForResponse,
                        isDisabled: gameManager.gameState == .countdown
                    ) {
                        switch gameManager.gameState {
                        case .idle, .completed:
                            if let level = gameManager.currentLevel {
                                // 📊 统计系统：开始练习时初始化
                                initializePracticeStatistics(levelName: level.description)

                                // 🔄 保存练习状态
                                practiceStateManager.saveNoteState(
                                    levelIndex: levelManager.currentLevelIndex,
                                    currentRound: gameManager.currentRound,
                                    totalRounds: gameManager.currentLevelRoundCount
                                )

                                gameManager.startLevelGame(with: level, levelManager: levelManager)
                            }
                        case .playing, .waitingForResponse:
                            // 📊 统计系统：停止练习时保存数据
                            savePracticeStatistics()
                            gameManager.stopGame()
                        case .countdown:
                            break // 倒计时期间不能操作
                        }
                    }
                    .padding(.trailing, 0)
                    .padding(.bottom, 0) // 为键盘预留空间
                }
            }
        }
        // 📊 统计系统：监听关卡完成状态
        .onReceive(gameManager.$showLevelClearView) { showLevelClear in
            if showLevelClear && practiceStartTime != nil {
                // 关卡自然完成，立即保存统计数据
                savePracticeStatistics()
                print("📊 统计系统：关卡完成，已保存数据")
            }
        }
        .onReceive(configManager.$config) { config in
            // 当配置更新时，更新游戏管理器
            gameManager.updateConfig(config)
        }
        // MIDI处理现在由MusicAppView统一管理，移除直接监听
        .onAppear {
            // 创建并注册MIDI处理器
            midiHandler = NotesViewMIDIHandler(
                midiManager: midiManager,
                stringController: stringController,
                waveStringController: waveStringController,
                useWaveEffect: $useWaveEffect
            )
            
            if let handler = midiHandler {
                midiRoutingManager.registerHandler(handler, for: .notes)
            }
            
            // 🔄 优先恢复上次练习状态
            if let noteState = practiceStateManager.getNoteState() {
                // 恢复上次练习的关卡
                levelManager.currentLevelIndex = noteState.selectedLevelIndex
                gameManager.currentLevel = levelManager.currentLevel
                print("🔄 恢复Note练习状态: Level \(noteState.selectedLevelIndex), Round \(noteState.currentRound)/\(noteState.totalRounds)")
            } else {
                // 初始化时如果有关卡数据，自动选择最近解锁的关卡
                if !levelManager.levels.isEmpty && gameManager.currentLevel == nil {
                    let achievementManager = AchievementManager.shared
                    if let lastUnlockedLevelId = achievementManager.getLastUnlockedLevelId(in: levelManager) {
                        levelManager.setCurrentLevel(by: lastUnlockedLevelId)
                        gameManager.currentLevel = levelManager.currentLevel
                        print("Auto-selected last unlocked level: \(lastUnlockedLevelId)")
                    } else {
                        // 如果没有找到解锁的关卡，选择第一关
                        levelManager.currentLevelIndex = 0
                        gameManager.currentLevel = levelManager.currentLevel
                        print("Auto-selected first level: \(gameManager.currentLevel?.id ?? "None")")
                    }
                }
            }
        }
        .onDisappear {
            // 取消注册MIDI处理器
            midiRoutingManager.unregisterHandler(for: .notes)
            midiHandler = nil
        }
    }
    
    private var gameStatusText: String {
        switch gameManager.gameState {
        case .idle:
            return gameManager.currentLevel != nil ? 
                NSLocalizedString("press_start", value: "Press Start!", comment: "") : 
                NSLocalizedString("select_level_first", value: "Select Level", comment: "")
        case .playing:
            return NSLocalizedString("playing", value: "Playing...", comment: "")
        case .waitingForResponse:
            return "♪"
        case .completed:
            return NSLocalizedString("complete", value: "Complete!", comment: "")
        case .countdown:
            return NSLocalizedString("get_ready", value: "Get Ready!", comment: "")
        }
    }
    
    var chordNameView:some View {
        //if !midiManager.chordNames.isEmpty {
            VStack(spacing: 4) {
                // 第一个和弦名称最大
                //let _ = print("\(midiManager.chordNames)")
                Text(midiManager.chordNames.first ?? "")
                    .font(.title)
                    .fontWeight(.bold)
                    .foregroundColor(.primary)
                
                // 其他和弦名称显示在下面，字体略小
                if midiManager.chordNames.count > 1 {
                    ForEach(Array(midiManager.chordNames.dropFirst().enumerated()), id: \.offset) { index, chordName in
                        Text(chordName)
                            .font(.title2)
                            .fontWeight(.medium)
                            .foregroundColor(.secondary)
                    }
                }
            }
            .padding(.horizontal)
       // }
    }
    
    // MARK: - 紧凑版游戏状态视图（右上角）
    var GameScoreView: some View {
        HStack(spacing: 8) {
            // 正确次数
            HStack(spacing: 4) {
                Image(systemName: "star")
                    .foregroundColor(.green)
                Text("\(gameManager.rightNoteCount)")
                    .font(.title3)
                    .fontWeight(.bold)
            }
            
            // 错误次数  
            HStack(spacing: 4) {
                Image(systemName: "xmark")
                    .foregroundColor(.red)
                Text("\(gameManager.errorNoteCount)")
                    .font(.title3)
                    .fontWeight(.bold)
            }
            
            // 当前轮次
            HStack(spacing: 4) {
                Image(systemName: "figure.boxing.circle")
                    .foregroundColor(.blue)
                Text("\(gameManager.currentRound)/\(gameManager.currentLevelRoundCount)")
                    .font(.title3)
                    .fontWeight(.bold)
            }
        }
        .frame(maxWidth:.infinity,alignment:.trailing)
        .padding(.horizontal, 12)
        .padding(.vertical, 8)
        .background(.clear
          //  RoundedRectangle(cornerRadius: 8)
          //      .fill(.ultraThinMaterial)
        )
    }
    


    var levelSelectButton:some View{
        Button(action:{
            showLevelSelect = true
        }){
            Image(systemName:"list.triangle")
                .foregroundColor(gameManager.gameState == .idle ? .white : .gray)
        }
        .frame(width: 44, height: 44)
        .background(
            Circle()
                .fill(.ultraThinMaterial)
                .opacity(gameManager.gameState == .idle ? 1.0 : 0.6)
        )
        .disabled(gameManager.gameState != .idle)
        .animation(.easeInOut(duration: 0.2), value: gameManager.gameState)
    }
    
    // MARK: - Replay Button
    
    var replayButton: some View {
        Button(action: {
            gameManager.replayCurrentNote()
        }) {
            Image(systemName: "speaker.wave.2.fill")
                .font(.title2)
                .foregroundColor(gameManager.gameState == .waitingForResponse ? .white : .gray)
        }
        .frame(width: 44, height: 44)
        .background(
            Circle()
                .fill(.ultraThinMaterial)
                .opacity(gameManager.gameState == .waitingForResponse ? 1.0 : 0.6)
        )
        .disabled(gameManager.gameState != .waitingForResponse)
        .animation(.easeInOut(duration: 0.2), value: gameManager.gameState)
    }
    
    // MARK: - Middle C Button
    
    var middleCButton: some View {
        Button(action: {
            playMiddleC()
        }) {
            Image(systemName: "music.note")
                .font(.title2)
                .foregroundColor(gameManager.gameState == .idle ? .white : .gray)
        }
        .frame(width: 44, height: 44)
        .background(
            Circle()
                .fill(.ultraThinMaterial)
                .opacity(gameManager.gameState == .idle ? 1.0 : 0.6)
        )
        .disabled(gameManager.gameState != .idle)
        .animation(.easeInOut(duration: 0.2), value: gameManager.gameState)
    }
    
    private func playMiddleC() {
        // C4 = MIDI note 60
        let middleC = 60
        print("Playing Middle C (C4): \(middleC)")
        midiManager.playListenNote(middleC, velocity: 100)
        
        // 1秒后停止
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            self.midiManager.stopListenNote(middleC)
        }
    }
    
    // MARK: - Background Color View
    
    var backgroundColorView: some View {
        
        Rectangle()
            .fill( LinearGradient(
                gradient: Gradient(colors:
                    feedbackBackgroundColor
                ),
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            ))
            .ignoresSafeArea()
            .animation(.easeInOut(duration: 0.5), value: gameManager.noteFeedback)
        /*
            .background(
                LinearGradient(
                    gradient: Gradient(colors: [
                        feedbackBackgroundColor,
                        Color.purple.opacity(0.4),
                        Color.pink.opacity(0.3)
                    ]),
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
                )
         */
    }
    
    
    var feedbackBackgroundColor: [Color] {
        switch gameManager.noteFeedback {
        case .neutral:  //正常状态 紫色
            return [
                Color(hex:"#7B2CBF"),
                Color(hex:"#5A189A"),
                Color(hex:"#3C096C")
                ]
        case .correct:  //正确 绿色系
            return [
                Color(hex:"#70e000"),
                Color(hex:"#007200"),
                Color(hex:"#004b23")
                    ]
        case .sharp(let difference):    //高了 红色系

            let intensity = min(Double(difference) / 12.0, 1.0) // 最大12个半音差
            let red = min(0.4 * intensity,1.0)
            let green = 0.0
            let blue = 0.0
            return [
                Color(hex:"#ff006e"),
                Color(hex:"#dd2d4a"),
                Color(hex:"#dd2d4a"),
                Color(red: red, green: green, blue: blue),
                Color(hex:"#d62828")
            ]
        case .flat(let difference):         //  低了 蓝色系
            // 偏低：蓝色到暗蓝色渐变，差值越大越接近暗蓝色
            let intensity = min(Double(difference) / 12.0, 1.0) // 最大12个半音差
            let red = 0.0
            let green = 0.0
            let blue = 1.0 - (intensity * 0.5) // 越大越暗
            return
            [
                Color(red: red, green: green, blue: blue),
                Color(hex:"#006494"),
                Color(hex:"#003554")
            ]
        }
    }
    
    // MARK: - MIDI处理器管理
    
    // MARK: - Effect Toggle Button
    
    var effectToggleButton: some View {
        Button(action: {
            useWaveEffect.toggle()
        }) {
            Image(systemName: useWaveEffect ? "waveform.path.ecg" : "waveform.path")
                .font(.title2)
                .foregroundColor(useWaveEffect ? .cyan : .orange)
        }
        .frame(width: 44, height: 44)
        .background(
            Circle()
                .fill(.ultraThinMaterial)
        )
        .animation(.easeInOut(duration: 0.2), value: useWaveEffect)
    }
    
    // MARK: - Start Practice Button
    
    var startPracticeButton: some View {
        Button(action: {
            // 开始倒计时，然后开始练习
            startCountdownAndPractice()
        }) {
            HStack(spacing: 12) {
                Image(systemName: "play.circle.fill")
                    .font(.title2)
                Text(NSLocalizedString("start_practice", value: "Start Practice", comment: ""))
                    .font(.title2)
                    .fontWeight(.semibold)
            }
            .foregroundColor(.white)
            .padding(.horizontal, 40)
            .padding(.vertical, 16)
            .background(
                LinearGradient(
                    gradient: Gradient(colors: [.green, .mint]),
                    startPoint: .leading,
                    endPoint: .trailing
                )
            )
            .cornerRadius(30)
            .shadow(color: .green.opacity(0.3), radius: 10, x: 0, y: 5)
        }
        .disabled(gameManager.gameState == .countdown)
    }
    
    // MARK: - Wait Time Progress Bar
    
    var waitTimeProgressBar: some View {
        VStack(spacing: 8) {
            if gameManager.gameState == .waitingForResponse {
                HStack {
                    Text(NSLocalizedString("time_remaining", value: "Time Remaining", comment: ""))
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Spacer()
                    
                    Text(String(format: "%.1f", gameManager.waitTimeRemaining))
                        .font(.caption)
                        .fontWeight(.semibold)
                        .foregroundColor(.primary)
                }
                
                ProgressView(value: gameManager.waitTimeProgress, total: 1.0)
                    .progressViewStyle(LinearProgressViewStyle(tint: progressBarColor))
                    .scaleEffect(x: 1, y: 2, anchor: .center)
                    .animation(.easeInOut(duration: 0.1), value: gameManager.waitTimeProgress)
            }
        }
        .frame(width: 200)
        .padding(.top, 8)
    }
    
    private var progressBarColor: Color {
        if gameManager.waitTimeProgress > 0.6 {
            return .green
        } else if gameManager.waitTimeProgress > 0.3 {
            return .orange
        } else {
            return .red
        }
    }
    
    // MARK: - Countdown and Practice Logic
    
    private func startCountdownAndPractice() {
        // 显示关卡开始界面（包含倒计时）
        if let level = gameManager.currentLevel {
            gameManager.showLevelStart(for: level)
        }
    }

    // MARK: - 📊 统计系统

    /// 初始化练习统计
    private func initializePracticeStatistics(levelName: String) {
        // 📊 双重保险：如果上一轮数据还没保存，先保存
        if practiceStartTime != nil && !statisticsAlreadySaved {
            print("📊 统计系统：发现未保存的上一轮数据，先保存")
            savePracticeStatistics()
        }

        // 开始新的练习统计
        practiceStartTime = Date()
        statisticsAlreadySaved = false

        print("📊 统计系统：开始音符练习 - \(levelName)")
    }

    /// 保存练习统计数据
    private func savePracticeStatistics() {
        guard let startTime = practiceStartTime else {
            print("📊 统计系统：未找到开始时间，跳过保存")
            return
        }

        // 防止重复保存
        guard !statisticsAlreadySaved else {
            print("📊 统计系统：数据已保存，跳过重复保存")
            return
        }

        statisticsAlreadySaved = true

        // 获取练习数据
        let workoutName = gameManager.currentLevel?.description ?? "音符练习"
        let totalNotes = gameManager.currentLevelRoundCount  // 总音符数 = 关卡轮数
        let rightNotes = gameManager.rightNoteCount          // 正确音符数

        // 使用统计工具类保存数据
        PracticeStatisticsHelper.savePracticeData(
            to: statisticsViewModel,
            startTime: startTime,
            workoutType: .note,
            workoutName: workoutName,
            totalNoteCount: totalNotes,
            totalCount: totalNotes,      // 音符练习：总数量 = 音符数
            rightCount: rightNotes
        )
    }
}

// MARK: - Config Info Helper
struct ConfigInfo: View {
    let title: String
    let value: String
    
    var body: some View {
        VStack(spacing: 4) {
            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
            Text(value)
                .font(.headline)
                .foregroundColor(.primary)
        }
    }
}

#Preview {
    NotesView(midiManager: MIDIManager())
}

