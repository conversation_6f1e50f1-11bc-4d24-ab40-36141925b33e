//
//  CountdownView.swift
//  VoicingTrainer
//
//  Created by <PERSON> on 2025/1/5.
//

import SwiftUI

struct CountdownView: View {
    @State private var currentNumber: Int = 3
    @State private var scale: CGFloat = 1.0
    @State private var opacity: Double = 1.0
    
    let onComplete: () -> Void
    
    var body: some View {
        ZStack {
            // 半透明背景
            Color.black.opacity(0.7)
                .ignoresSafeArea()
            
            // 倒计时数字
            Text("\(currentNumber)")
                .font(.system(size: 120, weight: .bold, design: .rounded))
                .foregroundStyle(
                    LinearGradient(
                        gradient: Gradient(colors: [.yellow, .orange, .red]),
                        startPoint: .top,
                        endPoint: .bottom
                    )
                )
                .scaleEffect(scale)
                .opacity(opacity)
                .shadow(color: .black.opacity(0.5), radius: 10, x: 0, y: 5)
        }
        .onAppear {
            startCountdown()
        }
    }
    
    private func startCountdown() {
        performCountdownStep()
    }
    
    private func performCountdownStep() {
        // 重置动画状态
        scale = 0.5
        opacity = 0.0
        
        // 动画进入
        withAnimation(.spring(response: 0.3, dampingFraction: 0.6)) {
            scale = 1.2
            opacity = 1.0
        }
        
        // 0.5秒后缩放回正常并淡出
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            withAnimation(.easeInOut(duration: 0.3)) {
                scale = 1.0
                opacity = 0.3
            }
        }
        
        // 1秒后继续下一个数字或完成
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            if currentNumber > 1 {
                currentNumber -= 1
                performCountdownStep()
            } else {
                // 倒计时完成
                withAnimation(.easeOut(duration: 0.3)) {
                    opacity = 0.0
                    scale = 0.5
                }
                
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                    onComplete()
                }
            }
        }
    }
}

#Preview {
    CountdownView {
        print("Countdown completed!")
    }
} 