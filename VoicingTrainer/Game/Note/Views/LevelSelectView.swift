//
//  LevelSelectView.swift
//  VoicingTrainer
//
//  Created by <PERSON> on 2025/1/5.
//

import SwiftUI

struct LevelSelectView: View {
    @ObservedObject var levelManager: NoteLevelManager
    @ObservedObject var achievementManager = AchievementManager.shared
    @Binding var isPresented: Bool
    let onLevelSelected: (Int) -> Void
    
    var body: some View {
        #if os(macOS)
        // macOS上直接使用VStack，避免NavigationView的布局问题
        VStack(spacing: 0) {
            // 顶部工具栏
            HStack {
                Text("Select Level")
                    .font(.largeTitle)
                    .fontWeight(.bold)
                    .foregroundColor(.white)
                
                Spacer()
                
                Button("Done") {
                    isPresented = false
                }
                .buttonStyle(.borderedProminent)
                .foregroundColor(.white)
            }
            .padding()
            .background(.ultraThinMaterial)
            
            // 主内容区
            ZStack {
                // 渐变背景
                LinearGradient(
                    gradient: Gradient(colors: [
                        Color.indigo.opacity(0.3),
                        Color.purple.opacity(0.4),
                        Color.pink.opacity(0.3)
                    ]),
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
                .ignoresSafeArea()
                
                ScrollView {
                    LazyVGrid(columns: [
                        GridItem(.adaptive(minimum: 300), spacing: 16)
                    ], spacing: 20) {
                        ForEach(Array(levelManager.levels.enumerated()), id: \.element.id) { index, level in
                            LevelCard(
                                level: level,
                                levelNumber: index + 1,
                                isCurrentLevel: index == levelManager.currentLevelIndex,
                                achievement: achievementManager.getAchievement(for: level.id),
                                isUnlocked: achievementManager.isLevelUnlocked(level.id, in: levelManager),
                                onTap: {
                                    selectLevel(index)
                                }
                            )
                        }
                    }
                    .padding(.horizontal, 20)
                    .padding(.top, 20)
                }
            }
        }
        #else
        // iOS上保持原有的NavigationView结构
        NavigationView {
            ZStack {
                // 渐变背景
                LinearGradient(
                    gradient: Gradient(colors: [
                        Color.indigo.opacity(0.3),
                        Color.purple.opacity(0.4),
                        Color.pink.opacity(0.3)
                    ]),
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
                .ignoresSafeArea()
                
                ScrollView {
                    LazyVGrid(columns: [
                        GridItem(.adaptive(minimum: 300), spacing: 16)
                    ], spacing: 20) {
                        ForEach(Array(levelManager.levels.enumerated()), id: \.element.id) { index, level in
                            LevelCard(
                                level: level,
                                levelNumber: index + 1,
                                isCurrentLevel: index == levelManager.currentLevelIndex,
                                achievement: achievementManager.getAchievement(for: level.id),
                                isUnlocked: achievementManager.isLevelUnlocked(level.id, in: levelManager),
                                onTap: {
                                    selectLevel(index)
                                }
                            )
                        }
                    }
                    .padding(.horizontal, 20)
                    .padding(.top, 20)
                }
            }
            .navigationTitle("Select Level")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        isPresented = false
                    }
                    .foregroundColor(.white)
                    .fontWeight(.semibold)
                }
            }
        }
        .navigationViewStyle(StackNavigationViewStyle())
        #endif
    }
    
    private func selectLevel(_ index: Int) {
        let level = levelManager.levels[index]
        // 只允许选择已解锁的关卡
        if achievementManager.isLevelUnlocked(level.id, in: levelManager) {
            levelManager.selectLevel(index)
            onLevelSelected(index)
            isPresented = false
        }
    }
}
//关卡选择UI: 显示关卡选择信息
struct LevelCard: View {
    let level: NoteExercise
    let levelNumber: Int
    let isCurrentLevel: Bool
    let achievement: LevelAchievement
    let isUnlocked: Bool
    let onTap: () -> Void
    
    // 内购状态
    private var isPurchaseAvailable: Bool {
        level.isAvailable()
    }
    
    private var lockDescription: String? {
        level.getLockDescription()
    }
    
    @State private var isPressed = false
    
    // 根据LevelClearView的starRating逻辑计算星级评分
    var starRating: Int {
        let accuracy = achievement.bestScore.accuracy
        switch accuracy {
        case 90...100: return 5
        case 80..<90: return 4
        case 60..<80: return 3
        case 40..<60: return 2
        default: return achievement.bestScore.totalCount > 0 ? 1 : 0
        }
    }
    
    // 关卡头部视图
    @ViewBuilder
    private var headerView: some View {
        HStack {
            VStack(alignment: .leading, spacing: 4) {
               Text("\(levelNumber).\(level.description)")
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(.white)
                
                // 显示内购锁定状态
                if let lockDescription = lockDescription {
                    LockedItemIndicator(description: lockDescription)
                }
            }
            
            Spacer()
            
            if isCurrentLevel {
                Image(systemName: "crown.fill")
                    .foregroundColor(.yellow)
                    .font(.title2)
            }
        }
    }
    
    // 星级评分视图
    @ViewBuilder
    private var starRatingView: some View {
        HStack(spacing: 2) {
            ForEach(1...5, id: \.self) { index in
                Image(systemName: index <= starRating ? "star.fill" : "star")
                    .foregroundColor(index <= starRating ? .yellow : .gray.opacity(0.4))
                    .font(.caption)
            }
            
            if achievement.isPerfectCompleted {
                Image(systemName: "crown.fill")
                    .foregroundColor(.yellow)
                    .font(.caption)
                    .padding(.leading, 4)
            }
        }
    }
    
    // 时间显示视图
    @ViewBuilder
    private var timeView: some View {
        HStack {
            Image(systemName: "clock")
                .foregroundColor(.purple)
                .frame(width: 16)
            Text(String(format: "%.1fs", achievement.bestScore.totalTime))
                .font(.caption)
                .foregroundColor(.white.opacity(0.8))
        }
    }
    
    // 成就信息视图
    @ViewBuilder
    private var achievementView: some View {
        if achievement.bestScore.totalCount > 0 {
            HStack {
                HStack(alignment: .top, spacing: 4) {
                    starRatingView
                    Spacer()
                    timeView
                }
                Spacer()
            }
        }
    }
    
    // 特定音符详情视图
    @ViewBuilder
    private var specificNotesView: some View {
        if let notes = level.noteSelection.notes,
           let octaveRange = level.noteSelection.octaveRange {
            VStack(alignment: .leading, spacing: 4) {
                HStack {
                    Image(systemName: "music.note.list")
                        .foregroundColor(.cyan)
                        .frame(width: 16)
                    Text("Notes: \(notes.joined(separator: ", "))")
                        .font(.caption)
                        .foregroundColor(.white.opacity(0.8))
                    Spacer()
                }
                
                HStack {
                    Image(systemName: "waveform")
                        .foregroundColor(.orange)
                        .frame(width: 16)
                    Text("Octaves: \(octaveRange.min)-\(octaveRange.max)")
                        .font(.caption)
                        .foregroundColor(.white.opacity(0.8))
                    Spacer()
                }
            }
        }
    }
    
    // 音符范围详情视图
    @ViewBuilder
    private var rangeNotesView: some View {
        if let startNote = level.noteSelection.startNote,
           let endNote = level.noteSelection.endNote,
           let keyType = level.noteSelection.keyType {
            VStack(alignment: .leading, spacing: 4) {
                HStack {
                    Image(systemName: "arrow.left.and.right")
                        .foregroundColor(.green)
                        .frame(width: 16)
                    Text("Range: \(startNote)-\(endNote)")
                        .font(.caption)
                        .foregroundColor(.white.opacity(0.8))
                    Spacer()
                }
                
                HStack {
                    Image(systemName: keyType == "white" ? "rectangle.portrait" : keyType == "black" ? "rectangle.portrait.fill" : "rectangle.grid.2x2")
                        .foregroundColor(.purple)
                        .frame(width: 16)
                    Text("Keys: \(keyType.capitalized)")
                        .font(.caption)
                        .foregroundColor(.white.opacity(0.8))
                    Spacer()
                }
            }
        }
    }
    
    // 关卡详情视图
    @ViewBuilder
    private var levelDetailsView: some View {
        VStack(alignment: .leading, spacing: 8) {
            switch level.noteSelection.type {
            case "specificNotes":
                specificNotesView
            case "range":
                rangeNotesView
            default:
                EmptyView()
            }
        }
    }
    
    // 状态按钮视图
    @ViewBuilder
    private var statusButtonView: some View {
        HStack {
            Spacer()
            if !isPurchaseAvailable {
                // 内购锁定
                Image(systemName: "lock.fill")
                    .foregroundColor(.orange)
                    .font(.title2)
            } else if isUnlocked {
                Image(systemName: "play.circle.fill")
                    .foregroundColor(.green)
                    .font(.title2)
            } else {
                Image(systemName: "lock.fill")
                    .foregroundColor(.red)
                    .font(.title2)
            }
        }
    }
    
    // 边框渐变
    private var borderGradient: LinearGradient {
        if isCurrentLevel {
            return LinearGradient(gradient: Gradient(colors: [.yellow, .orange]), startPoint: .topLeading, endPoint: .bottomTrailing)
        } else if achievement.isPerfectCompleted {
            return LinearGradient(gradient: Gradient(colors: [.green, .mint]), startPoint: .topLeading, endPoint: .bottomTrailing)
        } else {
            return LinearGradient(gradient: Gradient(colors: [.clear]), startPoint: .topLeading, endPoint: .bottomTrailing)
        }
    }
    
    var body: some View {
        Button(action: onTap) {
            VStack(alignment: .leading, spacing: 16) {
                headerView
                achievementView
                
                HStack {
                    levelDetailsView
                    statusButtonView
                }
            }
            .padding(20)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(.ultraThinMaterial)
                    .opacity(isUnlocked ? 1.0 : 0.6)
                    .overlay(
                        RoundedRectangle(cornerRadius: 16)
                            .stroke(
                                borderGradient,
                                lineWidth: (isCurrentLevel || achievement.isPerfectCompleted) ? 2 : 0
                            )
                    )
                    .shadow(color: .black.opacity(0.2), radius: 10, x: 0, y: 5)
            )
            .scaleEffect(isPressed ? 0.95 : 1.0)
            .animation(.spring(response: 0.3, dampingFraction: 0.6), value: isPressed)
        }
        .buttonStyle(PlainButtonStyle())
        .disabled(!isUnlocked)
        .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity) { isPressing in
            isPressed = isPressing && isUnlocked
        } perform: {
            if isUnlocked {
                onTap()
            }
        }
    }
}

#Preview {
    LevelSelectView(
        levelManager: NoteLevelManager(),
        isPresented: .constant(true)
    ) { _ in
        print("Level selected")
    }
} 
