//
//  LevelStartView.swift
//  VoicingTrainer
//
//  Created by <PERSON> on 2025/1/5.
//

import SwiftUI

struct LevelStartView: View {
    let level: NoteExercise
    let levelNumber: Int
    @Binding var isPresented: Bool
    let onStartGame: () -> Void
    
    @State private var showCountdown = false
    @State private var animateTitle = false
    @State private var animateDescription = false
    
    var body: some View {
        ZStack {
            // 渐变背景
            LinearGradient(
                gradient: Gradient(colors: [
                    Color.purple.opacity(0.3),
                    Color.blue.opacity(0.4),
                    Color.cyan.opacity(0.3)
                ]),
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
            .ignoresSafeArea()
            
            if showCountdown {
                CountdownView {
                    isPresented = false
                    onStartGame()
                }
            } else {
                VStack(spacing: 30) {
                    Spacer()
                    
                    // 关卡描述卡片
                    VStack(spacing: 20) {
                        Text(level.id)
                            .font(.system(size: 25, weight: .bold, design: .rounded))
                            .foregroundColor(.white)
                            .multilineTextAlignment(.center)
                            .lineLimit(nil)

                        Text(level.description)
                            .font(.system(size: 32, weight: .bold, design: .rounded))
                            .foregroundColor(.white)
                            .multilineTextAlignment(.center)
                            .lineLimit(nil)
                             
                        // 关卡详情
                        levelDetailsView
                        
                        //start button
                        playStopButtonView
                        
                        //Spacer()
                   
                    }
                    .padding(32)
                    .background(
                        RoundedRectangle(cornerRadius: 24)
                            .fill(.ultraThinMaterial)
                            .shadow(color: .black.opacity(0.2), radius: 20, x: 0, y: 10)
                    )
                    .scaleEffect(animateDescription ? 1.0 : 0.9)
                    .opacity(animateDescription ? 1.0 : 0.0)
                    
                    
                    

                    Spacer()
                }
                .padding(.horizontal, 32)
            }
        }
        .onAppear {
            startAnimations()
        }
    }
    
    private var playStopButtonView: some View{
        // 开始按钮
        Button(action: {
            startCountdown()
        }) {
            HStack(spacing: 12) {
                Image(systemName: "play.circle.fill")
                    .font(.title2)
                Text("Start Level")
                    .font(.title2)
                    .fontWeight(.semibold)
            }
            .foregroundColor(.white)
            .padding(.horizontal, 40)
            .padding(.vertical, 16)
            .background(
                LinearGradient(
                    gradient: Gradient(colors: [.green, .mint]),
                    startPoint: .leading,
                    endPoint: .trailing
                )
            )
            .cornerRadius(30)
            .shadow(color: .green.opacity(0.3), radius: 10, x: 0, y: 5)
        }
        .scaleEffect(animateDescription ? 1.0 : 0.9)
        .opacity(animateDescription ? 1.0 : 0.0)
        
    }
    
    private var levelDetailsView: some View {
        VStack(spacing: 12) {
            switch level.noteSelection.type {
            case "specificNotes":
                if let notes = level.noteSelection.notes,
                   let octaveRange = level.noteSelection.octaveRange {
                    HStack {
                        Image(systemName: "music.note.list")
                            .foregroundColor(.cyan)
                        Text("Notes: \(notes.joined(separator: ", "))")
                            .foregroundColor(.white.opacity(0.9))
                        Spacer()
                    }
                    
                    HStack {
                        Image(systemName: "waveform")
                            .foregroundColor(.orange)
                        Text("Octaves: \(octaveRange.min) - \(octaveRange.max)")
                            .foregroundColor(.white.opacity(0.9))
                        Spacer()
                    }
                }
                
            case "range":
                if let startNote = level.noteSelection.startNote,
                   let endNote = level.noteSelection.endNote,
                   let keyType = level.noteSelection.keyType {
                    HStack {
                        Image(systemName: "arrow.left.and.right")
                            .foregroundColor(.green)
                        Text("Range: \(startNote) - \(endNote)")
                            .foregroundColor(.white.opacity(0.9))
                        Spacer()
                    }
                    
                    HStack {
                        Image(systemName: keyType == "white" ? "rectangle.portrait" : keyType == "black" ? "rectangle.portrait.fill" : "rectangle.grid.2x2")
                            .foregroundColor(.purple)
                        Text("Keys: \(keyType.capitalized)")
                            .foregroundColor(.white.opacity(0.9))
                        Spacer()
                    }
                }
                
            default:
                EmptyView()
            }
            
            // 显示轮数信息
            HStack {
                Image(systemName: "repeat")
                    .foregroundColor(.pink)
                if let roundCount = level.roundCount {
                    Text("Game Rounds: \(roundCount)")
                        .foregroundColor(.white.opacity(0.9))
                } else {
                    Text("Rounds: Default")
                        .foregroundColor(.white.opacity(0.9))
                }
                Spacer()
            }
        }
        .font(.body)
    }
    
    private func startAnimations() {
        withAnimation(.spring(response: 0.6, dampingFraction: 0.8).delay(0.2)) {
            animateTitle = true
        }
        
        withAnimation(.spring(response: 0.8, dampingFraction: 0.8).delay(0.5)) {
            animateDescription = true
        }
    }
    
    private func startCountdown() {
        withAnimation(.easeInOut(duration: 0.3)) {
            showCountdown = true
        }
    }
}

#Preview {
    LevelStartView(
        level: NoteExercise(
            id: "exercise1",
            description: "中央C",
            noteSelection: NoteSelection(
                type: "specificNotes",
                notes: ["C"],
                octaveRange: OctaveRange(min: 4, max: 4),
                startNote: nil,
                endNote: nil,
                keyType: nil
            ),
            roundCount: 2,
            priceType: "free"
        ),
        levelNumber: 1,
        isPresented: .constant(true)
    ) {
        print("Start game")
    }
} 
