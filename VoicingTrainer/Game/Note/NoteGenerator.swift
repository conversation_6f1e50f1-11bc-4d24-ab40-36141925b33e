import Foundation

class NoteGenerator {
    private let gameConfig: GameConfig
    private var levelNotes: [Int] = []
    
    init(gameConfig: GameConfig) {
        self.gameConfig = gameConfig
    }
    
    // 设置关卡音符范围
    func setLevelNotes(_ notes: [Int]) {
        levelNotes = notes
        print("NoteGenerator: Set level notes: \(notes)")
    }
    
    // 获取当前可用的音符列表
    func getAvailableNotes() -> [Int] {
        if !levelNotes.isEmpty {
            return levelNotes
        }
        
        // 使用默认配置生成音符列表
        let range = gameConfig.gameSettings.loKey...gameConfig.gameSettings.hiKey
        if gameConfig.gameSettings.whiteKeyOnly {
            return getWhiteKeysInRange(loKey: gameConfig.gameSettings.loKey, 
                                     hiKey: gameConfig.gameSettings.hiKey)
        } else {
            return Array(range)
        }
    }
    
    private func isWhiteKey(_ midiNote: Int) -> Bool {
        let noteClass = midiNote % 12
        return [0, 2, 4, 5, 7, 9, 11].contains(noteClass)
    }
    
    private func getWhiteKeysInRange(loKey: Int, hiKey: Int) -> [Int] {
        return (loKey...hiKey).filter { isWhiteKey($0) }
    }
    
    func generateRandomNote() -> Int {
        // 如果有关卡音符，优先使用关卡音符
        if !levelNotes.isEmpty {
            let note = levelNotes.randomElement() ?? 60
            print("Generated level note: \(note)")
            return note
        }
        
        // 否则使用默认配置
        let range = gameConfig.gameSettings.loKey...gameConfig.gameSettings.hiKey
        if gameConfig.gameSettings.whiteKeyOnly {
            let whiteKeys = getWhiteKeysInRange(loKey: gameConfig.gameSettings.loKey, 
                                              hiKey: gameConfig.gameSettings.hiKey)
            let note = whiteKeys.randomElement() ?? Int.random(in: range)
            print("Generated white key note: \(note)")
            return note
        } else {
            let note = Int.random(in: range)
            print("Generated note: \(note)")
            return note
        }
    }
}