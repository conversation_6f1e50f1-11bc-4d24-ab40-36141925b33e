//
//  WaveStringView.swift
//  VoicingTrainer
//
//  Created by <PERSON> on 2025/5/29.
//

import SwiftUI

// MARK: - Wave String Controller
class WaveStringController: ObservableObject {
    @Published var phase: Float = 1000.0
    @Published var damper: Float = 1.0
    @Published var isActive: Bool = false
    
    // 可调参数
    @Published var curveCount: Int = 5           // 曲线数量
    @Published var amplitudeStep: Float = 0.1    // 振幅增量
    @Published var phaseSpeed: Float = 0.05      // 相位变化速度
    @Published var dampingRate: Float = 0.998    // 阻尼率
    @Published var waveFrequency: Float = 0.02   // 波形频率
    @Published var opacity: Double = 0.8         // 不透明度
    
    private var timer: Timer?
    
    func playNote(_ note: MusicalNote) {
        print("🌊 Wave播放音符: \(note.name), 强度: \(note.intensity)")
        
        // 重置参数
        phase = 1000.0
        damper = 1.0
        isActive = true
        
        // 启动动画定时器
        startAnimation()
        
        // 根据音符强度调整参数 - 改进的映射算法
        let normalizedIntensity = Float(note.intensity / 10.0) // 0.1 - 1.0
        
        // 强度影响振幅：轻击时小振幅，重击时大振幅
        amplitudeStep = 0.5 + normalizedIntensity * 1.0  // 0.5 到 1.5 的范围
        
        // 强度影响阻尼：轻击时快速衰减，重击时慢速衰减
        dampingRate = 0.999 - (normalizedIntensity * 0.006) // 0.993 到 0.999 的范围
        
        // 强度影响透明度：轻击时半透明，重击时不透明
        opacity = 0.4 + Double(normalizedIntensity) * 0.5 // 0.4 到 0.9 的范围
        
        // 强度影响相位速度：轻击时慢，重击时快
        phaseSpeed = 0.03 + normalizedIntensity * 0.05  // 0.03 到 0.08 的范围
    }
    
    private func startAnimation() {
        stopAnimation() // 确保只有一个定时器运行
        
        timer = Timer.scheduledTimer(withTimeInterval: 1.0/60.0, repeats: true) { [weak self] _ in
            guard let self = self else { return }
            
            // 更新相位
            self.phase -= self.phaseSpeed
            
            // 逐渐减小阻尼
            self.damper *= self.dampingRate
            
            // 当阻尼很小时停止动画
            if self.damper < 0.1 {
                self.stopAnimation()
                self.isActive = false
            }
        }
    }
    
    private func stopAnimation() {
        timer?.invalidate()
        timer = nil
    }
    
    // 预设效果
    func applyPreset(_ preset: WavePreset) {
        switch preset {
        case .gentle:
            curveCount = 3
            amplitudeStep = 0.05
            phaseSpeed = 0.03
            dampingRate = 0.995
            waveFrequency = 0.015
            
        case .dramatic:
            curveCount = 5
            amplitudeStep = 0.15
            phaseSpeed = 0.08
            dampingRate = 0.990
            waveFrequency = 0.025
            
        case .subtle:
            curveCount = 8
            amplitudeStep = 0.03
            phaseSpeed = 0.02
            dampingRate = 0.998
            waveFrequency = 0.01
        }
    }
    
    deinit {
        stopAnimation()
    }
}

enum WavePreset {
    case gentle   // 温和
    case dramatic // 戏剧性
    case subtle   // 微妙
}

// MARK: - Wave String View
struct WaveStringView: View {
    @ObservedObject var controller: WaveStringController
    
    var body: some View {
        Canvas { context, size in
            drawWavePattern(context: context, size: size)
        }
        .background(Color(red: 30/255, green: 30/255, blue: 30/255))
        .ignoresSafeArea()
    }
    
    private func drawWavePattern(context: GraphicsContext, size: CGSize) {
        let width = Float(size.width)
        let height = Float(size.height)
        
        print("width:\(width)")
        
        // 设置绘制样式
        let strokeColor = Color(red: 100/255, green: 150/255, blue: 1.0)
        
        for i in 0..<controller.curveCount {
            var path = Path()
            let yOffset = height / 2
            var amplitude: Float = 0
            let phaseOffset = Float(i) * 0.5
            
            var isFirstPoint = true
            
            
            for x in stride(from: Float(0), to: width, by: Float(1)) {
                // 计算振幅
                if x < width / 2 {
                    amplitude += controller.amplitudeStep
                }else {
                    amplitude -= controller.amplitudeStep
                }
                
                // 应用阻尼
                amplitude *= controller.damper
                
                // 计算y坐标（正弦函数）
                let y = yOffset + amplitude * sin(x * controller.waveFrequency + phaseOffset + controller.phase)
                
                let point = CGPoint(x: CGFloat(x), y: CGFloat(y))
                
                if isFirstPoint {
                    path.move(to: point)
                    isFirstPoint = false
                } else {
                    path.addLine(to: point)
                }
            }
            


            
            // 绘制路径
            context.stroke(
                path,
                with: .color(strokeColor.opacity(controller.isActive ? controller.opacity : 0.3)),
                lineWidth: 0.5
            )
        }
    }
} 
