import SwiftUI
import Foundation
import Combine

// MARK: - 按键状态枚举

/// 按键类型枚举
enum KeyPressType {
    case user       // 用户按键
    case app        // 程序按键
    case mixed      // 混合按键（用户+程序）
}

/// 按键状态模型
struct KeyState {
    let isUserPressed: Bool
    let isAppPressed: Bool
    let isTarget: Bool
    
    var pressType: KeyPressType {
        switch (isUserPressed, isAppPressed, isTarget) {
        case (true, true, _):
            return .mixed
        case (true, false, true):
            return .mixed  // 用户按下了目标音符
        case (true, false, false):
            return .user
        case (false, true, _), (false, false, true):
            return .app
        case (false, false, false):
            return .mixed // 这种情况不应该存在，但为了完整性
        }
    }
    
    var isPressed: Bool {
        return isUserPressed || isAppPressed || isTarget
    }
}

// MARK: - 颜色主题

/// 钢琴键盘颜色主题
struct PianoKeyboardColorTheme {
    let userPressedColor: Color = .mintyellow        // 用户按键颜色
    let appPressedColor: Color = .mintblue          // 程序按键颜色
    let mixedPressedColor: Color = .mintgrass       // 混合按键颜色
    let defaultWhiteKeyColor: Color = .white
    let defaultBlackKeyColor: Color = .black
    let borderColor: Color = .gray
}

// 扩展Color以添加自定义颜色a2d2ff
extension Color {
    static let crimson = Color(red: 0.86, green: 0.08, blue: 0.24)  // 深红色
    static let azure = Color(red: 0.0, green: 0.75, blue: 1.0)     // 天蓝色
    static let mintcyan = Color(red:0.635,green:0.82,blue:1.0)      //青色
    static let mintyellow = Color(red:1.0,green:0.72,blue:0.01)     //黄色
    static let mintgrass = Color(red: 0.54, green: 0.79, blue: 0.15)    // 草绿色
    static let mintblue = Color(red: 0.22745098 ,green: 0.5254902 ,blue: 1.0)
}

// MARK: - PianoKeyboardViewModel

/// 钢琴键盘视图模型
/// 负责管理键盘状态，包括用户按键、程序按键和颜色逻辑
class PianoKeyboardViewModel: ObservableObject {
    
    // MARK: - Published Properties
    
    /// 用户按下的键
    @Published private(set) var userPressedKeys: Set<Int> = []
    
    /// 程序按下的键
    @Published private(set) var appPressedKeys: Set<Int> = []
    
    /// 目标音符（保持蓝色高亮）
    @Published private(set) var targetNotes: Set<Int> = []
    
    // --- Gemini Code Fix: Debouncing Mechanism ---
    // 1. Internal buffer to hold incoming key events without publishing immediately.
    private var userPressedKeysBuffer: Set<Int> = []
    
    // 2. A timer to delay the publishing of changes.
    private var debounceTimer: Timer?
    
    // 3. The time interval for debouncing. 30ms is a good balance for responsiveness.
    private let debounceInterval: TimeInterval = 0.03
    // --- End of Gemini Fix ---
    
    /// 颜色主题
    let colorTheme = PianoKeyboardColorTheme()
    
    // MARK: - Computed Properties
    
    /// 所有按下的键（用户+程序）
    var allPressedKeys: Set<Int> {
        return userPressedKeys.union(appPressedKeys)
    }
    
    // MARK: - Public Methods
    
    /// 获取指定键的状态
    /// - Parameter keyNumber: MIDI键号
    /// - Returns: 键状态
    func getKeyState(for keyNumber: Int) -> KeyState? {
        let isUserPressed = userPressedKeys.contains(keyNumber)
        let isAppPressed = appPressedKeys.contains(keyNumber)
        let isTarget = targetNotes.contains(keyNumber)
        
        guard isUserPressed || isAppPressed || isTarget else {
            return nil
        }
        
        return KeyState(isUserPressed: isUserPressed, isAppPressed: isAppPressed, isTarget: isTarget)
    }
    
    /// 获取指定键的颜色
    /// - Parameters:
    ///   - keyNumber: MIDI键号
    ///   - isBlackKey: 是否为黑键
    /// - Returns: 键的颜色
    func getKeyColor(for keyNumber: Int, isBlackKey: Bool) -> Color {
        guard let keyState = getKeyState(for: keyNumber) else {
            return isBlackKey ? colorTheme.defaultBlackKeyColor : colorTheme.defaultWhiteKeyColor
        }
        
        switch keyState.pressType {
        case .user:
            return colorTheme.userPressedColor
        case .app:
            return colorTheme.appPressedColor
        case .mixed:
            return colorTheme.mixedPressedColor
        }
    }
    
    // MARK: - User Interaction Methods
    
    /// 用户按下键
    /// - Parameter keyNumber: MIDI键号
    func userPressKey(_ keyNumber: Int) {
        userPressedKeysBuffer.insert(keyNumber)
        scheduleDebouncedUpdate()
    }
    
    /// 用户释放键
    /// - Parameter keyNumber: MIDI键号
    func userReleaseKey(_ keyNumber: Int) {
        userPressedKeysBuffer.remove(keyNumber)
        scheduleDebouncedUpdate()
    }
    
    /// 用户释放所有键
    func userReleaseAllKeys() {
        userPressedKeys.removeAll()
    }
    
    // MARK: - App Interaction Methods
    
    /// 程序按下键
    /// - Parameter keyNumber: MIDI键号
    func appPressKey(_ keyNumber: Int) {
        appPressedKeys.insert(keyNumber)
    }
    
    /// 程序释放键
    /// - Parameter keyNumber: MIDI键号
    func appReleaseKey(_ keyNumber: Int) {
        appPressedKeys.remove(keyNumber)
    }
    
    /// 程序按下多个键（和弦）
    /// - Parameter keyNumbers: MIDI键号数组
    func appPressKeys(_ keyNumbers: Set<Int>) {
        appPressedKeys.formUnion(keyNumbers)
    }
    
    /// 程序释放所有键
    func appReleaseAllKeys() {
        appPressedKeys.removeAll()
    }
    
    /// 程序设置当前和弦（释放之前的，按下新的）
    /// - Parameter keyNumbers: MIDI键号数组
    func appSetCurrentChord(_ keyNumbers: [Int]) {
        appPressedKeys.removeAll()
        appPressKeys(Set(keyNumbers))
    }
    
    // MARK: - Target Notes Methods
    
    /// 设置目标音符（保持蓝色高亮）
    /// - Parameter keyNumbers: MIDI键号数组
    func setTargetNotes(_ keyNumbers: [Int]) {
        targetNotes = Set(keyNumbers)
    }
    
    /// 清除目标音符
    func clearTargetNotes() {
        targetNotes.removeAll()
    }
    
    /// 添加目标音符
    /// - Parameter keyNumber: MIDI键号
    func addTargetNote(_ keyNumber: Int) {
        targetNotes.insert(keyNumber)
    }
    
    /// 移除目标音符
    /// - Parameter keyNumber: MIDI键号
    func removeTargetNote(_ keyNumber: Int) {
        targetNotes.remove(keyNumber)
    }
    
    // MARK: - Debug Methods
    
    /// 获取调试信息
    var debugInfo: String {
        let userKeys = Array(userPressedKeys).sorted()
        let appKeys = Array(appPressedKeys).sorted()
        let mixedKeys = Array(userPressedKeys.intersection(appPressedKeys)).sorted()
        
        return """
        用户按键: \(userKeys)
        程序按键: \(appKeys)
        混合按键: \(mixedKeys)
        总计按键: \(allPressedKeys.count)
        """
    }
    
    /// 打印当前状态
    func printCurrentState() {
        print("🎹 PianoKeyboardViewModel 状态:")
        print(debugInfo)
    }
    
    // --- Gemini Code Fix: Debouncing Mechanism ---
    // 1. Internal buffer to hold incoming key events without publishing immediately.
    private func scheduleDebouncedUpdate() {
        // Invalidate any existing timer to reset the debounce window.
        debounceTimer?.invalidate()
        
        // Schedule a new timer.
        debounceTimer = Timer.scheduledTimer(withTimeInterval: debounceInterval, repeats: false) { [weak self] _ in
            self?.commitBufferToPublishedState()
        }
    }
    
    // 2. A timer to delay the publishing of changes.
    private func commitBufferToPublishedState() {
        // Run on the main thread as it updates a @Published property.
        DispatchQueue.main.async {
            // Check for actual changes to avoid redundant updates.
            if self.userPressedKeys != self.userPressedKeysBuffer {
                self.userPressedKeys = self.userPressedKeysBuffer
            }
        }
    }
    // --- End of Gemini Fix ---
}

// MARK: - Extensions

extension PianoKeyboardViewModel {
    /// 创建一个共享的全局实例（可选）
    static let shared = PianoKeyboardViewModel()
} 
