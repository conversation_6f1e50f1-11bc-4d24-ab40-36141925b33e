//
//  CompactGameStatus.swift
//  FullUI
//
//  Created by <PERSON> on 2025/5/26.
//  紧凑游戏状态组件 - 用于右下角显示统计信息
//

import SwiftUI

// MARK: - 紧凑游戏状态视图

/**
 * 紧凑的游戏状态显示组件
 * 设计为在右下角显示，不占用太多空间
 */
struct CompactGameStatus: View {
    let gameState: ChordGameState
    let rightNoteCount: Int
    let errorNoteCount: Int
    let currentRound: Int
    
    var body: some View {
        VStack(spacing: 8) {
            // 游戏状态指示器
            HStack(spacing: 6) {
                Circle()
                    .fill(gameStateColor)
                    .frame(width: 8, height: 8)
                
                Text(gameStateText)
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(.secondary)
            }
            
            // 统计信息
            HStack(spacing: 12) {
                StatItem(
                    icon: "checkmark.circle.fill",
                    value: gameState == .idle ? "-" : "\(rightNoteCount)",
                    color: .green
                )
                
                StatItem(
                    icon: "xmark.circle.fill",
                    value: gameState == .idle ? "-" : "\(errorNoteCount)",
                    color: .red
                )
                
                StatItem(
                    icon: "number.circle.fill",
                    value: gameState == .idle ? "-/12" : "\(currentRound)/12",
                    color: .blue
                )
            }
        }
        .padding(.horizontal, 10)
        .padding(.vertical, 6)
        .background(
            RoundedRectangle(cornerRadius: 10)
                .fill(.ultraThinMaterial)
                .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
        )
        .frame(maxWidth: 140) // 限制最大宽度，确保不超出屏幕
    }
    
    private var gameStateColor: Color {
        switch gameState {
        case .idle:
            return .gray
        case .playingChord:
            return .orange
        case .waitingForResponse:
            return .green
        case .completed:
            return .blue
        }
    }
    
    private var gameStateText: String {
        switch gameState {
        case .idle:
            return "Ready"
        case .playingChord:
            return "Playing..."
        case .waitingForResponse:
            return "Listening"
        case .completed:
            return "Complete"
        }
    }
}

// MARK: - 统计项组件

/**
 * 单个统计项显示组件
 */
struct StatItem: View {
    let icon: String
    let value: String
    let color: Color
    
    var body: some View {
        VStack(spacing: 2) {
            Image(systemName: icon)
                .font(.caption)
                .foregroundColor(color)
            
            Text(value)
                .font(.caption2)
                .fontWeight(.semibold)
                .foregroundColor(.primary)
        }
        .frame(minWidth: 24)
    }
}

// MARK: - 预览

#Preview {
    VStack(spacing: 20) {
        CompactGameStatus(
            gameState: .idle,
            rightNoteCount: 0,
            errorNoteCount: 0,
            currentRound: 0
        )
        
        CompactGameStatus(
            gameState: .waitingForResponse,
            rightNoteCount: 5,
            errorNoteCount: 2,
            currentRound: 3
        )
        
        CompactGameStatus(
            gameState: .completed,
            rightNoteCount: 10,
            errorNoteCount: 2,
            currentRound: 12
        )
    }
    .padding()
    .background(Color.gray.opacity(0.1))
} 