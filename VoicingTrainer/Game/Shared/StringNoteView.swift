//
//  MusicalNote.swift
//  VoicingTrainer
//
//  Created by <PERSON> on 2025/6/2.
//


//
//  NoteStringView.swift
//  ActiveEffects
//
//  Created by <PERSON> on 2025/6/2.
//

import SwiftUI
import AVFoundation

#if os(macOS)
import AppKit
#endif

// MARK: - Musical Note Configuration
struct MusicalNote: Equatable {
    let name: String
    let frequency: Double  // 基频 Hz
    let color: Color
    let octave: Int
    let intensity:Double    //力度
    

    
    private static let noteNames = ["C", "C#", "D", "D#", "E", "F", "F#", "G", "G#", "A", "A#", "B"]
    
    static func note(for midiNumber: Int,intensity Force:Double = 1.0) -> MusicalNote {
        // 将MIDI范围外的值安全地映射到88键范围内，而不是崩溃
        let clampedMidiNumber = max(21, min(108, midiNumber))
        
        // 如果输入值超出范围，记录警告但继续执行
        if midiNumber != clampedMidiNumber {
            print("⚠️ MIDI Note \(midiNumber) 超出88键范围(21-108)，已自动调整为 \(clampedMidiNumber)")
        }
        
        // Calculate note name and octave (使用调整后的MIDI值)
        let noteIndex = (clampedMidiNumber - 21) % 12
        let octave = (clampedMidiNumber - 12) / 12
        let name = "\(noteNames[noteIndex])\(octave)"
        
        // Calculate frequency (A4 = 440Hz, MIDI 69) (使用调整后的MIDI值)
        let frequency = pow(2.0, Double(clampedMidiNumber - 69) / 12.0) * 440.0
        
        // Calculate color gradient from blue (low notes) to red (middle) to yellow (high) to white (C8)
        //let normalizedPosition = Double(midiNumber - 21) / Double(108 - 21)
        let color: Color = .white
        /*
        if midiNumber == 108 { // C8 is white
            color = .white
        } else if normalizedPosition < 0.5 { // Blue to red
            let progress = normalizedPosition * 2.0
            color = Color(
                red: progress,
                green: 0,
                blue: 1.0 - progress
            )
        } else { // Red to yellow
            let progress = (normalizedPosition - 0.5) * 2.0
            color = Color(
                red: 1.0,
                green: progress,
                blue: 0
            )
        }
        */
        
        return MusicalNote(
            name: name,
            frequency: frequency,
            color: color.opacity(0.8),
            octave: octave,
            intensity:Force
        )
    }
    // 生成谐波频率
    func harmonicFrequencies(count: Int) -> [Double] {
        return (1...count).map { Double($0) * frequency }
    }
    
    // 自定义 Equatable 实现，忽略 Color 比较
    static func == (lhs: MusicalNote, rhs: MusicalNote) -> Bool {
        return lhs.name == rhs.name && 
               lhs.frequency == rhs.frequency && 
               lhs.octave == rhs.octave
    }
}

// MARK: - String Physics Configuration
struct StringConfig {
    let fundamentalFreq: Double  // 基频 (Hz)
    let dampingFactor: Double    // 阻尼系数
    let tension: Double          // 张力
    let color: Color
    let lineWidth: CGFloat
    let harmonicCount: Int       // 谐波数量
    let note: MusicalNote?       // 关联的音符
    
    static let strings: [StringConfig] = [
        StringConfig(fundamentalFreq: 82.4, dampingFactor: 0.992, tension: 1.0,
                     color: Color.white.opacity(0.4), lineWidth: 1, harmonicCount: 6, note: nil),  // E弦
        StringConfig(fundamentalFreq: 110.0, dampingFactor: 0.994, tension: 1.2,
                     color: Color.white.opacity(0.7), lineWidth: 2, harmonicCount: 5, note: nil),  // A弦
        StringConfig(fundamentalFreq: 146.8, dampingFactor: 0.996, tension: 1.5,
                     color: Color.white.opacity(0.9), lineWidth: 3, harmonicCount: 4, note: nil)   // D弦
    ]
    
    // 创建音符专用配置
    static func noteConfig(for note: MusicalNote) -> StringConfig {
        return StringConfig(
            fundamentalFreq: note.frequency,
            dampingFactor: 0.995,
            tension: 1.0,
            color: note.color,
            lineWidth: 3.0,
            harmonicCount: 8,
            note: note
        )
    }
}

// MARK: - Harmonic Component
struct HarmonicComponent {
    var amplitude: Double
    var frequency: Double
    var phase: Double
    var dampingRate: Double
    
    mutating func update(deltaTime: Double) {
        // 频率相关的衰减 - 高频衰减更快
        amplitude *= pow(dampingRate, deltaTime * 60.0)
    }
}

// MARK: - String Physics Model
class StringPhysics {
    var harmonics: [HarmonicComponent] = []
    var restingAmplitude: Double = 0.02  // 静息时的微小振动
    var isExcited: Bool = false
    var excitationTime: Double = 0
    
    // 拖拽张力状态
    var dragTension: Double = 0.0
    var isDragTensioned: Bool = false
    
    // 音符模式
    var currentNote: MusicalNote?
    var noteStartTime: Double = 0
    var noteDuration: Double = 3.0  // 音符持续时间
    
    let config: StringConfig
    let stringLength: Double = 1.0  // 标准化弦长
    
    init(config: StringConfig) {
        self.config = config
        setupHarmonics()
    }
    
    private func setupHarmonics() {
        harmonics.removeAll()
        
        // 生成谐波分量：基频、2倍频、3倍频...
        for n in 1...config.harmonicCount {
            let harmonic = HarmonicComponent(
                amplitude: restingAmplitude / pow(Double(n),1.5),    //Double(n * n),  // 1/n² 衰减
                frequency: config.fundamentalFreq * Double(n),
                phase: Double.random(in: 0...(2 * .pi)),
                dampingRate: config.dampingFactor * pow(0.99, Double(n-1))  // 高频衰减更快
            )
            harmonics.append(harmonic)
        }
    }
    
    func excite(intensity: Double = 1.0) {
        isExcited = true
        excitationTime = 0
        
        // 重新设置谐波振幅 - 模拟拨弦的复杂激发
        for i in 0..<harmonics.count {
            let n = Double(i + 1)
            // 拨弦激发模式：奇次谐波更强
            let excitationFactor = intensity * (n.truncatingRemainder(dividingBy: 2) == 1 ? 1.0 : 0.3)
            harmonics[i].amplitude = (0.1 * excitationFactor) / (n * 0.5)
            harmonics[i].phase = Double.random(in: 0...(2 * .pi))
        }
    }
    
    // 播放特定音符
    func playNote(_ note: MusicalNote) {
        currentNote = note
        noteStartTime = 0
        isExcited = true
        
        // 重新设置谐波以匹配音符频率
        harmonics.removeAll()
        let noteHarmonics = note.harmonicFrequencies(count: config.harmonicCount)
        
        for (i, freq) in noteHarmonics.enumerated() {
            
          // print("intensity:\(note.intensity)")
            
            
            let n = Double(i + 1)
            let harmonic = HarmonicComponent(
                //把强度平方，加大数据
                amplitude: (note.intensity*note.intensity * 0.02) / (n * 0.7),  // 更明显的振幅
                frequency: freq,
                phase: Double.random(in: 0...(2 * .pi)),
                dampingRate: config.dampingFactor * pow(0.98, Double(i))
            )
            harmonics.append(harmonic)
        }
    }
    
    func update(deltaTime: Double) {
        if isExcited {
            excitationTime += deltaTime
        }
        
        if currentNote != nil {
            noteStartTime += deltaTime
        }
        
        // 更新所有谐波分量
        for i in 0..<harmonics.count {
            harmonics[i].update(deltaTime: deltaTime)
            
            // 检查是否衰减到静息状态
            if harmonics[i].amplitude < restingAmplitude * 1.1 && !isDragTensioned {
                harmonics[i].amplitude = restingAmplitude / Double((i + 1) * (i + 1))
            }
        }
        
        // 检查音符是否结束
        if let _ = currentNote, noteStartTime > noteDuration {
            currentNote = nil
            setupHarmonics()  // 恢复原始谐波
        }
        
        // 检查整体是否回到静息状态
        let totalEnergy = harmonics.reduce(0) { $0 + $1.amplitude }
        if totalEnergy < restingAmplitude * Double(config.harmonicCount) * 2 && !isDragTensioned && currentNote == nil {
            isExcited = false
        }
    }
    
    // 设置拖拽张力
    func setDragTension(_ tension: Double) {
        dragTension = tension
        isDragTensioned = tension > 0.01
        
        // 根据张力调整谐波振幅
        if isDragTensioned {
            for i in 0..<harmonics.count {
                let n = Double(i + 1)
                let tensionAmplitude = (tension * 0.02) / (n * 0.8)  // 张力产生的振幅
                harmonics[i].amplitude = max(harmonics[i].amplitude, tensionAmplitude)
            }
        }
    }
    
    // 应用拖拽张力（每帧调用）
    func applyDragTension(_ intensity: Double) {
        if isDragTensioned {
            // 模拟张力引起的微小振动
            for i in 0..<harmonics.count {
                let n = Double(i + 1)
                let tensionEffect = (intensity * 0.005) / n
                harmonics[i].amplitude += tensionEffect * sin(Double(CFAbsoluteTimeGetCurrent()) * 10.0)
            }
        }
    }
    
    // 清除拖拽张力
    func clearDragTension() {
        dragTension = 0.0
        isDragTensioned = false
    }
    
    // 计算琴弦在位置x处的位移 - 驻波模式 + 拖拽效果
    func displacement(at normalizedX: Double, time: Double) -> Double {
        var totalDisplacement: Double = 0
        
        for (index, harmonic) in harmonics.enumerated() {
            let n = Double(index + 1)
            
            // 驻波公式：sin(nπx/L) * cos(ωt + φ) * amplitude
            let spatialComponent = sin(n * .pi * normalizedX)  // 空间部分
            let temporalComponent = cos(2 * .pi * harmonic.frequency * time + harmonic.phase)  // 时间部分
            
            var amplitude = harmonic.amplitude
            
            // 如果正在拖拽，添加拖拽效果
            if isDragTensioned {
                // 模拟弦被拉伸时的形变 - 抛物线形状
                let dragDeformation = dragTension * 0.1 * (4 * normalizedX * (1 - normalizedX))
                amplitude += dragDeformation
            }
            
            totalDisplacement += spatialComponent * temporalComponent * amplitude
        }
        
        return totalDisplacement
    }
    
    // 获取当前状态信息
    func getCurrentState() -> (isPlayingNote: Bool, noteName: String?, noteColor: Color?) {
        if let note = currentNote {
            return (true, note.name, note.color)
        }
        return (false, nil, nil)
    }
}

// MARK: - String Animation State
class StringAnimationState: ObservableObject {
    @Published var needsUpdate: Bool = false
    
    private var timer: Timer?
    private var lastUpdateTime: Date = Date()
    
    var stringPhysics: [StringPhysics] = []
    var currentTime: Double = 0
    
    // 拖拽状态
    @Published var isDragging: Bool = false
    @Published var dragDistance: Double = 0.0
    @Published var dragIntensity: Double = 0.0
    
    init() {
        // 为每根弦创建物理模型
        stringPhysics = StringConfig.strings.map { StringPhysics(config: $0) }
    }
    
    func startAnimation() {
        guard timer == nil else { return }
        
        lastUpdateTime = Date()
        timer = Timer.scheduledTimer(withTimeInterval: 1/60.0, repeats: true) { _ in
            DispatchQueue.main.async {
                let now = Date()
                let deltaTime = now.timeIntervalSince(self.lastUpdateTime)
                self.lastUpdateTime = now
                
                self.currentTime += deltaTime
                
                // 更新所有弦的物理状态
                for physics in self.stringPhysics {
                    physics.update(deltaTime: deltaTime)
                    
                    // 如果正在拖拽，应用实时拖拽强度
                    if self.isDragging && self.dragIntensity > 0 {
                        physics.applyDragTension(self.dragIntensity)
                    }
                }
                
                self.needsUpdate.toggle()  // 触发重绘
            }
        }
    }
    
    func stopAnimation() {
        timer?.invalidate()
        timer = nil
    }
    
    func pluckString(at index: Int, intensity: Double = 1.0) {
        guard index < stringPhysics.count else { return }
        stringPhysics[index].excite(intensity: intensity)
    }
    
    func pluckAllStrings(intensity: Double = 1.0) {
        for physics in stringPhysics {
            physics.excite(intensity: intensity * Double.random(in: 0.8...1.2))  // 添加随机性
        }
    }
    
    // 开始拖拽
    func startDrag() {
        isDragging = true
        dragDistance = 0.0
        dragIntensity = 0.0
    }
    
    // 更新拖拽状态
    func updateDrag(distance: Double) {
        dragDistance = distance
        // 根据距离计算强度，使用平方根来模拟物理感觉
        dragIntensity = min(sqrt(distance / 50.0), 3.0)
        
        // 实时更新琴弦张力
        for physics in stringPhysics {
            physics.setDragTension(dragIntensity)
        }
    }
    
    // 结束拖拽
    func endDrag(finalDistance: Double) {
        isDragging = false
        
        // 根据最终拖拽距离激发琴弦
        let releaseIntensity = min(sqrt(finalDistance / 30.0), 4.0)
        pluckAllStrings(intensity: releaseIntensity)
        
        // 重置拖拽状态
        dragDistance = 0.0
        dragIntensity = 0.0
        
        // 清除拖拽张力
        for physics in stringPhysics {
            physics.clearDragTension()
        }
    }
    
    deinit {
        stopAnimation()
    }
}

// MARK: - String Renderer
struct StringRenderer {
    static func createStringPath(
        physics: StringPhysics,
        currentTime: Double,
        containerSize: CGSize,
        baseAmplitude: CGFloat
    ) -> Path {
        let width = containerSize.width
        let height = containerSize.height
        let centerY = height / 2
        
        var path = Path()
        let samples = 200  // 高精度采样
        
        for i in 0...samples {
            let normalizedX = Double(i) / Double(samples)  // 0 到 1
            let x = CGFloat(normalizedX) * width
            
            // 计算琴弦在此位置的位移
            let displacement = physics.displacement(at: normalizedX, time: currentTime)
            let y = centerY + CGFloat(displacement * Double(baseAmplitude))
            
            if i == 0 {
                path.move(to: CGPoint(x: x, y: y))
            } else {
                path.addLine(to: CGPoint(x: x, y: y))
            }
        }
        
        return path
    }
}

// MARK: - String Controller
class StringController: ObservableObject {
    @Published var triggerNote: MusicalNote?
    
    func playNote(_ note: MusicalNote) {
        triggerNote = note
    }
}

// MARK: - Main String View
struct NoteStringView: View{
    @StateObject private var animationState = StringAnimationState()
    @ObservedObject var controller = StringController()
    @State private var dragStartLocation: CGPoint = .zero
    @State private var currentDragLocation: CGPoint = .zero
    @State private var lastPlayedNote: MusicalNote?
    @State private var noteDisplayTimer: Timer?
    @State private var showGreeting: Bool = true
    
    var body: some View {
        GeometryReader { geometry in
            Canvas { context, size in
                let baseAmplitude = size.height / 8.0  // 合适的振幅比例
                
                // 绘制所有琴弦
                for (index, physics) in animationState.stringPhysics.enumerated() {
                    let path = StringRenderer.createStringPath(
                        physics: physics,
                        currentTime: animationState.currentTime,
                        containerSize: size,
                        baseAmplitude: baseAmplitude
                    )
                    
                    let config = StringConfig.strings[index]
                    let (isPlayingNote, _, noteColor) = physics.getCurrentState()
                    
                    // 根据激发状态和拖拽状态调整颜色亮度
                    var brightness = physics.isExcited ? 1.0 : 0.5
                    var dynamicColor = config.color
                    
                    // 如果正在播放音符，使用音符颜色
                    if isPlayingNote, let noteColor = noteColor {
                        dynamicColor = noteColor
                        brightness = 1.0
                    }
                    
                    // 拖拽时增加亮度和颜色强度
                    if animationState.isDragging {
                        brightness = max(brightness, 0.3 + animationState.dragIntensity * 0.7)
                    }
                    
                    let finalColor = dynamicColor.opacity(brightness)
                    
                    // 拖拽时增加线条宽度
                    let dynamicLineWidth = config.lineWidth + (animationState.isDragging ? CGFloat(animationState.dragIntensity * 1.5) : 0)
                    
                    context.stroke(
                        path,
                        with: .color(finalColor),
                        style: StrokeStyle(
                            lineWidth: dynamicLineWidth,
                            lineCap: .round,
                            lineJoin: .round
                        )
                    )
                }
            }
            .clipped()
            .background(Color.clear)
            .contentShape(Rectangle())
            
            .onReceive(Timer.publish(every: 0.1, on: .main, in: .common).autoconnect()) { _ in
                // 检测右键点击 - 仅在macOS上
                #if os(macOS)
                if NSApp.currentEvent?.type == .rightMouseDown {
                    playNoteOnRandomString(MusicalNote.note(for:60))
                }
                #endif
            }
         
        }
        .onReceive(animationState.$needsUpdate) { _ in
            // 强制重绘
        }
        .onChange(of: controller.triggerNote) { newValue in
            if let note = newValue {
                playNoteOnRandomString(note)
                controller.triggerNote = nil  // 重置触发器
            }
        }
        .onAppear {
            animationState.startAnimation()
            setupGlobalEventMonitor()
        }
        .onDisappear {
            animationState.stopAnimation()
            removeGlobalEventMonitor()
        }
    }
    
    // 在随机琴弦上播放音符
    public func playNoteOnRandomString(_ note: MusicalNote) {
        let randomIndex = Int.random(in: 0..<animationState.stringPhysics.count)
        animationState.stringPhysics[randomIndex].playNote(note)
        lastPlayedNote = note
        
        // 设置显示计时器
        noteDisplayTimer?.invalidate()
        noteDisplayTimer = Timer.scheduledTimer(withTimeInterval: 2.0, repeats: false) { _ in
            lastPlayedNote = nil
        }
    }
    
    // 设置全局事件监听
    private func setupGlobalEventMonitor() {
        // 这里可以添加更复杂的键盘监听
    }
    
    private func removeGlobalEventMonitor() {
        noteDisplayTimer?.invalidate()
    }
    
}
