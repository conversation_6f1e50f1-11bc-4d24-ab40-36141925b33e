//
//  SimpleNoteDisplay.swift
//  VoicingTrainer
//
//  简化版音符显示组件 - 单一职责：显示音符
//
//  ⚠️ 【重要知识点】SwiftUI坐标空间定义规则：
//  1. 绝对不要在子视图中重复定义已存在的坐标空间名称
//  2. coordinateSpace(name: "SameName") 在子视图中会覆盖父视图的定义
//  3. 这会导致坐标获取错误，且难以调试（坐标值看起来合理但位置错误）
//  4. 本组件使用父级"RefactoredNotesDisplay"坐标空间，不可重复定义
//

import SwiftUI

/// 简化的音符显示状态
enum NoteDisplayState {
    case normal      // 正常显示
    case played      // 被弹奏
    case correct     // 正确
    case exploded    // 已爆炸（隐藏）
}

/// 单个音符显示组件
struct SimpleNoteCard: View {
    let note: Int
    let state: NoteDisplayState
    let onTap: (Int) -> Void
    let onPositionChange: (Int, CGPoint) -> Void
    
    @State private var animationScale: CGFloat = 1.0
    
    var body: some View {
        Text(NoteNameGenerator.getNoteName(note: UInt8(note)))
            .font(.system(size: 48, weight: .bold, design: .rounded))
            .foregroundColor(textColor)
            .shadow(color: .black.opacity(0.3), radius: 2, x: 1, y: 1)
            .frame(width: 120, height: 60)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color.clear)
                    .shadow(color: shadowColor, radius: shadowRadius, x: 0, y: shadowOffset)
            )
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(borderColor, lineWidth: borderWidth)
            )
            .scaleEffect(animationScale)
            .opacity(opacity)
            .background(
                GeometryReader { geometry in
                    Color.clear
                        .onAppear {
                            reportPosition(geometry)
                        }
                        .onChange(of: geometry.frame(in: .named("RefactoredNotesDisplay"))) { _ in
                            reportPosition(geometry)
                        }
                }
            )
            .onTapGesture {
                onTap(note)
            }
            .onChange(of: state) { newState in
                withAnimation(.spring(response: 0.3, dampingFraction: 0.6)) {
                    switch newState {
                    case .played, .correct:
                        animationScale = 1.2
                        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                            animationScale = 1.0
                        }
                    default:
                        animationScale = 1.0
                    }
                }
            }
    }
    
    private func reportPosition(_ geometry: GeometryProxy) {
        let center = CGPoint(
            x: geometry.frame(in: .named("RefactoredNotesDisplay")).midX,
            y: geometry.frame(in: .named("RefactoredNotesDisplay")).midY
        )
        
        // 🔧 详细调试信息
        print("📍 音符 \(note) 坐标报告:")
        print("   原始frame: \(geometry.frame(in: .local))")
        print("   相对于RefactoredNotesDisplay的frame: \(geometry.frame(in: .named("RefactoredNotesDisplay")))")
        print("   计算出的中心点: \(center)")
        print("   音符名: \(NoteNameGenerator.getNoteName(note: UInt8(note)))")
        
        onPositionChange(note, center)
    }
    
    // MARK: - 样式属性
    
    private var textColor: Color {
        switch state {
        case .normal: return .primary
        case .played: return .blue
        case .correct: return .green
        case .exploded: return .white
        }
    }
    
    private var borderColor: Color {
        switch state {
        case .normal: return .primary.opacity(0.6)
        case .played: return .blue
        case .correct: return .green
        case .exploded: return .white
        }
    }
    
    private var borderWidth: CGFloat {
        switch state {
        case .normal: return 2
        case .played: return 3
        case .correct: return 4
        case .exploded: return 4
        }
    }
    
    private var shadowColor: Color {
        switch state {
        case .normal: return .black.opacity(0.1)
        case .played: return .blue.opacity(0.3)
        case .correct: return .green.opacity(0.5)
        case .exploded: return .green.opacity(0.5)
        }
    }
    
    private var shadowRadius: CGFloat {
        switch state {
        case .normal: return 2
        default: return 8
        }
    }
    
    private var shadowOffset: CGFloat {
        switch state {
        case .normal: return 1
        default: return 0
        }
    }
    
    private var opacity: Double {
        state == .exploded ? 0.0 : 1.0
    }
}

/// 简化的音符显示组件
struct SimpleNoteDisplay: View {
    let expectedNotes: Set<Int>
    let playedNotes: Set<Int>
    let correctNotes: Set<Int>
    let explodedNotes: Set<Int>
    
    let onNoteTap: (Int) -> Void
    let onNotePosition: (Int, CGPoint) -> Void
    
    var body: some View {
        LazyVGrid(columns: Array(repeating: GridItem(.flexible(), spacing: 12), count: min(expectedNotes.count, 4)), spacing: 12) {
            ForEach(Array(expectedNotes).sorted(), id: \.self) { note in
                SimpleNoteCard(
                    note: note,
                    state: noteState(for: note),
                    onTap: onNoteTap,
                    onPositionChange: onNotePosition
                )
            }
        }
        .padding()
    }
    
    private func noteState(for note: Int) -> NoteDisplayState {
        if explodedNotes.contains(note) {
            return .exploded
        } else if correctNotes.contains(note) {
            return .correct
        } else if playedNotes.contains(note) {
            return .played
        } else {
            return .normal
        }
    }
} 
