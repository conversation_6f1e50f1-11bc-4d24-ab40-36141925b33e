//
//  NoteGameStateManager.swift
//  VoicingTrainer
//
//  游戏状态管理器 - 单一职责：管理音符游戏状态
//

import Foundation
import Combine

/// 游戏状态管理器
@MainActor
class NoteGameStateManager: ObservableObject {
    
    // MARK: - 发布的状态
    @Published var correctNotes: Set<Int> = []
    @Published var explodedNotes: Set<Int> = []
    @Published var isChordComplete: Bool = false
    @Published var shouldShowPerfectAnimation: Bool = false
    
    // MARK: - 私有状态
    private var expectedNotes: Set<Int> = []
    private var playedNotes: Set<Int> = []
    private var hasShownPerfectAnimation: Bool = false
    
    // MARK: - 回调
    var onNoteHit: ((Int, CGPoint) -> Void)?
    var onChordComplete: (() -> Void)?
    
    // MARK: - 公共方法
    
    /// 设置期望的音符
    func setExpectedNotes(_ notes: Set<Int>) {
        print("🎯 设置期望音符: \(notes.sorted())")
        expectedNotes = notes
        resetGameState()
    }
    
    /// 更新当前弹奏的音符
    func updatePlayedNotes(_ notes: Set<Int>, notePositions: [Int: CGPoint]) {
        print("🎹 更新弹奏音符: \(notes.sorted())")
        let oldPlayedNotes = playedNotes
        playedNotes = notes
        
        // 检测新击中的音符
        let newHits = notes.subtracting(oldPlayedNotes).intersection(expectedNotes)
        
        for note in newHits {
            handleNoteHit(note, notePositions: notePositions)
        }
        
        // 检查和弦完成状态
        checkChordCompletion()
    }
    
    /// 重置游戏状态
    func resetGameState() {
        print("🔄 重置游戏状态")
        correctNotes.removeAll()
        explodedNotes.removeAll()
        isChordComplete = false
        shouldShowPerfectAnimation = false
        hasShownPerfectAnimation = false
    }
    
    // MARK: - 私有方法
    
    private func handleNoteHit(_ note: Int, notePositions: [Int: CGPoint]) {
        print("🎯 音符击中: \(note)")
        
        // 标记为正确
        correctNotes.insert(note)
        
        // 触发爆炸效果
        if let position = notePositions[note] {
            onNoteHit?(note, position)
            
            // 延迟标记为已爆炸，给动画时间
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                self.explodedNotes.insert(note)
            }
        }
        
        // 触觉反馈
        HapticFeedback.impact(.light)
    }
    
    private func checkChordCompletion() {
        let allNotesCorrect = correctNotes.count == expectedNotes.count && correctNotes == expectedNotes
        
        if allNotesCorrect && !hasShownPerfectAnimation {
            print("🌟 和弦完成！")
            isChordComplete = true
            shouldShowPerfectAnimation = true
            hasShownPerfectAnimation = true
            
            // 播放和弦正确音效
            SoundEffectManager.shared.playChordCorrect()
            
            // 触发完成回调
            onChordComplete?()
            
            // 触觉反馈
            HapticFeedback.impact(.success)
            
            // 重置Perfect动画标志
            DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                self.shouldShowPerfectAnimation = false
            }
        }
    }
} 