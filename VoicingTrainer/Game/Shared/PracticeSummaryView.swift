//
//  PracticeSummaryView.swift
//  VoicingTrainer
//
//  Created by <PERSON> on 2025/1/5.
//

import SwiftUI

struct PracticeSummaryView: View {
    let rightNoteCount: Int
    let errorNoteCount: Int
    let totalTime: TimeInterval
    @Binding var isPresented: Bool
    
    // 按钮控制属性
    let onReplay: (() -> Void)?
    let onNextLevel: (() -> Void)?
    let showReplayButton: Bool      // 是否显示重玩按钮
    let showNextLevelButton: Bool   // 是否显示下一关按钮
    
    @State private var animateTitle = false
    @State private var animateStats = false
    @State private var animateButtons = false
    @State private var showConfetti = false
    
    // 默认初始化器（向后兼容 - 仅显示关闭按钮）
    init(rightNoteCount: Int, errorNoteCount: Int, totalTime: TimeInterval, isPresented: Binding<Bool>) {
        self.rightNoteCount = rightNoteCount
        self.errorNoteCount = errorNoteCount
        self.totalTime = totalTime
        self._isPresented = isPresented
        self.onReplay = nil
        self.onNextLevel = nil
        self.showReplayButton = false
        self.showNextLevelButton = false
    }
    
    // 完整初始化器
    init(rightNoteCount: Int, errorNoteCount: Int, totalTime: TimeInterval,
         isPresented: Binding<Bool>, onReplay: (() -> Void)? = nil,
         onNextLevel: (() -> Void)? = nil, showReplayButton: Bool = false,
         showNextLevelButton: Bool = false) {
        self.rightNoteCount = rightNoteCount
        self.errorNoteCount = errorNoteCount
        self.totalTime = totalTime
        self._isPresented = isPresented
        self.onReplay = onReplay
        self.onNextLevel = onNextLevel
        self.showReplayButton = showReplayButton
        self.showNextLevelButton = showNextLevelButton
    }
    
    var accuracy: Double {
        let total = rightNoteCount + errorNoteCount
        return total > 0 ? Double(rightNoteCount) / Double(total) * 100 : 0
    }
    
    var starRating: Int {
        switch accuracy {
        case 90...100: return 5
        case 80..<90: return 4
        case 60..<80: return 3
        case 40..<60: return 2
        default: return 1
        }
    }
    
    var formattedTime: String {
        let minutes = Int(totalTime) / 60
        let seconds = Int(totalTime) % 60
        return String(format: "%02d:%02d", minutes, seconds)
    }
    
    var congratulationMessage: String {
        switch starRating {
        case 5: return "🎉 Perfect! Outstanding Performance!"
        case 4: return "⭐️ Excellent! Great Job!"
        case 3: return "👍 Good Work! Keep Practicing!"
        case 2: return "💪 Not Bad! You Can Do Better!"
        default: return "🎯 Keep Trying! Practice Makes Perfect!"
        }
    }
    
    var body: some View {
        ZStack {
            // 渐变背景
            LinearGradient(
                gradient: Gradient(colors: [
                    Color.purple.opacity(0.4),
                    Color.blue.opacity(0.6),
                    Color.cyan.opacity(0.4)
                ]),
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
            .ignoresSafeArea()
            
            // 闪烁效果（用简单的方式模拟）
            if showConfetti {
                ForEach(0..<20, id: \.self) { index in
                    ConfettiParticle(index: index)
                }
            }
            
            // 主内容
            VStack(spacing: 32) {
                Spacer()
                
                // 标题和星级
                VStack(spacing: 20) {
                    Text("🎉 Level Complete!")
                        .font(.system(size: 32, weight: .bold, design: .rounded))
                        .foregroundStyle(
                            LinearGradient(
                                gradient: Gradient(colors: [.yellow, .orange]),
                                startPoint: .leading,
                                endPoint: .trailing
                            )
                        )
                        .scaleEffect(animateTitle ? 1.0 : 0.8)
                        .opacity(animateTitle ? 1.0 : 0.0)
                    
                    // 星级评分
                    HStack(spacing: 8) {
                        ForEach(1...5, id: \.self) { index in
                            Image(systemName: index <= starRating ? "star.fill" : "star")
                                .foregroundColor(index <= starRating ? .yellow : .gray.opacity(0.5))
                                .font(.title)
                                .scaleEffect(animateTitle ? 1.0 : 0.5)
                                .animation(.spring(response: 0.6, dampingFraction: 0.8).delay(Double(index) * 0.1), value: animateTitle)
                        }
                    }
                    
                    // 祝贺信息
                    Text(congratulationMessage)
                        .font(.headline)
                        .foregroundColor(.white)
                        .multilineTextAlignment(.center)
                        .opacity(animateTitle ? 1.0 : 0.0)
                }
                
                // 统计卡片
                VStack(spacing: 16) {
                    // 精确度图表
                    AccuracyChart(percent: accuracy, starRating: starRating)
                        .scaleEffect(animateStats ? 1.0 : 0.8)
                        .opacity(animateStats ? 1.0 : 0.0)

                    // 详细统计
                    VStack(spacing: 12) {
                        StatisticRow(
                            icon: "checkmark.circle.fill",
                            iconColor: .green,
                            title: "Correct Notes",
                            value: "\(rightNoteCount)",
                            animateOffset: animateStats ? 0 : 100
                        )
                        
                        StatisticRow(
                            icon: "xmark.circle.fill",
                            iconColor: .red,
                            title: "Wrong Notes",
                            value: "\(errorNoteCount)",
                            animateOffset: animateStats ? 0 : 100
                        )
                        
                        StatisticRow(
                            icon: "percent",
                            iconColor: .blue,
                            title: "Accuracy",
                            value: String(format: "%.1f%%", accuracy),
                            animateOffset: animateStats ? 0 : 100
                        )
                        
                        StatisticRow(
                            icon: "clock.fill",
                            iconColor: .orange,
                            title: "Total Time",
                            value: formattedTime,
                            animateOffset: animateStats ? 0 : 100
                        )
                    }
                }
                .padding(24)
                .background(
                    RoundedRectangle(cornerRadius: 24)
                        .fill(.ultraThinMaterial)
                        .shadow(color: .black.opacity(0.2), radius: 20, x: 0, y: 10)
                )
                
                Spacer()
                
                // 按钮组 - 所有按钮排成一行
                HStack(spacing: 12) {
                    // 重玩按钮（根据模式显示）
                    if showReplayButton {
                        Button(action: {
                            onReplay?()
                        }) {
                            HStack(spacing: 6) {
                                Image(systemName: "arrow.clockwise.circle.fill")
                                Text("Replay")
                            }
                            .font(.subheadline)
                            .fontWeight(.semibold)
                            .foregroundColor(.white)
                            .padding(.horizontal, 16)
                            .padding(.vertical, 10)
                            .background(
                                LinearGradient(
                                    gradient: Gradient(colors: [.orange, .red]),
                                    startPoint: .leading,
                                    endPoint: .trailing
                                )
                            )
                            .cornerRadius(20)
                        }
                    }

                    // 下一关按钮（仅在关卡模式显示）
                    if showNextLevelButton {
                        Button(action: {
                            onNextLevel?()
                        }) {
                            HStack(spacing: 6) {
                                Image(systemName: "arrow.right.circle.fill")
                                Text("Next Level")
                            }
                            .font(.subheadline)
                            .fontWeight(.semibold)
                            .foregroundColor(.white)
                            .padding(.horizontal, 16)
                            .padding(.vertical, 10)
                            .background(
                                LinearGradient(
                                    gradient: Gradient(colors: [.green, .mint]),
                                    startPoint: .leading,
                                    endPoint: .trailing
                                )
                            )
                            .cornerRadius(20)
                            .shadow(color: .green.opacity(0.3), radius: 8, x: 0, y: 3)
                        }
                    }

                    // 返回按钮
                    Button(action: {
                        withAnimation(.easeInOut(duration: 0.3)) {
                            isPresented = false
                        }
                    }) {
                        HStack(spacing: 6) {
                            Image(systemName: "house.circle.fill")
                            Text("Menu")
                        }
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(.white)
                        .padding(.horizontal, 16)
                        .padding(.vertical, 10)
                        .background(
                            LinearGradient(
                                gradient: Gradient(colors: [.blue, .purple]),
                                startPoint: .leading,
                                endPoint: .trailing
                            )
                        )
                        .cornerRadius(20)
                    }
                }
                .offset(y: animateButtons ? 0 : 100)
                .opacity(animateButtons ? 1.0 : 0.0)
                
                Spacer()
            }
            .padding(.horizontal, 32)
        }
        .onAppear {
            startAnimations()
        }
    }
    
    private func startAnimations() {
        // 显示闪烁效果
        showConfetti = true
        
        withAnimation(.spring(response: 0.8, dampingFraction: 0.8).delay(0.2)) {
            animateTitle = true
        }
        
        withAnimation(.spring(response: 0.8, dampingFraction: 0.8).delay(0.6)) {
            animateStats = true
        }
        
        withAnimation(.spring(response: 0.8, dampingFraction: 0.8).delay(1.0)) {
            animateButtons = true
        }
        
        // 3秒后隐藏闪烁效果
        DispatchQueue.main.asyncAfter(deadline: .now() + 3.0) {
            showConfetti = false
        }
    }
}

// MARK: - 统计行组件
struct StatisticRow: View {
    let icon: String
    let iconColor: Color
    let title: String
    let value: String
    let animateOffset: CGFloat
    
    var body: some View {
        HStack {
            Image(systemName: icon)
                .foregroundColor(iconColor)
                .font(.title2)
                .frame(width: 30)
            
            Text(title)
                .font(.body)
                .foregroundColor(.white.opacity(0.9))
            
            Spacer()
            
            Text(value)
                .font(.headline)
                .fontWeight(.semibold)
                .foregroundColor(.white)
        }
        .padding(.vertical, 4)
        .offset(x: animateOffset)
        .animation(.spring(response: 0.6, dampingFraction: 0.8), value: animateOffset)
    }
}

// MARK: - 精确度图表
struct AccuracyChart: View {
    let percent: Double
    let starRating: Int
    
    var chartColor: Color {
        switch starRating {
        case 5: return .green
        case 4: return .mint
        case 3: return .yellow
        case 2: return .orange
        default: return .red
        }
    }
    
    var body: some View {
        ZStack {
            // 背景圆圈
            Circle()
                .stroke(Color.gray.opacity(0.3), lineWidth: 20)
            
            // 进度圆圈
            Circle()
                .trim(from: 0, to: percent / 100)
                .stroke(
                    AngularGradient(
                        gradient: Gradient(colors: [chartColor.opacity(0.7), chartColor]),
                        center: .center
                    ),
                    style: StrokeStyle(lineWidth: 20, lineCap: .round)
                )
                .rotationEffect(.degrees(-90))
            
            // 中心文字
            VStack(spacing: 4) {
                Text(String(format: "%.0f%%", percent))
                    .font(.system(size: 36, weight: .bold, design: .rounded))
                    .foregroundColor(.white)
                
                Text("Accuracy")
                    .font(.caption)
                    .foregroundColor(.white.opacity(0.7))
            }
        }
        .frame(width: 160, height: 160)
    }
}

// MARK: - 闪烁粒子效果
struct ConfettiParticle: View {
    let index: Int
    @State private var animate = false
    
    private var randomColor: Color {
        [.red, .blue, .green, .yellow, .purple, .orange, .pink].randomElement() ?? .blue
    }
    
    private var randomDelay: Double {
        Double.random(in: 0...2)
    }
    
    var body: some View {
        Circle()
            .fill(randomColor)
            .frame(width: 6, height: 6)
            .offset(
                x: animate ? CGFloat.random(in: -200...200) : 0,
                y: animate ? CGFloat.random(in: -300...300) : 0
            )
            .opacity(animate ? 0 : 1)
            .scaleEffect(animate ? 2 : 0.5)
            .onAppear {
                withAnimation(.easeOut(duration: 2.0).delay(randomDelay)) {
                    animate = true
                }
            }
    }
}

#Preview {
    PracticeSummaryView(
        rightNoteCount: 10,
        errorNoteCount: 2,
        totalTime: 125.5,
        isPresented: .constant(true),
        onReplay: { print("Replay") },
        onNextLevel: { print("Next Level") },
        showReplayButton: true,
        showNextLevelButton: true
    )
}
