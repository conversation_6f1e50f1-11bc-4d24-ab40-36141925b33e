//
//  PianoKey.swift
//  FullUI
//
//  Created by <PERSON> Li on 2025/5/26.
//

import SwiftUI
import AudioKit
import CoreMIDI

//琴键的结构体:midi值，是否黑键,音名字符串
struct PianoKey: Identifiable {
    let id: Int // MIDI note number
    let isBlack: Bool
    let label: String
}

struct PianoKeyboardView: View {
    // MARK: - Properties
    
    /// 键盘视图模型
    @ObservedObject var viewModel: PianoKeyboardViewModel
    
    /// 回调函数
    var onKeyDown: ((Int, Int) -> Void)? // (noteNumber, velocity)
    var onKeyUp: ((Int) -> Void)?
    
    /// 键盘范围
    let firstKey: Int
    let lastKey: Int
    
    /// 布局状态
    @State private var availableWidth: CGFloat = 0
    
    // MARK: - Computed Properties
    
    /// 生成的键列表
    private var keys: [PianoKey] {
        Self.generateKeys(from: firstKey, to: lastKey)
    }
    
    /// 白键列表
    private var whiteKeys: [PianoKey] {
        keys.filter { !$0.isBlack }
    }
    
    /// 白键宽度
    private var whiteKeyWidth: CGFloat {
        guard whiteKeys.count > 0 && availableWidth > 0 else { return 24 }
        return availableWidth / CGFloat(whiteKeys.count)
    }
    
    /// 黑键宽度
    private var blackKeyWidth: CGFloat {
        whiteKeyWidth * 2 / 3
    }
    
    /// 键盘高度
    private var keyboardHeight: CGFloat {
        120
    }
    
    /// 黑键高度
    private var blackKeyHeight: CGFloat {
        keyboardHeight * 2 / 3
    }
    
    // MARK: - Initializers
    
    /// 使用指定的ViewModel初始化
    init(
        viewModel: PianoKeyboardViewModel,
        firstKey: Int = 21,
        lastKey: Int = 108,
        onKeyDown: ((Int, Int) -> Void)? = nil,
        onKeyUp: ((Int) -> Void)? = nil
    ) {
        self.viewModel = viewModel
        self.firstKey = firstKey
        self.lastKey = lastKey
        self.onKeyDown = onKeyDown
        self.onKeyUp = onKeyUp
    }
    
    /// 使用highlightedNotes的兼容性初始化器（向后兼容）
    init(
        highlightedNotes: Set<Int>,
        firstKey: Int = 21,
        lastKey: Int = 108,
        onKeyDown: ((Int, Int) -> Void)? = nil,
        onKeyUp: ((Int) -> Void)? = nil
    ) {
        let viewModel = PianoKeyboardViewModel()
        // 将highlightedNotes设置为程序按键
        for note in highlightedNotes {
            viewModel.appPressKey(note)
        }
        
        self.viewModel = viewModel
        self.firstKey = firstKey
        self.lastKey = lastKey
        self.onKeyDown = onKeyDown
        self.onKeyUp = onKeyUp
    }
    
    // MARK: - Body
    
    var body: some View {
        GeometryReader { geometry in
            ZStack(alignment: .topLeading) {
                // 白键层
                HStack(spacing: 0) {
                    ForEach(whiteKeys) { key in
                        PianoKeyView(
                            key: key,
                            keyColor: viewModel.getKeyColor(for: key.id, isBlackKey: false),
                            whiteKeyWidth: whiteKeyWidth,
                            blackKeyWidth: blackKeyWidth,
                            keyboardHeight: keyboardHeight,
                            blackKeyHeight: blackKeyHeight,
                            viewModel: viewModel,
                            onDown: { velocity in 
                                handleKeyDown(key.id, velocity: velocity)
                            },
                            onUp: { 
                                handleKeyUp(key.id)
                            }
                        )
                    }
                }
                .frame(width: availableWidth, height: keyboardHeight, alignment: .leading)
                .background(Color.white)
                
                // 黑键层
                .overlay(
                    ZStack(alignment: .topLeading) {
                        ForEach(keys.filter { $0.isBlack }) { key in
                            PianoKeyView(
                                key: key,
                                keyColor: viewModel.getKeyColor(for: key.id, isBlackKey: true),
                                whiteKeyWidth: whiteKeyWidth,
                                blackKeyWidth: blackKeyWidth,
                                keyboardHeight: keyboardHeight,
                                blackKeyHeight: blackKeyHeight,
                                viewModel: viewModel,
                                onDown: { velocity in 
                                    handleKeyDown(key.id, velocity: velocity)
                                },
                                onUp: { 
                                    handleKeyUp(key.id)
                                }
                            )
                            .position(x: blackKeyOffset(for: key.id), y: blackKeyHeight / 2)
                            .offset(y: -(keyboardHeight - blackKeyHeight) / 2)
                        }
                    }
                    .frame(width: availableWidth, height: blackKeyHeight)
                )
            }
            .frame(height: keyboardHeight)
            .onAppear {
                availableWidth = geometry.size.width
            }
            .onChange(of: geometry.size.width) { newWidth in
                availableWidth = newWidth
            }
        }
    }
    
    // MARK: - Private Methods
    
    /// 处理按键按下
    private func handleKeyDown(_ keyNumber: Int, velocity: Int) {
        viewModel.userPressKey(keyNumber)
        onKeyDown?(keyNumber, velocity)
    }
    
    /// 处理按键释放
    private func handleKeyUp(_ keyNumber: Int) {
        viewModel.userReleaseKey(keyNumber)
        onKeyUp?(keyNumber)
    }
    
    /// 计算黑键偏移量
    private func blackKeyOffset(for midi: Int) -> CGFloat {
        let noteInOctave = midi % 12
        
        // 找到这个八度的C键
        let octaveC = midi - noteInOctave
        
        // 找到C键在白键数组中的索引
        // 对于88键钢琴的第一个黑键(MIDI 22, A#0)，其对应的C键是MIDI 12(C1)，不在88键范围内
        // 我们需要使用相对位置计算
        if let cIndex = whiteKeys.firstIndex(where: { $0.id == octaveC }) {
            // 标准情况：找到了对应的C键
            let cPosition = CGFloat(cIndex) * whiteKeyWidth
            return calculateBlackKeyPosition(noteInOctave: noteInOctave, cPosition: cPosition)
        } else {
            // 特殊情况：C键不在当前键盘范围内（如88键的第一个黑键A#0）
            // 使用第一个白键作为参考点，计算相对位置
            let firstWhiteKey = whiteKeys.first!
            //let firstWhiteKeyPosition = 0.0
            
            // 计算从第一个白键到目标黑键的相对位置
            let semitonesFromFirstWhite = midi - firstWhiteKey.id
            let approximatePosition = CGFloat(semitonesFromFirstWhite) * whiteKeyWidth * (7.0/12.0) + blackKeyWidth/2
            
            //print("🎹 特殊黑键位置计算: MIDI \(midi), 相对于第一个白键的位置: \(approximatePosition)")
            return approximatePosition
        }
    }
    
    /// 根据音符在八度中的位置计算黑键位置
    private func calculateBlackKeyPosition(noteInOctave: Int, cPosition: CGFloat) -> CGFloat {
        
        switch noteInOctave {
        case 1:  // C#
            // 把7个白键宽度分成12份，C#左边有1份
            return cPosition + whiteKeyWidth * 7/12.0 * 1 + blackKeyWidth/2
            
        case 3:  // D#
            // 把7个白键宽度分成12份，D#左边有3份
            return cPosition + whiteKeyWidth * 7/12.0 * 3 +  blackKeyWidth/2
            
        case 6:  // F#
            // 把7个白键宽度分成12份，F#左边有6份
            return cPosition + whiteKeyWidth * 7/12.0 * 6 + blackKeyWidth/2
            
        case 8:  // G#
            // 把7个白键宽度分成12份，G#左边有8份
            return cPosition + whiteKeyWidth * 7/12.0 * 8 +  blackKeyWidth/2
            
        case 10: // A#
            // 把7个白键宽度分成12份，A#左边有10份
            return cPosition + whiteKeyWidth * 7/12.0 * 10 +  blackKeyWidth/2
            
        default:
            return 0
        }
    }
    
    /// 生成键盘的静态方法
    private static func generateKeys(from firstKey: Int, to lastKey: Int) -> [PianoKey] {
        var keys = [PianoKey]()
        for midi in firstKey...lastKey {
            let noteInOctave = midi % 12
            let isBlack = [1, 3, 6, 8, 10].contains(noteInOctave)
            let label = NoteNameGenerator.getNoteNameOnly(note: midi)
            keys.append(PianoKey(id: midi, isBlack: isBlack, label: label))
        }
        return keys
    }
}

// MARK: - PianoKeyView

/// 显示单个琴键的视图
struct PianoKeyView: View {
    let key: PianoKey
    let keyColor: Color
    let whiteKeyWidth: CGFloat
    let blackKeyWidth: CGFloat
    let keyboardHeight: CGFloat
    let blackKeyHeight: CGFloat
    let viewModel: PianoKeyboardViewModel
    var onDown: (Int) -> Void // velocity parameter
    var onUp: () -> Void
    
    @State private var isPressed: Bool = false
    
    // 计算是否应该显示背景图像（只有在没有任何按键状态时才显示）
    private var shouldShowBackgroundImage: Bool {
        return viewModel.getKeyState(for: key.id) == nil
    }
    
    // 计算是否显示音名（只有用户按键时显示）
    private var shouldShowNoteName: Bool {
        if let keyState = viewModel.getKeyState(for: key.id) {
            return keyState.isUserPressed
        }
        return false
    }
    
    // 计算音名字体大小（根据键宽自适应）
    private var noteNameFontSize: CGFloat {
        let keyWidth = key.isBlack ? blackKeyWidth : whiteKeyWidth
        return min(keyWidth * 0.8, 12) // 最大12pt，最小为键宽的30%
    }
    
    var body: some View {
        ZStack {
            // 背景图像层（只在没有按键状态时显示）
            if shouldShowBackgroundImage {
                if key.isBlack {
                    #if os(iOS)
                    if let uiImage = UIImage(named: "bk_1") {
                        Image(uiImage: uiImage)
                            .resizable()
                            .frame(
                                width: blackKeyWidth,
                                height: blackKeyHeight
                            )
                    } else {
                        Rectangle()
                            .foregroundColor(.black)
                    }
                    #elseif os(macOS)
                    if let nsImage = NSImage(named: "bk_1") {
                        Image(nsImage: nsImage)
                            .resizable()
                            .frame(
                                width: blackKeyWidth,
                                height: blackKeyHeight
                            )
                    } else {
                        Rectangle()
                            .foregroundColor(.black)
                    }
                    #endif
                } else {
                    #if os(iOS)
                    if let uiImage = UIImage(named: "wk_1") {
                        Image(uiImage: uiImage)
                            .resizable()
                            .frame(
                                width: whiteKeyWidth,
                                height: keyboardHeight
                            )
                    } else {
                        Rectangle()
                            .foregroundColor(.white)
                    }
                    #elseif os(macOS)
                    if let nsImage = NSImage(named: "wk_1") {
                        Image(nsImage: nsImage)
                            .resizable()
                            .frame(
                                width: whiteKeyWidth,
                                height: keyboardHeight
                            )
                    } else {
                        Rectangle()
                            .foregroundColor(.white)
                    }
                    #endif
                }
            } else {
                // 原有的纯色背景（按下时显示）
                Rectangle()
                    .foregroundColor(keyColor)
            }
            
            // 边框
            Rectangle()
                .stroke(Color.gray, lineWidth: 1)
                .frame(
                    width: key.isBlack ? blackKeyWidth : whiteKeyWidth,
                    height: key.isBlack ? blackKeyHeight : keyboardHeight
                )
            
            // 音名显示（在按键顶部上方） 黑白键错开
            if shouldShowNoteName {
                Text(key.label)
                    .font(.system(size: noteNameFontSize, weight: .bold))
                    .foregroundColor(.black)
                    .background(
                        RoundedRectangle(cornerRadius: 4)
                            .fill(Color.white.opacity(0.9))
                            .frame(width: min(key.isBlack ? blackKeyWidth*1.5  : whiteKeyWidth*0.8, 40))
                    )
                    .frame(
                        width: key.isBlack ? blackKeyWidth * 1.5: whiteKeyWidth,
                    )
                    .position(
                        x: (key.isBlack ? blackKeyWidth : whiteKeyWidth) / 2,
                        y: (key.isBlack ? -25 : -10)
                       // y: -15 // 在按键顶部上方15pt
                    )
                    .zIndex(10)
            }
        }
        .frame(
            width: key.isBlack ? blackKeyWidth: whiteKeyWidth,
            height: key.isBlack ? blackKeyHeight : keyboardHeight
        )
        .overlay(
            // 显示C音符标签
            !key.isBlack && key.id % 12 == 0 ?
            Text(String(key.id/12 - 1))
            //Text(key.label)
                .font(.caption2)
                .foregroundColor(labelColor)
                .opacity(labelOpacity)
                .padding(.top, keyboardHeight * 0.8)
            : nil
        )
        .gesture(
            DragGesture(minimumDistance: 0)
                .onChanged { value in 
                    if !isPressed {
                        isPressed = true
                        let velocity = calculateVelocity(from: value)
                        onDown(velocity)
                    }
                }
                .onEnded { _ in 
                    if isPressed {
                        isPressed = false
                        onUp()
                    }
                }
        )
        .zIndex(key.isBlack ? 1 : 0)
    }
    
    /// 计算标签颜色
    private var labelColor: Color {
        // 根据按键颜色调整标签颜色以确保可读性
        if shouldShowBackgroundImage {
            // 如果显示背景图像，使用深色文字以确保在白键图像上可见
            return key.isBlack ? .white : .black
        } else if keyColor == .white {
            return .black
        } else {
            return .white
        }
    }
    
    /// 计算标签透明度
    private var labelOpacity: Double {
        if shouldShowBackgroundImage {
            return 0.8 // 在背景图像上稍微增加透明度以提高可读性
        }
        return keyColor == .white ? 0.5 : 1.0
    }
    
    /// 计算按键速度
    private func calculateVelocity(from dragValue: DragGesture.Value) -> Int {
        let speed = sqrt(pow(dragValue.velocity.width, 2) + pow(dragValue.velocity.height, 2))
        let normalizedSpeed = min(speed / 1000.0, 1.0)
        
        let baseVelocity = 60
        let velocityRange = 67
        let velocity = baseVelocity + Int(normalizedSpeed * Double(velocityRange))
        
        return max(60, min(127, velocity))
    }
}

// MARK: - Extensions

extension PianoKeyboardView {
    /// 创建一个指定键范围的钢琴键盘视图
    static func withKeyRange(
        firstKey: Int,
        lastKey: Int,
        viewModel: PianoKeyboardViewModel,
        onKeyDown: ((Int, Int) -> Void)? = nil,
        onKeyUp: ((Int) -> Void)? = nil
    ) -> PianoKeyboardView {
        return PianoKeyboardView(
            viewModel: viewModel,
            firstKey: firstKey,
            lastKey: lastKey,
            onKeyDown: onKeyDown,
            onKeyUp: onKeyUp
        )
    }
    
    /// 兼容性方法：使用highlightedNotes（向后兼容）
    static func withKeyRange(
        firstKey: Int,
        lastKey: Int,
        highlightedNotes: Set<Int>,
        onKeyDown: ((Int, Int) -> Void)? = nil,
        onKeyUp: ((Int) -> Void)? = nil
    ) -> PianoKeyboardView {
        return PianoKeyboardView(
            highlightedNotes: highlightedNotes,
            firstKey: firstKey,
            lastKey: lastKey,
            onKeyDown: onKeyDown,
            onKeyUp: onKeyUp
        )
    }
}
