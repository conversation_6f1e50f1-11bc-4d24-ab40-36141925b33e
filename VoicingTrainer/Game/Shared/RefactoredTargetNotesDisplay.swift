//
//  RefactoredTargetNotesDisplay.swift
//  VoicingTrainer
//
//  重构后的目标音符显示组件 - 使用组件分离架构
//

import SwiftUI

/// 重构后的目标音符显示组件
struct RefactoredTargetNotesDisplay: View {
    let expectedNotes: Set<Int>
    let playedNotes: Set<Int>
    let currentKey: String
    let gameState: ChordGameState
    
    @StateObject private var gameStateManager = NoteGameStateManager()
    @ObservedObject private var particleCoordinator = ParticleCoordinator.shared
    @State private var notePositions: [Int: CGPoint] = [:]
    
    var body: some View {
        ZStack {
            // 简化的音符显示
            if !expectedNotes.isEmpty {
                SimpleNoteDisplay(
                    expectedNotes: expectedNotes,
                    playedNotes: playedNotes,
                    correctNotes: gameStateManager.correctNotes,
                    explodedNotes: gameStateManager.explodedNotes,
                    onNoteTap: handleNoteTap,
                    onNotePosition: handleNotePosition
                )
            } else {
                emptyStateView
            }
            
            // 粒子效果层
            SpriteKitParticleView()
                .environmentObject(particleCoordinator)
                .allowsHitTesting(false)
        }
        .coordinateSpace(name: "RefactoredNotesDisplay")
        .onAppear {
            setupGameStateManager()
            // 立即设置期望音符，确保第一轮正常工作
            if !expectedNotes.isEmpty {
                gameStateManager.setExpectedNotes(expectedNotes)
            }
        }
        .onChange(of: expectedNotes) { newNotes in
            print("🔄 expectedNotes 改变: \(newNotes.sorted())")
            gameStateManager.setExpectedNotes(newNotes)
        }
        .onChange(of: playedNotes) { newPlayedNotes in
            print("🎹 playedNotes 改变: \(newPlayedNotes.sorted())")
            print("📍 当前notePositions: \(notePositions)")
            gameStateManager.updatePlayedNotes(newPlayedNotes, notePositions: notePositions)
        }
        .onChange(of: gameStateManager.shouldShowPerfectAnimation) { shouldShow in
            if shouldShow {
                triggerPerfectAnimation()
            }
        }
    }
    
    private var emptyStateView: some View {
        Text("Select a voicing and start the game")
            .font(.title3)
            .foregroundColor(.secondary)
            .multilineTextAlignment(.center)
            .padding(.vertical, 40)
    }
    
    // MARK: - 事件处理
    
    private func setupGameStateManager() {
        gameStateManager.onNoteHit = { note, position in
            triggerNoteExplosion(note: note, at: position)
        }
        
        gameStateManager.onChordComplete = {
            // 可以在这里添加和弦完成的额外逻辑
        }
    }
    
    private func handleNoteTap(_ note: Int) {
        print("🎹 点击音符: \(note)")
        // 可以添加音符播放逻辑
        HapticFeedback.selection()
    }
    
    private func handleNotePosition(_ note: Int, _ position: CGPoint) {
        notePositions[note] = position
        print("📍 音符 \(note) 位置更新: \(position)")
        print("📊 当前所有位置: \(notePositions)")
    }
    
    private func triggerNoteExplosion(note: Int, at position: CGPoint) {
        print("💥 触发音符爆炸: \(note) at \(position)")
        let noteName = NoteNameGenerator.getNoteName(note: UInt8(note))
        particleCoordinator.explodeNote(noteName, at: position, color: .green)
        HapticFeedback.impact(.medium)
    }
    
    private func triggerPerfectAnimation() {
        print("🌟 触发Perfect动画")
        particleCoordinator.showPerfectEffect(fromScale: 1.0, toScale: 1.5)
    }
}

// MARK: - 预览
#Preview {
    RefactoredTargetNotesDisplay(
        expectedNotes: Set([60, 64, 67]),
        playedNotes: Set([60]),
        currentKey: "C Major",
        gameState: .waitingForResponse
    )
    .padding()
    .background(Color.gray.opacity(0.1))
} 