---
description:
globs:
alwaysApply: false
---
# VoicingTrainer Project Architecture

## Project Overview
VoicingTrainer is a musical training application built with SwiftUI that provides level-based note recognition games using MIDI input. The app supports both iOS and macOS platforms.

## Core Architecture

### Main Components
- **[NotesView.swift](mdc:VoicingTrainer/Game/Note/NotesView.swift)** - Primary game interface with visual effects and controls
- **[NoteGameManager.swift](mdc:VoicingTrainer/Core/Managers/NoteGameManager.swift)** - Core game logic, state management, and level progression
- **[NoteLevelData.swift](mdc:VoicingTrainer/Game/Note/Models/NoteLevelData.swift)** - Level data models and note generation logic
- **[MIDIManager.swift](mdc:VoicingTrainer/Core/Managers/MIDIManager.swift)** - MIDI input/output handling and note playback

### Data Flow
1. **Level Selection** → LevelSelectView → NoteLevelManager
2. **Level Start** → LevelStartView → CountdownView → Game Start
3. **Game Loop** → Note Generation → MIDI Playback → User Input → Feedback → Next Round
4. **Level Complete** → LevelClearView → Statistics → Progression

## State Management Patterns

### ObservableObject Pattern
- All managers use `@Published` properties for reactive UI updates
- State changes trigger automatic UI refreshes
- Complex state transitions managed through enum-based game states

### Key State Properties
- `gameState: GameState` - Controls game flow (.idle, .playing, .waitingForResponse, .completed, .countdown)
- `currentLevel: NoteExercise?` - Active level configuration
- `currentRound/currentLevelRoundCount` - Progress tracking
- `noteFeedback: NoteFeedback` - Visual feedback state

## Level System

### Level Configuration
- **[notesGame.json](mdc:VoicingTrainer/Resources/Data/notesGame.json)** - Level definitions and note selection rules
- Two note selection types: `specificNotes` (with octave ranges) and `range` (with key filters)
- Optional `roundCount` per level overrides default configuration

### Level Data Models
```swift
struct NoteExercise: Codable, Identifiable {
    let id: String
    let description: String
    let noteSelection: NoteSelection
    let roundCount: Int? // Optional per-level round count
}
```

## MIDI Integration

### Key Considerations
- **Race Condition Prevention**: Use `DispatchQueue.main.sync` for atomic state checking
- **Note State Management**: Separate `pressedNotes` from `listenNote` playback
- **Input Processing**: Track `lastProcessedNotes` to avoid duplicate processing

### Critical Pattern
```swift
// Always check game state before processing MIDI input
guard gameState == .waitingForResponse else { return }
```

## Visual Effects System

### Dual Effect Support
- **String Effects** - Traditional guitar string vibration simulation
- **Wave Effects** - Modern waveform visualization
- Toggle between effects using `useWaveEffect` state

### Effect Controllers
- `StringController` - Manages string vibration animations
- `WaveStringController` - Manages wave effect animations

## Common Development Patterns

### Async Timer Management
```swift
// Always invalidate timers in stopAllTimers()
waitTimer?.invalidate()
responseTimer?.invalidate()
```

### Cross-Platform Compatibility
```swift
#if os(iOS)
// iOS-specific code
#endif
```

### Error Prevention
- Check array bounds before accessing levels
- Validate MIDI note ranges (0-127)
- Handle empty note collections gracefully

## Debugging Guidelines

### Common Issues
1. **Infinite Loops** - Check single-note levels vs. repetition avoidance
2. **State Transitions** - Verify game state changes in proper sequence
3. **Timer Conflicts** - Ensure proper timer cleanup
4. **MIDI Timing** - Watch for race conditions between Note On/Off events

### Debug Logging Pattern
```swift
print("ComponentName: Action - State: \(currentState)")
```
