---
description:
globs:
alwaysApply: false
---
# UI Components Guide

## View Hierarchy

### Main Game Interface
- **[NotesView.swift](mdc:VoicingTrainer/Game/Note/NotesView.swift)** - Root game view containing all game UI elements
  - Background effects (string/wave animations)
  - Top navigation (level select, replay, middle C, effect toggle)
  - Center content (level info, status, play controls)
  - Overlay views (level start, level clear, level select)

### Level Management Views
- **[LevelSelectView.swift](mdc:VoicingTrainer/Game/Note/Views/LevelSelectView.swift)** - Grid-based level selection interface
- **[LevelStartView.swift](mdc:VoicingTrainer/Game/Note/Views/LevelStartView.swift)** - Level introduction with animated details
- **[CountdownView.swift](mdc:VoicingTrainer/Game/Note/Views/CountdownView.swift)** - 3-2-1 countdown with gradient animations
- **[LevelClearView.swift](mdc:VoicingTrainer/Game/Note/Views/LevelClearView.swift)** - Results screen with star ratings and celebration effects

## SwiftUI Patterns

### State Management
```swift
@StateObject private var gameManager: NoteGameManager
@StateObject private var levelManager = NoteLevelManager()
@ObservedObject var midiManager: MIDIManager
```

### Conditional UI Rendering
```swift
// Level-based content visibility
if let level = gameManager.currentLevel {
    // Show level-specific UI
} else {
    // Show level selection prompt
}
```

### Z-Index Layering
```swift
.zIndex(3) // Level Select (top)
.zIndex(2) // Level Start
.zIndex(1) // Level Clear
// Default (0) // Main game interface
```

## Visual Effects Integration

### Background Color Feedback
- Dynamic background colors based on `noteFeedback` state
- Smooth color transitions using SwiftUI animations
- Color coding: Green (correct), Red/Yellow (sharp), Blue (flat)

### Animation Patterns
```swift
.scaleEffect(animated ? 1.0 : 0.8)
.opacity(animated ? 1.0 : 0.0)
.animation(.spring(response: 0.6, dampingFraction: 0.8), value: animated)
```

## UI State Synchronization

### Reactive Updates
- `@Published` properties automatically trigger UI updates
- Combine framework used for complex state subscriptions
- MIDI input changes immediately reflect in visual feedback

### Timer-Based UI Updates
- Response timers control game flow transitions
- Visual countdown animations sync with game timing
- Progress indicators update in real-time

## Accessibility Considerations

### Button Design
- Consistent 44x44 point touch targets
- Clear visual feedback for interactive elements
- Semantic colors (green for play, red for stop, etc.)

### Information Hierarchy
- Large, prominent game status indicators
- Secondary information in compact status view
- Clear visual separation between game areas

## Cross-Platform UI Adaptations

### Conditional Compilation
```swift
#if os(iOS)
// iOS-specific UI adjustments
#endif
```

### Responsive Layout
- Flexible spacing and sizing using SwiftUI's adaptive layout
- Platform-appropriate visual materials (ultraThinMaterial)
- Safe area handling for different device form factors

## Component Communication Patterns

### Parent-Child Data Flow
- Parent views pass data down via initializer parameters
- Child views communicate up via closure callbacks
- Shared state managed through ObservableObject managers

### Modal Presentation
```swift
@Binding var isPresented: Bool
// Child view can dismiss itself by setting isPresented = false
```

## Performance Considerations

### View Updates
- Minimize expensive operations in view body
- Use `@State` for local UI state, `@Published` for shared state
- Avoid unnecessary view recomputations with proper state scope

### Animation Performance
- Use SwiftUI's built-in animation modifiers
- Prefer implicit animations over explicit ones for simple cases
- Limit concurrent animations to prevent performance issues
