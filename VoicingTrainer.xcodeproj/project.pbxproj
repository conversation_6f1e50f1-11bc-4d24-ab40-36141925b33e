// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXBuildFile section */
		2E4AE7E22E2A50C900DF3D49 /* VoicingWorkout_IAP.storekit in Resources */ = {isa = PBXBuildFile; fileRef = 2E4AE7E12E2A50C900DF3D49 /* VoicingWorkout_IAP.storekit */; };
		2E79D0C02DE436700052D3E8 /* AudioKit in Frameworks */ = {isa = PBXBuildFile; productRef = 2E79D0BF2DE436700052D3E8 /* AudioKit */; };
		2E79D0C32DE436B70052D3E8 /* MIDIKit in Frameworks */ = {isa = PBXBuildFile; productRef = 2E79D0C22DE436B70052D3E8 /* MIDIKit */; };
		2E79D0C52DE436B70052D3E8 /* MIDIKitControlSurfaces in Frameworks */ = {isa = PBXBuildFile; productRef = 2E79D0C42DE436B70052D3E8 /* MIDIKitControlSurfaces */; };
		2E79D0C72DE436B70052D3E8 /* MIDIKitCore in Frameworks */ = {isa = PBXBuildFile; productRef = 2E79D0C62DE436B70052D3E8 /* MIDIKitCore */; };
		2E79D0C92DE436B70052D3E8 /* MIDIKitIO in Frameworks */ = {isa = PBXBuildFile; productRef = 2E79D0C82DE436B70052D3E8 /* MIDIKitIO */; };
		2E79D0CB2DE436B70052D3E8 /* MIDIKitSMF in Frameworks */ = {isa = PBXBuildFile; productRef = 2E79D0CA2DE436B70052D3E8 /* MIDIKitSMF */; };
		2E79D0CE2DE436D10052D3E8 /* AudioKitEX in Frameworks */ = {isa = PBXBuildFile; productRef = 2E79D0CD2DE436D10052D3E8 /* AudioKitEX */; };
		2EA9DC6D2DE458750074AB65 /* SoundpipeAudioKit in Frameworks */ = {isa = PBXBuildFile; productRef = 2EA9DC6C2DE458750074AB65 /* SoundpipeAudioKit */; };
		2ED3B66D2E36408000470B2E /* Lottie in Frameworks */ = {isa = PBXBuildFile; productRef = 2ED3B66C2E36408000470B2E /* Lottie */; };
		2ED808052E35F96300262CCF /* progressions in Resources */ = {isa = PBXBuildFile; fileRef = 2ED808042E35F96300262CCF /* progressions */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		2E79D0732DE41F3D0052D3E8 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 2E79D05C2DE41F3C0052D3E8 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 2E79D0632DE41F3C0052D3E8;
			remoteInfo = VoicingTrainer;
		};
		2E79D07D2DE41F3D0052D3E8 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 2E79D05C2DE41F3C0052D3E8 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 2E79D0632DE41F3C0052D3E8;
			remoteInfo = VoicingTrainer;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		2E4AE7E12E2A50C900DF3D49 /* VoicingWorkout_IAP.storekit */ = {isa = PBXFileReference; lastKnownFileType = text; name = VoicingWorkout_IAP.storekit; path = VoicingTrainer/Resources/VoicingWorkout_IAP.storekit; sourceTree = "<group>"; };
		2E79D0642DE41F3C0052D3E8 /* VoicingTrainer.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = VoicingTrainer.app; sourceTree = BUILT_PRODUCTS_DIR; };
		2E79D0722DE41F3D0052D3E8 /* VoicingTrainerTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = VoicingTrainerTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		2E79D07C2DE41F3D0052D3E8 /* VoicingTrainerUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = VoicingTrainerUITests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		2ED808042E35F96300262CCF /* progressions */ = {isa = PBXFileReference; lastKnownFileType = folder; name = progressions; path = VoicingTrainer/Resources/Data/progressions; sourceTree = "<group>"; };
		2EDE760D2DE552AA006E89BD /* VoicingTrainer.xctestplan */ = {isa = PBXFileReference; lastKnownFileType = text; path = VoicingTrainer.xctestplan; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedBuildFileExceptionSet section */
		2EB1FFF52DE8A26D0083562E /* Exceptions for "VoicingTrainer" folder in "VoicingTrainer" target */ = {
			isa = PBXFileSystemSynchronizedBuildFileExceptionSet;
			membershipExceptions = (
				chord_name_generator.py,
				ChordMatchManager_Integration_Test.md,
				ChromaCircleView_BugFix_Summary.md,
				Circle12NotesView_BugFix_TestGuide.md,
				Circle12NotesView_ParticleEffect_Guide.md,
				Documentation/DailyTestingPractice.md,
				Documentation/DevLogs/COORDINATE_CONVERSION_FIXES.md,
				Documentation/DevLogs/COORDINATE_DEBUG_SUMMARY.md,
				Documentation/DevLogs/COORDINATE_FIX_FINAL.md,
				Documentation/DevLogs/CROSS_PLATFORM_FIXES.md,
				Documentation/DevLogs/DEBUG_PARTICLE_FIXES.md,
				Documentation/DevLogs/OFF_BY_ONE_FIX.md,
				Documentation/DevLogs/PARTICLE_EXPLOSION_FIXES.md,
				Documentation/DevLogs/PARTICLE_SYSTEM_IMPLEMENTATION.md,
				Documentation/DevLogs/PERFECT_ANIMATION_ENHANCEMENT.md,
				Documentation/DevLogs/PRACTICE_PATTERN_REFACTOR.md,
				Documentation/DevLogs/REFACTOR_SUMMARY.md,
				Documentation/FXChordView.md,
				Documentation/Localization_Resources_List.md,
				Documentation/ProgressionStyleGuide.md,
				Documentation/ProjectRename.md,
				Documentation/ProjectReorganization.md,
				Documentation/ProjectStructure.md,
				Documentation/README_Testing.md,
				Documentation/TestingGuide.md,
				Info.plist,
				piano_touch_test_guide.md,
				PowerTestScript.md,
				progression_converter_v2.py,
				progression_converter.py,
				"Resources/Data/progressions/251 simple form.progression",
				"Resources/Data/progressions/251 simple form2.progression",
				"Resources/Data/progressions/251-4noteA.progression",
				Resources/Data/progressions/1625.progression,
				Resources/Data/progressions/bigcity.progression,
				"Resources/Data/progressions/chill 01.progression",
				"Resources/Data/progressions/chill mood.progression",
				"Resources/Data/progressions/Chill RnB2.progression",
				Resources/Data/progressions/CityPop01.progression,
				"Resources/Data/progressions/Diatonic Triads.progression",
				Resources/Data/progressions/funk01.progression,
				Resources/Data/progressions/funk02.progression,
				Resources/Data/progressions/funk03.progression,
				Resources/Data/progressions/funk04.progression,
				Resources/Data/progressions/funk05.progression,
				"Resources/Data/progressions/I-IV RnB.progression",
				Resources/Data/progressions/JazzHipHop01.progression,
				Resources/Data/progressions/JazzHipHop02.progression,
				Resources/Data/progressions/JazzHipHop03.progression,
				Resources/Data/progressions/Lofi01.progression,
				Resources/Data/progressions/lofi03.progression,
				Resources/Data/progressions/Lofi04.progression,
				Resources/Data/progressions/Lofi05.progression,
				Resources/Data/progressions/Lofi08.progression,
				"Resources/Data/progressions/major 251 close mode B.progression",
				"Resources/Data/progressions/major 251(voicing A).progression",
				"Resources/Data/progressions/major 251(voicing B).progression",
				"Resources/Data/progressions/major251 simple form3.progression",
				"Resources/Data/progressions/minor 251 P1.progression",
				"Resources/Data/progressions/minor I-IV RnB01.progression",
				"Resources/Data/progressions/minor251-simple form2.progression",
				Resources/Data/progressions/RnB01.progression,
				"Resources/Data/progressions/rootless major 251 mode B.progression",
				"Resources/Data/progressions/rootless major 251-mode A.progression",
				"Resources/Data/progressions/so what.progression",
				"Resources/Data/progressions/soulful chordz.progression",
				"Resources/Data/progressions/soulful neosoul B.progression",
				"Resources/Data/progressions/soulful neosoul.progression",
				"Resources/Data/progressions/Soulful RnB03.progression",
			);
			target = 2E79D0632DE41F3C0052D3E8 /* VoicingTrainer */;
		};
/* End PBXFileSystemSynchronizedBuildFileExceptionSet section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		2E79D0662DE41F3C0052D3E8 /* VoicingTrainer */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			exceptions = (
				2EB1FFF52DE8A26D0083562E /* Exceptions for "VoicingTrainer" folder in "VoicingTrainer" target */,
			);
			path = VoicingTrainer;
			sourceTree = "<group>";
		};
		2E79D0752DE41F3D0052D3E8 /* VoicingTrainerTests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = VoicingTrainerTests;
			sourceTree = "<group>";
		};
		2E79D07F2DE41F3D0052D3E8 /* VoicingTrainerUITests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = VoicingTrainerUITests;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		2E79D0612DE41F3C0052D3E8 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				2E79D0C52DE436B70052D3E8 /* MIDIKitControlSurfaces in Frameworks */,
				2E79D0CE2DE436D10052D3E8 /* AudioKitEX in Frameworks */,
				2ED3B66D2E36408000470B2E /* Lottie in Frameworks */,
				2EA9DC6D2DE458750074AB65 /* SoundpipeAudioKit in Frameworks */,
				2E79D0C92DE436B70052D3E8 /* MIDIKitIO in Frameworks */,
				2E79D0C32DE436B70052D3E8 /* MIDIKit in Frameworks */,
				2E79D0CB2DE436B70052D3E8 /* MIDIKitSMF in Frameworks */,
				2E79D0C02DE436700052D3E8 /* AudioKit in Frameworks */,
				2E79D0C72DE436B70052D3E8 /* MIDIKitCore in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2E79D06F2DE41F3D0052D3E8 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2E79D0792DE41F3D0052D3E8 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		2E79D05B2DE41F3C0052D3E8 = {
			isa = PBXGroup;
			children = (
				2ED808042E35F96300262CCF /* progressions */,
				2E4AE7E12E2A50C900DF3D49 /* VoicingWorkout_IAP.storekit */,
				2EDE760D2DE552AA006E89BD /* VoicingTrainer.xctestplan */,
				2E79D0662DE41F3C0052D3E8 /* VoicingTrainer */,
				2E79D0752DE41F3D0052D3E8 /* VoicingTrainerTests */,
				2E79D07F2DE41F3D0052D3E8 /* VoicingTrainerUITests */,
				2E79D0652DE41F3C0052D3E8 /* Products */,
			);
			sourceTree = "<group>";
		};
		2E79D0652DE41F3C0052D3E8 /* Products */ = {
			isa = PBXGroup;
			children = (
				2E79D0642DE41F3C0052D3E8 /* VoicingTrainer.app */,
				2E79D0722DE41F3D0052D3E8 /* VoicingTrainerTests.xctest */,
				2E79D07C2DE41F3D0052D3E8 /* VoicingTrainerUITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		2E79D0632DE41F3C0052D3E8 /* VoicingTrainer */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 2E79D0862DE41F3D0052D3E8 /* Build configuration list for PBXNativeTarget "VoicingTrainer" */;
			buildPhases = (
				2E79D0602DE41F3C0052D3E8 /* Sources */,
				2E79D0612DE41F3C0052D3E8 /* Frameworks */,
				2E79D0622DE41F3C0052D3E8 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				2E79D0662DE41F3C0052D3E8 /* VoicingTrainer */,
			);
			name = VoicingTrainer;
			packageProductDependencies = (
				2E79D0BF2DE436700052D3E8 /* AudioKit */,
				2E79D0C22DE436B70052D3E8 /* MIDIKit */,
				2E79D0C42DE436B70052D3E8 /* MIDIKitControlSurfaces */,
				2E79D0C62DE436B70052D3E8 /* MIDIKitCore */,
				2E79D0C82DE436B70052D3E8 /* MIDIKitIO */,
				2E79D0CA2DE436B70052D3E8 /* MIDIKitSMF */,
				2E79D0CD2DE436D10052D3E8 /* AudioKitEX */,
				2EA9DC6C2DE458750074AB65 /* SoundpipeAudioKit */,
				2ED3B66C2E36408000470B2E /* Lottie */,
			);
			productName = VoicingTrainer;
			productReference = 2E79D0642DE41F3C0052D3E8 /* VoicingTrainer.app */;
			productType = "com.apple.product-type.application";
		};
		2E79D0712DE41F3D0052D3E8 /* VoicingTrainerTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 2E79D0892DE41F3D0052D3E8 /* Build configuration list for PBXNativeTarget "VoicingTrainerTests" */;
			buildPhases = (
				2E79D06E2DE41F3D0052D3E8 /* Sources */,
				2E79D06F2DE41F3D0052D3E8 /* Frameworks */,
				2E79D0702DE41F3D0052D3E8 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				2E79D0742DE41F3D0052D3E8 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				2E79D0752DE41F3D0052D3E8 /* VoicingTrainerTests */,
			);
			name = VoicingTrainerTests;
			packageProductDependencies = (
			);
			productName = VoicingTrainerTests;
			productReference = 2E79D0722DE41F3D0052D3E8 /* VoicingTrainerTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		2E79D07B2DE41F3D0052D3E8 /* VoicingTrainerUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 2E79D08C2DE41F3D0052D3E8 /* Build configuration list for PBXNativeTarget "VoicingTrainerUITests" */;
			buildPhases = (
				2E79D0782DE41F3D0052D3E8 /* Sources */,
				2E79D0792DE41F3D0052D3E8 /* Frameworks */,
				2E79D07A2DE41F3D0052D3E8 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				2E79D07E2DE41F3D0052D3E8 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				2E79D07F2DE41F3D0052D3E8 /* VoicingTrainerUITests */,
			);
			name = VoicingTrainerUITests;
			packageProductDependencies = (
			);
			productName = VoicingTrainerUITests;
			productReference = 2E79D07C2DE41F3D0052D3E8 /* VoicingTrainerUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		2E79D05C2DE41F3C0052D3E8 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1600;
				LastUpgradeCheck = 1600;
				TargetAttributes = {
					2E79D0632DE41F3C0052D3E8 = {
						CreatedOnToolsVersion = 16.0;
					};
					2E79D0712DE41F3D0052D3E8 = {
						CreatedOnToolsVersion = 16.0;
						TestTargetID = 2E79D0632DE41F3C0052D3E8;
					};
					2E79D07B2DE41F3D0052D3E8 = {
						CreatedOnToolsVersion = 16.0;
						TestTargetID = 2E79D0632DE41F3C0052D3E8;
					};
				};
			};
			buildConfigurationList = 2E79D05F2DE41F3C0052D3E8 /* Build configuration list for PBXProject "VoicingTrainer" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
				"zh-Hans",
			);
			mainGroup = 2E79D05B2DE41F3C0052D3E8;
			minimizedProjectReferenceProxies = 1;
			packageReferences = (
				2E79D0BE2DE436700052D3E8 /* XCRemoteSwiftPackageReference "AudioKit" */,
				2E79D0C12DE436B70052D3E8 /* XCRemoteSwiftPackageReference "MIDIKit" */,
				2E79D0CC2DE436D10052D3E8 /* XCRemoteSwiftPackageReference "AudioKitEX" */,
				2EA9DC6B2DE458750074AB65 /* XCRemoteSwiftPackageReference "SoundpipeAudioKit" */,
				2ED3B66B2E36408000470B2E /* XCRemoteSwiftPackageReference "lottie-ios" */,
			);
			preferredProjectObjectVersion = 77;
			productRefGroup = 2E79D0652DE41F3C0052D3E8 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				2E79D0632DE41F3C0052D3E8 /* VoicingTrainer */,
				2E79D0712DE41F3D0052D3E8 /* VoicingTrainerTests */,
				2E79D07B2DE41F3D0052D3E8 /* VoicingTrainerUITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		2E79D0622DE41F3C0052D3E8 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				2ED808052E35F96300262CCF /* progressions in Resources */,
				2E4AE7E22E2A50C900DF3D49 /* VoicingWorkout_IAP.storekit in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2E79D0702DE41F3D0052D3E8 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2E79D07A2DE41F3D0052D3E8 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		2E79D0602DE41F3C0052D3E8 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2E79D06E2DE41F3D0052D3E8 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2E79D0782DE41F3D0052D3E8 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		2E79D0742DE41F3D0052D3E8 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 2E79D0632DE41F3C0052D3E8 /* VoicingTrainer */;
			targetProxy = 2E79D0732DE41F3D0052D3E8 /* PBXContainerItemProxy */;
		};
		2E79D07E2DE41F3D0052D3E8 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 2E79D0632DE41F3C0052D3E8 /* VoicingTrainer */;
			targetProxy = 2E79D07D2DE41F3D0052D3E8 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		2E79D0842DE41F3D0052D3E8 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 16.0;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MACOSX_DEPLOYMENT_TARGET = 15.0;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx";
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		2E79D0852DE41F3D0052D3E8 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 16.0;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MACOSX_DEPLOYMENT_TARGET = 15.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx";
				SWIFT_COMPILATION_MODE = wholemodule;
			};
			name = Release;
		};
		2E79D0872DE41F3D0052D3E8 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = VoicingTrainer/VoicingTrainer.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"VoicingTrainer/Preview Content\"";
				DEVELOPMENT_TEAM = 2NT377F2QG;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = VoicingTrainer/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = VoicingWorkout;
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.music";
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				INFOPLIST_KEY_NSMediaLibraryUsageDescription = "This app needs media library access for MIDI device detection.";
				INFOPLIST_KEY_NSMicrophoneUsageDescription = "This app needs microphone access to detect MIDI input devices and process audio.";
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UIRequiresFullScreen = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations = "UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight UIInterfaceOrientationPortrait";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown";
				IPHONEOS_DEPLOYMENT_TARGET = 16.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				"LD_RUNPATH_SEARCH_PATHS[sdk=macosx*]" = (
					"$(inherited)",
					"@executable_path/../Frameworks",
				);
				MACOSX_DEPLOYMENT_TARGET = 15.0;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.notesprite.voicing.workout.app;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx";
				SUPPORTS_MACCATALYST = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,6";
			};
			name = Debug;
		};
		2E79D0882DE41F3D0052D3E8 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = VoicingTrainer/VoicingTrainer.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"VoicingTrainer/Preview Content\"";
				DEVELOPMENT_TEAM = 2NT377F2QG;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = VoicingTrainer/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = VoicingWorkout;
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.music";
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				INFOPLIST_KEY_NSMediaLibraryUsageDescription = "This app needs media library access for MIDI device detection.";
				INFOPLIST_KEY_NSMicrophoneUsageDescription = "This app needs microphone access to detect MIDI input devices and process audio.";
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UIRequiresFullScreen = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations = "UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight UIInterfaceOrientationPortrait";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown";
				IPHONEOS_DEPLOYMENT_TARGET = 16.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				"LD_RUNPATH_SEARCH_PATHS[sdk=macosx*]" = (
					"$(inherited)",
					"@executable_path/../Frameworks",
				);
				MACOSX_DEPLOYMENT_TARGET = 15.0;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.notesprite.voicing.workout.app;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx";
				SUPPORTS_MACCATALYST = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,6";
			};
			name = Release;
		};
		2E79D08A2DE41F3D0052D3E8 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 17.6;
				MACOSX_DEPLOYMENT_TARGET = 15.0;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.yourcompany.VoicingTrainerTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,6";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/VoicingTrainer.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/VoicingTrainer";
			};
			name = Debug;
		};
		2E79D08B2DE41F3D0052D3E8 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 17.6;
				MACOSX_DEPLOYMENT_TARGET = 15.0;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.yourcompany.VoicingTrainerTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,6";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/VoicingTrainer.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/VoicingTrainer";
			};
			name = Release;
		};
		2E79D08D2DE41F3D0052D3E8 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 16.0;
				MACOSX_DEPLOYMENT_TARGET = 15.0;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.yourcompany.VoicingTrainerUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,6";
				TEST_TARGET_NAME = VoicingTrainer;
			};
			name = Debug;
		};
		2E79D08E2DE41F3D0052D3E8 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 16.0;
				MACOSX_DEPLOYMENT_TARGET = 15.0;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.yourcompany.VoicingTrainerUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,6";
				TEST_TARGET_NAME = VoicingTrainer;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		2E79D05F2DE41F3C0052D3E8 /* Build configuration list for PBXProject "VoicingTrainer" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				2E79D0842DE41F3D0052D3E8 /* Debug */,
				2E79D0852DE41F3D0052D3E8 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		2E79D0862DE41F3D0052D3E8 /* Build configuration list for PBXNativeTarget "VoicingTrainer" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				2E79D0872DE41F3D0052D3E8 /* Debug */,
				2E79D0882DE41F3D0052D3E8 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		2E79D0892DE41F3D0052D3E8 /* Build configuration list for PBXNativeTarget "VoicingTrainerTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				2E79D08A2DE41F3D0052D3E8 /* Debug */,
				2E79D08B2DE41F3D0052D3E8 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		2E79D08C2DE41F3D0052D3E8 /* Build configuration list for PBXNativeTarget "VoicingTrainerUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				2E79D08D2DE41F3D0052D3E8 /* Debug */,
				2E79D08E2DE41F3D0052D3E8 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */

/* Begin XCRemoteSwiftPackageReference section */
		2E79D0BE2DE436700052D3E8 /* XCRemoteSwiftPackageReference "AudioKit" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/AudioKit/AudioKit";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 5.6.4;
			};
		};
		2E79D0C12DE436B70052D3E8 /* XCRemoteSwiftPackageReference "MIDIKit" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/orchetect/MIDIKit";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 0.9.5;
			};
		};
		2E79D0CC2DE436D10052D3E8 /* XCRemoteSwiftPackageReference "AudioKitEX" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/AudioKit/AudioKitEX";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 5.6.0;
			};
		};
		2EA9DC6B2DE458750074AB65 /* XCRemoteSwiftPackageReference "SoundpipeAudioKit" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/AudioKit/SoundpipeAudioKit";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 5.6.1;
			};
		};
		2ED3B66B2E36408000470B2E /* XCRemoteSwiftPackageReference "lottie-ios" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/airbnb/lottie-ios";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 4.5.2;
			};
		};
/* End XCRemoteSwiftPackageReference section */

/* Begin XCSwiftPackageProductDependency section */
		2E79D0BF2DE436700052D3E8 /* AudioKit */ = {
			isa = XCSwiftPackageProductDependency;
			package = 2E79D0BE2DE436700052D3E8 /* XCRemoteSwiftPackageReference "AudioKit" */;
			productName = AudioKit;
		};
		2E79D0C22DE436B70052D3E8 /* MIDIKit */ = {
			isa = XCSwiftPackageProductDependency;
			package = 2E79D0C12DE436B70052D3E8 /* XCRemoteSwiftPackageReference "MIDIKit" */;
			productName = MIDIKit;
		};
		2E79D0C42DE436B70052D3E8 /* MIDIKitControlSurfaces */ = {
			isa = XCSwiftPackageProductDependency;
			package = 2E79D0C12DE436B70052D3E8 /* XCRemoteSwiftPackageReference "MIDIKit" */;
			productName = MIDIKitControlSurfaces;
		};
		2E79D0C62DE436B70052D3E8 /* MIDIKitCore */ = {
			isa = XCSwiftPackageProductDependency;
			package = 2E79D0C12DE436B70052D3E8 /* XCRemoteSwiftPackageReference "MIDIKit" */;
			productName = MIDIKitCore;
		};
		2E79D0C82DE436B70052D3E8 /* MIDIKitIO */ = {
			isa = XCSwiftPackageProductDependency;
			package = 2E79D0C12DE436B70052D3E8 /* XCRemoteSwiftPackageReference "MIDIKit" */;
			productName = MIDIKitIO;
		};
		2E79D0CA2DE436B70052D3E8 /* MIDIKitSMF */ = {
			isa = XCSwiftPackageProductDependency;
			package = 2E79D0C12DE436B70052D3E8 /* XCRemoteSwiftPackageReference "MIDIKit" */;
			productName = MIDIKitSMF;
		};
		2E79D0CD2DE436D10052D3E8 /* AudioKitEX */ = {
			isa = XCSwiftPackageProductDependency;
			package = 2E79D0CC2DE436D10052D3E8 /* XCRemoteSwiftPackageReference "AudioKitEX" */;
			productName = AudioKitEX;
		};
		2EA9DC6C2DE458750074AB65 /* SoundpipeAudioKit */ = {
			isa = XCSwiftPackageProductDependency;
			package = 2EA9DC6B2DE458750074AB65 /* XCRemoteSwiftPackageReference "SoundpipeAudioKit" */;
			productName = SoundpipeAudioKit;
		};
		2ED3B66C2E36408000470B2E /* Lottie */ = {
			isa = XCSwiftPackageProductDependency;
			package = 2ED3B66B2E36408000470B2E /* XCRemoteSwiftPackageReference "lottie-ios" */;
			productName = Lottie;
		};
/* End XCSwiftPackageProductDependency section */
	};
	rootObject = 2E79D05C2DE41F3C0052D3E8 /* Project object */;
}
